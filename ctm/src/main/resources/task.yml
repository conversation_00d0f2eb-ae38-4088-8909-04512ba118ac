befun:
  task:
    type: redisStream
    redis-stream:
      prefix: ${BEFUN_TASK_PREFIX:befun.task}
  extension:
    inbox-message.enable: true
    shorturl:
      root: ${SHORTURL:https://dev-t.xmplus.cn}
      survey-client-prefix: ${SURVEY_CLIENT_PREFIX:https://dev.xmplus.cn/lite/l}
      enable: true
    update-file:
      enable: true
    upload:
      customer-return-size: ${UPLOAD_CUSTOMER_RETUREN_SIZE:50}
      customer-file-max-size: ${UPLOAD_CUSTOMER_FILE_MAX_SIZE:5}