ALTER TABLE `cem_platform`.`journey_component`
ADD COLUMN `relation_id` BIGINT NULL COMMENT '组件关联的其他项目（客户画像id）' AFTER `type`;

ALTER TABLE `cem_platform`.`journey_component`
ADD COLUMN `parent_id` BIGINT NULL DEFAULT 0 AFTER `id`;

ALTER TABLE `cem_platform`.`journey_component_publish`
ADD COLUMN `relation_id` BIGINT NULL COMMENT '组件关联的其他项目（客户画像id）' AFTER `type`;

ALTER TABLE `cem_platform`.`journey_component_publish`
ADD COLUMN `parent_id` BIGINT NULL DEFAULT 0 AFTER `id`;

CREATE TABLE `element_persona`
(
    `id`             bigint    NOT NULL AUTO_INCREMENT,
    `org_id`         bigint,
    `journey_map_id` bigint COMMENT '旅程id',
    `component_id`   bigint COMMENT '组件id',
    `journey_id`     bigint COMMENT '场景id',
    `content`        text COMMENT '内容',
    `create_time`    timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`    timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT '旅程组件-画像(编辑)';

CREATE TABLE `element_persona_publish`
(
    `id`             bigint    NOT NULL AUTO_INCREMENT,
    `org_id`         bigint,
    `journey_map_id` bigint COMMENT '旅程id',
    `component_id`   bigint COMMENT '组件id',
    `journey_id`     bigint COMMENT '场景id',
    `content`        text COMMENT '内容',
    `create_time`    timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`    timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB COMMENT '旅程组件-画像(发布)';