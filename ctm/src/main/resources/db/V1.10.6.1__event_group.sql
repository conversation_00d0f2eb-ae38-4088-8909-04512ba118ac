-- cem_platform.event_result_group_query definition

CREATE TABLE `event_result_group_query` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `user_id` bigint NOT NULL COMMENT '用户id',
  `org_id` bigint NOT NULL COMMENT '组织id',
  `group_id` bigint NOT NULL COMMENT '用户id',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `sequence` int DEFAULT '0' COMMENT '排序',
  `property` text COMMENT '查询条件',
  PRIMARY KEY (`id`),
  KEY `event_result_group_user_id_IDX` (`group_id`) USING BTREE
) ENGINE=InnoDB COMMENT='事件分组表';


-- cem_platform.event_result_group definition

CREATE TABLE `event_result_group` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `title` varchar(200) NOT NULL COMMENT '组名',
  `org_id` bigint NOT NULL COMMENT '组织id',
  `user_id` bigint NOT NULL COMMENT '用户id',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '修改时间',
  `sequence` int DEFAULT '0' COMMENT '排序',
  `type` varchar(10) DEFAULT NULL COMMENT '分组类型',
  `logic` varchar(10) DEFAULT NULL COMMENT '查询逻辑',
  `with_department` int DEFAULT NULL COMMENT '按照当前用户的所属部门过滤数据',
  PRIMARY KEY (`id`),
  KEY `event_result_group_user_id_IDX` (`user_id`) USING BTREE
) ENGINE=InnoDB COMMENT='事件分组表';


INSERT INTO cem_platform.menu (id,create_time,modify_time,display,full_name,full_path,name,`path`,pid,`type`,`sequence`)
	VALUES (107,'2022-09-14 14:35:19','2021-09-09 17:52:51',1,'事件中心/事件处理/分组','/Events/EventAction/group','分组','group',26,3,623)