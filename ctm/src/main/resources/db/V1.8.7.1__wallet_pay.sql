ALTER TABLE `cem_platform`.`organization_wallet`
CHANGE COLUMN `money` `money` INT NOT NULL DEFAULT 0 COMMENT '账户余额' ;

CREATE TABLE `organization_order` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `org_id` bigint DEFAULT NULL,
  `source_id` bigint DEFAULT NULL COMMENT '订单来源id',
  `title` varchar(256) DEFAULT NULL COMMENT '订单名称',
  `type` varchar(45) DEFAULT NULL COMMENT '订单类型：短信消费，红包消费，企业升级',
  `pay_type` varchar(45) DEFAULT NULL COMMENT '支付方式：余额，微信，支付宝，混合（余额+微信），混合（余额+支付宝）',
  `amount` int DEFAULT NULL COMMENT '订单金额（分）',
  `amount_wallet` int DEFAULT NULL COMMENT '余额支付金额',
  `amount_recharge` int DEFAULT NULL COMMENT '充值支付金额',
  `recharge_id` bigint DEFAULT NULL COMMENT '充值支付id',
  `status` varchar(10) DEFAULT NULL COMMENT '订单状态：init 未支付 success 已成功 failure 已失败 cancel 已取消',
  `create_user_id` bigint DEFAULT NULL,
  `deleted` int DEFAULT 0,
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='企业订单';

CREATE TABLE `organization_order_refund` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `org_id` bigint DEFAULT NULL,
  `order_id` bigint DEFAULT NULL COMMENT '订单id',
  `refund_no` varchar(64) DEFAULT NULL COMMENT '退款单号',
  `title` varchar(256) DEFAULT NULL COMMENT '退款订单名称',
  `type` varchar(45) DEFAULT NULL COMMENT '订单类型：短信消费，红包消费，企业升级',
  `amount_refund` int DEFAULT NULL COMMENT '退款金额',
  `create_user_id` bigint DEFAULT NULL,
  `deleted` int DEFAULT 0,
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='企业订单-退款';

CREATE TABLE `organization_bill` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `org_id` bigint DEFAULT NULL,
  `title` varchar(256) DEFAULT NULL COMMENT '交易名称',
  `type` varchar(45) DEFAULT NULL COMMENT '交易类型：短信消费（支出），红包消费（支出），企业升级（支出），余额充值（收入），红包返还（收入）',
  `order_id` bigint DEFAULT NULL  COMMENT '订单id：如果是订单产生的账单',
  `recharge_id` bigint DEFAULT NULL  COMMENT '充值id：如果是充值产生的账单',
  `amount` int DEFAULT NULL COMMENT '交易金额（分）',
  `balance` int DEFAULT NULL COMMENT '账户余额（分）',
  `pay_type` varchar(45) DEFAULT NULL COMMENT '支付方式：余额，微信，支付宝，系统',
  `create_user_id` bigint DEFAULT NULL,
  `deleted` int DEFAULT 0,
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='企业账单';

CREATE TABLE `organization_recharge` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `org_id` bigint DEFAULT NULL,
  `pay_no` varchar(64) DEFAULT NULL COMMENT '企业支付单号',
  `thirtparty_pay_no` varchar(64) DEFAULT NULL COMMENT '第三方支付单号',
  `title` varchar(256) DEFAULT NULL COMMENT '充值名称（商品名称）',
  `order_id` bigint DEFAULT NULL COMMENT '企业订单id，如果是企业订单发起的充值则有此参数',
  `type` varchar(45) DEFAULT NULL COMMENT '充值渠道：微信，支付宝',
  `recharge_amount` int DEFAULT NULL COMMENT '原始充值金额',
  `service_amount` int DEFAULT NULL COMMENT '服务费金额',
  `total_amount` int DEFAULT NULL COMMENT '合计金额',
  `service_rate` FLOAT DEFAULT NULL COMMENT '服务费率',
  `status` varchar(20) DEFAULT NULL COMMENT '充值状态：init 未支付 success 已成功 failure 已失败 cancel 已取消',
  `thirtparty_status` varchar(20) DEFAULT NULL COMMENT '第三方支付状态',
  `mock_recharge` int DEFAULT 0 COMMENT '0 第三方支付充值；1 模拟充值',
  `thirtparty_response` text DEFAULT NULL COMMENT '原始响应内容',
  `create_user_id` bigint DEFAULT NULL,
  `deleted` int DEFAULT 0,
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `expire_time` timestamp NULL DEFAULT NULL COMMENT '过期时间',
  `pay_time` timestamp NULL DEFAULT NULL COMMENT '支付时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='企业充值（微信|支付宝）';

CREATE TABLE `organization_sms_record` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `org_id` bigint DEFAULT NULL,
  `task_progress_id` bigint DEFAULT NULL,
  `content` text DEFAULT NULL COMMENT '短信内容',
  `type` varchar(45) DEFAULT NULL COMMENT '消费类型：短信发送，短信购买',
  `amount` int DEFAULT NULL COMMENT '短信变更',
  `balance` int DEFAULT NULL COMMENT '短信余额',
  `status` varchar(10) DEFAULT NULL COMMENT '状态：init success failure cancel',
  `create_user_id` bigint DEFAULT NULL,
  `deleted` int DEFAULT 0,
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='企业短信记录';