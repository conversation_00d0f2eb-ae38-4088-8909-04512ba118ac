CREATE TABLE `element_event_stat`
(
    `id`             bigint    NOT NULL AUTO_INCREMENT,
    `org_id`         bigint,
    `journey_map_id` bigint COMMENT '旅程id',
    `component_id`   bigint COMMENT '组件id',
    `journey_id`     bigint COMMENT '场景id',
    `event_rule_id`  bigint COMMENT '预警规则id',
    `stat_type`      int COMMENT '统计类型：1 全部 2 待处理',
    `create_time`    timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`    timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT '旅程组件-事件统计(编辑)';

CREATE TABLE `element_event_stat_publish`
(
    `id`             bigint    NOT NULL AUTO_INCREMENT,
    `org_id`         bigint,
    `journey_map_id` bigint COMMENT '旅程id',
    `component_id`   bigint COMMENT '组件id',
    `journey_id`     bigint COMMENT '场景id',
    `event_rule_id`  bigint COMMENT '预警规则id',
    `stat_type`      int COMMENT '统计类型：1 全部 2 待处理',
    `create_time`    timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`    timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT '旅程组件-事件统计(发布)';

CREATE TABLE `journey_warning`
(
    `id`                bigint    NOT NULL AUTO_INCREMENT,
    `org_id`            BIGINT,
    `journey_map_id`    BIGINT COMMENT '旅程id',
    `component_id`      BIGINT COMMENT '组件id',
    `journey_id`        BIGINT COMMENT '场景id',
    `relation_id`       BIGINT COMMENT '关联元素id: 预警规则是和体验指标或者事件统计关联的，这里的id则是指标id或者事件统计id',
    `relation_type`     INT COMMENT '关联组件类型：事件统计 event_stat; 体验指标 experience_indicator',
    `enable_warning`    INT COMMENT '预警规则是否开启：0 否 1 是',
    `warning_frequency` INT COMMENT '预警频率：1 天 2 月',
    `warning_range`     INT COMMENT '预警范围：0(全部)，1，3，6，7，30',
    `warning_compare`   INT COMMENT '预警规则：0 低于 1 高于',
    `warning_value`     DOUBLE COMMENT '预警值：整数或者小数，如果是百分比，先转换为小数在保存',
    `enable_notify`     INT COMMENT '通知是否开启：0 否 1 是',
    `receiver`          VARCHAR(500) COMMENT '通知接收对象',
    `create_time`       timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`       timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE
) COMMENT '旅程预警(发布)';

CREATE TABLE `journey_warning_publish`
(
    `id`                bigint    NOT NULL AUTO_INCREMENT,
    `org_id`            BIGINT,
    `journey_map_id`    BIGINT COMMENT '旅程id',
    `component_id`      BIGINT COMMENT '组件id',
    `journey_id`        BIGINT COMMENT '场景id',
    `relation_id`       BIGINT COMMENT '关联元素id: 预警规则是和体验指标或者事件统计关联的，这里的id则是指标id或者事件统计id',
    `relation_type`     INT COMMENT '关联组件类型：事件统计 event_stat; 体验指标 experience_indicator',
    `enable_warning`    INT COMMENT '预警规则是否开启：0 否 1 是',
    `warning_frequency` INT COMMENT '预警频率：1 天 2 月',
    `warning_range`     INT COMMENT '预警范围：0(全部)，1，3，6，7，30',
    `warning_compare`   INT COMMENT '预警规则：0 低于 1 高于',
    `warning_value`     DOUBLE COMMENT '预警值：整数或者小数，如果是百分比，先转换为小数在保存',
    `enable_notify`     INT COMMENT '通知是否开启：0 否 1 是',
    `receiver`          VARCHAR(500) COMMENT '通知接收对象',
    `create_time`       timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`       timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE
) COMMENT '旅程预警(发布)';