ALTER TABLE `cem_platform`.`thirdparty_customer`
ADD COLUMN `thirdparty_auth_id` BIGINT NULL AFTER `org_id`;

ALTER TABLE `cem_platform`.`thirdparty_template`
ADD COLUMN `thirdparty_auth_id` BIGINT NULL AFTER `org_id`;

ALTER TABLE `cem_platform`.`experience_interaction`
ADD COLUMN `config` TEXT NULL AFTER `expire_moment`;

ALTER TABLE `cem_platform`.`experience_interaction_publish`
ADD COLUMN `config` TEXT NULL AFTER `expire_moment`;

CREATE TABLE `cem_platform`.`wechat_template` (
  `id`                  bigint NOT NULL AUTO_INCREMENT,
  `org_id`              bigint DEFAULT NULL COMMENT '企业id',
  `name`                varchar(64) DEFAULT NULL COMMENT '名称',
  `example`             varchar(1024) DEFAULT NULL COMMENT '描述',
  `content`             varchar(1024) DEFAULT NULL COMMENT '参数内容',
  `parameters`          varchar(1024) DEFAULT NULL COMMENT '参数',
  `thirdparty_template_ids`    text DEFAULT NULL COMMENT '已绑定模版',
  `source_thirdparty_template_id`  bigint DEFAULT NULL COMMENT '来源模版',
  `create_time`         timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `modify_time`         timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='平台微信模版';

ALTER TABLE `cem_platform`.`experience_interaction`
CHANGE COLUMN `interactions_collectors` `interactions_collectors` VARCHAR(24)  NULL DEFAULT '' COMMENT '互动渠道，枚举类型：1-wechat,3-shortmsg,' ,
CHANGE COLUMN `ctm_template_ids` `ctm_template_ids` VARCHAR(255) NULL DEFAULT '[]' COMMENT '推送模版id' ,
CHANGE COLUMN `survey_urls` `survey_urls` VARCHAR(1024) NULL DEFAULT '[]' COMMENT '生成匿名链接' ,
CHANGE COLUMN `is_valid` `is_valid` TINYINT NULL DEFAULT '1' COMMENT '是否有效' ,
CHANGE COLUMN `survey_source` `survey_source` TINYINT NULL DEFAULT '0' COMMENT '问卷来源 0-调研家，1-surveylite' ;

ALTER TABLE `cem_platform`.`experience_interaction_publish`
CHANGE COLUMN `interactions_collectors` `interactions_collectors` VARCHAR(24)  NULL DEFAULT '' COMMENT '互动渠道，枚举类型：1-wechat,3-shortmsg,' ,
CHANGE COLUMN `ctm_template_ids` `ctm_template_ids` VARCHAR(255) NULL DEFAULT '[]' COMMENT '推送模版id' ,
CHANGE COLUMN `survey_urls` `survey_urls` VARCHAR(1024) NULL DEFAULT '[]' COMMENT '生成匿名链接' ,
CHANGE COLUMN `is_valid` `is_valid` TINYINT NULL DEFAULT '1' COMMENT '是否有效' ,
CHANGE COLUMN `survey_source` `survey_source` TINYINT NULL DEFAULT '0' COMMENT '问卷来源 0-调研家，1-surveylite' ;
