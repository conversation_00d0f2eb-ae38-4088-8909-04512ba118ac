CREATE TABLE `task_progress` (
  `id`          bigint NOT NULL AUTO_INCREMENT,
  `org_id`      bigint DEFAULT NULL,
  `user_id`     bigint DEFAULT NULL,
  `type`        varchar(45) DEFAULT NULL COMMENT '任务类型',
  `type_belong` varchar(45) DEFAULT NULL COMMENT '任务所属',
  `status`      varchar(45) DEFAULT NULL COMMENT '任务状态',
  `total_size`  int DEFAULT NULL COMMENT '总数',
  `success_size`int DEFAULT NULL COMMENT '成功数',
  `failed_size` int DEFAULT NULL COMMENT '失败数',
  `params`      text COMMENT '任务参数',
  `result`      text COMMENT '任务结果',
  `expire_time` timestamp NULL DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='任务进度';
