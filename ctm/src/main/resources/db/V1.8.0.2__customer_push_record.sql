CREATE TABLE `customer_push_record` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `org_id` bigint DEFAULT NULL,
  `customer_id` bigint DEFAULT NULL COMMENT '客户id，可能不存在',
  `customer_name` varchar(45) DEFAULT NULL COMMENT '客户姓名，可能不存在',
  `customer_euid` varchar(64) DEFAULT NULL COMMENT '客户外部编号，可能不存在',
  `customer_mobile` varchar(20) DEFAULT NULL COMMENT '客户手机号，可能不存在',
  `survey_id` bigint DEFAULT NULL COMMENT '问卷id',
  `relation_type` varchar(45) DEFAULT NULL COMMENT '记录来源：问卷渠道 SURVEY_CHANNEL；客户旅程 JOURNEY',
  `relation_id` bigint DEFAULT NULL COMMENT '记录来源id：问卷渠道发送记录id；客户旅程记录id',
  `push_type` varchar(45) DEFAULT NULL COMMENT '推送类型:SMS,WECHAT,API',
  `push_params` varchar(512) DEFAULT NULL COMMENT '推送参数：',
  `push_content` varchar(512) DEFAULT NULL COMMENT '推送最终内容，已替换占位符',
  `push_status` int DEFAULT NULL COMMENT '推送状态：0 未发送 1 发送成功 2 发送失败',
  `push_response` text COMMENT '推送结果详情',
  `sms_cost` int DEFAULT NULL COMMENT '短信消耗数量',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='客户推送记录';