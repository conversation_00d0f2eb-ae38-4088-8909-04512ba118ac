CREATE TABLE `customer_persona` (
  `id`          bigint NOT NULL AUTO_INCREMENT,
  `org_id`      bigint DEFAULT NULL,
  `user_id`     bigint DEFAULT NULL,
  `category_id` bigint DEFAULT NULL COMMENT '目录id',
  `avatar`      varchar(512) DEFAULT NULL COMMENT '画像头像',
  `name`        varchar(45) DEFAULT NULL COMMENT '画像名称',
  `professional`    varchar(64) DEFAULT NULL COMMENT '职业',
  `journey_map_titles` text DEFAULT NULL COMMENT '关联的旅程',
  `modify_user_id`     bigint DEFAULT NULL,
  `deleted`     int DEFAULT 0,
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='客户画像';

CREATE TABLE `customer_persona_category` (
  `id`          bigint NOT NULL AUTO_INCREMENT,
  `org_id`      bigint DEFAULT NULL,
  `user_id`     bigint DEFAULT NULL,
  `avatar`      varchar(512) DEFAULT NULL COMMENT '目录图标',
  `name`        varchar(45) DEFAULT NULL COMMENT '目录名称',
  `modify_user_id`     bigint DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='客户画像目录';

CREATE TABLE `customer_persona_component` (
  `id`          bigint NOT NULL AUTO_INCREMENT,
  `org_id`      bigint DEFAULT NULL,
  `persona_id`  bigint DEFAULT NULL COMMENT '客户画像id',
  `name`        varchar(45) DEFAULT NULL COMMENT '组件名称',
  `type`        varchar(64) DEFAULT NULL COMMENT '组件类型',
  `content`     text DEFAULT NULL COMMENT '组件内容',
  `parameters`  text DEFAULT NULL COMMENT '组件参数',
  `layout`      varchar(45) DEFAULT NULL COMMENT '定位信息',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='客户画像组件';
