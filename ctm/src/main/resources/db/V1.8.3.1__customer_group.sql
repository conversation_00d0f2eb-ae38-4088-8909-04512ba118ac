CREATE TABLE `customer_group`
(
    `id`             bigint    NOT NULL AUTO_INCREMENT,
    `org_id`         bigint,
    `name`           varchar(45) COMMENT '名称',
    `code`           varchar(45) COMMENT '编号',
    `create_time`    timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`    timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT '客户分组';

CREATE TABLE `customer_group_relation`
(
    `id`             bigint    NOT NULL AUTO_INCREMENT,
    `group_id`       bigint COMMENT '分组id',
    `customer_id`    bigint COMMENT '客户id',
    `create_time`    timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`    timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT '客户分组和客户的关系';
