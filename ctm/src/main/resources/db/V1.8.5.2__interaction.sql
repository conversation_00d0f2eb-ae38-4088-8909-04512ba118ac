ALTER TABLE `cem_platform`.`experience_interaction`
ADD COLUMN `expire_moment` VARCHAR(512) NULL COMMENT '过期规则' AFTER `disturb_moment`;
ALTER TABLE `cem_platform`.`experience_interaction_publish`
ADD COLUMN `expire_moment` VARCHAR(512) NULL COMMENT '过期规则' AFTER `disturb_moment`;

ALTER TABLE `cem_platform`.`customer_journey_record`
ADD COLUMN `task_progress_id` bigint NULL AFTER `send_status`;

ALTER TABLE `cem_platform`.`customer_journey_record`
ADD COLUMN `survey_id` bigint NULL AFTER `task_progress_id`;

ALTER TABLE `cem_platform`.`customer_journey_record`
ADD COLUMN `client_id` VARCHAR(128) NULL AFTER `survey_id`;



