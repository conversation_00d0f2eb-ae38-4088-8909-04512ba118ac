-- Create syntax for TABLE 'content_audit_record'
CREATE TABLE `cem_platform`.`content_audit_record` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `sign` varchar(256) DEFAULT NULL COMMENT '签名',
  `type` varchar(20) DEFAULT NULL COMMENT '类型：text，image，survey',
  `source_id` bigint DEFAULT NULL COMMENT '来源id',
  `content` text COMMENT '审核内容',
  `response` text DEFAULT NULL COMMENT '第三方审核响应',
  `status` varchar(20) DEFAULT NULL COMMENT '审核结果：pass 通过、noPass 不通过、suspected 疑似、unknown 未知',
  `words` text DEFAULT NULL COMMENT '不合规、疑似关键字',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_type_sign` (`type`, `sign`, `create_time`)
) ENGINE=InnoDB COMMENT='内容审核记录';