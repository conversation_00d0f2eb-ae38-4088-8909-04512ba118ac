ALTER TABLE `cem_platform`.`event_result` ADD `external_company_id` VARCHAR(255)  NULL  DEFAULT NULL  COMMENT '外部企业ID'  AFTER `external_user_id`;
ALTER TABLE `cem_platform`.`event_result` ADD `department_code` VARCHAR(255)  NULL  DEFAULT NULL  COMMENT '外部组织编号'  AFTER `external_company_id`;
ALTER TABLE `cem_platform`.`event_result` ADD `department_name` VARCHAR(255)  NULL  DEFAULT NULL  COMMENT '部门名称'  AFTER `department_code`;
ALTER TABLE `cem_platform`.`event_result` ADD `customer_name` VARCHAR(255)  NULL  DEFAULT NULL  COMMENT '客户名称'  AFTER `department_name`;
ALTER TABLE `cem_platform`.`event_result` ADD `customer_gender` VARCHAR(255)  NULL  DEFAULT NULL  COMMENT '客户性别'  AFTER `customer_name`;
