CREATE TABLE `element_linker`
(
    `id`             bigint    NOT NULL AUTO_INCREMENT,
    `org_id`         bigint,
    `journey_map_id` bigint COMMENT '旅程id',
    `component_id`   bigint COMMENT '组件id',
    `journey_id`     bigint COMMENT '场景id',
    `name`           varchar(45) COMMENT '名称',
    `linker_type`    varchar(45) COMMENT '连接器类型：baiduTongji',
    `linker_config`  varchar(1024) COMMENT '连接器配置',
    `create_time`    timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`    timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT '旅程组件-连接器(编辑)';

CREATE TABLE `element_linker_publish`
(
    `id`             bigint    NOT NULL AUTO_INCREMENT,
    `org_id`         bigint,
    `journey_map_id` bigint COMMENT '旅程id',
    `component_id`   bigint COMMENT '组件id',
    `journey_id`     bigint COMMENT '场景id',
    `name`           varchar(45) COMMENT '名称',
    `linker_type`    varchar(45) COMMENT '连接器类型：baiduTongji',
    `linker_config`  varchar(1024) COMMENT '连接器配置',
    `create_time`    timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`    timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT '旅程组件-连接器(发布)';
