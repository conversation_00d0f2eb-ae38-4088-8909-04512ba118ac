ALTER TABLE `cem_platform`.`organization_wallet`
ADD COLUMN `ai_point` INT NULL DEFAULT 0 COMMENT 'AI点数' AFTER `sms`;

CREATE TABLE `cem_platform`.`organization_ai_point_record` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `org_id` bigint DEFAULT NULL COMMENT '企业id',
  `type` varchar(45) DEFAULT NULL COMMENT '消费类型：recharge AI点数充值，text_analysis 文本分析，warning 预警',
  `source_id` bigint DEFAULT NULL COMMENT '来源id：recharge 时为null, text_analysis 时为 文本分析id, warning 时为 问卷id',
  `amount` INT COMMENT 'AI点数变更',
  `status` varchar(20) DEFAULT NULL COMMENT '记录状态：init 处理中、success 成功、failure 失败、cancel 取消',
  `create_user_id` bigint DEFAULT NULL COMMENT '创建人id',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_org_id` (`org_id`, `type`, `source_id`)
) ENGINE=InnoDB COMMENT='AI点数消费记录';

CREATE TABLE `cem_platform`.`organization_ai_point_record_response` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `org_id` bigint DEFAULT NULL COMMENT '企业id',
  `record_id` bigint DEFAULT NULL COMMENT '消费记录id',
  `response_id` bigint DEFAULT NULL COMMENT '答卷id',
  `rule_id` bigint DEFAULT NULL COMMENT '规则id',
  `amount` INT COMMENT 'AI点数变更',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_org_id` (`org_id`, `response_id`, `record_id`)
) ENGINE=InnoDB COMMENT='AI点数消费记录详情';

ALTER TABLE `cem_platform`.`event_monitor_rules`
ADD COLUMN `last_close_time` TIMESTAMP NULL COMMENT '最后关闭时间' AFTER `tags`;