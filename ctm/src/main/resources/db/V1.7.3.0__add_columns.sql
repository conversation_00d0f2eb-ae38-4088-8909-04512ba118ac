ALTER TABLE `cem_platform`.`event_result` CHANGE `response_time` `response_time` TIMESTAMP  NOT NULL;
ALTER TABLE `cem_platform`.`event_result` ADD `response_id` BIGINT(20)  NULL  DEFAULT NULL  COMMENT '答卷ID'  AFTER `survey_id`;
ALTER TABLE `cem_platform`.`event_result` ADD `response_status` TINYINT(1)  NULL  DEFAULT NULL  COMMENT '答卷状态'  AFTER `response_id`;
ALTER TABLE `cem_platform`.`experience_indicator` ADD `is_valid` tinyint NOT NULL DEFAULT '1' COMMENT '问卷删除后，互动无效'  AFTER `is_delete`;
ALTER TABLE `cem_platform`.`experience_indicator_publish` ADD `is_valid` tinyint NOT NULL DEFAULT '1' COMMENT '问卷删除后，互动无效'  AFTER `is_delete`;

ALTER TABLE `cem_platform`.`connector` ADD `authorize_config` JSON  NULL  COMMENT '验证配置信息 json格式'  AFTER `gateway`;
ALTER TABLE `cem_platform`.`connector` ADD `http_method` TINYINT(1)  NULL  DEFAULT '0'  COMMENT '请求方式 默认POST'  AFTER `authorize_config`;
ALTER TABLE `cem_platform`.`connector` ADD `data_type` TINYINT(1)  NULL  DEFAULT '0'  COMMENT '数据结构 默认json'  AFTER `http_method`;
ALTER TABLE `cem_platform`.`connector` ADD `related_id` BIGINT(20)  NULL  DEFAULT NULL  COMMENT '推送关联的id'  AFTER `data_type`;
ALTER TABLE `cem_platform`.`connector` ADD `conditions`  VARCHAR(20) NULL  DEFAULT NULL  COMMENT '推送条件 创建、更新、停用'  AFTER `related_id`;
ALTER TABLE `cem_platform`.`connector` ADD `push_type` TINYINT(1)  NULL  DEFAULT NULL  COMMENT '数据类型  问卷、答卷、旅程、互动'  AFTER `conditions`;
ALTER TABLE `cem_platform`.`push` ADD `content` VARCHAR(2048)  NULL  DEFAULT NULL  COMMENT '响应内容' AFTER `strategy`;
ALTER TABLE `cem_platform`.`push` ADD `response` VARCHAR(2048)  NULL  DEFAULT NULL  COMMENT '响应内容' AFTER `content`;
ALTER TABLE `cem_platform`.`push` ADD `status` TINYINT(1)  NULL  DEFAULT NULL  COMMENT '推送状态 0失败 1成功'  AFTER `response`;


UPDATE cem_platform.journey_component
set title = (case title when '体验矩阵' then '体验问卷' when '体验互动' then '场景推送' else title END)
where type = 'experience_matrix' or type = 'experience_interaction';

CREATE TABLE `cem_platform`.`thirdparty_auth` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `org_id` bigint DEFAULT NULL,
  `auth_type` varchar(45) DEFAULT NULL,
  `app` varchar(45) DEFAULT NULL,
  `source` varchar(45) DEFAULT NULL,
  `enable_white_list` int DEFAULT NULL,
  `config` varchar(512) DEFAULT NULL COMMENT '配置信息',
  `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `source_unique` (`source`)
) COMMENT='第三方登录';


CREATE TABLE `cem_platform`.`thirdparty_auth_white_list` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `org_id` bigint DEFAULT NULL,
  `third_party_auth_id` bigint DEFAULT NULL,
  `open_id` varchar(255) DEFAULT NULL,
  `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) COMMENT='第三方登录白名单';

update `cem_platform`.`menu` set display=1 where id in(1,2,3,4);
update `cem_platform`.`menu` set display=0 where id in(19,20,21);
update `cem_platform`.`menu` set full_name='问卷管理',name='问卷管理' where id = 15;
update `cem_platform`.`menu` set full_name='问卷管理/问卷管理',name='问卷管理' where id = 16;
update `cem_platform`.`menu` set full_name='问卷管理/问卷管理/查看' where id = 17;
update `cem_platform`.`menu` set full_name='问卷管理/问卷管理/编辑' where id = 18;
update `cem_platform`.`menu` set full_name='问卷管理/投放管理',name='投放管理' where id = 19;
update `cem_platform`.`menu` set full_name='问卷管理/投放管理/查看' where id = 20;
update `cem_platform`.`menu` set full_name='问卷管理/投放管理/编辑' where id = 21;