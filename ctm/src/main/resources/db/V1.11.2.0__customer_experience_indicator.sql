-- cem_platform.customer_experience_indicator definition

CREATE TABLE `customer_experience_indicator` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `survey_id` bigint NOT NULL DEFAULT '0' COMMENT '问卷id',
  `question_id` bigint NOT NULL DEFAULT '0' COMMENT '题型id',
  `customer_id` bigint DEFAULT '0' COMMENT '题型id',
  `name` varchar(20)  NOT NULL DEFAULT '' COMMENT '指标名称',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后修改时间',
  `item_id` bigint DEFAULT NULL COMMENT '测量项id',
  `last_score` varchar(20) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '最后一次答题分值',
  `last_answer_time` timestamp NULL DEFAULT NULL COMMENT '最近一次指标答题时间',
  `org_id` bigint NOT NULL COMMENT '组织id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB COMMENT='客户中心体验指标';