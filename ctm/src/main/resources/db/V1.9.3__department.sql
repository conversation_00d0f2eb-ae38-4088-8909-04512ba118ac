ALTER TABLE `cem_platform`.`user`
ADD COLUMN `department_ids` VARCHAR(256) NULL COMMENT 'v1.9.3 新增，部门id列表：[[123],[456],[789]]' AFTER `department_id`,
CHANGE COLUMN `department_id` `department_id` BIGINT NULL DEFAULT '0' COMMENT 'v1.9.3 用户部门修改为多部门，此字段废弃，使用departmentIds' ;

UPDATE `cem_platform`.`user` SET `department_ids` = CONCAT('[[',`department_id`,']]') where id > 0 and `department_id` != 0 and `department_id` is not null;

ALTER TABLE `cem_platform`.`operate_log`
ADD COLUMN `department_ids` VARCHAR(256) NULL COMMENT 'v1.9.3 新增，部门id列表：[[123],[456],[789]]' AFTER `department_id`,
CHANGE COLUMN `department_id` `department_id` BIGINT NULL DEFAULT NULL COMMENT 'v1.9.3 用户部门修改为多部门，此字段废弃，使用departmentIds' ;

UPDATE `cem_platform`.`operate_log` SET `department_ids` = CONCAT('[[',`department_id`,']]') where id > 0 and `department_id` != 0 and `department_id` is not null;

