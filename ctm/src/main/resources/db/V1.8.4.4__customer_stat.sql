CREATE TABLE `cem_platform`.`customer_stat` (
  `id`                          bigint NOT NULL AUTO_INCREMENT,
  `customer_id`                 bigint NOT NULL,
  `count_journey_record`        int         COMMENT '客户历程数',
  `last_journey_record_time`    timestamp NULL   COMMENT '最后历程时间',
  `count_send_survey`           int         COMMENT '问卷发送数',
  `last_send_survey_time`       timestamp NULL  COMMENT '最后问卷发送时间',
  `count_complete_survey`       int         COMMENT '问卷完成数',
  `last_complete_survey_time`   timestamp NULL  COMMENT '最后问卷完成时间',
  `count_join_survey`           int         COMMENT '问卷参与数',
  `count_event`                 int         COMMENT '预警触发数',
  `last_event_time`             timestamp NULL  COMMENT '最后预警触发时间',
  `most_event_rule`             VARCHAR(60) COMMENT '预警统计：触发次数最多的预警规则，次数（预警规则名称）',
  `most_event_rule_times`       int         COMMENT '预警统计：触发次数最多的预警规则，次数',
  `most_event_rule_name`        VARCHAR(60) COMMENT '预警统计：触发次数最多的预警规则，名称',
  `journey_indicator`           VARCHAR(60) COMMENT '体验指标：最近一次评价的指标和得分，分值（体验指标名称）',
  `journey_indicator_score`     FLOAT       COMMENT '体验指标：最近一次评价的指标和得分，分值',
  `journey_indicator_name`      VARCHAR(60) COMMENT '体验指标：最近一次评价的指标和得分，名称',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE INDEX `customer_id` (`customer_id` ASC)
) ENGINE=InnoDB COMMENT='客户统计';
