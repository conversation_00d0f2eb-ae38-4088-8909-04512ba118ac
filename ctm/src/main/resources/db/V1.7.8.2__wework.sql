CREATE TABLE `cem_platform`.`wework_order` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `org_id` BIGINT NULL,
  `corp_id` VARCHAR(128) NULL,
  `buyer_user_id` VARCHAR(128) NULL,
  `base_count` INT NULL,
  `external_contact_count` INT NULL,
  `account_duration` INT NULL,
  `wework_order_id` VARCHAR(245) NULL COMMENT '订单号',
  `pay_status` INT NULL COMMENT '支付状态：0 未支付 1 已支付',
  `use_base_count` INT NULL,
  `use_external_contact_count` INT NULL,
  `create_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `modify_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`))
COMMENT = '企业微信订单';

CREATE TABLE `cem_platform`.`wework_order_account` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `org_id` BIGINT NULL,
  `order_id` BIGINT NULL,
  `corp_id` VARCHAR(128) NULL,
  `active_code` VARCHAR(256) NULL,
  `open_id` VARCHAR(128) NULL,
  `status` INT NULL,
  `active_time` TIMESTAMP NULL,
  `expire_time` TIMESTAMP NULL,
  `create_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  `modify_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`));