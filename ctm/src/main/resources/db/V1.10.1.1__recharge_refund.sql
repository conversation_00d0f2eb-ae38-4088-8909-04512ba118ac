-- Create syntax for TABLE 'organization_recharge_refund'
CREATE TABLE `cem_platform`.`organization_recharge_refund` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `org_id` bigint DEFAULT NULL,
  `order_id` bigint DEFAULT NULL COMMENT '企业订单id，如果是企业订单发起的充值则有此参数',
  `order_refund_id` bigint DEFAULT NULL COMMENT '订单退款id，如果是企业订单发起的充值则有此参数',
  `recharge_id` bigint DEFAULT NULL COMMENT '充值id',
  `refund_no` varchar(128) DEFAULT NULL COMMENT '企业退款单号',
  `thirtparty_refund_no` varchar(128) DEFAULT NULL COMMENT '第三方退款单号',
  `title` varchar(256) DEFAULT NULL COMMENT '退款名称（商品名称）',
  `type` varchar(45) DEFAULT NULL COMMENT '退款渠道：微信，支付宝',
  `amount` INTEGER DEFAULT NULL COMMENT '退款金额',
  `status` varchar(45) DEFAULT NULL COMMENT '退款状态：init 已发起 success 已成功 failure 已失败',
  `thirtparty_status` varchar(45) DEFAULT NULL COMMENT '第三方退款状态',
  `thirtparty_request` text DEFAULT NULL COMMENT '原始请求内容',
  `thirtparty_response` text DEFAULT NULL COMMENT '原始响应内容',
  `create_user_id` bigint DEFAULT NULL COMMENT '创建人',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '删除标记',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `success_time` timestamp NULL DEFAULT NULL COMMENT '成功时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='支付退款记录';

ALTER TABLE `cem_platform`.`organization_order`
CHANGE COLUMN `status` `status` VARCHAR(20) NULL DEFAULT NULL COMMENT '订单状态：init 未支付 success 已成功 failure 已失败 cancel 已取消' ;
