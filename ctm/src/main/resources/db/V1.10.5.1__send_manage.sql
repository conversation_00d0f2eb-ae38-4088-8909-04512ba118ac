ALTER TABLE `cem_platform`.`send_manage`
ADD COLUMN `trigger_timer` VARCHAR(512) NULL COMMENT '触发时间';
ALTER TABLE `cem_platform`.`send_manage`
ADD COLUMN `trigger_timer_target` TEXT NULL COMMENT '触发目标';
ALTER TABLE `cem_platform`.`send_manage`
ADD COLUMN `last_trigger_timer` DATETIME NULL COMMENT '最后一次触发时间';

ALTER TABLE `cem_platform`.`send_manage_record`
ADD COLUMN `task_progress_id` BIGINT NULL COMMENT '任务id';
