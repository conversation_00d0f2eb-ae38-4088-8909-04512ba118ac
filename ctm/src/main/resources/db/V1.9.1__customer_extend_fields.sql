ALTER TABLE `cem_platform`.`customer`
ADD COLUMN `extend_fields` TEXT NULL COMMENT '扩展字段' AFTER `external_user_id`;

ALTER TABLE `cem_platform`.`experience_indicator`
CHANGE COLUMN `percent_item` `percent_item` VARCHAR(128) NULL DEFAULT NULL COMMENT '选项占比计算方式的选项' ;

ALTER TABLE `cem_platform`.`experience_indicator_publish`
CHANGE COLUMN `percent_item` `percent_item` VARCHAR(128) NULL DEFAULT NULL COMMENT '选项占比计算方式的选项' ;


ALTER TABLE `push` ADD `retry` INT  NULL  DEFAULT 0  AFTER `status`;
