CREATE TABLE `event_result_rule_relation`
(
    `id`             bigint    NOT NULL AUTO_INCREMENT,
    `event_id`       bigint COMMENT '事件id',
    `rule_id`        bigint COMMENT '预警id',
    `response_id`    bigint COMMENT '答卷id',
    `create_time`    timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `modify_time`    timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB COMMENT '预警事件和预警规则的关系';

create index rid
    on cem_platform.event_result_rule_relation (response_id);

