ALTER TABLE `cem_platform`.`element_curve`
CHANGE COLUMN `layout` `layout` INT NOT NULL DEFAULT '3' COMMENT '布局' ;
ALTER TABLE `cem_platform`.`element_curve_publish`
<PERSON>ANGE COLUMN `layout` `layout` INT NOT NULL DEFAULT '3' COMMENT '布局' ;

update `cem_platform`.`element_curve` set `layout` = 1 where `layout` = 32 and id > 0;
update `cem_platform`.`element_curve` set `layout` = 2 where `layout` = 82 and id > 0;
update `cem_platform`.`element_curve` set `layout` = 3 where `layout` = 132 and id > 0;
update `cem_platform`.`element_curve` set `layout` = 4 where `layout` = 182 and id > 0;
update `cem_platform`.`element_curve` set `layout` = 5 where `layout` = 232 and id > 0;

update `cem_platform`.`element_curve_publish` set `layout` = 1 where `layout` = 32 and id > 0;
update `cem_platform`.`element_curve_publish` set `layout` = 2 where `layout` = 82 and id > 0;
update `cem_platform`.`element_curve_publish` set `layout` = 3 where `layout` = 132 and id > 0;
update `cem_platform`.`element_curve_publish` set `layout` = 4 where `layout` = 182 and id > 0;
update `cem_platform`.`element_curve_publish` set `layout` = 5 where `layout` = 232 and id > 0;