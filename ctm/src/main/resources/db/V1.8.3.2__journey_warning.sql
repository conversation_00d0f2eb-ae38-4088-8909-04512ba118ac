ALTER TABLE `cem_platform`.`journey_warning`
CHANGE COLUMN `relation_type` `relation_type` VARCHAR(45) NULL DEFAULT NULL COMMENT '关联组件类型：事件统计 event_stat; 体验指标 experience_indicator' ;

ALTER TABLE `cem_platform`.`journey_warning_publish`
CHANGE COLUMN `relation_type` `relation_type` VARCHAR(45) NULL DEFAULT NULL COMMENT '关联组件类型：事件统计 event_stat; 体验指标 experience_indicator' ;

update cem_platform.journey_warning set relation_type="event_stat" where relation_type="7" and id > 0;
update cem_platform.journey_warning set relation_type="experience_indicator" where relation_type="2" and id > 0;
update cem_platform.journey_warning set relation_type="experience_indicator" where relation_type="3" and id > 0;


update cem_platform.journey_warning_publish set relation_type="event_stat" where relation_type="7" and id > 0;
update cem_platform.journey_warning_publish set relation_type="experience_indicator" where relation_type="2" and id > 0;
update cem_platform.journey_warning_publish set relation_type="experience_indicator" where relation_type="3" and id > 0;


