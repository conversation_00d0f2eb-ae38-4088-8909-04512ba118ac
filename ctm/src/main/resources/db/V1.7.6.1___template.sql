ALTER TABLE `cem_platform`.`thirdparty_template`
ADD COLUMN `create_type` INT NULL DEFAULT 0 COMMENT '0 企业创建的模板 >0 系统模板' AFTER `is_delete`;

ALTER TABLE `cem_platform`.`thirdparty_template`
ADD COLUMN `last_version` INT NULL DEFAULT 1 COMMENT '最后更新时的版本' AFTER `create_type`;

ALTER TABLE `cem_platform`.`thirdparty_template`
DROP INDEX `thirdparty_template_org_id_open_id_uindex` ;

INSERT INTO `menu` (`id`, `create_time`, `modify_time`, `display`, `full_name`, `full_path`, `name`, `path`, `pid`, `type`, `sequence`) VALUES ('63', '2022-01-20 17:45:04', '2021-09-09 17:52:51', '1', '问卷管理/问卷管理/发布', '/TouchManager/Survey/publish', '发布', 'publish', '16', '3', '0');

INSERT INTO `menu` (`id`, `create_time`, `modify_time`, `display`, `full_name`, `full_path`, `name`, `path`, `pid`, `type`, `sequence`) VALUES ('64', '2021-12-01 15:46:51', '2021-12-01 15:46:51', '1', '数据洞察/文本分析', '/Bi/DataAnalysis', '文本分析', 'DataAnalysis', '53', '2', '0');
INSERT INTO `menu` (`id`, `create_time`, `modify_time`, `display`, `full_name`, `full_path`, `name`, `path`, `pid`, `type`, `sequence`) VALUES ('65', '2021-12-01 15:46:51', '2021-12-01 15:46:51', '1', '数据洞察/文本分析/查看', '/Bi/DataAnalysis/view', '查看', 'view', '64', '3', '0');
INSERT INTO `menu` (`id`, `create_time`, `modify_time`, `display`, `full_name`, `full_path`, `name`, `path`, `pid`, `type`, `sequence`) VALUES ('66', '2021-12-01 15:46:51', '2021-12-01 15:46:51', '1', '数据洞察/文本分析/编辑', '/Bi/DataAnalysis/edit', '编辑', 'edit', '64', '3', '0');

ALTER TABLE `event_result` ADD `push_ids` VARCHAR(500)  NULL  DEFAULT NULL  COMMENT '设置了推送的id'  AFTER `default_pc`;


