CREATE TABLE `user_config` (
  `id`          bigint NOT NULL AUTO_INCREMENT,
  `org_id`      bigint DEFAULT NULL,
  `user_id`     bigint DEFAULT NULL,
  `type`        varchar(45) DEFAULT NULL COMMENT '配置类型',
  `config`      text COMMENT '配置内容',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='用户配置';
