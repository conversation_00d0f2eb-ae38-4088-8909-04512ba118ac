ALTER TABLE `cem_platform`.`customer_persona`
ADD COLUMN `group_id` BIGINT NULL AFTER `user_id`;

CREATE TABLE `customer_persona_group` (
  `id`          bigint NOT NULL AUTO_INCREMENT,
  `org_id`      bigint DEFAULT NULL,
  `user_id`     bigint DEFAULT NULL,
  `avatar`      varchar(512) DEFAULT NULL COMMENT '目录图标',
  `name`        varchar(45) DEFAULT NULL COMMENT '目录名称',
  `modify_user_id`     bigint DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='客户画像目录';

CREATE TABLE `journey_map_group` (
  `id`          bigint NOT NULL AUTO_INCREMENT,
  `org_id`      bigint DEFAULT NULL,
  `user_id`     bigint DEFAULT NULL,
  `avatar`      varchar(512) DEFAULT NULL COMMENT '目录图标',
  `name`        varchar(45) DEFAULT NULL COMMENT '目录名称',
  `modify_user_id`     bigint DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='客户旅程目录';

ALTER TABLE `cem_platform`.`journey_map`
ADD COLUMN `group_id` BIGINT NULL AFTER `org_id`,
ADD COLUMN `user_id` BIGINT NULL AFTER `group_id`;

update `cem_platform`.`journey_map` set `user_id` = `owner_user_id` where id > 0 and `user_id` is null;