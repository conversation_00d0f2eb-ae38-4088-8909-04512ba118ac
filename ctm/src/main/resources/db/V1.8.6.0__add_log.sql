CREATE TABLE `operate_log` (
                               `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                               `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                               `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                               `org_id` bigint DEFAULT NULL COMMENT '组织id',
                               `user_id` bigint DEFAULT NULL COMMENT '用户id',
                               `department_id` bigint DEFAULT NULL COMMENT '部门id',
                               `ip` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                               `country` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL,
                               `province` varchar(10) COLLATE utf8mb4_general_ci DEFAULT NULL,
                               `city` varchar(10) COLLATE utf8mb4_general_ci DEFAULT NULL,
                               `module` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作板块',
                               `action` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '操作行为',
                               `result` int DEFAULT NULL COMMENT '操作结果0-失败1成功',
                               `url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '请求url',
                               `params` varchar(1000) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '请求参数',
                               `content` varchar(5000) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '响应内容',
                               `app` varchar(10) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '请求app来源',
                               PRIMARY KEY (`id`)
);

CREATE TABLE `xpack_config` (
                                `id` bigint unsigned NOT NULL AUTO_INCREMENT,
                                `enabled` int DEFAULT NULL COMMENT '是否开启',
                                `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                `app` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '请求app来源',
                                `type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '配置类型 Enum',
                                `config` varchar(5000) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '配置内容 自行定义dto',
                                PRIMARY KEY (`id`)
) ;

INSERT INTO `xpack_config` (`id`, `enabled`, `create_time`, `modify_time`, `app`, `type`, `config`)
VALUES
    (1, 1, '2022-10-18 14:56:42', '2022-10-19 16:05:14', '', 'OPERATE_LOG', '{"oprateLogConfig":{"POST:/api/auth/login/password/cem":{"method":"POST","module":"登录模块","action":"登录账号","path":"/api/auth/login/password/cem","code":200,"internalCode":0},"POST:/api/auth/login/mobile/cem":{"method":"POST","module":"登录模块","action":"登录账号","path":"/api/auth/login/mobile/cem"},"POST:/api/auth/login/refresh-token/cem":{"method":"POST","module":"登录模块","action":"登录账号","path":"/api/auth/login/refresh-token/cem"},"GET:/api/auth/login/callback/wechat_work/cem":{"method":"GET","module":"登录模块","action":"登录账号","path":"/api/auth/login/callback/wechat_work/cem"},"GET:/api/auth/login/callback/cas-*/cem":{"method":"GET","module":"登录模块","action":"登录账号","path":"/api/auth/login/callback/cas-*/cem"},"POST:/api/auth/logout":{"method":"POST","module":"登录模块","action":"退出登录","path":"/api/auth/logout"},"POST:/api/auth/users/current/update-password":{"method":"POST","module":"个人中心","action":"修改密码","path":"/api/auth/users/current/update-password"},"POST:/api/auth/users/*/disable":{"method":"POST","module":"成员管理","action":"禁用成员","path":"/api/auth/users/*/disable"},"POST:/api/auth/users/*/enable":{"method":"POST","module":"成员管理","action":"启用成员","path":"/api/auth/users/*/enable"},"POST:/api/auth/users/invite/email":{"method":"POST","module":"成员管理","action":"邀请成员","path":"/api/auth/users/invite/email"},"DELETE:/api/auth/users/*":{"method":"DELETE","module":"成员管理","action":"删除成员","path":"/api/auth/users/*"}}}');
