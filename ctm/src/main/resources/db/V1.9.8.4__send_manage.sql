-- Create syntax for TABLE 'send_manage'
CREATE TABLE `send_manage` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `org_id` bigint NOT NULL DEFAULT '0' COMMENT '企业id',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `modify_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录最后修改时间',
  `user_id` bigint NOT NULL DEFAULT '0' COMMENT '创建者',
  `editor_id` bigint NOT NULL DEFAULT '0' COMMENT '修改者',
  `trigger_id` bigint DEFAULT '0' COMMENT '发送关联id',
  `send_name` varchar(20)  NOT NULL DEFAULT '' COMMENT '发送名称',
  `survey_titles` varchar(512)  DEFAULT '' COMMENT '问卷名数组，例如:[''11111'', ''22222'']',
  `send_sids` varchar(512)  DEFAULT '' COMMENT '问卷id数组，例如:[''11111'', ''22222'']',
  `send_moment` varchar(512)  DEFAULT '' COMMENT '时间，格式例如：{"":"immediately"，或者是later-10-minutes/hours/days/months',
  `disturb_moment` varchar(512)  DEFAULT NULL COMMENT '勿扰模式，格式例如：{"enable":false,',
  `expire_moment` varchar(512)  DEFAULT NULL COMMENT '过期规则',
  `channel` text DEFAULT NULL COMMENT '渠道',
  `send_token` varchar(40)  DEFAULT NULL COMMENT '发送id',
  `send_filter` varchar(500)  DEFAULT NULL COMMENT '过滤条件',
  `repeat_times` varchar(10)  NOT NULL DEFAULT '' COMMENT '重复次数',
  `trigger_type` varchar(10)  NOT NULL DEFAULT '' COMMENT '触发类型',
  `send_type` varchar(10)  NOT NULL DEFAULT '' COMMENT '发送类型',
  `enable` tinyint NOT NULL DEFAULT '0' COMMENT '开关',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `index_org_token` (`org_id`,`send_token`),
  KEY `index_trigger_id` (`trigger_id`)
) ENGINE=InnoDB COMMENT='发送管理';

-- Create syntax for TABLE 'send_manage_record'
CREATE TABLE `send_manage_record` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `org_id` bigint DEFAULT NULL COMMENT '企业id',
  `send_manage_id` bigint DEFAULT NULL COMMENT '发送管理id',
  `source_id` bigint DEFAULT NULL COMMENT '如果是数据接入触发的，则是数据接入的消息id',
  `survey_id` bigint DEFAULT NULL COMMENT '问卷id',
  `customer_id` bigint DEFAULT NULL COMMENT '客户id',
  `client_id` varchar(64)  DEFAULT NULL COMMENT '答卷唯一编号',
  `send_url` varchar(512)  DEFAULT NULL COMMENT '答卷地址',
  `link_id` bigint DEFAULT NULL COMMENT '答卷地址短链id',
  `response_id` bigint DEFAULT NULL COMMENT '答卷id',
  `send_status` varchar(20)  DEFAULT NULL COMMENT '发送状态',
  `receive_status` varchar(20)  DEFAULT NULL COMMENT '接受状态',
  `reply_status` varchar(20)  DEFAULT NULL COMMENT '填答状态',
  `status` varchar(20)  DEFAULT NULL COMMENT '记录状态',
  `send_channel_info` text  COMMENT '发送渠道内容',
  `send_channel_status` text  COMMENT '发送渠道状态',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `index_send_manage_id` (`send_manage_id`)
) ENGINE=InnoDB COMMENT='发送管理记录';