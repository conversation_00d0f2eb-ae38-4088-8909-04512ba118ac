-- Create syntax for TABLE 'data_access'
CREATE TABLE `data_access` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `org_id` bigint DEFAULT NULL COMMENT '企业id',
  `name` varchar(20) NOT NULL COMMENT '名称',
  `access_configuration` text COMMENT '数据接入配置内容',
  `enable` tinyint DEFAULT '1' COMMENT '是否启用',
  `type` varchar(20)  DEFAULT NULL COMMENT '类型',
  `version` int DEFAULT '1' COMMENT '配置版本号',
  `active_version` int DEFAULT NULL COMMENT '运行中版本号',
  `active_time` timestamp NULL DEFAULT NULL COMMENT '最后活跃时间',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `storage_period` int DEFAULT '0' COMMENT '保存时间',
  PRIMARY KEY (`id`),
  KEY `index_org_id` (`org_id`)
) ENGINE=InnoDB COMMENT='数据接入';

-- Create syntax for TABLE 'data_access_cell'
CREATE TABLE `data_access_cell` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `access_id` bigint DEFAULT NULL COMMENT '数据接入id',
  `status` varchar(20) DEFAULT NULL COMMENT '消息状态',
  `message_id` varchar(128) DEFAULT NULL COMMENT '消息唯一id',
  `message_data` text COMMENT '消息数据',
  `extra_data` text COMMENT '额外数据',
  `parsed_params` text COMMENT '已解析参数',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `index_access_id` (`access_id`),
  KEY `index_message_id` (`message_id`)
) ENGINE=InnoDB COMMENT='数据接入消息';

-- Create syntax for TABLE 'data_access_cell_backup'
CREATE TABLE `data_access_cell_backup` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `access_id` bigint DEFAULT NULL COMMENT '数据接入id',
  `status` varchar(20) DEFAULT NULL COMMENT '消息状态',
  `message_id` varchar(128) DEFAULT NULL COMMENT '消息唯一id',
  `message_data` text COMMENT '消息数据',
  `extra_data` text COMMENT '额外数据',
  `parsed_params` text COMMENT '已解析参数',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='数据接入消息-备份库';

-- Create syntax for TABLE 'data_access_params'
CREATE TABLE `data_access_params` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `access_id` bigint DEFAULT NULL COMMENT '数据接入id',
  `params_format` varchar(20)  DEFAULT NULL COMMENT '参数格式',
  `params_name` varchar(20)  DEFAULT NULL COMMENT '参数名称',
  `params_title` varchar(20)  DEFAULT NULL COMMENT '参数显示名称',
  `params_type` varchar(20)  DEFAULT NULL COMMENT '参数类型',
  `params_match_rule` text  COMMENT '参数提取规则',
  `params_value` text  COMMENT '参数值选项',
  `is_check` tinyint DEFAULT '0' COMMENT '是否启用强校验',
  `is_unique` tinyint DEFAULT '0' COMMENT '是否为唯一参数',
  `sequence` int DEFAULT '1',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `index_access-id` (`access_id`)
) ENGINE=InnoDB COMMENT='数据接入参数';
