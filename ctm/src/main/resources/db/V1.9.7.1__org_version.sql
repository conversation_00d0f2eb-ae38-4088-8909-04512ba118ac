CREATE TABLE `cem_platform`.`organization_version_record` (
  `id`                  bigint NOT NULL AUTO_INCREMENT,
  `org_id`              bigint DEFAULT NULL COMMENT '企业id',
  `version`             varchar(64) DEFAULT NULL COMMENT '版本类型',
  `type`                varchar(20) DEFAULT NULL COMMENT '版本变化：upgrade 升级; renew 续费',
  `price`               INTEGER DEFAULT NULL COMMENT '版本价格',
  `cost_amount`         INTEGER DEFAULT NULL COMMENT '实际支付金额',
  `start_date`          DATE DEFAULT NULL COMMENT '购买的版本开始时间',
  `end_date`            DATE DEFAULT NULL COMMENT '购买的版本结束时间',
  `status`              varchar(20) DEFAULT NULL COMMENT '状态',
  `create_user_id`      bigint DEFAULT NULL COMMENT '创建人',
  `deleted`             INTEGER DEFAULT NULL COMMENT '删除标记',
  `create_time`         timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `modify_time`         timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='企业版本购买记录';

ALTER TABLE `cem_platform`.`organization`
CHANGE COLUMN `optional_limit` `optional_limit` VARCHAR(1024) NULL DEFAULT NULL COMMENT 'child_user_limit：子账户数量限制；customer_lifecycle_limit：客户旅程数量限制；surveys_limit：问卷数量限制' ;

ALTER TABLE `cem_platform`.`organization_sms_record`
ADD COLUMN `source` TEXT NULL COMMENT '来源' AFTER `task_progress_id`;



ALTER TABLE `cem_platform`.`customer_answers`
ADD INDEX `idx_customer_id` (`customer_id` ASC);

ALTER TABLE `cem_platform`.`customer_journey_record`
ADD INDEX `idx_customer_id` (`customer_id` ASC);

ALTER TABLE `cem_platform`.`customer_group_relation`
ADD INDEX `idx_customer_id` (`customer_id` ASC);

ALTER TABLE `cem_platform`.`customer`
ADD INDEX `idx_org_department` (`org_id` ASC, `department_id` ASC, `is_delete` ASC);

ALTER TABLE `cem_platform`.`customer`
ADD INDEX `idx_org_euid` (`org_id` ASC, `external_user_id` ASC);

ALTER TABLE `cem_platform`.`customer`
ADD INDEX `idx_org_mobile` (`org_id` ASC, `mobile` ASC);

ALTER TABLE `cem_platform`.`customer`
ADD INDEX `idx_org_thirdparty` (`org_id` ASC, `thirdparty_customer_id` ASC);

ALTER TABLE `cem_platform`.`customer`
ADD INDEX `idx_org_username` (`org_id` ASC, `username` ASC);