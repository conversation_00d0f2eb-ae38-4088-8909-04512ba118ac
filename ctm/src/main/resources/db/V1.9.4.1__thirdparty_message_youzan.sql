ALTER TABLE `cem_platform`.`thirdparty_auth`
<PERSON><PERSON><PERSON> COLUMN `config` `config` VARCHAR(2048) NULL DEFAULT NULL COMMENT '配置信息' ;
ALTER TABLE `cem_platform`.`thirdparty_auth`
DROP INDEX `source_unique` ;
CREATE TABLE `thirdparty_message_youzan` (
  `id`                  bigint NOT NULL AUTO_INCREMENT,
  `msg_id`              varchar(64) DEFAULT NULL COMMENT '消息唯一标识',
  `client_id`           varchar(64) DEFAULT NULL COMMENT '有赞id',
  `kdt_id`              varchar(64) DEFAULT NULL COMMENT '有赞店铺id',
  `trade_id`            varchar(64) DEFAULT NULL COMMENT '有赞交易id',
  `wx_open_id`          varchar(64) DEFAULT NULL COMMENT '微信公众号openId',
  `type`                varchar(64) DEFAULT NULL COMMENT '消息类型',
  `message`             text DEFAULT NULL COMMENT '消息内容',
  `status`              varchar(64) DEFAULT NULL COMMENT '消息状态：ignore, wait_binding, bound, trigger_journey, success_journey',
  `create_time`         timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `modify_time`         timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='第三方平台消息-有赞';