ALTER TABLE `cem_platform`.`connector` CHANGE `conditions` `conditions` VARCHAR(300)  CHARACTER SET utf8mb4  COLLATE utf8mb4_general_ci  NULL  DEFAULT NULL  COMMENT '推送条件 创建、更新、停用';
ALTER TABLE `cem_platform`.`push` CHAN<PERSON> `response` `response` TEXT  CHARACTER SET utf8mb4  COLLATE utf8mb4_general_ci  NULL  COMMENT '响应内容';

ALTER TABLE `event_monitor_thesaurus` ADD `user_id` BIGINT(20)  NULL  DEFAULT NULL  AFTER `deleted`;
ALTER TABLE `event_monitor_thesaurus` ADD `editor_id` BIGINT(20)  NULL  DEFAULT NULL  AFTER `user_id`;


CREATE TABLE `connector_consumer` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `org_id` bigint DEFAULT NULL,
  `user_id` bigint DEFAULT NULL,
  `connector_id` bigint DEFAULT NULL,
  `relation_id` bigint DEFAULT NULL,
  `enable` tinyint DEFAULT '1',
  `modify_time` timestamp NULL DEFAULT NULL,
  `create_time` timestamp NULL DEFAULT NULL,
PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1802386726106113 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC;

ALTER TABLE `event_monitor_rules` ADD `thesaurus_ids` VARCHAR(255)  CHARACTER SET utf8mb4  COLLATE utf8mb4_general_ci  NULL  DEFAULT NULL  AFTER `question_ids`;
