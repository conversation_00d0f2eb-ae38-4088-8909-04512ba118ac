ALTER TABLE cem_platform.element_curve ADD dynamic_emotions varchar(1024) NULL COMMENT '动态表情配置';
ALTER TABLE cem_platform.element_curve_publish ADD dynamic_emotions varchar(1024) NULL COMMENT '动态表情配置';
ALTER TABLE cem_platform.element_curve ADD enable_dynamic_emotion tinyint DEFAULT 0 NULL COMMENT '是否开启动态表情（0：否，1：是）';
ALTER TABLE cem_platform.element_curve_publish ADD enable_dynamic_emotion tinyint DEFAULT 0 NULL COMMENT '是否开启动态表情（0：否，1：是）';
UPDATE cem_platform.element_curve SET dynamic_emotions = '[{"color":"#FFC500","emotionType":0,"min":null,"max":null},{"color":"#FFC500","emotionType":1,"min":null,"max":null},{"color":"#FFC500","emotionType":2,"min":null,"max":null},{"color":"#E13B32","emotionType":3,"min":null,"max":null},{"color":"#E13B32","emotionType":4,"min":null,"max":null}]' where enable_dynamic_emotion = 0;
UPDATE cem_platform.element_curve_publish SET dynamic_emotions = '[{"color":"#FFC500","emotionType":0,"min":null,"max":null},{"color":"#FFC500","emotionType":1,"min":null,"max":null},{"color":"#FFC500","emotionType":2,"min":null,"max":null},{"color":"#E13B32","emotionType":3,"min":null,"max":null},{"color":"#E13B32","emotionType":4,"min":null,"max":null}]' where enable_dynamic_emotion = 0;