ALTER TABLE `experience_indicator` ADD `calculating_filter` VARCHAR(5000)  CHARACTER SET utf8mb4  COLLATE utf8mb4_general_ci NULL  COMMENT '过滤方式： {"AND": [TypeFilterMethodDto,TypeFilterMethodDto], "OR":[]} '  AFTER `calculating_method`;
ALTER TABLE `experience_indicator_publish` ADD `calculating_filter` VARCHAR(5000)  CHARACTER SET utf8mb4  COLLATE utf8mb4_general_ci  NULL  COMMENT '过滤方式： {"AND": [TypeFilterMethodDto,TypeFilterMethodDto], "OR":[]} '  AFTER `calculating_method`;
