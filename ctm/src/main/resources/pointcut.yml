pointcut:
  enable: true
  methods:
    permission:
      enable: ${PERMISSION_ENABLE:true}
      class-name: org.befun.core.limiter.aspect.PermissionPointcut
      expression: execution(* cn.hanyi.ctm.controller..*.*(..))
      params: ${PERMISSION_PARAMS:{"POST:/api/ctm/journey-maps/*/components":"/CustomerLife/CustomerLife/edit","PUT:/api/ctm/journey-maps/*/components":"/CustomerLife/CustomerLife/edit","DELETE:/api/ctm/journey-maps/*/components":"/CustomerLife/CustomerLife/edit","POST:/api/ctm/journey-maps/*/publish":"/CustomerLife/CustomerLife/edit","POST:/api/ctm/journey-maps/*":"/CustomerLife/CustomerLife/edit","PUT:/api/ctm/journey-maps/*":"/CustomerLife/CustomerLife/edit","DELETE:/api/ctm/journey-maps/*":"/CustomerLife/CustomerLife/edit","POST:/api/ctm/event-monitor-rules":"/Events/EventWarning/edit","PUT:/api/ctm/event-monitor-rules/*":"/Events/EventWarning/edit","DELETE:/api/ctm/event-monitor-rules/*":"/Events/EventWarning/edit","POST:/api/ctm/event-monitor-thesaurus/*":"/Events/WarningWord/edit","PUT:/api/ctm/event-monitor-thesaurus/*":"/Events/WarningWord/edit","DELETE:/api/ctm/event-monitor-thesaurus/*":"/Events/WarningWord/edit","POST:/api/ctm/customers":"/CustomerCentre/CustomerCentre/edit","PUT:/api/ctm/customers":"/CustomerCentre/CustomerCentre/edit","DELETE:/api/ctm/customers":"/CustomerCentre/CustomerCentre/edit","POST:/api/ctm/journey-maps/*/components/*/elementTexts/*":"/CustomerLife/CustomerLife/edit","PUT:/api/ctm/journey-maps/*/components/*/elementTexts/*":"/CustomerLife/CustomerLife/edit","DELETE:/api/ctm/journey-maps/*/components/*/elementTexts/*":"/CustomerLife/CustomerLife/edit"}}

