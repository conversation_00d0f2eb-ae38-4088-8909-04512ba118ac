befun:
  extension:
    sms:
      enable-limit: ${ENABLE_LIMIT_SMS:true}
      providers:
        - name: feige
          feige:
            app-id: ${FEIGE_APP_ID:N51247f3ad}
            app-secret: ${FEIGE_APP_SECRET:5124787f336c5cb7}
            signature: ${FEIGE_SIGNATURE:128756}
            real-signature: ${FEIGE_REAL_SIGNATURE:【体验家DEV】}
          templates:
            - name: verify-code
              id: ${FEIGE_TEMPLATE_ID_VERIFY_CODE:116170}
              content: 您的验证码是${code}，请于5分钟内填写。如非本人操作，请忽略本短信
              pattern: ^您的验证码是(\d+)，请于5分钟内填写。如非本人操作，请忽略本短信$
              variables: $$
            - name: event-action-warning
              id: ${FEIGE_TEMPLATE_ID_EVENT_WARNING:121745}
              content: ${targetTruename}，您好！您收到一条${warningLevelSimple}预警事件：${warningTitle}，请尽快登录体验家XM处理！
              pattern: ^(.+)，您好！您收到一条(.+)预警事件：(.+)，请尽快登录体验家XM处理！$
              variables: $$,$$,$$
            - name: event-action-cooperation
              id: ${FEIGE_TEMPLATE_ID_EVENT_COOPERATION:121746}
              content: ${targetTruename}，您好！您收到一条${formUserName}的事件协作邀请，请尽快登录体验家XM处理！
              pattern: ^(.+)，您好！您收到一条(.+)的事件协作邀请，请尽快登录体验家XM处理！$
              variables: $$,$$
            - name: journey-indicator-warning
              id: ${FEIGE_TEMPLATE_ID_EVENT_JOURNEY:123638}
              content: ${targetTruename}，您好！您收到一条指标预警：${indicatorName}的值为${currentValue}(${indicatorCompareLabel})，请尽快登录体验家XM处理！
              pattern: ^(.+)，您好！您收到一条指标预警：(.+)的值为(\d+.?\d+)\((.+)\)，请尽快登录体验家XM处理！$
              variables: $$,$$,$$,$$
            - name: event-notify-customer
              id: ${FEIGE_TEMPLATE_ID_EVENT_NOTIFY_CUSTOMER:116172}
              content: 尊敬的${customer.username}，非常抱歉给您造成不良的消费体验，我们会针对您的反馈进行改进，期盼您能再次光临。
              pattern: ^尊敬的(.+)，非常抱歉给您造成不良的消费体验，我们会针对您的反馈进行改进，期盼您能再次光临。$
              variables: $$
            - name: PLATFORM_FEEDBACK
              id: ${FEIGE_TEMPLATE_ID_PLATFORM_FEEDBACK:116174}
              content: 尊敬的${customer.username}，您好！感谢您的光临，为了带给您更好的服务体验，请对我们做出评价：${xmplus.short}/${url.code}
              pattern: ^尊敬的(.+)，您好！感谢您的光临，为了带给您更好的服务体验，请对我们做出评价：${xmplus.short}/(\w+)$
              variables: $$,$$
              parameters: '[{"name": "customer.username", "title": "姓名"}]'
            - name: PLATFORM_EVENT_ACTION
              id: ${FEIGE_TEMPLATE_ID_PLATFORM_EVENT_ACTION:116172}
              content: 尊敬的${customer.username}，非常抱歉给您造成不良的消费体验，我们会针对您的反馈进行改进，期盼您能再次光临。
              pattern: ^尊敬的(.+)，非常抱歉给您造成不良的消费体验，我们会针对您的反馈进行改进，期盼您能再次光临。$
              variables: $$
              parameters: '[{"name": "customer.username", "title": "姓名"}]'
            - name: PLATFORM_RESEARCH
              id: ${FEIGE_TEMPLATE_ID_PLATFORM_RESEARCH:119569}
              content: 尊敬的${customer.username}，您好！特邀您参加本次问卷调查，点击链接马上填答：${xmplus.short}/${url.code}
              pattern: ^尊敬的(.+)，您好！特邀您参加本次问卷调查，点击链接马上填答：${xmplus.short}/(\w+)$
              variables: $$,$$
              parameters: '[{"name": "customer.username", "title": "姓名"}]'
