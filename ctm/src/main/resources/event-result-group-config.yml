ctm:
  event-result:
    query-config:
      query:
        - property-name: id
          template-id: -1
          property-label: 事件ID
          query-item-type: oneToManyInSelect
          property-column: id
          property-type: arrayString
          input-type: ARRAY_TEXT
        - property-name: id
          template-id: 0
          property-label: 预警名称
          query-item-type: oneToManyInSelect
          property-source: event_result_rule_relation
          property-on: event_id
          property-column: rule_id
          property-type: arrayString
          input-type: ARRAY_TEXT
        - property-name: survey_id
          template-id: 1
          property-label: 问卷名称
          query-item-type: oneToManyInSelect
          property-source: survey
          property-column: id
          property-type: arrayString
          input-type: ARRAY_TEXT
        - property-name: warning_level
          template-id: 2
          property-label: 事件类型
          query-item-type: oneToManyInSelect
          property-column: warning_level
          input-type: ARRAY_TEXT
        - property-name: tags
          template-id: 3
          property-label: 所属部门
          query-item-type: textInput
          property-column: tags
          property-type: string
          input-type: TEXT
        - property-name: status
          template-id: 4
          property-label: 事件状态
          query-item-type: oneToManyInSelect
          property-column: status
          input-type: ARRAY_TEXT
        - property-name: id
          template-id: 5
          property-label: 行动人
          query-item-type: oneToManyInSelect
          property-source: event_action
          property-on: event_id
          property-column: action_user_id
          input-type: ARRAY_TEXT
        #        - property-name: id
        #          template-id: 7
        #          property-label: 协作成员
        #          query-item-type: oneToManyInSelect
        #          property-source: event_action
        #          property-on: event_id
        #          property-column: target_user_ids
        #          input-type: ARRAY_TEXT
        - property-name: response_time
          template-id: 8
          property-label: 问卷提交时间
          query-item-type: dateSelect
          property-column: response_time
          column-type: function
          property-type: string
          input-type: DATE
        #        - property-name: id
        #          template-id: 9
        #          property-label: 事件行动时间
        #          query-item-type: dateSelect
        #          property-source: event_action
        #          property-on: event_id
        #          property-column: create_time
        #          column-type: function
        #          property-type: string
        #          input-type: DATE
        #        - property-name: department_id
        #          template-id: 10
        #          property-label: 数据来源
        #          query-item-type: oneToManyInSelect
        #          property-column: department_id
        #          input-type: ARRAY_TEXT
        - property-name: parameters
          template-id: 11
          property-label: 外部参数
          property-column: parameters
          query-item-type: jsonValue
          input-type: COLON_TEXT
          column-type: function
        #        - property-name: id
        #          template-id: 12
        #          property-label: 备注内容
        #          property-source: event_action
        #          property-on: event_id
        #          property-column: content
        #          query-item-type: textInput
        #          input-type: TEXT
      #        - property-name: last_action_username
      #          template-id: 13
      #          property-label: 最近一次行动人
      #          property-column: last_action_username
      #          query-item-type: textInput
      #          input-type: TEXT
      #        - property-name: last_action_time
      #          template-id: 14
      #          property-label: 最近一次行动时间
      #          query-item-type: dateSelect
      #          property-column: last_action_time
      #          property-type: string
      #          column-type: function
      #          input-type: DATE
      query-type:
        textInput:
          - query-type-label: 包含
            query-type: like
            query-value-type: inputText
          - query-type-label: 不包含
            query-type: notLike
            query-value-type: inputText
          - query-type-label: 为空
            query-type: isEmpty
            query-value-type: none
          - query-type-label: 不为空
            query-type: notEmpty
            query-value-type: none
        numberInput:
          - query-type-label: 等于
            query-type: eq
            query-value-type: inputNumber
          - query-type-label: 不等于
            query-type: neq
            query-value-type: inputNumber
          - query-type-label: 大于
            query-type: gt
            query-value-type: inputNumber
          - query-type-label: 大于等于
            query-type: ge
            query-value-type: inputNumber
          - query-type-label: 小于
            query-type: lt
            query-value-type: inputNumber
          - query-type-label: 小于等于
            query-type: le
            query-value-type: inputNumber
          - query-type-label: 为空
            query-type: isEmpty
            query-value-type: none
          - query-type-label: 不为空
            query-type: notEmpty
            query-value-type: none
        oneToOneSelect:
          - query-type-label: 等于
            query-type: eq
            query-value-type: selectMulti
          - query-type-label: 不等于
            query-type: neq
            query-value-type: selectMulti
          - query-type-label: 为空
            query-type: isEmpty
            query-value-type: none
          - query-type-label: 不为空
            query-type: notEmpty
            query-value-type: none
        oneToManyInSelect:
          - query-type-label: 包含
            query-type: in
            query-value-type: selectMulti
          - query-type-label: 不包含
            query-type: notIn
            query-value-type: selectMulti
          - query-type-label: 为空
            query-type: isEmpty
            query-value-type: none
          - query-type-label: 不为空
            query-type: notEmpty
            query-value-type: none
        oneToManyLikeSelect:
          - query-type-label: 包含
            query-type: like
            query-value-type: selectMulti
          - query-type-label: 不包含
            query-type: notLike
            query-value-type: selectMulti
          - query-type-label: 为空
            query-type: isEmpty
            query-value-type: none
          - query-type-label: 不为空
            query-type: notEmpty
            query-value-type: none
        dateSelect:
          - query-type-label: 等于
            query-type: eq
            query-value-type: selectDate
          - query-type-label: 不等于
            query-type: neq
            query-value-type: selectDate
          - query-type-label: 早于
            query-type: lt
            query-value-type: selectDate
          - query-type-label: 晚于
            query-type: gt
            query-value-type: selectDate
          - query-type-label: 介于
            query-type: range
            query-value-type: selectDateRange
          - query-type-label: 为空
            query-type: isEmpty
            query-value-type: none
          - query-type-label: 不为空
            query-type: notEmpty
            query-value-type: none
          - query-type-label: 最近N天
            query-type: beforeDays
            query-value-type: inputNumber
        #          - query-type-label: 后N天
        #            query-type: afterDays
        #            query-value-type: inputNumber
        jsonValue:
          - query-type-label: 包含
            query-type: in
            query-value-type: jsonValue
