xmplus:
  domain: ${XMPLUS_DOMAIN:https://dev.xmplus.cn}
  short: ${XMPLUS_SHORT:https://dev-t.xmplus.cn}
server:
  shutdown: graceful
  port: ${PORT:8080}
  servlet:
    context-path: /api/ctm
  error:
    whitelabel:
      enabled: false

spring:
  cloud:
    compatibility-verifier:
      enabled: false
    nacos:
      config:
        server-addr: ${NACOS_SERVER_ADDR:mse-8ba4aca6-p.nacos-ans.mse.aliyuncs.com:8848}
        group: ${NACOS_GROUP:cem}
        file-extension: ${NACOS_FILE_EXTENSION:properties}
        access-key: ${NACOS_ACCESS_KEY:}
        secret-key: ${NACOS_SECRET_KEY:}
  lifecycle:
    timeout-per-shutdown-phase: 60s
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
  config:
    import:
      - nacos:${NACOS_DATA_ID:cem-common}
      - nacos:${NACOS_DATA_ID:ctm}
      - classpath:auth.yml
      - classpath:connector.yml
      - classpath:flyway.yml
      - classpath:journey-map-init.yml
      - classpath:mail.yml
      - classpath:persona.yml
      - classpath:placeholder.yml
      - classpath:pointcut.yml
      - classpath:befun-sms-chuanglan.yml
      - classpath:befun-metrics.yml
      - classpath:task.yml
      - classpath:befun-xpack-wechatopen.yml
      - classpath:befun-xpack-wechatpay.yml
      - classpath:data-access.yml
      - classpath:befun-user-config.yml
      - classpath:file.yml
      - classpath:befun-xpack-wechatminiprogram.yml
      - classpath:event-result-group-config.yml
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    jdbc-url: ${MYSQL_URL:*********************************************************************************************************************************}
    username: ${MYSQL_USER:root}
    password: ${MYSQL_PASSWORD:Health2020@SZ}
    connection-init-sql: SET NAMES utf8mb4
  jpa:
    show-sql: ${SHOW_SQL:false}
  data:
    redis.repositories.enabled: false
    jdbc.repositories.enabled: false
    rest:
      default-media-type: application/json
  redis:
    host: ${REDIS_HOST:*********}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:bsxrzQ2l2zm}
    database: ${REDIS_DB:0}
    username: ${REDIS_USERNAME:}

hanyi:
  shorturl: ${SHORTURL:https://dev-t.xmplus.cn}
  open:
    key: Xxff8ft35h77qD8VDYWQh4sW9H5kcdCd

wechat:
  open:
    app_id: ${WECHAT_OPEN_APP_ID:wxbb2e0ad30fee2502}
    app_secret: ${WECHAT_OPEN_APP_SECRET:372012a24728ce35610b37e8eb539538}
    token: ${WECHAT_OPEN_TOKEN:surveyplus}
    aes_key: ${WECHAT_OPEN_AES_KEY:0F68wpOxqNJs4bL5xUoCyrlWinYF6QNPtmYVx8Ex0BB}
    callback: ${WECHAT_OPEN_CALLBACK:https://dev.xmplus.cn/cem/setting/account?from=wechat_open}

logging:
  level:
    root: ${LOG_LEVEL:info}
    org.befun: ${LOG_LEVEL_BEFUN:info}
    org.befun.task.service: error
    org.hibernate:
      SQL: ${LOG_LEVEL_SQL:error}
      type.descriptor.sql: ${LOG_LEVEL_SQL:error}
    me.chanjar.weixin.mp.api.impl.BaseWxMpServiceImpl: ${LOG_LEVEL_WECHAT:error}
    org.befun.extension.service.NativeSqlHelper: ${LOG_LEVEL_NATIVE_SQL:info}

befun:
  extension:
    http-log.enable: ${LOG_HTTP:false}
    system-update.enable: true
  server:
    enable-open-api-filter: true

feige:
  template:
    customer: default_sms_customer

springdoc:
  swagger-ui.enabled: ${ENABLE_DOC:false}
  api-docs.enabled: ${ENABLE_DOC:false}

ctm:
  build-send-qrcode: ${CTM_BUILD_SEND_QRCODE:false}
  test-worker-token: 3f72be73c06e47e18a3dbd748ac77cd1
  enable-mock-sync-customer: ${ENABLE_MOCK_SYNC_CUSTOMER:false}
  enable-task:
    all-task: ${ENABLE_TASK:true}
    customer-push: true
    customer-push-pageable: true
    api-send: true
    event-delay: true
    journey-trigger-test: true
    resource-user-change: true
    sms-send: true
    sync-customer-wechat-info: true
    sync-customer-wechat-list: true
    webhook-send: true
    wechatopen-send: true
  info:
    domain: ${CTM_INFO_DOMAIN:https://dev.xmplus.cn/}
  journey:
    scheduling:
      lock-prefix: ${CTM_JOURNEY_REDIS_LOCK_PREFIX:befun:worker:scheduling:journey:}
      enabled: ${CTM_JOURNEY_SCHEDULING_ENABLE:false}
      cron-calculate: ${CTM_JOURNEY_SCHEDULING_CRON_CALCULATE:0 1 0 * * *}
      cron-push: ${CTM_JOURNEY_SCHEDULING_CRON_PUSH:0 0 3 * * *}
      warnings: [ experience_indicator, event_stat ]
    journey-url: ${ctm.info.domain}cem/journeymap
    email-template-add: journey-add-user
    email-template-remove: journey-remove-user
    email-template-indicator: journey-indicator-warning
    journey-size:
      empty: 0
      base: 1
      update: 5
      profession: 99999
  event:
    enable-notify: ${ENABLE_NOTIFY:true}
    default-group: ctm
    target-url: ${CTM_INFO_DOMAIN:https://dev.xmplus.cn/}cem/event/operateData/
    notify-topic: ctm_notify_event
    survey-response-topic: survey_response
    survey-change-topic: survey_change
    user-create-topic: queuing-user-create
    share-url: ${xmplus.domain}/cem/event/operateData/share/%s
    share-message: 请点击链接查看%s分享的事件：%s
  notify:
    app: cem
    warning: event-action-warning
    cooperation: event-action-cooperation
    close: event-action-close
    journey: journey-indicator-warning
    customer: event-notify-customer
    default-sms-template: default_sms_event
#  customer:
#    address-file: true
survey:
  survey-url-prefix:
    root: ${LITE_INFO_DOMAIN:https://dev.xmplus.cn/lite/}

