hanyi:
  common:
    file-storage:
      default-platform: ${FILE_PLATFORM:oss}
      enable-delete: ${FILE_ENABLE_DELETE:true}
      local:
        - platform: local
          enable-storage: true
          enable-access: true
          domain: ${FILE_URL:https://dev.xmplus.cn/api/ctm/files?url=}
          base-path: ${FILE_PATH:/tmp/files/ctm/}
          path-patterns: ${FILE_PATH_PATTERNS:/tmp/files/ctm/**}
      aliyun-oss:
        - platform: oss
          enable-storage: true
          access-key: ${ALICLOUD_ACCESS_KEY:LTAI4G5RfyxPtajMojKJPvmM}
          secret-key: ${ALICLOUD_SECRET_KEY:******************************}
          end-point: ${ALICLOUD_OSS_ENDPOINT:https://oss-cn-shenzhen.aliyuncs.com}
          bucket-name: ${ALICLOUD_OSS_BUCKET:dev-assets-sp}
          domain: ${ALICLOUD_OSS_DOMAIN:https://dev.xmplus.cn/api/ctm/files?url=}
          base-path: ${ALICLOUD_OSS_BASEPATH:cem/ctm/}