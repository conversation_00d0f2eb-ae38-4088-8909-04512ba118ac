management:
  endpoints.web.exposure.include: ${METRICS_ENDPOINTS:prometheus}
  server:
    port: ${METRICS_PORT:9996}

data.access.metrics:
  enabled: ${DATA_ACCESS_METRICS_ENABLE:true}

befun.task.metrics:
  enabled: ${BEFUN_TASK_METRICS_ENABLE:true}
  support-queues:
    - cem-event
    - cem-task
    - delay
befun.core.metrics:
  support-metrics:
    - jvm
    - befun
    - hikaricp
    - data.access