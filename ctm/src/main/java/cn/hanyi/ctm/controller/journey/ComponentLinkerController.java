package cn.hanyi.ctm.controller.journey;

import cn.hanyi.ctm.dto.journey.LinkerDataResponseDto;
import cn.hanyi.ctm.dto.journey.LinkerShenCeDataPreviewParamDto;
import cn.hanyi.ctm.service.JourneyMapService;
import cn.hanyi.ctm.service.data.LinkerDataService;
import cn.hanyi.ctm.service.journey.elements.JourneyComponentPublishService;
import cn.hanyi.ctm.service.journey.elements.JourneyComponentService;
import cn.hanyi.ctm.service.journey.elements.linker.JourneyLinkerPublishService;
import cn.hanyi.ctm.service.journey.elements.linker.JourneyLinkerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.dto.ResourceListResponseDto;
import org.befun.core.dto.ResourceResponseDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Slf4j
@Tag(name = "客户旅程-组件-连接器")
@RestController
@RequestMapping("/journey-maps/{journeyMapId}/components")
@PreAuthorize("isAuthenticated()")
@Validated
public class ComponentLinkerController {

    @Autowired
    private JourneyMapService journeyMapService;
    @Autowired
    private JourneyComponentService journeyComponentService;
    @Autowired
    private JourneyComponentPublishService journeyComponentPublishService;
    @Autowired
    private JourneyLinkerService journeyLinkerService;
    @Autowired
    private JourneyLinkerPublishService journeyLinkerPublishService;
    @Autowired
    private LinkerDataService linkerDataService;


    @GetMapping("{componentId}/linkers/all-data")
    @Operation(summary = "获得连接器的数据-指定组件的所有数据")
    public ResourceListResponseDto<LinkerDataResponseDto> dataAll(
            @PathVariable Long journeyMapId,
            @PathVariable Long componentId,
//            @Parameter(name = "startDate", description = "开始日期") @RequestParam(required = false) String startDate,
//            @Parameter(name = "endDate", description = "结束日期") @RequestParam(required = false) String endDate,
            @Parameter(name = "publish", description = "是否已发布，默认未发布") @RequestParam(required = false) boolean publish) {
        journeyMapService.checkIsCurrentOrg(journeyMapId);
        if (publish) {
            journeyComponentPublishService.checkIsCurrentOrg(componentId);
        } else {
            journeyComponentService.checkIsCurrentOrg(componentId);
        }
        return new ResourceListResponseDto<>(linkerDataService.dataList(journeyMapId, componentId, publish, null, null));
    }

    @GetMapping("{componentId}/linkers/{linkerId}/data")
    @Operation(summary = "获得连接器的数据")
    public ResourceResponseDto<LinkerDataResponseDto> eventStatData(
            @PathVariable Long journeyMapId,
            @PathVariable Long componentId,
            @PathVariable Long linkerId,
//            @Parameter(name = "startDate", description = "开始日期") @RequestParam(required = false) String startDate,
//            @Parameter(name = "endDate", description = "结束日期") @RequestParam(required = false) String endDate,
            @Parameter(name = "publish", description = "是否已发布，默认未发布") @RequestParam(required = false) boolean publish) {
        journeyMapService.checkIsCurrentOrg(journeyMapId);
        if (publish) {
            journeyComponentPublishService.checkIsCurrentOrg(componentId);
            journeyLinkerPublishService.checkIsCurrentOrg(linkerId);
        } else {
            journeyComponentService.checkIsCurrentOrg(componentId);
            journeyLinkerService.checkIsCurrentOrg(linkerId);
        }
        return new ResourceResponseDto<>(linkerDataService.data(journeyMapId, componentId, linkerId, publish, null, null));
    }

    @GetMapping("{componentId}/linkers/data/preview/baiduTongji")
    @Operation(summary = "预览百度统计数据")
    public ResourceResponseDto<LinkerDataResponseDto> previewBaiduTongji(
            @PathVariable Long journeyMapId,
            @PathVariable Long componentId,
            @RequestParam String method,
            @RequestParam String metrics,
            @RequestParam String gran,
            @RequestParam(required = false, defaultValue = "") String visitor) {
        journeyMapService.checkIsCurrentOrg(journeyMapId);
        journeyComponentService.checkIsCurrentOrg(componentId);
        return new ResourceResponseDto<>(linkerDataService.dataBaiduTongji(journeyMapId, componentId, method, metrics, gran, visitor));
    }

    @PostMapping("{componentId}/linkers/data/preview/shenCe")
    @Operation(summary = "预览神策数据")
    public ResourceResponseDto<LinkerDataResponseDto> previewShenCe(
            @PathVariable Long journeyMapId,
            @PathVariable Long componentId,
            @Valid @RequestBody LinkerShenCeDataPreviewParamDto params) {
        journeyMapService.checkIsCurrentOrg(journeyMapId);
        journeyComponentService.checkIsCurrentOrg(componentId);
        return new ResourceResponseDto<>(linkerDataService.dataShenCe(journeyMapId, componentId, params.getReportPath(), params.getRequestBody()));
    }
}
