package cn.hanyi.ctm.controller;

import cn.hanyi.ctm.constant.TypeFilter;
import cn.hanyi.ctm.dto.journey.TypeFilterDto;
import cn.hanyi.ctm.service.IndicatorCustomerService;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@Tag(name = "客户旅程-问卷过滤")
@RestController
@RequestMapping("/survey-filter/{surveyId}")
@PreAuthorize("isAuthenticated()")
public class SurveyFilterController {

    @Autowired
    private IndicatorCustomerService indicatorCustomerService;


    @GetMapping("/indicator")
    @Operation(
            summary = "渠道过滤"
    )
    @JsonView(ResourceViews.Basic.class)
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<List<TypeFilterDto>> channelFilter(@PathVariable long surveyId, @RequestParam TypeFilter type) {

        return new ResourceResponseDto(indicatorCustomerService.typeFilter(surveyId, type));
    }
}
