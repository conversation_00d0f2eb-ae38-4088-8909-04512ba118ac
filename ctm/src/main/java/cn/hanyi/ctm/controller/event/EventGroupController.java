package cn.hanyi.ctm.controller.event;

import cn.hanyi.ctm.dto.group.EventGroupQueryItemsPropertyDto;
import cn.hanyi.ctm.entity.EventGroup;
import cn.hanyi.ctm.entity.EventGroupQuery;
import cn.hanyi.ctm.repository.EventGroupQueryRepository;
import cn.hanyi.ctm.repository.EventGroupRepository;
import cn.hanyi.ctm.service.EventGroupService;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.ResourceEmbeddedMany;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Tag(name = "事件分组")
@PreAuthorize("isAuthenticated()")
@RestController
@RequestMapping("/event-groups")
@ResourceController(
        entityClass = EventGroup.class,
        repositoryClass = EventGroupRepository.class,
        serviceClass = EventGroupService.class,
        permission = "isAuthenticated()",
        docTag = "事件分组",
        docCrud = "分组"
)
@ResourceEmbeddedMany(
        path = "query",
        fieldNameInRoot = "queries",
        entityClass = EventGroupQuery.class,
        repositoryClass = EventGroupQueryRepository.class,
        docTag = "事件分组-查询",
        docCrud = "查询"
)
public class EventGroupController extends BaseController<EventGroupService> {

    @GetMapping("query-builder")
    @Operation(summary = "获取查询条件")
    public ResourceResponseDto queryBuilder() {
        return new ResourceResponseDto<>(service.getBuilder());
    }

    @PostMapping("/{id}/query/batch-new")
    @Operation(
            summary = "批量新增查询"
    )
    @Tag(
            name = "事件分组-查询"
    )
    @JsonView(ResourceViews.Detail.class)
    public ResourceResponseDto<EventGroupQueryItemsPropertyDto> createEventGroupQuery(
            @PathVariable long id,
            @RequestBody EventGroupQueryItemsPropertyDto data) {
        return new ResourceResponseDto<>(service.batchNewQuery(id, data));
    }
}
