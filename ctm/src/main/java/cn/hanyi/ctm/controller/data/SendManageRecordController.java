package cn.hanyi.ctm.controller.data;

import cn.hanyi.ctm.entity.SendManage;
import cn.hanyi.ctm.repository.SendManageRepository;
import cn.hanyi.ctm.service.SendManageRecordService;
import cn.hanyi.ctm.service.SendManageService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Tag(name = "发送管理记录")
@PreAuthorize("isAuthenticated()")
@RestController
@RequestMapping("/send-manage/{id}/record")
@ResourceController(
        entityClass = SendManage.class,
        repositoryClass = SendManageRepository.class,
        serviceClass = SendManageService.class,
        permission = "isAuthenticated()",
        docTag = "发送管理记录",
        docCrud = "发送管理记录"
)
public class SendManageRecordController extends BaseController<SendManageRecordService> {

}