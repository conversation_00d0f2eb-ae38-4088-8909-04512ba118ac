package cn.hanyi.ctm.controller.customer;

import cn.hanyi.ctm.dto.wechattemplate.WechatOpenTemplateDto;
import cn.hanyi.ctm.dto.wechattemplate.WechatOpenTemplateStatusDto;
import cn.hanyi.ctm.dto.wechattemplate.WechatTemplateCreateDto;
import cn.hanyi.ctm.dto.wechattemplate.WechatTemplateUpdateDto;
import cn.hanyi.ctm.entity.WechatTemplate;
import cn.hanyi.ctm.repository.WechatTemplateRepository;
import cn.hanyi.ctm.service.WechatTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.dto.ResourceListResponseDto;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.ResourceMethodDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import static org.befun.core.rest.annotation.processor.ResourceMethod.*;

@Tag(name = "消息模板库-微信")
@RestController
@RequestMapping("wechat-templates")
@ResourceController(
        entityClass = WechatTemplate.class,
        repositoryClass = WechatTemplateRepository.class,
        serviceClass = WechatTemplateService.class,
        permission = "isAuthenticated()",
        excludeActions = {BATCH_UPDATE},
        methodDtoClass = {
                @ResourceMethodDto(method = CREATE, dtoClass = WechatTemplateCreateDto.class, valid = true),
                @ResourceMethodDto(method = UPDATE_ONE, dtoClass = WechatTemplateUpdateDto.class, valid = true),
        },
        docTag = "消息模板库-微信",
        docCrud = "微信消息模板"
)
@Slf4j
@Validated
@PreAuthorize("isAuthenticated()")
public class WechatTemplateController {

    @Autowired
    private WechatTemplateService wechatTemplateService;

    @GetMapping("wechat-open/templates")
    @Operation(summary = "所有已绑定微信公众号的所有模版")
    public ResourceListResponseDto<WechatOpenTemplateDto> getAllWechatOpenTemplates() {
        return new ResourceListResponseDto<>(wechatTemplateService.getAllWechatOpenTemplates());
    }

    @GetMapping("{id}/bind/status")
    @Operation(summary = "关联-当前关联的状态")
    public ResourceResponseDto<WechatOpenTemplateStatusDto> bindStatus(@PathVariable long id) {
        return new ResourceResponseDto<>(wechatTemplateService.bindStatus(id));
    }

    @PostMapping("{id}/bind/update")
    @Operation(summary = "关联-更新关联")
    public ResourceResponseDto<WechatOpenTemplateStatusDto> bindUpdate(@PathVariable long id) {
        return new ResourceResponseDto<>(wechatTemplateService.bindUpdate(id));
    }
}
