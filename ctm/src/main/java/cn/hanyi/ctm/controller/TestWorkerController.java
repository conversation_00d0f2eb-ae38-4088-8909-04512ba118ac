//package cn.hanyi.ctm.controller;
//
//import cn.hanyi.ctm.constant.event.EventNotifyMoment;
//import cn.hanyi.ctm.constant.event.EventType;
//import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
//import cn.hanyi.ctm.dto.EventMqDto;
//import cn.hanyi.ctm.service.EventNotifyService;
//import cn.hanyi.ctm.service.JourneyWarningService;
//import cn.hanyi.ctm.worker.JourneyWarningWorker;
//import io.swagger.v3.oas.annotations.Operation;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.data.redis.core.StringRedisTemplate;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.validation.constraints.Min;
//import javax.validation.constraints.NotNull;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Objects;
//import java.util.Set;
//import java.util.concurrent.ExecutorService;
//import java.util.concurrent.Executors;
//import java.util.stream.Collectors;
//
//@Tag(name = "测试")
//@Slf4j
//@Validated
//@RestController
//@RequestMapping("test/worker")
//public class TestWorkerController {
//
//    @Autowired
//    private StringRedisTemplate stringRedisTemplate;
//    @Autowired
//    private JourneyWarningService journeyWarningService;
//    @Autowired(required = false)
//    private List<JourneyWarningWorker> journeyWarningWorkers;
//    @Autowired
//    private EventNotifyService eventNotifyService;
//    @Value("${ctm.test-worker-token:3f72be73c06e47e18a3dbd748ac77cd1}")
//    private String token;
//
//    private final ExecutorService executorService = Executors.newFixedThreadPool(8);
//    private final HashMap<Long, Boolean> lock = new HashMap<>();
//
//    @Operation(summary = "触发旅程预警的定时任务")
//    @GetMapping("notify/trigger")
//    public String triggerNotify(@NotNull @Min(1) @RequestParam("journeyMapId") Long journeyMapId, @RequestParam("token") String token) {
//        if (!this.token.equals(token)) {
//            return "token 错误";
//        }
//        synchronized (this) {
//            if (lock.containsKey(journeyMapId)) {
//                Boolean success = lock.get(journeyMapId);
//                if (success) {
//                    return String.format("旅程 %d 已通知完毕", journeyMapId);
//                } else {
//                    return String.format("旅程 %d 正在计算中", journeyMapId);
//                }
//            }
//        }
//        lock.put(journeyMapId, false);
//        executorService.execute(() -> {
//            try {
//                run(journeyMapId);
//            } catch (Throwable e) {
//                log.error("", e);
//            } finally {
//                lock.put(journeyMapId, true);
//            }
//        });
//        return String.format("旅程 %d 开始计算，并通知", journeyMapId);
//    }
//
//    @Operation(summary = "重置旅程预警")
//    @GetMapping("notify/reset")
//    public String resetNotify(@NotNull @Min(1) @RequestParam("journeyMapId") Long journeyMapId, @RequestParam("token") String token) {
//        if (!this.token.equals(token)) {
//            return "token 错误";
//        }
//        synchronized (this) {
//            if (lock.containsKey(journeyMapId)) {
//                Boolean success = lock.get(journeyMapId);
//                if (success) {
//                    lock.remove(journeyMapId);
//                    return String.format("旅程 %d 已重置", journeyMapId);
//                } else {
//                    return String.format("旅程 %d 正在计算中", journeyMapId);
//                }
//            }
//        }
//        return String.format("旅程 %d 已重置", journeyMapId);
//    }
//
//    @Operation(summary = "清空缓存")
//    @GetMapping("cache/clear")
//    public String clearCache(@RequestParam("token") String token) {
//        if (!this.token.equals(token)) {
//            return "token 错误";
//        }
//        Set<String> keys = stringRedisTemplate.opsForSet().members("stat:all-keys");
//        if (CollectionUtils.isNotEmpty(keys)) {
//            stringRedisTemplate.delete(keys);
//        }
//        stringRedisTemplate.delete(List.of(
//                "stat:all-keys",
//                "journey-warning:value",
//                "journey-warning:notify:event_stat",
//                "journey-warning:notify:experience_indicator"));
//        return "ok";
//    }
//
//    public void run(Long journeyMapId) {
//        if (CollectionUtils.isNotEmpty(journeyWarningWorkers)) {
//            journeyWarningWorkers.forEach(i -> i.cacheData(journeyMapId));
//        }
//        journeyWarningService.consumerWarningNotify(
//                JourneyComponentType.event_stat,
//                i -> journeyMapId.equals(i.getJourneyMapId()),
//                (i, v) -> {
//                    log.info("发送通知,id={}, value={}, type={}", i.getId(), v, JourneyComponentType.event_stat.name());
//                    String roleIds = Objects.requireNonNull(i.getReceiver()).getRoleIds().stream().map(String::valueOf).collect(Collectors.joining(","));
//                    if (StringUtils.isEmpty(roleIds)) {
//                        return;
//                    }
//                    var eventMqDto = new EventMqDto();
//                    eventMqDto.setEventId(i.getId());
//                    eventMqDto.setOrgId(i.getOrgId());
//                    eventMqDto.setRoleIds(roleIds);
//                    eventMqDto.setNotifyMoment(EventNotifyMoment.IMMEDIATE);
//                    eventMqDto.setNotifyChannel(i.getReceiver().getNotifyChannel());
//                    eventMqDto.setEventType(EventType.JOURNEY);
//                    eventMqDto.setValue(v);
//                    eventNotifyService.notifyImmediately(eventMqDto);
//                });
//
//        journeyWarningService.consumerWarningNotify(
//                JourneyComponentType.experience_indicator,
//                i -> journeyMapId.equals(i.getJourneyMapId()),
//                (i, v) -> {
//                    log.info("发送通知,id={}, value={}, type={}", i.getId(), v, JourneyComponentType.experience_indicator.name());
//                    String roleIds = Objects.requireNonNull(i.getReceiver()).getRoleIds().stream().map(String::valueOf).collect(Collectors.joining(","));
//                    if (StringUtils.isEmpty(roleIds)) {
//                        return;
//                    }
//                    var eventMqDto = new EventMqDto();
//                    eventMqDto.setEventId(i.getId());
//                    eventMqDto.setOrgId(i.getOrgId());
//                    eventMqDto.setRoleIds(roleIds);
//                    eventMqDto.setNotifyMoment(EventNotifyMoment.IMMEDIATE);
//                    eventMqDto.setNotifyChannel(i.getReceiver().getNotifyChannel());
//                    eventMqDto.setEventType(EventType.JOURNEY);
//                    eventMqDto.setValue(v);
//                    eventNotifyService.notifyImmediately(eventMqDto);
//                });
//    }
//}
