package cn.hanyi.ctm.controller;

import cn.hanyi.ctm.entity.SendGroup;
import cn.hanyi.ctm.repository.SendGroupRepository;
import cn.hanyi.ctm.service.SendGroupService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.resource.ResourceCollectionType;
import org.befun.core.rest.annotation.ResourceController;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static org.befun.core.rest.annotation.processor.ResourceMethod.BATCH_UPDATE;
import static org.befun.core.rest.annotation.processor.ResourceMethod.FIND_ONE;

@Tag(name = "发送-分组")
@Validated
@RestController
@RequestMapping("/send-groups")
@PreAuthorize("isAuthenticated()")
@ResourceController(
        entityClass = SendGroup.class,
        repositoryClass = SendGroupRepository.class,
        serviceClass = SendGroupService.class,
        collectionType = ResourceCollectionType.COLLECTION_NO_PAGE,
        permission = "isAuthenticated()",
        excludeActions = {FIND_ONE, BATCH_UPDATE},
        docCrud = "分组"
)
public class SendGroupController {
}