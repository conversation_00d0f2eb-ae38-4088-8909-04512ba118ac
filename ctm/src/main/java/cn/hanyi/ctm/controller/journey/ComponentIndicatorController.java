package cn.hanyi.ctm.controller.journey;

import cn.hanyi.ctm.dto.journey.IndicatorDataResponseDto;
import cn.hanyi.ctm.dto.journey.JourneyWarningDto;
import cn.hanyi.ctm.entity.journey.ExperienceIndicator;
import cn.hanyi.ctm.service.JourneyMapService;
import cn.hanyi.ctm.service.JourneyWarningService;
import cn.hanyi.ctm.service.data.IndicatorDataService;
import cn.hanyi.ctm.service.journey.elements.JourneyComponentPublishService;
import cn.hanyi.ctm.service.journey.elements.JourneyComponentService;
import cn.hanyi.ctm.service.journey.elements.indicator.JourneyIndicatorPublishService;
import cn.hanyi.ctm.service.journey.elements.indicator.JourneyIndicatorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.dto.ResourceListResponseDto;
import org.befun.core.dto.ResourceResponseDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

@Slf4j
@Tag(name = "客户旅程-组件-体验指标")
@RestController
@RequestMapping("/journey-maps/{journeyMapId}/components")
@PreAuthorize("isAuthenticated()")
public class ComponentIndicatorController {
    @Autowired
    private JourneyMapService journeyMapService;
    @Autowired
    private JourneyComponentService journeyComponentService;
    @Autowired
    private JourneyComponentPublishService journeyComponentPublishService;
    @Autowired
    private JourneyIndicatorService journeyIndicatorService;
    @Autowired
    private JourneyIndicatorPublishService journeyIndicatorPublishService;
    @Autowired
    private IndicatorDataService indicatorService;
    @Autowired
    private JourneyWarningService journeyWarningService;

    @GetMapping("{componentId}/experienceIndicators/{indicatorId}/data")
    @Operation(summary = "获得体验指标的数据-指定id的指标", description = "获得体验指标的数据")
    public ResourceResponseDto<IndicatorDataResponseDto> experienceIndicatorData(
            @PathVariable Long journeyMapId,
            @PathVariable Long componentId,
            @PathVariable Long indicatorId,
//            @Parameter(name = "departmentId", description = "部门id") @RequestParam(required = false) Long departmentId,
//            @Parameter(name = "startDate", description = "开始日期") @RequestParam(required = false) String startDate,
//            @Parameter(name = "endDate", description = "结束日期") @RequestParam(required = false) String endDate,
            @Parameter(name = "publish", description = "是否已发布，默认未发布") @RequestParam(required = false) boolean publish) {
        journeyMapService.checkIsCurrentOrg(journeyMapId);
        if (publish) {
            journeyComponentPublishService.checkIsCurrentOrg(componentId);
            journeyIndicatorPublishService.checkIsCurrentOrg(indicatorId);
        } else {
            journeyComponentService.checkIsCurrentOrg(componentId);
            journeyIndicatorService.checkIsCurrentOrg(indicatorId);
        }
        return new ResourceResponseDto<>(indicatorService.data(journeyMapId, componentId, indicatorId, publish, null, null, null));
    }

    @GetMapping("{componentId}/experienceIndicators/all-data")
    @Operation(summary = "获得体验指标的数据-指定组件的所有指标")
    public ResourceListResponseDto<IndicatorDataResponseDto> experienceIndicatorDataAll(
            @PathVariable Long journeyMapId,
            @PathVariable Long componentId,
//            @Parameter(name = "departmentId", description = "部门id") @RequestParam(required = false) Long departmentId,
//            @Parameter(name = "startDate", description = "开始日期") @RequestParam(required = false) String startDate,
//            @Parameter(name = "endDate", description = "结束日期") @RequestParam(required = false) String endDate,
            @Parameter(name = "publish", description = "是否已发布，默认未发布") @RequestParam(required = false) boolean publish) {
        journeyMapService.checkIsCurrentOrg(journeyMapId);
        if (publish) {
            journeyComponentPublishService.checkIsCurrentOrg(componentId);
        } else {
            journeyComponentService.checkIsCurrentOrg(componentId);
        }
        return new ResourceListResponseDto<>(indicatorService.dataList(journeyMapId, componentId, publish, null, null, null));
    }

    @PostMapping("{componentId}/experienceIndicators/{indicatorId}/warning")
    @Operation(summary = "保存指标的预警")
    public ResourceResponseDto<JourneyWarningDto> saveWarning(
            @PathVariable Long journeyMapId,
            @PathVariable Long componentId,
            @PathVariable Long indicatorId,
            @RequestBody @NotNull JourneyWarningDto dto) {
        journeyMapService.checkIsCurrentOrg(journeyMapId);
        journeyComponentService.checkIsCurrentOrg(componentId);
        ExperienceIndicator entity = journeyIndicatorService.checkIsCurrentOrg(indicatorId);
        return new ResourceResponseDto<>(journeyWarningService.saveIndicatorWarning(journeyMapId, componentId, entity.getJourneyId(), indicatorId, dto));
    }

}
