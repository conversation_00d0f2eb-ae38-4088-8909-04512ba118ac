package cn.hanyi.ctm.controller.event;

import cn.hanyi.ctm.dto.event.EventActionCloseRequestDto;
import cn.hanyi.ctm.dto.event.EventActionCooperationRequestDto;
import cn.hanyi.ctm.dto.event.EventActionRemarkRequestDto;
import cn.hanyi.ctm.dto.event.EventActionWebReplyDto;
import cn.hanyi.ctm.dto.event.EventActionWechatRequestDto;
import cn.hanyi.ctm.service.EventActionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.projection.SimpleUser;
import org.befun.core.dto.ResourceListResponseDto;
import org.befun.core.dto.ResourceResponseDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Slf4j
@Tag(name = "事件中心-行动")
@PreAuthorize("isAuthenticated()")
@RestController
@RequestMapping("/events/{eventId}")
public class EventActionController {
    @Autowired
    private EventActionService eventActionService;

    @PostMapping("action-sms")
    @Operation(summary = "事件处理-短信")
    public ResourceResponseDto<Boolean> actionSms(@PathVariable Long eventId) {
        return new ResourceResponseDto<>(eventActionService.actionSms(eventId));
    }

    @PostMapping("action-wechat")
    @Operation(summary = "事件处理-微信")
    public ResourceResponseDto<Boolean> actionWechat(@PathVariable Long eventId, @RequestBody EventActionWechatRequestDto action) {
        return new ResourceResponseDto<>(eventActionService.actionWechat(eventId, action));
    }

    @PostMapping("action-cooperation")
    @Operation(summary = "事件处理-协作")
    public ResourceResponseDto<Boolean> actionCooperation(@PathVariable Long eventId, @RequestBody EventActionCooperationRequestDto action) {
        return new ResourceResponseDto<>(eventActionService.actionCooperation(eventId, action));
    }

    @PostMapping("action-web-reply")
    @Operation(summary = "事件处理-网页回复")
    public ResourceResponseDto<Boolean> actionWebReply(@PathVariable Long eventId, @RequestBody EventActionWebReplyDto action) {
        return new ResourceResponseDto<>(eventActionService.actionWebReply(eventId, action));
    }

    @PostMapping("action-remark")
    @Operation(summary = "事件处理-备注")
    public ResourceResponseDto<Boolean> actionRemark(@PathVariable Long eventId, @RequestBody @Valid EventActionRemarkRequestDto action) {
        return new ResourceResponseDto<>(eventActionService.actionRemark(eventId, action));
    }

    @PostMapping("action-close")
    @Operation(summary = "事件处理-关闭")
    public ResourceResponseDto<Boolean> actionClose(@PathVariable Long eventId, @RequestBody(required = false) EventActionCloseRequestDto action) {
        return new ResourceResponseDto<>(eventActionService.actionClose(eventId, action));
    }

    @GetMapping("cooperation-users")
    @Operation(summary = "事件处理-协作-用户列表")
    public ResourceListResponseDto<SimpleUser> cooperationUsers(@PathVariable Long eventId) {
        return new ResourceListResponseDto<>(eventActionService.cooperationUsers(eventId));
    }
}