package cn.hanyi.ctm.controller.journey;

import cn.hanyi.ctm.dto.journey.*;
import cn.hanyi.ctm.entity.journey.*;
import cn.hanyi.ctm.repository.*;
import cn.hanyi.ctm.service.JourneyMapService;
import cn.hanyi.ctm.service.journey.elements.interaction.JourneyInteractionService;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.ResourceDeepEmbeddedMany;
import org.befun.core.rest.annotation.ResourceEmbeddedMany;
import org.befun.core.rest.annotation.ResourceMethodDto;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static org.befun.core.rest.annotation.processor.ResourceMethod.*;

@Slf4j
@Tag(name = "客户旅程")
@PreAuthorize("isAuthenticated()")
@RestController
@RequestMapping("/journey-maps")
@ResourceController(
        entityClass = JourneyMap.class,
        repositoryClass = JourneyMapRepository.class,
        serviceClass = JourneyMapService.class,
        permission = "isAuthenticated()",
        excludeActions = {COUNT, BATCH_UPDATE, CREATE},
        docTag = "客户旅程",
        docCrud = "客户旅程",
        methodDtoClass = {
                @ResourceMethodDto(method = FIND_ALL, dtoClass = JourneyQueryDto.class, valid = true),
                @ResourceMethodDto(method = UPDATE_ONE, dtoClass = JourneyUpdateRequestDto.class),
        }
)
@ResourceEmbeddedMany(
        path = "components",
        fieldNameInRoot = "components",
        entityClass = JourneyComponent.class,
        repositoryClass = JourneyComponentRepository.class,
        excludeActions = {CREATE, FIND_ALL, COUNT, BATCH_UPDATE}, // FIND_ONE,UPDATE_ONE,DELETE_ONE
        docTag = "客户旅程-组件",
        docCrud = "组件",
        deepEmbeddedMany = {
                @ResourceDeepEmbeddedMany(
                        path = "journeys",
                        fieldNameInEmbedded = "journeys",
                        entityClass = Journey.class,
                        repositoryClass = JourneyRepository.class,
                        excludeActions = {FIND_ALL, COUNT, BATCH_UPDATE, CREATE, DELETE_ONE},// UPDATE_ONE,FIND_ONE
                        docTag = "客户旅程-场景",
                        docCrud = "场景"
                ),
                @ResourceDeepEmbeddedMany(
                        path = "elementTexts",
                        fieldNameInEmbedded = "elementTexts",
                        entityClass = ElementTextBox.class,
                        repositoryClass = ElementTextBoxRepository.class,
                        excludeActions = {FIND_ALL, FIND_ONE, COUNT, BATCH_UPDATE, CREATE, DELETE_ONE}, // none
                        docTag = "客户旅程-组件-文本框",
                        docCrud = "文本框"
                ),
                @ResourceDeepEmbeddedMany(
                        path = "elementCurves",
                        fieldNameInEmbedded = "elementCurves",
                        entityClass = ElementCurve.class,
                        repositoryClass = ElementCurveRepository.class,
                        excludeActions = {FIND_ALL, COUNT, BATCH_UPDATE, CREATE, DELETE_ONE}, // UPDATE_ONE,FIND_ONE
                        docTag = "客户旅程-组件-情绪曲线",
                        docCrud = "情绪曲线"
                ),
                @ResourceDeepEmbeddedMany(
                        path = "experienceInteractions",
                        fieldNameInEmbedded = "experienceInteractions",
                        entityClass = ExperienceInteraction.class,
                        repositoryClass = ExperienceInteractionRepository.class,
                        excludeActions = {FIND_ALL, COUNT, BATCH_UPDATE}, // CREATE,UPDATE_ONE,DELETE_ONE,FIND_ONE
                        docTag = "客户旅程-组件-体验互动",
                        docCrud = "体验互动"
                ),
                @ResourceDeepEmbeddedMany(
                        path = "experienceIndicators",
                        fieldNameInEmbedded = "experienceIndicators",
                        entityClass = ExperienceIndicator.class,
                        repositoryClass = ExperienceIndicatorRepository.class,
                        excludeActions = {FIND_ALL, COUNT, BATCH_UPDATE},// CREATE,UPDATE_ONE,DELETE_ONE,FIND_ONE
                        docTag = "客户旅程-组件-体验指标",
                        docCrud = "体验指标"
                ),
                @ResourceDeepEmbeddedMany(
                        path = "eventStats",
                        fieldNameInEmbedded = "eventStats",
                        entityClass = ElementEventStat.class,
                        repositoryClass = ElementEventStatRepository.class,
                        excludeActions = {FIND_ALL, COUNT, BATCH_UPDATE},// CREATE,UPDATE_ONE,DELETE_ONE,FIND_ONE
                        methodDtoClass = {
                                @ResourceMethodDto(method = CREATE, dtoClass = EventStatSaveDto.class, valid = true),
                                @ResourceMethodDto(method = UPDATE_ONE, dtoClass = EventStatSaveDto.class, valid = true),
                        },
                        docTag = "客户旅程-组件-事件统计",
                        docCrud = "事件统计"
                ),
                @ResourceDeepEmbeddedMany(
                        path = "linkers",
                        fieldNameInEmbedded = "linkers",
                        entityClass = ElementLinker.class,
                        repositoryClass = ElementLinkerRepository.class,
                        excludeActions = {FIND_ALL, COUNT, BATCH_UPDATE},// CREATE,UPDATE_ONE,DELETE_ONE,FIND_ONE
                        methodDtoClass = {
                                @ResourceMethodDto(method = CREATE, dtoClass = ElementLinkerSaveDto.class, valid = true),
                                @ResourceMethodDto(method = UPDATE_ONE, dtoClass = ElementLinkerSaveDto.class, valid = true),
                        },
                        docTag = "客户旅程-组件-连接器",
                        docCrud = "连接器"
                ),
                @ResourceDeepEmbeddedMany(
                        path = "personas",
                        fieldNameInEmbedded = "personas",
                        entityClass = ElementPersona.class,
                        repositoryClass = ElementPersonaRepository.class,
                        excludeActions = {FIND_ALL, COUNT, BATCH_UPDATE},// CREATE,UPDATE_ONE,DELETE_ONE,FIND_ONE
                        methodDtoClass = {
                                @ResourceMethodDto(method = CREATE, dtoClass = ElementPersonaSaveDto.class, valid = true),
                                @ResourceMethodDto(method = UPDATE_ONE, dtoClass = ElementPersonaSaveDto.class, valid = true),
                        },
                        docTag = "客户旅程-组件-画像",
                        docCrud = "画像"
                )
        }
)
public class JourneyMapController extends BaseController<JourneyMapService> {

    @Autowired
    private JourneyInteractionService journeyInteractionService;

    @PostMapping
    @JsonView(ResourceViews.Detail.class)
    @Operation(summary = "新增旅程")
    public ResourceResponseDto<JourneyMapCreateResponseDto> create(@RequestBody @Validated JourneyCreateRequestDto dto) {
        return new ResourceResponseDto<>(service.create(dto));
    }

    @PostMapping("{id}/publish")
    @Operation(summary = "发布旅程")
    public ResourceResponseDto<Boolean> publish(@PathVariable long id) {
        return new ResourceResponseDto<>(service.publish(service.require(id)));
    }

    @PostMapping("{id}/add-myself")
    @Operation(summary = "将自己添加到管理员中")
    public ResourceResponseDto<Boolean> addMyself(@PathVariable long id) {
        return new ResourceResponseDto<>(service.addMyself(service.require(id)));
    }

    @PostMapping("{id}/change-owner")
    @Operation(summary = "修改拥有者")
    public ResourceResponseDto<Boolean> changeOwner(@PathVariable long id, @RequestBody @Validated JourneyOwnerRequestDto dto) {
        return new ResourceResponseDto<>(service.changeOwner(service.require(id), dto));
    }

    @PostMapping("{id}/clone")
    @Operation(summary = "复制旅程")
    public ResourceResponseDto<JourneyMapCreateResponseDto> clone(@PathVariable long id, @RequestBody JourneyRequestCloneDto dto) {
        return new ResourceResponseDto<>(service.clone(service.require(id), dto.getGroupId(), dto.getTitle()));
    }

    @GetMapping("interaction-relations")
    @Operation(summary = "场景推送关联的相关配置")
    public ResourceResponseDto<InteractionRelationDto> getInteractionRelation() {
        return new ResourceResponseDto<>(journeyInteractionService.getInteractionRelation());
    }

    @PostMapping("change-group")
    @Operation(summary = "修改旅程分组")
    public ResourceResponseDto<Boolean> changeGroup(@Valid @RequestBody JourneyMapUpdateGroupDto dto) {
        return new ResourceResponseDto<>(service.updateGroup(dto.getTargetGroupId(), dto.getIds()));
    }

}
