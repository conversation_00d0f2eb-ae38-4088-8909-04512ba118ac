package cn.hanyi.ctm.controller;

import cn.hanyi.ctm.dto.AppMassPushRequestDto;
import cn.hanyi.ctm.dto.PushResponseDto;
import cn.hanyi.ctm.dto.TextPushRequestDto;
import cn.hanyi.ctm.entity.journey.JourneyMap;
import cn.hanyi.ctm.repository.JourneyMapRepository;
import cn.hanyi.ctm.service.JourneyMapService;
import cn.hanyi.ctm.service.PushService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.annotation.ResourceCollectionAction;
import org.befun.core.rest.annotation.ResourceController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

import static org.befun.core.rest.annotation.processor.ResourceMethod.*;

@Tag(name = "系统管理")
@RestController
@RequestMapping("/admin")
@ResourceController(
        entityClass = JourneyMap.class,
        serviceClass = JourneyMapService.class,
        repositoryClass = JourneyMapRepository.class,
        excludeActions = {FIND_ALL, FIND_ONE, UPDATE_ONE, DELETE_ONE, CREATE, COUNT, BATCH_UPDATE}
)
@Slf4j
public class AdminController {

    @Autowired
    PushService pushService;

//    @Autowired
//    WechatOpenConenctorProvider wechatOpenConenctorProvider;

    @ResourceCollectionAction(
            path = "send",
            method = RequestMethod.POST,
            description = "系统发送模版消息"
    )

    public ResourceResponseDto<PushResponseDto> send(@RequestBody AppMassPushRequestDto pushDto) {
        return new ResourceResponseDto(Optional.ofNullable(pushService.sendMessageByApp(pushDto)));
    }

    @ResourceCollectionAction(
            path = "text",
            method = RequestMethod.POST,
            description = "系统发送文本消息"
    )
    public ResourceResponseDto<PushResponseDto> text(@RequestBody TextPushRequestDto pushDto) {
        return new ResourceResponseDto(Optional.ofNullable(pushService.sendTextMessageByApp(pushDto)));
    }

    @ResourceCollectionAction(
            path = "evaluate",
            method = RequestMethod.POST,
            description = "系统评估"
    )
    public ResourceResponseDto<PushResponseDto> evaluate(@RequestBody AppMassPushRequestDto pushDto) {
        return new ResourceResponseDto(Optional.ofNullable(pushService.evaluateByApp(pushDto)));
    }

//    @ResourceCollectionAction(
//            action = "activate",
//            method = RequestMethod.POST,
//            description = "激活微信开放平台"
//    )
//    public ResourceResponseDto<String> activate(@RequestBody RemoteCommandDto commandDto) {
//        return new ResourceResponseDto(Optional.ofNullable(wechatOpenConenctorProvider.execute(commandDto)));
//    }
}