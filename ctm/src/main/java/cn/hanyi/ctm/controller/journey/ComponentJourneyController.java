package cn.hanyi.ctm.controller.journey;

import cn.hanyi.ctm.dto.journey.JourneyCreateDto;
import cn.hanyi.ctm.dto.journey.JourneyCreateResponseDto;
import cn.hanyi.ctm.dto.journey.JourneyOrderDto;
import cn.hanyi.ctm.service.JourneyMapService;
import cn.hanyi.ctm.service.journey.elements.JourneyComponentService;
import cn.hanyi.ctm.service.journey.elements.scene.JourneySceneService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.ResourceResponseDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Tag(name = "客户旅程-场景")
@RestController
@RequestMapping("/journey-maps/{journeyMapId}/components")
@PreAuthorize("isAuthenticated()")
public class ComponentJourneyController {

    @Autowired
    private JourneySceneService journeySceneService;
    @Autowired
    private JourneyMapService journeyMapService;
    @Autowired
    private JourneyComponentService journeyComponentService;

    @PostMapping("{componentId}/journeys/update-order")
    @Operation(summary = "更新场景顺序")
    public ResourceResponseDto<Boolean> updateJourneysOrder(
            @PathVariable long journeyMapId,
            @PathVariable long componentId,
            @RequestBody List<JourneyOrderDto> journeyOrderDtoList) {
        journeyMapService.checkIsCurrentOrg(journeyMapId);
        journeyComponentService.checkIsCurrentOrg(componentId);
        return new ResourceResponseDto<>(journeySceneService.updateJourneyOrder(journeyOrderDtoList));
    }

    @PostMapping("{componentId}/journeys")
    @Operation(summary = "创建场景同时创建元素")
    public ResourceResponseDto<JourneyCreateResponseDto> createJourney(
            @PathVariable Long journeyMapId,
            @PathVariable Long componentId,
            @RequestBody JourneyCreateDto journey) {
        journeyMapService.checkIsCurrentOrg(journeyMapId);
        journeyComponentService.checkIsCurrentOrg(componentId);
        return new ResourceResponseDto<>(journeySceneService.createJourney(journeyMapId, componentId, journey));
    }

    @DeleteMapping("{componentId}/journeys/{journeyId}")
    @Operation(summary = "删除场景")
    public ResourceResponseDto<JourneyCreateResponseDto> deleteJourney(
            @PathVariable long journeyMapId,
            @PathVariable long componentId,
            @PathVariable long journeyId) {
        return new ResourceResponseDto<>(journeySceneService.deleteJourney(journeyMapId, componentId, journeyId));
    }

}
