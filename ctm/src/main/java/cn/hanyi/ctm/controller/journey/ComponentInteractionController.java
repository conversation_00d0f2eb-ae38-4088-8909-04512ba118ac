package cn.hanyi.ctm.controller.journey;

import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Tag(name = "客户旅程-组件-体验互动")
@RestController
@RequestMapping("/journey-maps/{id}/components")
@PreAuthorize("isAuthenticated()")
public class ComponentInteractionController {
}
