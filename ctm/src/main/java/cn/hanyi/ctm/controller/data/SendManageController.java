package cn.hanyi.ctm.controller.data;

import cn.hanyi.ctm.dto.*;
import cn.hanyi.ctm.dto.customer.CustomerSelectedDto;
import cn.hanyi.ctm.entity.SendManage;
import cn.hanyi.ctm.entity.SendManageDto;
import cn.hanyi.ctm.repository.SendManageRepository;
import cn.hanyi.ctm.service.SendCustomQueryService;
import cn.hanyi.ctm.service.SendGroupService;
import cn.hanyi.ctm.service.SendManageService;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.dto.ResourcePageResponseDto;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.ResourceQueryCustom;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;

import static org.befun.core.rest.annotation.processor.ResourceMethod.UPDATE_ONE;

@Slf4j
@Tag(name = "发送管理")
@PreAuthorize("isAuthenticated()")
@RestController
@RequestMapping("/send-manage")
@ResourceController(
        entityClass = SendManage.class,
        repositoryClass = SendManageRepository.class,
        serviceClass = SendManageService.class,
        excludeActions = {UPDATE_ONE},
        permission = "isAuthenticated()",
        docTag = "发送管理",
        docCrud = "发送管理"
)
public class SendManageController extends BaseController<SendManageService> {

    @Autowired
    private SendCustomQueryService sendCustomQueryService;

    @Autowired
    private SendGroupService sendGroupService;

    @PostMapping(path = "/{id}/analysis")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "发送统计分析")
    public ResourceResponseDto<SendManageAnalysisResponseDto> analysis(@PathVariable long id, @RequestBody(required = false) SendManageAnalysisRequestDto analysisDto) {
        return new ResourceResponseDto<>(service.analysis(id, analysisDto == null ? new SendManageAnalysisRequestDto() : analysisDto));
    }

    @GetMapping(path = "/check-token/{token}")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "发送Id校验")
    public ResourceResponseDto<SendManageAnalysisResponseDto> checkToken(@PathVariable String token) {
        service.checkToken(token);
        return new ResourceResponseDto<>();
    }

//    @GetMapping("page/{type}")
//    @Operation(summary = "全部发送管理")
//    @JsonView(ResourceViews.Basic.class)
//    public ResourcePageResponseDto<SendManageDto> findByPage(
//            @Parameter(name = "type", description = "all 全部发送，owner 我的发送") @PathVariable SendManagePageType type,
//            @ResourceQueryPredicate @Valid ResourceEntityQueryDto<SendManageDto> params) {
//        return new ResourcePageResponseDto<>(service.findByPage(type, params));
//    }

    @GetMapping("page/{type}")
    @Operation(summary = "发送管理（我的问卷，与我共享,我的审核）")
    @JsonView(ResourceViews.Basic.class)
    public ResourcePageResponseDto<SendSimpleListDto> findPageByGroup(
            @Parameter(name = "type", description = "all 全部问卷，owner 我的问卷，share 与我共享 audit 我的审核") @PathVariable String type,
            @Valid @ResourceQueryCustom SendSearchDto params) {
        params.setType(type);
        return new ResourcePageResponseDto<>(sendCustomQueryService.findPageByGroup(params));
    }

    @PutMapping("/{id}")
    @Operation(summary = "修改发送管理")
    @JsonView(ResourceViews.Detail.class)
    public ResourceResponseDto<SendManageDto> updateOne(@PathVariable long id,
                                                        @RequestBody HashMap<String, Object> data) {
        return new ResourceResponseDto<>(service.updateOne(id, data));
    }

    @PostMapping("/{id}/countCustomer")
    @Operation(summary = "计算选择的客户数量")
    @JsonView(ResourceViews.Detail.class)
    public ResourceResponseDto<Integer> countCustomer(@PathVariable long id,
                                                      @RequestBody CustomerSelectedDto data) {
        return new ResourceResponseDto<>(service.countCustomer(data));
    }

    @PostMapping("change-group")
    @Operation(summary = "修改发送分组")
    public ResourceResponseDto<Boolean> changeGroup(@Valid @RequestBody SendChangeGroupDto dto) {
        return new ResourceResponseDto<>(sendGroupService.changeGroup(dto.getTargetGroupId(), dto.getSendIds()));
    }
}