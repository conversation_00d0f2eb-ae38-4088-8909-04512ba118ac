package cn.hanyi.ctm.controller.customer;

import cn.hanyi.ctm.dto.connector.ConnectorConsumerRelationIdsDto;
import cn.hanyi.ctm.dto.connector.ConsumerConnectorDto;
import cn.hanyi.ctm.entity.ConnectorConsumer;
import cn.hanyi.ctm.entity.ConnectorConsumerDto;
import cn.hanyi.ctm.service.ConnectorConsumerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.ResourcePageResponseDto;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.rest.annotation.ResourceQueryPredicate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@Tag(name = "连接器")
@RestController
@RequestMapping("/connectors")
@PreAuthorize("isAuthenticated()")
public class ConnectorConsumerController {
    @Autowired
    private ConnectorConsumerService connectorConsumerService;

    @GetMapping("/consumers")
    @Operation(summary = "获取推送")
    public ResourcePageResponseDto<ConnectorConsumerDto> consumers(@ResourceQueryPredicate ResourceEntityQueryDto<ConnectorConsumerDto> queryDto) {
        if (null == queryDto.getSorts() && null == queryDto.getSort()) {
            queryDto.setSorts(Sort.by(Sort.Direction.DESC, "modifyTime"));
        }
        return connectorConsumerService.consumers(queryDto);
    }

    @PostMapping("/{id}/save-consumer")
    @Operation(summary = "创建推送")
    public ResourceResponseDto saveConsumer(@PathVariable Long id, @RequestBody ConnectorConsumerRelationIdsDto data) {
        return new ResourceResponseDto<>(connectorConsumerService.consumer(id, data));
    }

    @PutMapping("/consumers/{id}")
    @Operation(summary = "修改推送")
    public ResourceResponseDto<ConnectorConsumer> update(@PathVariable Long id, @RequestBody ConsumerConnectorDto data) {
        return new ResourceResponseDto<>(connectorConsumerService.consumerUpdate(id, data));
    }

    @DeleteMapping("/consumers/{id}")
    @Operation(summary = "删除推送")
    public ResourceResponseDto<Void> delete(@PathVariable Long id) {
        connectorConsumerService.deleteConsumers(id);
        return new ResourceResponseDto<>();
    }

    @PostMapping("/consumers/{id}/enable")
    @Operation(summary = "开启推送")
    public ResourceResponseDto<ConnectorConsumer> enable(@PathVariable Long id) {
        return new ResourceResponseDto<>(connectorConsumerService.consumerEnable(id, true));
    }

    @PostMapping("/consumers/{id}/disable")
    @Operation(summary = "关闭推送")
    public ResourceResponseDto<ConnectorConsumer> disable(@PathVariable Long id) {
        return new ResourceResponseDto<>(connectorConsumerService.consumerEnable(id, false));
    }
}
