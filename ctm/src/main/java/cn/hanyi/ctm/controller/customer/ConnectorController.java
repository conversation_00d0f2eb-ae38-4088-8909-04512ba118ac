package cn.hanyi.ctm.controller.customer;

import cn.hanyi.ctm.entity.Connector;
import cn.hanyi.ctm.entity.Template;
import cn.hanyi.ctm.entity.ThirdPartyCustomer;
import cn.hanyi.ctm.entity.ThirdPartyTemplate;
import cn.hanyi.ctm.repository.ConnectorRepository;
import cn.hanyi.ctm.repository.TemplateRepository;
import cn.hanyi.ctm.repository.ThirdPartyCustomerRepository;
import cn.hanyi.ctm.service.ConnectorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.ResourceEmbeddedMany;
import org.befun.core.rest.annotation.ResourcePermission;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import static org.befun.core.rest.annotation.processor.ResourceMethod.DELETE_ONE;
import static org.befun.core.rest.annotation.processor.ResourceMethod.FIND_ALL;

@Slf4j
@Tag(name = "连接器")
@RestController
@RequestMapping("/connectors")
@PreAuthorize("isAuthenticated()")
@ResourceController(
        entityClass = Connector.class,
        repositoryClass = ConnectorRepository.class,
        serviceClass = ConnectorService.class,
        excludeActions = {DELETE_ONE},
        permission = "isAuthenticated()",
        docTag = "连接器",
        docCrud = "连接器"
)
public class ConnectorController extends BaseController<ConnectorService> {

    @DeleteMapping("/{id}")
    @Operation(summary = "删除连接器")
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<String> deleteOne(@PathVariable Long id, @RequestParam(value = "delete_user", required = false) boolean deleteUser) {
        return new ResourceResponseDto<>(service.deleteConnector(id, deleteUser));
    }

}