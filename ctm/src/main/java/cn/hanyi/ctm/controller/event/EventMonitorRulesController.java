package cn.hanyi.ctm.controller.event;

import cn.hanyi.ctm.dto.event.WarningCountRerunRequestDto;
import cn.hanyi.ctm.dto.event.WarningCountRerunResponseDto;
import cn.hanyi.ctm.dto.event.WarningRerunDto;
import cn.hanyi.ctm.entity.EventMonitorRules;
import cn.hanyi.ctm.repository.EventMonitorRulesRepository;
import cn.hanyi.ctm.service.EventRuleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "预警规则")
@PreAuthorize("isAuthenticated()")
@RestController
@RequestMapping("/event-monitor-rules")
@ResourceController(
        entityClass = EventMonitorRules.class,
        repositoryClass = EventMonitorRulesRepository.class,
        serviceClass = EventRuleService.class,
        permission = "isAuthenticated()",
        excludeActions = {},
        docTag = "预警规则",
        docCrud = "预警规则"

)
@Validated
public class EventMonitorRulesController extends BaseController<EventRuleService> {


    @PostMapping("{id}/enable")
    @Operation(summary = "开启规则")
    public ResourceResponseDto<Boolean> enable(@PathVariable long id) {
        return new ResourceResponseDto<>(service.status(id, true));
    }

    @PostMapping("{id}/disable")
    @Operation(summary = "关闭规则")
    public ResourceResponseDto<Boolean> disable(@PathVariable long id) {
        return new ResourceResponseDto<>(service.status(id, false));
    }

    @PostMapping("{id}/rerun")
    @Operation(summary = "重跑规则")
    public ResourceResponseDto<Boolean> rerun(@PathVariable long id, @Valid @RequestBody WarningRerunDto dto) {
        return new ResourceResponseDto<>(service.rerun(id, dto));
    }

    @GetMapping("{id}/rerun/countData")
    @Operation(summary = "重跑规则-计算数据量")
    public ResourceResponseDto<WarningCountRerunResponseDto> countRerun(
            @PathVariable long id,
            @RequestParam @NotEmpty @Parameter(required = true, description = "数据范围：all 全部，waiting 待分析，dateRange 时间区间") String dataScope,
            @RequestParam(required = false) @Parameter(description = "时间区间，包含两个日期（,）分隔") String dateRange) {
        return new ResourceResponseDto<>(service.countRerun(id, new WarningCountRerunRequestDto(dataScope,dateRange)));
    }
}