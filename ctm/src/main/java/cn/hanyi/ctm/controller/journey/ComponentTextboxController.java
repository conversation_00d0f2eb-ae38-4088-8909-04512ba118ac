package cn.hanyi.ctm.controller.journey;

import cn.hanyi.ctm.entity.journey.ElementTextBox;
import cn.hanyi.ctm.repository.ElementTextBoxRepository;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.context.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Slf4j
@Tag(name = "客户旅程-组件-文本框")
@RestController
@RequestMapping("/journey-maps/{id}/components")
@PreAuthorize("isAuthenticated()")
public class ComponentTextboxController {

    @Autowired
    private ElementTextBoxRepository elementTextBoxRepository;

    @PostMapping("elementTexts/batchUpdate")
    @Operation(summary = "批量更新文本框", description = "批量更新文本框")
    public ResourceResponseDto<String> batchUpdateTextBox(
            @PathVariable long id,
            @RequestBody List<ElementTextBox> textBoxList) {
        List<ElementTextBox> elementTextBoxes = new ArrayList<>();
        textBoxList.forEach(t -> {
            Optional<ElementTextBox> elementTextBox = elementTextBoxRepository.findByIdAndOrgId(t.getId(),
                    TenantContext.getCurrentTenant());
            elementTextBox.ifPresent(et -> {
                et.setContent(t.getContent());
                elementTextBoxes.add(et);
            });
        });
        elementTextBoxRepository.saveAll(elementTextBoxes);
        return new ResourceResponseDto<>("Success");
    }
}
