package cn.hanyi.ctm.controller.customer;

import cn.hanyi.ctm.service.TemplateService;
import org.befun.core.rest.annotation.ResourceController;
import cn.hanyi.ctm.entity.Template;
import cn.hanyi.ctm.repository.TemplateRepository;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.rest.annotation.ResourcePermission;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("templates")
@PreAuthorize("isAuthenticated()")
@ResourceController(
        entityClass = Template.class,
        repositoryClass = TemplateRepository.class,
        serviceClass = TemplateService.class,
        permission = "isAuthenticated()",
        permissions = {
                @ResourcePermission(action = "findAll", permission = "isAuthenticated()"),
                @ResourcePermission(action = "updateOne", permission = "isAuthenticated()"),
                @ResourcePermission(action = "create", permission = "isAuthenticated()"),
                @ResourcePermission(action = "deleteOne", permission = "isAuthenticated()")
        },
        docTag = "第三方模板",
        docCrud = "第三方模板"
)
@Slf4j
public class TemplateController {
}