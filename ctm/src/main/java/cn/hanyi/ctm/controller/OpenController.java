package cn.hanyi.ctm.controller;

import cn.hanyi.ctm.dto.WechatSignatureResponseDto;
import cn.hanyi.ctm.dto.customer.DeleteCustomerDto;
import cn.hanyi.ctm.dto.customer.SyncCustomerDto;
import cn.hanyi.ctm.dto.customer.SyncCustomerResponseDto;
import cn.hanyi.ctm.dto.journey.JourneyCopyDto;
import cn.hanyi.ctm.dto.journey.JourneyTriggerRequestDto;
import cn.hanyi.ctm.entity.Customer;
import cn.hanyi.ctm.service.CustomerSyncService;
import cn.hanyi.ctm.service.JourneyMapService;
import cn.hanyi.ctm.service.OpenService;
import cn.hanyi.ctm.service.WechatShareService;
import com.alibaba.fastjson.JSONArray;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import org.befun.core.dto.ResourceListResponseDto;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.context.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Tag(name = "开放接口")
@RestController
@RequestMapping("/open")
public class OpenController {

    @Autowired
    private CustomerSyncService customerSyncService;
    @Autowired
    private OpenService openService;
    @Autowired
    private WechatShareService wechatShareService;
    @Autowired
    private JourneyMapService journeyMapService;

    @PostMapping("journey/trigger")
    @Operation(summary = "触发体验互动")
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<String> journeyTrigger(
            @RequestBody JourneyTriggerRequestDto journeyTriggerRequestDto) {
        ResourceResponseDto<String> res = new ResourceResponseDto<>();
        res.setMessage(openService.triggerJourney(journeyTriggerRequestDto));
        return res;
    }

    @PostMapping("customer/sync")
    @Operation(summary = "客户中心-同步客户信息（新增或者修改）")
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<SyncCustomerResponseDto> syncCustomer(@Valid @RequestBody SyncCustomerDto dto) {
        Customer customer = customerSyncService.syncCustomer(TenantContext.getCurrentTenant(), dto);
        return new ResourceResponseDto<>(new SyncCustomerResponseDto(customer.getId(), customer.getExternalUserId()));
    }

    @DeleteMapping("customer/delete")
    @Operation(summary = "客户中心-删除客户信息")
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<SyncCustomerResponseDto> deleteCustomer(@Valid @RequestBody DeleteCustomerDto dto) {
        Customer customer = customerSyncService.deleteCustomer(TenantContext.getCurrentTenant(), dto);
        return new ResourceResponseDto<>(new SyncCustomerResponseDto(customer.getId(), customer.getExternalUserId()));
    }

    @GetMapping("/{app}/wechat-share")
    @Operation(summary = "微信问卷分享，微信JS-SDK权限签名")
    public ResourceResponseDto<WechatSignatureResponseDto> getWxSignature(@PathVariable("app") String app, String url) {
        WxJsapiSignature wxSignature = wechatShareService.getWxSignature(app, url);
        return new ResourceResponseDto<>(new WechatSignatureResponseDto(wxSignature.getAppId(), wxSignature.getNonceStr(), wxSignature.getTimestamp(), wxSignature.getUrl(), wxSignature.getSignature()));
    }

    @PostMapping("journey/copy")
    @Operation(summary = "复制旅程")
    public ResourceListResponseDto<Long> copyJourney(@RequestBody JourneyCopyDto dto) {
        if (!"5fa84f3f9648477387332cb1048d20ff".equals(dto.getCopyToken())) {
            return new ResourceListResponseDto<>();
        }
        var journeyIds = new ArrayList<Long>();

        dto.getSourceJourneyMapIds().forEach(sourceJourneyMapId -> {
            try {
                if (journeyMapService.clone(sourceJourneyMapId, dto.getTargetOrgId(), dto.getTargetUserId())) {
                    journeyIds.add(sourceJourneyMapId);
                }
            } catch (Exception e) {
                log.error("复制旅程失败: {}", sourceJourneyMapId, e);
            }
        });

        return new ResourceListResponseDto<>(journeyIds);
    }

    //    @GetMapping("customer/sync/progress")
//    @Operation(summary = "客户中心-同步客户信息的进度", hidden = true)
//    @PreAuthorize("isAuthenticated()")
//    public ResourceResponseDto<TaskProgressDto> syncCustomerProgress() {
//        return new ResourceResponseDto<>(syncCustomerOpenExecutor.syncUpdateProgress(TenantContext.getCurrentTenant()));
//    }

    @GetMapping("/test")
    public void test(){

        String address = null;
        try {
            address = new String(new ClassPathResource("static/address.json").getInputStream().readAllBytes(), StandardCharsets.UTF_8);
        } catch (IOException e) {
            e.printStackTrace();
        }

    }
}