package cn.hanyi.ctm.controller.event;

import cn.hanyi.ctm.repository.EventMonitorThesaurusRepository;
import cn.hanyi.ctm.service.EventThesaurusService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Slf4j
@Tag(name = "预警词库")
@PreAuthorize("isAuthenticated()")
@RestController
@RequestMapping("/event-monitor-thesaurus")
@ResourceController(
        entityClass = cn.hanyi.ctm.entity.EventMonitorThesaurus.class,
        repositoryClass = EventMonitorThesaurusRepository.class,
        serviceClass = EventThesaurusService.class,
        permission = "isAuthenticated()",
        docTag = "预警词库",
        docCrud = "词库"
)
public class EventMonitorThesaurusController extends BaseController<EventThesaurusService> {
}