package cn.hanyi.ctm.controller.customer;

import cn.hanyi.ctm.dto.WechatOpenSyncCustomerStatusDto;
import cn.hanyi.ctm.entity.ThirdPartyTemplate;
import cn.hanyi.ctm.service.CustomerWechatService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.dto.ResourceListResponseDto;
import org.befun.core.dto.ResourceResponseDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@Tag(name = "微信用户")
@RestController
@RequestMapping("customers/wechat-open")
@PreAuthorize("isAuthenticated()")
public class CustomerWechatController {

    @Autowired
    private HttpServletRequest request;
    @Autowired
    private CustomerWechatService customerWechatService;

    @GetMapping("sync-customer/auto-sync-status")
    @Operation(summary = "同步客户信息-是否开启自动同步")
    public ResourceResponseDto<Boolean> enableAutoSyncCustomer() {
        return new ResourceResponseDto<>(customerWechatService.enableAutoSyncCustomer());
    }

    @PostMapping("sync-customer/{configId}")
    @Operation(summary = "同步客户信息(异步)-指定公众号")
    public ResourceResponseDto<Boolean> asyncCustomer(@PathVariable long configId) {
        String mock = request.getHeader("mockSync");
        return new ResourceResponseDto<>(customerWechatService.asyncCustomer(configId, mock != null));
    }

    @GetMapping("sync-customer/{configId}/progress")
    @Operation(summary = "同步客户信息-查询进度")
    public ResourceResponseDto<WechatOpenSyncCustomerStatusDto> syncCustomerProgress(@PathVariable long configId) {
        return new ResourceResponseDto<>(customerWechatService.syncCustomerProgress(configId));
    }

    @PostMapping("sync-template/{configId}")
    @Operation(summary = "同步第三方模板(同步)-指定公众号")
    public ResourceListResponseDto<ThirdPartyTemplate> syncTemplate(@PathVariable long configId) {
        return new ResourceListResponseDto<>(customerWechatService.syncTemplate(configId));
    }

    @PostMapping("sync-template/all")
    @Operation(summary = "同步第三方模板(异步)-全部")
    public ResourceResponseDto<Boolean> asyncTemplateAll() {
        return new ResourceResponseDto<>(customerWechatService.asyncTemplateAll());
    }

    @GetMapping("sync-template/all/progress")
    @Operation(summary = "同步第三方模板-全部-查询进度")
    public ResourceResponseDto<WechatOpenSyncCustomerStatusDto> syncTemplateAllProgress() {
        return new ResourceResponseDto<>(customerWechatService.syncTemplateAllProgress());
    }
}