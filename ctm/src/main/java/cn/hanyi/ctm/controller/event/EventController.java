package cn.hanyi.ctm.controller.event;

import cn.hanyi.ctm.dto.PreviewNextIdDto;
import cn.hanyi.ctm.dto.event.CustomerEventQueryDto;
import cn.hanyi.ctm.dto.event.UpdateAndActionDto;
import cn.hanyi.ctm.entity.Event;
import cn.hanyi.ctm.entity.EventAction;
import cn.hanyi.ctm.entity.EventDto;
import cn.hanyi.ctm.repository.EventActionRepository;
import cn.hanyi.ctm.repository.EventRepository;
import cn.hanyi.ctm.service.EventDownloadService;
import cn.hanyi.ctm.service.EventQueryService;
import cn.hanyi.ctm.service.EventService;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.constant.PermissionPath;
import org.befun.core.dto.CountDto;
import org.befun.core.dto.ResourcePageResponseDto;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.limiter.annotation.RequirePermissions;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.ResourceEmbeddedMany;
import org.befun.core.rest.annotation.ResourceQueryCustom;
import org.befun.core.rest.annotation.ResourceQueryPredicate;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import static org.befun.core.rest.annotation.processor.ResourceMethod.*;

@Slf4j
@Tag(name = "事件中心")
@PreAuthorize("isAuthenticated()")
@RestController
@RequestMapping("/events")
@ResourceController(
        entityClass = Event.class,
        repositoryClass = EventRepository.class,
        serviceClass = EventService.class,
        excludeActions = {CREATE, FIND_ALL, COUNT},
        permission = "isAuthenticated()",
        docTag = "事件中心",
        docCrud = "事件"
)
@ResourceEmbeddedMany(
        path = "actions",
        fieldNameInRoot = "actions",
        entityClass = EventAction.class,
        repositoryClass = EventActionRepository.class,
        excludeActions = {CREATE, UPDATE_ONE, COUNT, FIND_ONE},
        docTag = "事件中心-行动",
        docCrud = "行动"
)
public class EventController extends BaseController<EventService> {
    @Autowired
    private EventDownloadService eventDownloadService;

    @Autowired
    private EventQueryService eventQueryService;

    @GetMapping("/download")
    @Operation(summary = "事件下载")
    @JsonView(ResourceViews.Basic.class)
    @RequirePermissions(PermissionPath.EVENTS_EVENT_ACTION_DOWNLOAD)
    public void download(
            @ResourceQueryPredicate ResourceEntityQueryDto<EventDto> params, HttpServletResponse response) throws Exception {
        eventDownloadService.download(params, response);
    }

    @GetMapping("/customer")
    @Operation(summary = "客户的预警事件列表")
    @JsonView(ResourceViews.Basic.class)
    public ResourcePageResponseDto<EventDto> customerEvents(@ResourceQueryCustom CustomerEventQueryDto params) {
        return new ResourcePageResponseDto<>(service.customerEvents(params));
    }

    @GetMapping("/{id}/preview-next")
    @Operation(summary = "上下页")
    @Tag(name = "事件中心")
    @JsonView(ResourceViews.Detail.class)
    public ResourceResponseDto<PreviewNextIdDto> previewNext(
            @PathVariable long id,
            @ResourceQueryPredicate @Valid ResourceEntityQueryDto<EventDto> params
    ) {
        return new ResourceResponseDto<>(service.previewNext(id, params));
    }

    @PostMapping("/{id}/update-and-action")
    @Operation(summary = "修改事件并行动")
    @Tag(name = "事件中心")
    @JsonView(ResourceViews.Detail.class)
    public ResourceResponseDto<Void> updateAndAction(
            @PathVariable long id,
            @RequestBody UpdateAndActionDto dto
    ) {
        service.updateAndAction(id, dto);
        return new ResourceResponseDto<>();
    }

    @GetMapping("")
    @Operation(
            summary = "全部事件"
    )
    @Tag(
            name = "事件中心"
    )
    @JsonView(ResourceViews.Basic.class)
    public ResourcePageResponseDto<EventDto> findAll(
            @ResourceQueryPredicate @Valid ResourceEntityQueryDto<EventDto> params) {
        return service.customQuery(params);
    }

    @GetMapping("/count")
    @Operation(
            summary = "统计事件"
    )
    @Tag(
            name = "事件中心"
    )
    @JsonView(ResourceViews.Basic.class)
    public ResourceResponseDto<CountDto> count(
            @ResourceQueryPredicate @Valid ResourceEntityQueryDto<EventDto> params) {
        return new ResourceResponseDto(service.customCount(params));
    }
}
