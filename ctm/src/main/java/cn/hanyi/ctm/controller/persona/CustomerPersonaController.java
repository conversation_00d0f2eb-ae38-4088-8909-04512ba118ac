package cn.hanyi.ctm.controller.persona;

import cn.hanyi.ctm.dto.journey.CustomerPersonalCloneDto;
import cn.hanyi.ctm.dto.journey.JourneyMapCreateResponseDto;
import cn.hanyi.ctm.dto.journey.JourneyRequestCloneDto;
import cn.hanyi.ctm.dto.persona.*;
import cn.hanyi.ctm.entity.persona.CustomerPersona;
import cn.hanyi.ctm.entity.persona.CustomerPersonaComponent;
import cn.hanyi.ctm.entity.persona.CustomerPersonaDto;
import cn.hanyi.ctm.repository.CustomerPersonComponentRepository;
import cn.hanyi.ctm.repository.CustomerPersonRepository;
import cn.hanyi.ctm.service.JourneyMapService;
import cn.hanyi.ctm.service.persona.CustomerPersonaService;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.ResourceEmbeddedMany;
import org.befun.core.rest.annotation.ResourceMethodDto;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static org.befun.core.rest.annotation.processor.ResourceMethod.*;

@Slf4j
@Tag(name = "客户画像")
@PreAuthorize("isAuthenticated()")
@RestController
@RequestMapping("/personas")
@ResourceController(
        entityClass = CustomerPersona.class,
        repositoryClass = CustomerPersonRepository.class,
        serviceClass = CustomerPersonaService.class,
        permission = "isAuthenticated()",
        excludeActions = {COUNT, BATCH_UPDATE},
        docTag = "客户画像",
        docCrud = "客户画像",
        methodDtoClass = {
                @ResourceMethodDto(method = FIND_ALL, dtoClass = CustomerPersonaQueryDto.class, valid = true),
                @ResourceMethodDto(method = CREATE, dtoClass = CustomerPersonaAddDto.class, valid = true),
                @ResourceMethodDto(method = UPDATE_ONE, dtoClass = CustomerPersonaUpdateDto.class, valid = true),
        }
)
@ResourceEmbeddedMany(
        path = "persona-components",
        fieldNameInRoot = "components",
        entityClass = CustomerPersonaComponent.class,
        repositoryClass = CustomerPersonComponentRepository.class,
        excludeActions = {COUNT, BATCH_UPDATE, FIND_ONE, FIND_ALL},
        docTag = "客户画像",
        docCrud = "组件",
        methodDtoClass = {
                @ResourceMethodDto(method = CREATE, dtoClass = CustomerPersonaComponentAddDto.class, valid = true),
                @ResourceMethodDto(method = UPDATE_ONE, dtoClass = CustomerPersonaComponentUpdateDto.class, valid = true),
        }
)
public class CustomerPersonaController extends BaseController<CustomerPersonaService> {

    @Autowired
    private CustomerPersonaService customerPersonaService;

    @GetMapping("/{id}/containComponents")
    @Operation(summary = "单个客户画像(包含组件数据)")
    @JsonView(ResourceViews.Detail.class)
    public ResourceResponseDto<CustomerPersonaDto> findOne(@PathVariable long id,
                                                           @RequestParam(required = false, defaultValue = "0") long journeyMapId,
                                                           @RequestParam(required = false, defaultValue = "0") long componentId,
                                                           @RequestParam(required = false) boolean publish
    ) {
        return new ResourceResponseDto<>(customerPersonaService.findOneContainComponents(id, journeyMapId,componentId,publish));
    }

    @PostMapping("change-group")
    @Operation(summary = "修改画像分组")
    public ResourceResponseDto<Boolean> changeGroup(@Valid @RequestBody CustomerPersonaUpdateGroupDto dto) {
        return new ResourceResponseDto<>(customerPersonaService.updateGroup(dto.getTargetGroupId(), dto.getIds()));
    }

    @PostMapping("{id}/clone")
    @Operation(summary = "复制画像")
    public ResourceResponseDto<CustomerPersonaDto> clone(@PathVariable long id, @RequestBody CustomerPersonalCloneDto dto) {
        return new ResourceResponseDto<>(service.clone(service.require(id), dto.getGroupId(), dto.getTitle()));
    }
}
