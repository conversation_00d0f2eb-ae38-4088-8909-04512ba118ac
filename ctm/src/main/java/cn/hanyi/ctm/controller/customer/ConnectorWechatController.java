package cn.hanyi.ctm.controller.customer;

import cn.hanyi.ctm.dto.WechatOpenSyncCustomerStatusDto;
import cn.hanyi.ctm.entity.Connector;
import cn.hanyi.ctm.entity.ThirdPartyTemplate;
import cn.hanyi.ctm.exception.ConnectorUnAuthorizeException;
import cn.hanyi.ctm.repository.ThirdPartyTemplateRepository;
import cn.hanyi.ctm.service.ConnectorService;
import cn.hanyi.ctm.service.TemplateService;
import cn.hanyi.ctm.workertrigger.CtmTaskTrigger;
import cn.hanyi.ctm.workertrigger.dto.WechatOpenSyncCustomerListDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.constant.UserTaskType;
import org.befun.auth.service.OrganizationService;
import org.befun.auth.service.UserTaskService;
import org.befun.core.dto.ResourceListResponseDto;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.entity.BaseEntity;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.befun.task.dto.TaskProgressDto;
import org.befun.task.entity.TaskProgress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

@Deprecated
@Slf4j
@Tag(name = "连接器")
@RestController
@RequestMapping("/connectors")
@PreAuthorize("isAuthenticated()")
public class ConnectorWechatController {

    @Autowired
    private HttpServletRequest request;
    @Autowired
    private ConnectorService connectorService;
    @Autowired
    private TemplateService templateService;
    @Autowired
    private ThirdPartyTemplateRepository thirdpartyTemplateRepository;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private CtmTaskTrigger ctmTaskTrigger;
    @Autowired
    private UserTaskService userTaskService;

    @GetMapping("{id}/sync-customer/auto-sync-status")
    @Operation(summary = "同步客户信息-是否开启自动同步")
    public ResourceResponseDto<Boolean> enableAutoSyncCustomer(@PathVariable long id) {
        Connector connector = connectorService.require(id);
        return new ResourceResponseDto<>(organizationService.enableAutoSyncCustomer(connector.getOrgId()));
    }

    @PostMapping("{id}/sync-customer")
    @Operation(summary = "同步客户信息")
    public ResourceResponseDto<Boolean> syncCustomer(@PathVariable long id) {
        Long orgId = TenantContext.getCurrentTenant();
        Long userId = TenantContext.getCurrentUserId();
        Connector connector = connectorService.require(id);
        if (connector.getAuthorizeStatus().getValue() != 1) {
            throw new ConnectorUnAuthorizeException();
        }
        if (userTaskService.lastTaskIsCompleted(orgId, userId, UserTaskType.syncWechatOpenSyncCustomer)) {
            TaskProgress progress = userTaskService.createTask(orgId, userId, UserTaskType.syncWechatOpenSyncCustomer, 0,
                    Map.of("connectorId", connector.getId(), "appId", connector.getAppId()));
            String mock = request.getHeader("mockSync");
            if (mock != null) {
                ctmTaskTrigger.wechatOpenSyncCustomerList(new WechatOpenSyncCustomerListDto(orgId, userId, progress.getId(), connector.getId(), "mockSync"));
            } else {
                ctmTaskTrigger.wechatOpenSyncCustomerList(new WechatOpenSyncCustomerListDto(orgId, userId, progress.getId(), connector.getId(), connector.getAppId()));
            }
        } else {
            throw new BadRequestException("正在同步中");
        }
        return new ResourceResponseDto<>(true);
    }

    @GetMapping("{id}/sync-customer/progress")
    @Operation(summary = "同步客户信息-查询进度")
    public ResourceResponseDto<WechatOpenSyncCustomerStatusDto> syncCustomerProgress(@PathVariable long id) {
        Long orgId = TenantContext.getCurrentTenant();
        Long userId = TenantContext.getCurrentUserId();
        connectorService.require(id);
        Long taskProgressId = userTaskService.lastTask(orgId, userId, UserTaskType.syncWechatOpenSyncCustomer).map(BaseEntity::getId).orElse(0L);
        TaskProgressDto progress = userTaskService.progress(taskProgressId);
        return new ResourceResponseDto<>(new WechatOpenSyncCustomerStatusDto(progress.getStatus().isCompleted()));
    }

    @PostMapping("{id}/sync-template")
    @Operation(summary = "同步第三方模板")
    public ResourceListResponseDto<ThirdPartyTemplate> syncTemplate(@PathVariable long id) {
        Connector connector = connectorService.require(id);
        if (connector.getAuthorizeStatus().getValue() != 1) {
            throw new ConnectorUnAuthorizeException();
        }
        templateService.syncTemplate(connector);
        return new ResourceListResponseDto<>(thirdpartyTemplateRepository.findAllByConnector(connector));
    }

}