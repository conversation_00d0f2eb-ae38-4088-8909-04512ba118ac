package cn.hanyi.ctm.controller.journey;

import cn.hanyi.ctm.dto.journey.JourneyComponentDateFilterDto;
import cn.hanyi.ctm.dto.journey.JourneyMapPublishResponseDto;
import cn.hanyi.ctm.entity.journey.JourneyComponentDto;
import cn.hanyi.ctm.service.JourneyMapService;
import cn.hanyi.ctm.service.journey.elements.JourneyComponentPublishService;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@Slf4j
@Tag(name = "客户旅程-组件")
@Validated
@RestController
@PreAuthorize("isAuthenticated()")
@RequestMapping("/journey-maps/{journeyMapId}/publish-components")
public class ComponentPublishController {

    @Autowired
    private JourneyMapService journeyMapService;

    @GetMapping
    @Operation(summary = "旅程组件列表-已发布")
    public ResourceResponseDto<JourneyMapPublishResponseDto> findAll(@PathVariable long journeyMapId) {
        return new ResourceResponseDto<>(journeyMapService.publishComponents(journeyMapId));
    }
}
