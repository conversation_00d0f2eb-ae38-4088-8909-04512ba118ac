package cn.hanyi.ctm.controller;

import cn.hanyi.ctm.service.ConnectorWechatOpenService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.ResourceResponseDto;
import cn.hanyi.ctm.constant.connector.ConnectorProviderType;
import cn.hanyi.ctm.dto.AuthorizeRequestDto;
import cn.hanyi.ctm.dto.CallbackRequestDto;
import cn.hanyi.ctm.dto.WechatOpenPreAuthResponseDto;
import cn.hanyi.ctm.entity.Connector;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

@Tag(name = "系统管理")
@RestController
@RequestMapping("/wechatopen")
//@ResourceController(
//        entityClass = Connector.class,
//        repositoryClass = ConnectorRepository.class
//)
@Slf4j
public class WechatOpenController {

    @Autowired
    ConnectorWechatOpenService connectorService;

    @Operation(description = "微信开放平台预授权", summary = "预授权", tags = {"微信开放平台"})
    @RequestMapping(path = "preauth", method = RequestMethod.GET)
    public ResourceResponseDto<WechatOpenPreAuthResponseDto> preAuthenticate() {
        WechatOpenPreAuthResponseDto dto = new WechatOpenPreAuthResponseDto();
        String preAuthCode = connectorService.preAuthenticate(ConnectorProviderType.WECHATOPEN);
        dto.setPreAuthCode(preAuthCode);
        return new ResourceResponseDto(Optional.ofNullable(dto));
    }

    @Operation(description = "微信开放平台管理员扫码授权，授权完成之后创建连接器", summary = "授权创建", tags = {"微信开放平台"})
    @RequestMapping(path = "authorize", method = RequestMethod.POST)
    @PreAuthorize("isAuthenticated()")
    public ResourceResponseDto<Connector> authorize(@RequestBody AuthorizeRequestDto requestDto) {
        return new ResourceResponseDto(connectorService.authorize(ConnectorProviderType.WECHATOPEN, requestDto));
    }

    @Operation(description = "[系统集成] ticket callback", summary = "票据回调", tags = {"微信开放平台"})
    @RequestMapping(path = "ticket", method = RequestMethod.POST)
    public String ticket(
            @RequestBody(required = false) String requestBody, @RequestParam("timestamp") String timestamp,
            @RequestParam("nonce") String nonce, @RequestParam("signature") String signature,
            @RequestParam(name = "encrypt_type", required = false) String encType,
            @RequestParam(name = "msg_signature", required = false) String msgSignature
    ) {
        log.info("got wechatopen ticket：[signature=[{}], encType=[{}], msgSignature=[{}]," + " timestamp=[{}], nonce=[{}]",
                signature, encType, msgSignature, timestamp, nonce);
        return connectorService.handleTicket(requestBody, signature, encType, msgSignature, timestamp, nonce);
    }

    @Operation(description = "[系统集成] event callback", summary = "事件回调", tags = {"微信开放平台"})
    @RequestMapping(path = "callback/{appId}", method = RequestMethod.POST)
    public String callback(
            @PathVariable String appId,
            CallbackRequestDto callbackDto,
            @RequestBody(required = false) String requestBody) {

        return connectorService.handleCallback(appId, callbackDto, requestBody);
    }
}