package cn.hanyi.ctm.controller.customer;

import cn.hanyi.ctm.dto.CustomerStatDto;
import cn.hanyi.ctm.dto.customer.*;
import cn.hanyi.ctm.dto.journey.IndicatorCustomerDataResponseDto;
import cn.hanyi.ctm.dto.journey.JourneyTreeDto;
import cn.hanyi.ctm.entity.*;
import cn.hanyi.ctm.entity.journey.JourneyMap;
import cn.hanyi.ctm.repository.CustomerAnswersRepository;
import cn.hanyi.ctm.repository.CustomerHistoryRecordsRepository;
import cn.hanyi.ctm.repository.CustomerJourneyRecordsRepository;
import cn.hanyi.ctm.repository.CustomerRepository;
import cn.hanyi.ctm.sendmanage.SendMangeJourneyHelper;
import cn.hanyi.ctm.service.*;
import cn.hanyi.ctm.service.journey.elements.scene.JourneyScenePublishService;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.dto.ResourceListResponseDto;
import org.befun.core.dto.ResourcePageResponseDto;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.ResourceEmbeddedMany;
import org.befun.core.rest.annotation.ResourceMethodDto;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.Set;

@Validated
@Tag(name = "客户中心")
@RestController
@RequestMapping("/customers")
@PreAuthorize("isAuthenticated()")
@ResourceController(
        entityClass = Customer.class,
        repositoryClass = CustomerRepository.class,
        serviceClass = CustomerService.class,
        excludeActions = {ResourceMethod.COUNT, ResourceMethod.FIND_ALL, ResourceMethod.CREATE, ResourceMethod.UPDATE_ONE, ResourceMethod.DELETE_ONE, ResourceMethod.FIND_ONE},
        permission = "isAuthenticated()",
        methodDtoClass = {
                @ResourceMethodDto(method = ResourceMethod.CREATE, dtoClass = CustomerSaveDto.class, valid = true),
                @ResourceMethodDto(method = ResourceMethod.UPDATE_ONE, dtoClass = CustomerSaveDto.class, valid = true),
        },
        docTag = "客户中心",
        docCrud = "客户"
)
@ResourceEmbeddedMany(
        path = "answers",
        fieldNameInRoot = "answers",
        entityClass = CustomerAnswers.class,
        repositoryClass = CustomerAnswersRepository.class,
        docTag = "客户中心-历史填答",
        docCrud = "历史填答"
)
@ResourceEmbeddedMany(
        path = "journey-records",
        fieldNameInRoot = "journeyRecords",
        entityClass = CustomerJourneyRecord.class,
        repositoryClass = CustomerJourneyRecordsRepository.class,
        docTag = "客户中心-历程",
        docCrud = "历程"
)
@ResourceEmbeddedMany(
        path = "history-records",
        fieldNameInRoot = "historyRecords",
        entityClass = CustomerHistoryRecord.class,
        repositoryClass = CustomerHistoryRecordsRepository.class,
        docTag = "客户中心-历史记录",
        docCrud = "历史记录"
)
public class CustomerController extends BaseController<CustomerService> {

    @Autowired
    private CustomerService customerService;
    @Autowired
    private JourneyScenePublishService journeyScenePublishService;
    @Autowired
    private IndicatorCustomerService indicatorCustomerService;
    @Autowired
    private CustomerStatService customerStatService;
    @Autowired
    private CustomerGroupService customerGroupService;
    @Autowired
    private SendMangeJourneyHelper sendMangeJourneyHelper;
    @Autowired
    private CustomerExperienceIndicatorService customerExperienceIndicatorService;


    @GetMapping("/{id}")
    @Operation(summary = "单个客户")
    @JsonView(ResourceViews.Detail.class)
    public ResourceResponseDto<CustomerDto> findOne(@PathVariable long id) {
        service.resetCurrentDepartmentIds();
        return new ResourceResponseDto<>(service.findOne(id));
    }

    @PostMapping("")
    @Operation(summary = "新增客户")
    @JsonView(ResourceViews.Detail.class)
    public ResourceResponseDto<CustomerDto> create(@RequestBody @Valid CustomerSaveDto data) {
        return new ResourceResponseDto<>(service.create(data));
    }

    @PutMapping("/{id}")
    @Operation(summary = "修改客户")
    @JsonView(ResourceViews.Detail.class)
    public ResourceResponseDto<CustomerDto> updateOne(@PathVariable long id, @RequestBody @Valid CustomerSaveDto data) {
        service.resetCurrentDepartmentIds();
        return new ResourceResponseDto<>(service.updateOne(id, data));
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除客户")
    @JsonView(ResourceViews.Detail.class)
    public ResourceResponseDto<Boolean> deleteOne(@PathVariable long id) {
        service.resetCurrentDepartmentIds();
        return new ResourceResponseDto<>(service.deleteOne(id));
    }

    @PostMapping("combineSearch")
    @Operation(summary = "查询客户（v1.8.4）")
    public ResourcePageResponseDto<CustomerDto> queryCustomers(@Valid @RequestBody CustomerSearchDto dto) {
        return new ResourcePageResponseDto<>(service.combineSearch(dto));
    }

    @GetMapping("departments-and-groups")
    @Operation(summary = "客户中心-部门和组（v1.8.3）")
    public ResourceListResponseDto<CustomerDepartmentGroupDto> departmentsAndGroups(@Parameter(description = "联系人类型：ALL,WECHAT,MOBILE,EMAIL；默认是ALL") @RequestParam(required = false) String contactType) {
        boolean hasMobile = StringUtils.isNotEmpty(contactType) && contactType.equals("MOBILE");
        boolean hasWechat = StringUtils.isNotEmpty(contactType) && contactType.equals("WECHAT");
        boolean hasEmail = StringUtils.isNotEmpty(contactType) && contactType.equals("EMAIL");
        Long orgId = TenantContext.requireCurrentTenant();
        Long userId = TenantContext.requireCurrentUserId();
        customerService.triggerResetCustomerDepartmentTask(orgId, userId);
        return new ResourceListResponseDto<>(customerService.departmentsAndGroups(hasMobile, hasWechat, hasEmail));
    }

    @GetMapping("{id}/journey-maps")
    @Operation(summary = "客户参与的旅程列表")
    public ResourceListResponseDto<JourneyMap> journeyMaps(@PathVariable long id) {
        return new ResourceListResponseDto<>(indicatorCustomerService.getCustomerJourneyMap(id));
    }

    @GetMapping("{id}/journey-maps/{journeyMapId}/experienceIndicators")
    @Operation(summary = "客户体验指标的数据")
    public ResourceListResponseDto<IndicatorCustomerDataResponseDto> experienceIndicatorData(
            @PathVariable long id,
            @PathVariable long journeyMapId) {
        return new ResourceListResponseDto<>(indicatorCustomerService.data(id, journeyMapId));
    }

    @GetMapping("{id}/experience-indicators")
    @Operation(summary = "新版本-客户体验指标的数据")
    public ResourceListResponseDto<IndicatorCustomerResponseDto> experienceIndicatorData(@PathVariable long id) {
        return new ResourceListResponseDto<>(customerExperienceIndicatorService.experienceIndicatorScore(Set.of(id), 10, false));
    }

    @GetMapping("journey-tree")
    @Operation(summary = "客户旅程场景树")
    public ResourceListResponseDto<JourneyTreeDto> journeyTree() {
        return new ResourceListResponseDto<>(journeyScenePublishService.journeyTree());
    }

    @PostMapping("upload-file")
    @JsonView(ResourceViews.Detail.class)
    @Operation(summary = "批量上传客户")
    public ResourceResponseDto<UploadCustomerResponseDto> createByUpload(@RequestParam("file") MultipartFile multipartFile) {
        return new ResourceResponseDto<>(customerService.createByUpload(multipartFile, false));
    }

    @PostMapping("upload-file/preview")
    @JsonView(ResourceViews.Detail.class)
    @Operation(summary = "批量上传客户-预览")
    public ResourceResponseDto<UploadCustomerResponseDto> createByUploadPreview(@RequestParam("file") MultipartFile multipartFile) {
        return new ResourceResponseDto<>(customerService.createByUpload(multipartFile, true));
    }

    @GetMapping("{id}/stat")
    @Operation(summary = "客户数据统计")
    public ResourceResponseDto<CustomerStatDto> customerStat(@PathVariable long id) {
        return new ResourceResponseDto<>(customerStatService.customerStat(id));
    }

    @PutMapping("{id}/saveTags")
    @Operation(summary = "保存客户标签")
    public ResourceResponseDto<Boolean> saveTags(@PathVariable long id, @RequestBody CustomerTagSaveDto tags) {
        return new ResourceResponseDto<>(customerService.saveTags(id, tags.getTags()));
    }

    @PostMapping("batchUpdateGroup")
    @Operation(summary = "批量修改组")
    public ResourceResponseDto<Boolean> batchUpdateGroup(@Valid @RequestBody CustomerUpdateGroupDto dto) {
        service.resetCurrentDepartmentIds();
        return new ResourceResponseDto<>(customerService.updateCustomerGroups(dto));
    }

    @PostMapping("batchUpdateDepartment")
    @Operation(summary = "批量修改部门")
    public ResourceResponseDto<Boolean> batchUpdateDepartment(@Valid @RequestBody CustomerUpdateDepartmentDto dto) {
        service.resetCurrentDepartmentIds();
        return new ResourceResponseDto<>(customerService.batchUpdateDepartment(dto));
    }

    @PostMapping("batchUpdateBelongToUsers")
    @Operation(summary = "批量修改所属员工")
    public ResourceResponseDto<Boolean> batchUpdateBelongToUsers(@Valid @RequestBody CustomerUpdateBelongToUsersDto dto) {
        service.resetCurrentDepartmentIds();
        return new ResourceResponseDto<>(customerService.batchUpdateBelongToUsers(dto));
    }

    @DeleteMapping("batchDelete")
    @Operation(summary = "批量删除客户")
    public ResourceResponseDto<Boolean> batchDelete(@RequestBody CustomerSelectedDto dto) {
        service.resetCurrentDepartmentIds();
        return new ResourceResponseDto<>(customerService.deleteCustomers(dto));
    }

    @PostMapping("batchAddJourney")
    @Operation(summary = "批量添加客户历程")
    public ResourceResponseDto<Boolean> batchAddJourney(@Valid @RequestBody CustomerAddJourneyDto requestDto) {
        service.resetCurrentDepartmentIds();
        return new ResourceResponseDto<>(sendMangeJourneyHelper.addCustomerJourney(requestDto));
    }

    @PostMapping("batchUpdateInfo")
    @Operation(summary = "批量修改客户信息")
    public ResourceResponseDto<Boolean> batchUpdateInfo(@Valid @RequestBody CustomerUpdateBatchDto data) {
        service.resetCurrentDepartmentIds();
        return new ResourceResponseDto<>(customerService.batchUpdateInfo(data));
    }
}
