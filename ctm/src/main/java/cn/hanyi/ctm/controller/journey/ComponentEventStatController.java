package cn.hanyi.ctm.controller.journey;

import cn.hanyi.ctm.dto.journey.EventStatDataResponseDto;
import cn.hanyi.ctm.dto.journey.JourneyWarningDto;
import cn.hanyi.ctm.entity.journey.ElementEventStat;
import cn.hanyi.ctm.service.data.EventStatDataService;
import cn.hanyi.ctm.service.JourneyMapService;
import cn.hanyi.ctm.service.JourneyWarningService;
import cn.hanyi.ctm.service.journey.elements.JourneyComponentService;
import cn.hanyi.ctm.service.journey.elements.eventstat.JourneyEventStatService;
import cn.hanyi.ctm.service.journey.elements.JourneyComponentPublishService;
import cn.hanyi.ctm.service.journey.elements.eventstat.JourneyEventStatPublishService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.dto.ResourceListResponseDto;
import org.befun.core.dto.ResourceResponseDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotNull;

@Slf4j
@Tag(name = "客户旅程-组件-事件统计")
@RestController
@RequestMapping("/journey-maps/{journeyMapId}/components")
@PreAuthorize("isAuthenticated()")
public class ComponentEventStatController {

    @Autowired
    private JourneyEventStatService journeyEventStatService;
    @Autowired
    private JourneyEventStatPublishService journeyEventStatPublishService;
    @Autowired
    private JourneyMapService journeyMapService;
    @Autowired
    private JourneyComponentService journeyComponentService;
    @Autowired
    private JourneyComponentPublishService journeyComponentPublishService;
    @Autowired
    private JourneyWarningService journeyWarningService;
    @Autowired
    private EventStatDataService eventStatService;


    @PostMapping("{componentId}/eventStats/{eventStatId}/warning")
    @Operation(summary = "保存事件统计的预警")
    public ResourceResponseDto<JourneyWarningDto> saveWarning(
            @PathVariable Long journeyMapId,
            @PathVariable Long componentId,
            @PathVariable Long eventStatId,
            @RequestBody @NotNull JourneyWarningDto dto) {
        journeyMapService.checkIsCurrentOrg(journeyMapId);
        journeyComponentService.checkIsCurrentOrg(componentId);
        ElementEventStat entity = journeyEventStatService.checkIsCurrentOrg(eventStatId);
        return new ResourceResponseDto<>(journeyWarningService.saveEventStatWarning(journeyMapId, componentId, entity.getJourneyId(), eventStatId, dto));
    }

    @GetMapping("{componentId}/eventStats/all-data")
    @Operation(summary = "获得事件统计的数据-指定组件的所有数据")
    public ResourceListResponseDto<EventStatDataResponseDto> dataAll(
            @PathVariable Long journeyMapId,
            @PathVariable Long componentId,
//            @Parameter(name = "departmentId", description = "部门id") @RequestParam(required = false) Long departmentId,
//            @Parameter(name = "startDate", description = "开始日期") @RequestParam(required = false) String startDate,
//            @Parameter(name = "endDate", description = "结束日期") @RequestParam(required = false) String endDate,
            @Parameter(name = "publish", description = "是否已发布，默认未发布") @RequestParam(required = false) boolean publish) {
        journeyMapService.checkIsCurrentOrg(journeyMapId);
        if (publish) {
            journeyComponentPublishService.checkIsCurrentOrg(componentId);
        } else {
            journeyComponentService.checkIsCurrentOrg(componentId);
        }
        return new ResourceListResponseDto<>(eventStatService.dataList(journeyMapId, componentId, publish, null, null, null));
    }


    @GetMapping("{componentId}/eventStats/{eventStatId}/data")
    @Operation(summary = "获得事件统计的数据")
    public ResourceResponseDto<EventStatDataResponseDto> eventStatData(
            @PathVariable Long journeyMapId,
            @PathVariable Long componentId,
            @PathVariable Long eventStatId,
//            @Parameter(name = "departmentId", description = "部门id") @RequestParam(required = false) Long departmentId,
//            @Parameter(name = "startDate", description = "开始日期") @RequestParam(required = false) String startDate,
//            @Parameter(name = "endDate", description = "结束日期") @RequestParam(required = false) String endDate,
            @Parameter(name = "publish", description = "是否已发布，默认未发布") @RequestParam(required = false) boolean publish) {
        journeyMapService.checkIsCurrentOrg(journeyMapId);
        if (publish) {
            journeyComponentPublishService.checkIsCurrentOrg(componentId);
            journeyEventStatPublishService.checkIsCurrentOrg(eventStatId);
        } else {
            journeyComponentService.checkIsCurrentOrg(componentId);
            journeyEventStatService.checkIsCurrentOrg(eventStatId);
        }
        return new ResourceResponseDto<>(eventStatService.data(journeyMapId, componentId, eventStatId, publish, null, null, null));
    }
}
