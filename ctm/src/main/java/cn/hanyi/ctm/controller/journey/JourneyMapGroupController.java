package cn.hanyi.ctm.controller.journey;

import cn.hanyi.ctm.dto.journey.JourneyMapGroupEditDto;
import cn.hanyi.ctm.entity.journey.JourneyMapGroup;
import cn.hanyi.ctm.repository.JourneyMapGroupRepository;
import cn.hanyi.ctm.service.JourneyMapGroupService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.dto.resource.ResourceCollectionType;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.ResourceMethodDto;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static org.befun.core.rest.annotation.processor.ResourceMethod.*;

@Slf4j
@Tag(name = "客户旅程-文件夹")
@PreAuthorize("isAuthenticated()")
@RestController
@RequestMapping("/journey-map-groups")
@ResourceController(
        entityClass = JourneyMapGroup.class,
        repositoryClass = JourneyMapGroupRepository.class,
        serviceClass = JourneyMapGroupService.class,
        permission = "isAuthenticated()",
        collectionType = ResourceCollectionType.COLLECTION_NO_PAGE,
        excludeActions = {COUNT, BATCH_UPDATE, FIND_ONE},
        docTag = "客户旅程-文件夹",
        docCrud = "文件夹",
        methodDtoClass = {
                @ResourceMethodDto(method = CREATE, dtoClass = JourneyMapGroupEditDto.class, valid = true),
                @ResourceMethodDto(method = UPDATE_ONE, dtoClass = JourneyMapGroupEditDto.class, valid = true),
        }
)
public class JourneyMapGroupController {
}
