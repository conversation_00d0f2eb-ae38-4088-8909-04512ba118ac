package cn.hanyi.ctm.controller.event;

import cn.hanyi.ctm.dto.event.EventActionWebReplyDto;
import cn.hanyi.ctm.service.EventActionService;
import io.swagger.v3.oas.annotations.Operation;
import org.befun.core.dto.ResourceResponseDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("actions")
public class ActionController {
    @Autowired
    private EventActionService eventActionService;

    @GetMapping("/reply/{responseId}")
    @Operation(summary = "行动-网页回复")
    public ResourceResponseDto<EventActionWebReplyDto> getWebReplyAction(@PathVariable Long responseId) {
        return new ResourceResponseDto<>(eventActionService.getWebReplyAction(responseId));
    }
}
