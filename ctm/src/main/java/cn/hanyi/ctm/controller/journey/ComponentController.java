package cn.hanyi.ctm.controller.journey;

import cn.hanyi.ctm.dto.journey.*;
import cn.hanyi.ctm.entity.journey.JourneyComponentDto;
import cn.hanyi.ctm.service.JourneyMapService;
import cn.hanyi.ctm.service.journey.elements.JourneyComponentPublishService;
import cn.hanyi.ctm.service.journey.elements.JourneyComponentService;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Tag(name = "客户旅程-组件")
@RestController
@RequestMapping("/journey-maps/{journeyMapId}/components")
@PreAuthorize("isAuthenticated()")
public class ComponentController {

    @Autowired
    private JourneyMapService journeyMapService;
    @Autowired
    private JourneyComponentService journeyComponentService;
    @Autowired
    private JourneyComponentPublishService journeyComponentPublishService;

    @GetMapping
    @Operation(summary = "旅程组件列表")
    public ResourceResponseDto<JourneyMapResponseDto> findAll(@PathVariable long journeyMapId) {
        return new ResourceResponseDto<>(journeyMapService.components(journeyMapId));
    }

    @PostMapping
    @Operation(summary = "新增组件及默认元素")
    public ResourceResponseDto<JourneyComponentDto> create(
            @PathVariable long journeyMapId,
            @RequestBody JourneyComponentCreateDto data) {
        return new ResourceResponseDto<>(journeyComponentService.createJourneyComponent(journeyMapId, data));
    }

    @PostMapping("addPersonas")
    @Operation(summary = "批量新增画像子组件")
    public ResourceResponseDto<Boolean> createPersona(
            @PathVariable long journeyMapId,
            @RequestBody JourneyComponentCreatePersonaDto data) {
        return new ResourceResponseDto<>(journeyComponentService.createJourneyComponentPersona(journeyMapId, data));
    }

    @PostMapping("{componentId}/replacePersona")
    @Operation(summary = "替换画像子组件")
    public ResourceResponseDto<Boolean> replacePersona(
            @PathVariable long journeyMapId,
            @PathVariable long componentId,
            @RequestBody JourneyComponentReplacePersonaDto data) {
        return new ResourceResponseDto<>(journeyComponentService.replaceJourneyComponentPersona(journeyMapId, componentId, data));
    }

    @PostMapping("update-order")
    @Operation(summary = "更新组件顺序")
    public ResourceResponseDto<Boolean> updateComponentsOrder(
            @PathVariable long journeyMapId,
            @RequestBody List<JourneyOrderDto> journeyOrderDtoList) {
        return new ResourceResponseDto<>(journeyComponentService.updateComponentsOrder(journeyMapId, journeyOrderDtoList));
    }

    @PostMapping("/{componentId}/dateFilter")
    @Operation(summary = "修改旅程组件的时间条件")
    @JsonView(ResourceViews.Detail.class)
    public ResourceResponseDto<Boolean> updateJourneyComponentDateFilter(
            @PathVariable long journeyMapId,
            @PathVariable long componentId,
            @Valid @RequestBody JourneyComponentDateFilterDto data) {
        journeyMapService.checkMember(journeyMapId);
        return new ResourceResponseDto<>(journeyComponentPublishService.updateJourneyComponentDateFilter(journeyMapId, componentId, data.getDateFilter()));
    }
}
