package cn.hanyi.ctm.controller.customer;

import cn.hanyi.ctm.constant.connector.ConnectorType;
import cn.hanyi.ctm.dto.connector.ThirdPartyTemplateCreateDto;
import cn.hanyi.ctm.dto.connector.ThirdPartyTemplateSmsCreateDto;
import cn.hanyi.ctm.dto.connector.ThirdPartyTemplateSmsUpdateDto;
import cn.hanyi.ctm.dto.connector.ThirdPartyTemplateUpdateDto;
import cn.hanyi.ctm.entity.ThirdPartyTemplate;
import cn.hanyi.ctm.entity.ThirdPartyTemplateDto;
import cn.hanyi.ctm.properties.PlaceHolderProperties;
import cn.hanyi.ctm.repository.ThirdPartyTemplateRepository;
import cn.hanyi.ctm.service.ThirdPartyTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.dto.BaseResponseDto;
import org.befun.core.dto.ResourceListResponseDto;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.ResourceMethodDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static org.befun.core.rest.annotation.processor.ResourceMethod.*;

@Tag(name = "消息模板库")
@RestController
@RequestMapping("thirdparty-templates")
@ResourceController(
        entityClass = ThirdPartyTemplate.class,
        repositoryClass = ThirdPartyTemplateRepository.class,
        serviceClass = ThirdPartyTemplateService.class,
        permission = "isAuthenticated()",
        methodDtoClass = {
                @ResourceMethodDto(method = CREATE, dtoClass = ThirdPartyTemplateCreateDto.class, valid = true),
                @ResourceMethodDto(method = UPDATE_ONE, dtoClass = ThirdPartyTemplateUpdateDto.class, valid = true),
        },
        excludeActions = {COUNT, BATCH_UPDATE, FIND_ONE},
        docTag = "消息模板库",
        docCrud = "消息模板"
)
@Slf4j
@Validated
@PreAuthorize("isAuthenticated()")
public class ThirdPartyTemplateController {

    @Autowired
    private ThirdPartyTemplateService thirdPartyTemplateService;

    @Deprecated(since = "1.8.8")
    @PostMapping("create/sms")
    @Operation(summary = "手机短信-新增")
    public ResourceResponseDto<ThirdPartyTemplateDto> createSms(@Valid @RequestBody ThirdPartyTemplateSmsCreateDto params) {
        ThirdPartyTemplateCreateDto dto = new ThirdPartyTemplateCreateDto();
        dto.setName(params.getName());
        dto.setContent(params.getContent());
        dto.setConnectorType(ConnectorType.SMS);
        return new ResourceResponseDto<>(thirdPartyTemplateService.create(dto));
    }

    @Deprecated(since = "1.8.8")
    @PutMapping("update/sms")
    @Operation(summary = "手机短信-修改")
    public ResourceResponseDto<ThirdPartyTemplateDto> updateSms(@Valid @RequestBody ThirdPartyTemplateSmsUpdateDto params) {
        ThirdPartyTemplateUpdateDto dto = new ThirdPartyTemplateUpdateDto();
        dto.setName(params.getName());
        dto.setContent(params.getContent());
        return new ResourceResponseDto<>(thirdPartyTemplateService.updateOne(params.getId(), dto));
    }

    @PostMapping("/{id}/audit")
    @Operation(summary = "审核模板")
    public BaseResponseDto<Boolean> audit(@PathVariable long id) {
        return new BaseResponseDto<>(thirdPartyTemplateService.audit(id));
    }

    @GetMapping("placeholder")
    @Operation(summary = "获取占位符信息")
    public ResourceListResponseDto<PlaceHolderProperties.PlaceHolderConfig> placeholder() {
        return new ResourceListResponseDto<>(thirdPartyTemplateService.getPlaceHolders());
    }
}