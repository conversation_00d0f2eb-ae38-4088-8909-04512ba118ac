package cn.hanyi.ctm.controller;

import cn.hanyi.ctm.constant.OrganizationOptionalLimitType;
import cn.hanyi.ctm.dto.OrganizationLimitResponseDto;
import cn.hanyi.ctm.service.OptionalLimitService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.util.Pair;
import org.befun.core.dto.ResourceResponseDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "系统管理")
@RestController
@RequestMapping("/optional-limit")
@PreAuthorize("isAuthenticated()")
@Slf4j
public class OptionalLimitController {

    @Autowired
    private OptionalLimitService optionalLimitService;

    @Operation(summary = "额度校验")
    @GetMapping("/{type}")
    public ResourceResponseDto<OrganizationLimitResponseDto> check(@PathVariable OrganizationOptionalLimitType type) {
        Pair<Integer, Integer> size = optionalLimitService.optionalLimitCheck(type);
        return new ResourceResponseDto<>(new OrganizationLimitResponseDto(size.getFirst(), size.getSecond()));
    }
}