package cn.hanyi.ctm.controller.customer;

import cn.hanyi.ctm.dto.customer.CustomerIdsDto;
import cn.hanyi.ctm.dto.customer.group.CustomerGroupSaveDto;
import cn.hanyi.ctm.entity.CustomerGroup;
import cn.hanyi.ctm.repository.CustomerGroupRepository;
import cn.hanyi.ctm.service.CustomerGroupService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.dto.resource.ResourceCollectionType;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.ResourceMethodDto;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import static org.befun.core.rest.annotation.processor.ResourceMethod.*;

@Tag(name = "客户中心-分组")
@RestController
@Validated
@RequestMapping("/customer-groups")
@PreAuthorize("isAuthenticated()")
@ResourceController(
        entityClass = CustomerGroup.class,
        repositoryClass = CustomerGroupRepository.class,
        serviceClass = CustomerGroupService.class,
        excludeActions = {COUNT, BATCH_UPDATE, FIND_ONE},
        collectionType = ResourceCollectionType.COLLECTION_NO_PAGE,
        methodDtoClass = {
                @ResourceMethodDto(method = CREATE, dtoClass = CustomerGroupSaveDto.class, valid = true),
                @ResourceMethodDto(method = UPDATE_ONE, dtoClass = CustomerGroupSaveDto.class, valid = true),
        },
        permission = "isAuthenticated()",
        docTag = "客户中心-分组",
        docCrud = "分组"
)
public class CustomerGroupController extends BaseController<CustomerGroupService> {

    @PostMapping("{groupId}/add-customers")
    @Operation(summary = "组添加客户")
    public ResourceResponseDto<Boolean> addCustomers(
            @PathVariable("groupId") long groupId,
            @Valid @RequestBody CustomerIdsDto data) {

        return new ResourceResponseDto<>(service.addGroupCustomers(groupId, data.getCustomerIds()));
    }

    @PostMapping("{groupId}/remove-customers")
    @Operation(summary = "组移除客户")
    public ResourceResponseDto<Boolean> removeCustomers(
            @PathVariable("groupId") long groupId,
            @Valid @RequestBody CustomerIdsDto data) {

        return new ResourceResponseDto<>(service.removeGroupCustomers(groupId, data.getCustomerIds()));
    }

    @PostMapping("{groupId}/clear-customers")
    @Operation(summary = "清空组客户")
    public ResourceResponseDto<Boolean> removeCustomers(@PathVariable("groupId") long groupId) {
        return new ResourceResponseDto<>(service.clearGroupCustomers(groupId));
    }
}
