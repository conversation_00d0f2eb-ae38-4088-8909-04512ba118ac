package cn.hanyi.ctm.controller.customer;

import cn.hanyi.ctm.entity.CustomerExperienceIndicator;
import cn.hanyi.ctm.entity.CustomerExperienceIndicatorDto;
import cn.hanyi.ctm.repository.CustomerExperienceIndicatorRepository;
import cn.hanyi.ctm.service.CustomerExperienceIndicatorService;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.ResourceListResponseDto;
import org.befun.core.dto.resource.ResourceCollectionType;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

import static org.befun.core.rest.annotation.processor.ResourceMethod.COUNT;

@Tag(name = "客户中心-体验指标")
@RestController
@Validated
@RequestMapping("/customer-experience-indicator")
@PreAuthorize("isAuthenticated()")
@ResourceController(
        entityClass = CustomerExperienceIndicator.class,
        repositoryClass = CustomerExperienceIndicatorRepository.class,
        serviceClass = CustomerExperienceIndicatorService.class,
        excludeActions = {COUNT},
        collectionType = ResourceCollectionType.COLLECTION_NO_PAGE,
        permission = "isAuthenticated()",
        docTag = "客户中心-体验指标",
        docCrud = "体验指标"
)
public class CustomerExperienceIndicatorController extends BaseController<CustomerExperienceIndicatorService> {

    @PostMapping({"/batch-create"})
    @Operation(
            summary = "批量创建体验指标"
    )
    @Tag(
            name = "客户中心-体验指标"
    )
    @JsonView({ResourceViews.Basic.class})
    public ResourceListResponseDto<CustomerExperienceIndicatorDto> batchUpdate(@RequestBody List<CustomerExperienceIndicatorDto> batchs) {
        List<CustomerExperienceIndicatorDto> customerExperienceIndicatorDtos = new ArrayList<>();
        batchs.forEach(batch -> {
            service.create(batch);
            customerExperienceIndicatorDtos.add(batch);
        });
        return new ResourceListResponseDto<>(customerExperienceIndicatorDtos);
    }
}
