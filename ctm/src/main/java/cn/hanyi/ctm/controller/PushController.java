package cn.hanyi.ctm.controller;

import cn.hanyi.ctm.entity.Push;
import cn.hanyi.ctm.repository.PushRepository;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.exception.BadRequestException;
import cn.hanyi.ctm.dto.MassPushRequestDto;
import cn.hanyi.ctm.dto.PushResponseDto;
import cn.hanyi.ctm.service.PushService;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.rest.annotation.ResourceCollectionAction;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.ResourcePermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@Tag(name = "推送记录")
@RestController
@RequestMapping("/pushes")
@PreAuthorize("isAuthenticated()")
@Slf4j
@ResourceController(
        entityClass = Push.class,
        repositoryClass = PushRepository.class,
        serviceClass = PushService.class,
        permissions = {
                @ResourcePermission(action = "findAll", permission = "isAuthenticated()"),
                @ResourcePermission(action = "updateOne", permission = "isAuthenticated()"),
                @ResourcePermission(action = "create", permission = "isAuthenticated()"),
                @ResourcePermission(action = "deleteOne", permission = "isAuthenticated()")
        },
        docTag = "推送记录",
        docCrud = "推送"
)
public class PushController {

    @Autowired
    PushService pushService;

    @ResourceCollectionAction(
            action = "send",
            path = "send",
            method = RequestMethod.POST,
            description = "发送短信"
    )
    public ResourceResponseDto<Boolean> send(@RequestBody MassPushRequestDto requestDto) {
        if (requestDto.getTemplateId() == null) {
            throw new BadRequestException();
        }
        return new ResourceResponseDto<>(pushService.sendMessageAsync(requestDto));

    }

    @ResourceCollectionAction(
            action = "evaluate",
            path = "evaluate",
            method = RequestMethod.POST,
            description = "评估信息费用，发送统计"
    )
    public ResourceResponseDto<PushResponseDto> evaluate(@RequestBody MassPushRequestDto requestDto) {
        if (requestDto.getTemplateId() == null) {
            throw new BadRequestException();
        }
        return new ResourceResponseDto<>(pushService.evaluate(requestDto));

    }
}