package cn.hanyi.ctm.controller.persona;

import cn.hanyi.ctm.dto.persona.CustomerPersonaGroupAddDto;
import cn.hanyi.ctm.dto.persona.CustomerPersonaGroupUpdateDto;
import cn.hanyi.ctm.entity.persona.CustomerPersonaGroup;
import cn.hanyi.ctm.repository.CustomerPersonGroupRepository;
import cn.hanyi.ctm.service.persona.CustomerPersonaGroupService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.dto.resource.ResourceCollectionType;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.ResourceMethodDto;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static org.befun.core.rest.annotation.processor.ResourceMethod.*;

@Slf4j
@Tag(name = "客户画像-文件夹")
@PreAuthorize("isAuthenticated()")
@RestController
@RequestMapping("/persona-groups")
@ResourceController(
        entityClass = CustomerPersonaGroup.class,
        repositoryClass = CustomerPersonGroupRepository.class,
        serviceClass = CustomerPersonaGroupService.class,
        permission = "isAuthenticated()",
        collectionType = ResourceCollectionType.COLLECTION_NO_PAGE,
        excludeActions = {COUNT, BATCH_UPDATE, FIND_ONE},
        docTag = "客户画像-文件夹",
        docCrud = "文件夹",
        methodDtoClass = {
                @ResourceMethodDto(method = CREATE, dtoClass = CustomerPersonaGroupAddDto.class, valid = true),
                @ResourceMethodDto(method = UPDATE_ONE, dtoClass = CustomerPersonaGroupUpdateDto.class, valid = true),
        }
)
public class CustomerPersonaGroupController {
}
