package cn.hanyi.ctm;

import org.befun.core.repository.impl.BaseRepositoryImpl;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

@Configuration
@EntityScan({
        "cn.hanyi.survey.core.entity",
})
@EnableJpaRepositories(
        basePackages = {
                "cn.hanyi.survey.core.repository",
        },
        repositoryBaseClass = BaseRepositoryImpl.class)
public class SurveyCoreConfig {
}
