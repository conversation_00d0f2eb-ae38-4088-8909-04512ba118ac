package cn.hanyi.ctm;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.filter.Filter;
import ch.qos.logback.core.spi.FilterReply;
import org.apache.commons.lang3.StringUtils;

public class Logback<PERSON>ilter extends Filter<ILoggingEvent> {
    @Override
    public FilterReply decide(ILoggingEvent event) {
        if (event.getLevel() != null
                && event.getLevel() == Level.DEBUG
                && StringUtils.isNotEmpty(event.getLoggerName())
                && event.getLoggerName().equals("org.hibernate.SQL")
                && StringUtils.isNotEmpty(event.getFormattedMessage())
                && event.getFormattedMessage().contains("from inbox_message")) {
            return FilterReply.DENY;
        }
        return FilterReply.NEUTRAL;
    }
}
