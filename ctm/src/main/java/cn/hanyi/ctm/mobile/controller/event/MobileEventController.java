package cn.hanyi.ctm.mobile.controller.event;

import cn.hanyi.ctm.entity.Event;
import cn.hanyi.ctm.entity.EventAction;
import cn.hanyi.ctm.repository.EventActionRepository;
import cn.hanyi.ctm.repository.EventRepository;
import cn.hanyi.ctm.service.EventService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.rest.BaseController;
import org.befun.core.rest.annotation.ResourceController;
import org.befun.core.rest.annotation.ResourceEmbeddedMany;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static org.befun.core.rest.annotation.processor.ResourceMethod.*;

@Slf4j
@Tag(name = "移动端-事件中心")
@PreAuthorize("isAuthenticated()")
@RestController
@RequestMapping("/m/events")
@ResourceController(
        entityClass = Event.class,
        repositoryClass = EventRepository.class,
        serviceClass = EventService.class,
        permission = "isAuthenticated()",
        excludeActions = {CREATE, UPDATE_ONE, DELETE_ONE, BATCH_UPDATE, COUNT},
        docTag = "事件中心",
        docCrud = "事件"
)
@ResourceEmbeddedMany(
        path = "actions",
        fieldNameInRoot = "actions",
        entityClass = EventAction.class,
        repositoryClass = EventActionRepository.class,
        excludeActions = {CREATE, UPDATE_ONE, COUNT, FIND_ONE, BATCH_UPDATE, DELETE_ONE},
        docTag = "事件中心-行动",
        docCrud = "行动"
)
public class MobileEventController extends BaseController<EventService> {
}