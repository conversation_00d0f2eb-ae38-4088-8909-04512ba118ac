package cn.hanyi.ctm.mobile.controller.event;

import cn.hanyi.ctm.constant.EventOpenResourceType;
import cn.hanyi.ctm.dto.event.EventOpenResourceCreateRequestDto;
import cn.hanyi.ctm.dto.event.EventOpenResourceInfoDto;
import cn.hanyi.ctm.dto.event.EventOpenResourceParam;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.dto.OpenResourceInfo;
import org.befun.auth.service.OpenResourceService;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

@Slf4j
@Tag(name = "移动端-事件中心-分享")
@RestController
@RequestMapping("/m/events/share")
public class MobileEventShareController {

    @Autowired
    private OpenResourceService openResourceService;

    @PostMapping("/create")
    @PreAuthorize("isAuthenticated()")
    @Operation(summary = "创建分享链接")
    @JsonView(ResourceViews.Basic.class)
    public ResourceResponseDto<OpenResourceInfo> createShare(@RequestBody EventOpenResourceCreateRequestDto params) {
        return new ResourceResponseDto<>(openResourceService.create(
                EventOpenResourceType.event,
                params.getPassword(),
                params.parseExpireTime(),
                new EventOpenResourceParam(params.getEventId())
        ));
    }

    @GetMapping("/get")
    @Operation(summary = "查询分享内容")
    @JsonView(ResourceViews.Basic.class)
    public ResourceResponseDto<EventOpenResourceInfoDto> getShare(@RequestParam String token) {
        return new ResourceResponseDto<>(openResourceService.get(token));
    }

}
