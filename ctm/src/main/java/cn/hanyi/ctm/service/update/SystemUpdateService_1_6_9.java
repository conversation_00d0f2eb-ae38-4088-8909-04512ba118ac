package cn.hanyi.ctm.service.update;

import lombok.extern.slf4j.Slf4j;
import org.befun.auth.service.RoleService;
import org.befun.extension.service.AbstractSystemUpdateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.function.Consumer;

@Slf4j
@Service
public class SystemUpdateService_1_6_9 extends AbstractSystemUpdateService {

    @Autowired
    private RoleService roleService;

    @Override
    public String getSecret() {
        return "2b734061-869b-2846-44a3-110df2c1066c";
    }

    @Override
    public Map<String, Consumer<Map<String, String>>> getUpdateTasks() {
        return Map.of(
                "appendMemberRole", appendMemberRole()
        );
    }

    public Consumer<Map<String, String>> appendMemberRole() {
        return data -> {
        };
    }

}
