package cn.hanyi.ctm.service.update;

import cn.hanyi.ctm.constant.InteractionCollectorType;
import cn.hanyi.ctm.constant.connector.ConnectorAuthorizeStatus;
import cn.hanyi.ctm.constant.connector.ConnectorProviderType;
import cn.hanyi.ctm.constant.connector.ConnectorType;
import cn.hanyi.ctm.dto.journey.PushChannelConfigDto;
import cn.hanyi.ctm.entity.Connector;
import cn.hanyi.ctm.entity.Template;
import cn.hanyi.ctm.entity.journey.ExperienceInteraction;
import cn.hanyi.ctm.entity.journey.ExperienceInteractionPublish;
import cn.hanyi.ctm.repository.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.auth.constant.ThirdPartyAuthType;
import org.befun.auth.entity.ThirdPartyAuth;
import org.befun.auth.repository.ThirdPartyAuthRepository;
import org.befun.auth.service.ThirdPartyAuthService;
import org.befun.auth.service.auth.AuthWechatOpenService;
import org.befun.auth.service.auth.config.WechatOpenConfig;
import org.befun.extension.service.AbstractSystemUpdateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.stream.IntStream;

@Slf4j
@Service
public class SystemUpdateService_1_9_5 extends AbstractSystemUpdateService {

    @Autowired
    private ConnectorRepository connectorRepository;
    @Autowired
    private ThirdPartyAuthRepository thirdPartyAuthRepository;
    @Autowired
    private AuthWechatOpenService authWechatOpenService;
    @Autowired
    private ThirdPartyAuthService thirdPartyAuthService;
    @Autowired
    private ThirdPartyTemplateRepository thirdPartyTemplateRepository;
    @Autowired
    private ThirdPartyCustomerRepository thirdPartyCustomerRepository;
    @Autowired
    private TemplateRepository templateRepository;
    @Autowired
    private ExperienceInteractionRepository experienceInteractionRepository;
    @Autowired
    private ExperienceInteractionPublishRepository experienceInteractionPublishRepository;

    @Override
    public String getSecret() {
        return "fc60bc59-9e26-4c6a-a228-f9f926c51cf3";
    }

    @Override
    public Map<String, Consumer<Map<String, String>>> getUpdateTasks() {
        // 迁移 wechat connector 到 thirdparty auth
        // 旅程互动配置
        return Map.of(
                "transferConnector", transferConnector(),
                "updateInteractionConfig", updateInteractionConfig()
        );
    }

    public Consumer<Map<String, String>> transferConnector() {
        return data -> {
            // 查询所有wechat connector
            List<Connector> list = connectorRepository.findAll();
            if (CollectionUtils.isNotEmpty(list)) {
                list.forEach(connector -> {
                    if (!connector.getIsDelete()
                            && connector.getAuthorizeStatus() == ConnectorAuthorizeStatus.AUTHORIZED
                            && connector.getType() == ConnectorType.PLATFORM && connector.getProviderType() == ConnectorProviderType.WECHATOPEN) {
                        Long orgId = connector.getOrgId();
                        String appId = connector.getAppId();
                        if (CollectionUtils.isEmpty(thirdPartyAuthService.getListByOrg(orgId, ThirdPartyAuthType.WECHAT_OPEN, "cem"))) {
                            WechatOpenConfig config = new WechatOpenConfig();
                            config.setAppId(appId);
                            config.setAuthorized(true);
                            config.setLogo(connector.getLogo());
                            config.setName(connector.getName());
                            config.setDescription(connector.getDescription());
                            ThirdPartyAuth entity = authWechatOpenService.add(orgId, "cem", config, null);
                            // 写入所有的 thirdpartyTemplate thirdPartyAuthId
                            thirdPartyTemplateRepository.updateThirdpartyAuthIdByConnector(entity.getId(), connector);
                            // 写入所有的 thirdpartyCustomer thirdPartyAuthId
                            thirdPartyCustomerRepository.updateThirdpartyAuthIdByConnector(entity.getId(), connector);
                        }
                    }
                });
            }
        };
    }

    public Consumer<Map<String, String>> updateInteractionConfig() {
        return data -> {
            List<ExperienceInteraction> list = experienceInteractionRepository.findAll();
            if (CollectionUtils.isNotEmpty(list)) {
                list.forEach(interaction -> {
                    if (interaction.getConfig() == null) {
                        List<PushChannelConfigDto> configs = updateInteractionConfig(interaction.getInteractionsCollectors(), interaction.getCtmTemplateId());
                        interaction.setConfig(configs);
                        experienceInteractionRepository.save(interaction);
                    }
                });
            }
            List<ExperienceInteractionPublish> list2 = experienceInteractionPublishRepository.findAll();
            if (CollectionUtils.isNotEmpty(list2)) {
                list2.forEach(interaction -> {
                    if (interaction.getConfig() == null) {
                        List<PushChannelConfigDto> configs = updateInteractionConfig(interaction.getInteractionsCollectors(), interaction.getCtmTemplateId());
                        interaction.setConfig(configs);
                        experienceInteractionPublishRepository.save(interaction);
                    }
                });
            }
        };
    }

    private List<PushChannelConfigDto> updateInteractionConfig(List<Long> interactionsCollector, List<Long> ctmTemplateId) {
        List<PushChannelConfigDto> configs = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ctmTemplateId) &&
                CollectionUtils.isNotEmpty(interactionsCollector) &&
                ctmTemplateId.size() == interactionsCollector.size()) {
            IntStream.range(0, interactionsCollector.size()).forEach(i -> {
                InteractionCollectorType type = InteractionCollectorType.getEnumByValue(interactionsCollector.get(i));
                if (type != null) {
                    PushChannelConfigDto config = new PushChannelConfigDto();
                    config.setType(type);
                    if (type == InteractionCollectorType.WECHAT || type == InteractionCollectorType.SMS) {
                        Long templateId = ctmTemplateId.get(i);
                        if (templateId != null && templateId > 0) {
                            Template template = templateRepository.findById(templateId).orElse(null);
                            if (template != null && template.getThirdPartyTemplate() != null) {
                                config.setThirdpartyTemplateId(template.getThirdPartyTemplate().getId());
                                config.setContent(template.getContent());
                                configs.add(config);
                            }
                        }
                    } else if (type == InteractionCollectorType.APP) {
                        configs.add(config);
                    }
                }
            });
        }
        return configs;
    }

}
