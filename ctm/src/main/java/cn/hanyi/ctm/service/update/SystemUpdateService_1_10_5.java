package cn.hanyi.ctm.service.update;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.Cursor;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.Duration;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;

@Slf4j
@Service
public class SystemUpdateService_1_10_5 extends SystemUpdateServiceHelper {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public String getSecret() {
        return "75b6fadb-255e-4392-a659-0eb997070a1f";
    }

    @Override
    public Map<String, Consumer<Map<String, String>>> getUpdateTasks() {
        return Map.of(
                "expireCache", expireCache()
        );
    }

    public Consumer<Map<String, String>> expireCache() {
        return data -> {
            String keyMatch = data.get("match");
            Duration expireMin = Duration.parse(data.get("expireMin"));
            Duration expireMax = Duration.parse(data.get("expireMax"));
            expire(keyMatch, expireMin, expireMax);
        };
    }

    private Duration randomDuration(Duration min, Duration max) {
        long minSeconds = min.toSeconds();
        long maxSeconds = max.toSeconds();
        long randomSeconds = (long) (Math.random() * (maxSeconds - minSeconds)) + minSeconds;
        return Duration.ofSeconds(randomSeconds);
    }

    private void expire(String match, Duration expireMin, Duration expireMax) {
        AtomicInteger count = new AtomicInteger();
        stringRedisTemplate.execute((RedisCallback<Object>) coon -> {
            try (Cursor<byte[]> cursor = coon.scan(ScanOptions.scanOptions().match(match).count(1000).build());) {
                cursor.forEachRemaining(i -> {
                    String key = (String) stringRedisTemplate.getKeySerializer().deserialize(i);
                    if (StringUtils.isNotEmpty(key)) {
                        stringRedisTemplate.expire(key, randomDuration(expireMin, expireMax));
                    }
                    count.addAndGet(1);
                    if (count.get() % 1000 == 0) {
                        try {
                            Thread.sleep(200);
                        } catch (InterruptedException e) {
                            throw new RuntimeException(e);
                        }
                    }
                });
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
            return null;
        });
    }
}
