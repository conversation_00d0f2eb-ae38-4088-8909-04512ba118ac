package cn.hanyi.ctm.service.update;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.befun.auth.entity.Organization;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.repository.OrganizationRepository;
import org.befun.auth.service.OrganizationService;
import org.befun.auth.service.UserService;
import org.befun.extension.service.AbstractSystemUpdateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

@Slf4j
public abstract class SystemUpdateServiceHelper extends AbstractSystemUpdateService {

    @Autowired
    private UserService userService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private OrganizationRepository organizationRepository;

    /**
     * 遍历所有的企业，并调用 consumerOrg
     */
    protected final void consumerAllOrganization(Map<String, String> data, Consumer<Organization> consumerOrg) {
        boolean test = data != null && data.containsKey("test");
        boolean hasNext = true;
        int page = 0;
        int size = test ? 10 : 100;
        while (hasNext) {
            // 每次处理100条
            Page<Organization> list = organizationRepository.findAll(PageRequest.of(page, size));
            if (list.hasContent()) {
                list.get().forEach(org -> {
                    if (org != null && org.getId() != null && org.getId() > 0) {
                        consumerOrg.accept(org);
                    }
                });
                if (!test) {
                    page++;
                    continue;
                }
            }
            hasNext = false;
        }
    }

    protected final void getOrgAndOwnerUserAndApply(Long orgId, BiConsumer<Organization, SimpleUser> apply) {
        if (orgId != null && orgId > 0) {
            Organization organization = organizationService.get(orgId);
            if (organization != null && organization.getOwnerId() != null && organization.getOwnerId() > 0) {
                userService.getSimple(organization.getOwnerId()).ifPresent(user -> apply.accept(organization, user));
            }
        }
    }

    protected Integer parseInt(Map<String, String> data, String key) {
        String v = data.get(key);
        if (NumberUtils.isDigits(v)) {
            return Integer.parseInt(v);
        } else {
            return null;
        }
    }

    protected int parseInt(Map<String, String> data, String key, int defaultValue) {
        Integer v = parseInt(data, key);
        return v != null ? v : defaultValue;
    }

    protected Long parseLong(Map<String, String> data, String key) {
        String v = data.get(key);
        if (NumberUtils.isDigits(v)) {
            return Long.parseLong(v);
        } else {
            return null;
        }
    }
}
