package cn.hanyi.ctm.service.update;

import lombok.extern.slf4j.Slf4j;
import org.befun.auth.entity.Organization;
import org.befun.auth.entity.Permission;
import org.befun.auth.entity.Role;
import org.befun.auth.repository.OrganizationRepository;
import org.befun.auth.repository.PermissionRepository;
import org.befun.auth.repository.RoleRepository;
import org.befun.auth.service.DepartmentService;
import org.befun.extension.service.AbstractSystemUpdateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

@Slf4j
@Service
public class SystemUpdateService_1_7_5 extends AbstractSystemUpdateService {

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private PermissionRepository permissionRepository;

    @Autowired
    private OrganizationRepository organizationRepository;

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public String getSecret() {
        return "498b6ab7-e14b-4af3-9a9e-ee588f681951";
    }

    @Override
    public Map<String, Consumer<Map<String, String>>> getUpdateTasks() {
        return Map.of(
                "addEventThesaurus", addEventThesaurus(),
                "updateDepartmentParents", updateDepartmentParents()
        );
    }


    /**
     * 添加预警词库权限
     */
    public Consumer<Map<String, String>> addEventThesaurus() {
        List<String> action = List.of("/Events/WarningWord/view", "/Events/WarningWord/edit");

        return data -> {
            boolean test = data != null && data.containsKey("test");
            boolean hasNext = true;
            int page = 0;
            int size = test ? 10 : 100;
            while (hasNext) {
                // 每次处理100条
                Page<Role> list = roleRepository.findAll(
                        (Specification<Role>) (root, query, cb) -> cb.and(cb.equal(root.get("type"), 1)),
                        PageRequest.of(page, size));
                if (list.hasContent()) {
                    list.get().forEach(i -> {
                        boolean exist = permissionRepository.findByRoleId(i.getId())
                                .stream()
                                .anyMatch(p -> action.contains(p.getPermission()));
                        if (!exist) {
                            log.info("添加预警词库权限 RoleId：{}", i.getId());
                            permissionRepository.save(new Permission(i.getId(), "Action", action.get(0)));
                            permissionRepository.save(new Permission(i.getId(), "Action", action.get(1)));
                        }
                    });
                    if (!test) {
                        page++;
                        continue;
                    }
                }
                hasNext = false;
            }
        };
    }

    /**
     * 更新部门的上级信息
     */
    public Consumer<Map<String, String>> updateDepartmentParents() {
        return data -> {
            boolean test = data != null && data.containsKey("test");
            boolean hasNext = true;
            int page = 0;
            int size = test ? 10 : 100;
            while (hasNext) {
                // 每次处理100条
                Page<Organization> list = organizationRepository.findAll(PageRequest.of(page, size));
                if (list.hasContent()) {
                    list.get().forEach(i -> {
                        departmentService.updateParentList(i.getId());
                    });
                    if (!test) {
                        page++;
                        continue;
                    }
                }
                hasNext = false;
            }
        };
    }
}
