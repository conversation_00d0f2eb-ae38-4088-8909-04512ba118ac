package cn.hanyi.ctm.service.update;

import cn.hanyi.ctm.constant.SendManageRecordStatus;
import cn.hanyi.ctm.entity.SendManageRecord;
import cn.hanyi.ctm.repository.SendManageRecordRepository;
import cn.hanyi.survey.core.repository.SurveyResponseRepository;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Consumer;

@Slf4j
@Service
public class SystemUpdateService_1_10_7 extends SystemUpdateServiceHelper {

    private static final Logger log = LoggerFactory.getLogger(SystemUpdateService_1_10_7.class);
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private SendManageRecordRepository sendManageRecordRepository;

    @Autowired
    private SurveyResponseRepository surveyResponseRepository;

    @Override
    public String getSecret() {
        return "c2f56171-bb15-409d-acb8-a05d9edb9c4f";
    }

    @Override
    public Map<String, Consumer<Map<String, String>>> getUpdateTasks() {
        return Map.of(
                "trendData", trendData(),
                "cleanAttachmentSize", cleanAttachmentSize()
        );
    }

    public Consumer<Map<String, String>> cleanAttachmentSize() {
        String attachmentSizeKey = "survey:download:attachment:system-capacity";

        return data -> {
            stringRedisTemplate.delete(attachmentSizeKey);
        };

    }


    public Consumer<Map<String, String>> trendData() {
        return data -> {
            ExecutorService executorService = Executors.newFixedThreadPool(10);
            boolean hasNext = true;
            int page = 0;
            int size = 100;
            while (hasNext) {
                // 每次处理100条
                Page<SendManageRecord> list = sendManageRecordRepository.findAll((r, q, c) -> c.and(
                        c.equal(r.get("status"), SendManageRecordStatus.COMPLETE),
                        c.isNull(r.get("responseId"))
                ), PageRequest.of(page, size));
                log.info("trendData page: {}, size: {} total page: {}", page, list.getSize(), list.getTotalPages());
                if (list.hasContent()) {
                    executorService.execute(() -> {
                        list.get().forEach(record -> {
                            surveyResponseRepository.findOneBySurveyIdAndClientIdOrderByCreateTimeAsc(record.getSurveyId(), record.getClientId())
                                    .ifPresent(response -> {
                                        record.setResponseId(response.getId());
                                        sendManageRecordRepository.save(record);
                                    });
                        });
                    });
                    page++;
                    continue;
                }
                hasNext = false;
            }
        };

    }
}
