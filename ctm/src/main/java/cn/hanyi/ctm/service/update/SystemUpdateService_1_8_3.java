package cn.hanyi.ctm.service.update;

import cn.hanyi.ctm.constant.EventWarningType;
import cn.hanyi.ctm.dto.event.EventWarningDto;
import cn.hanyi.ctm.dto.event.EventWarningId;
import cn.hanyi.ctm.entity.Event;
import cn.hanyi.ctm.entity.EventRuleRelation;
import cn.hanyi.ctm.repository.EventRepository;
import cn.hanyi.ctm.repository.EventRuleRelationRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.service.AbstractSystemUpdateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.ScanOptions;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

@Slf4j
@Service
public class SystemUpdateService_1_8_3 extends AbstractSystemUpdateService {

    @Autowired
    private EventRepository eventRepository;

    @Autowired
    private EventRuleRelationRepository eventRuleRelationRepository;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public String getSecret() {
        return "75840b32-1063-4134-b5ab-8f6a485c91d4";
    }

    @Override
    public Map<String, Consumer<Map<String, String>>> getUpdateTasks() {
        return Map.of(
                "addEventRuleRelation", addEventRuleRelation(),
                "clearStatCache", clearStatCache()
        );
    }


    /**
     * 添加问卷发布权限
     */
    public Consumer<Map<String, String>> addEventRuleRelation() {
        return data -> {
            boolean test = data != null && data.containsKey("test");
            boolean hasNext = true;
            int page = 0;
            int size = test ? 10 : 100;
            while (hasNext) {
                // 每次处理100条
                int finalPage = page;
                Page<EventWarningId> list = eventRepository.queryEventWarnings(PageRequest.of(finalPage, size));
                if (list.hasContent()) {
                    List<EventRuleRelation> toSave = new ArrayList<>();
                    list.get().forEach(e->{
                        List<EventWarningDto> warnings = JsonHelper.toList(e.getWarnings(),EventWarningDto.class);
                        if (CollectionUtils.isNotEmpty(warnings)) {
                            warnings.forEach(w->{
                                Long rId = w.getResponseId();
                                if(!eventRuleRelationRepository.existsByResponseId(rId)){
                                    var er = new EventRuleRelation();
                                    var eId = e.getId();
                                    var lId= w.getRuleId();
                                    log.info("addEventRuleRelation: eId={},lId={},rId={}",eId,lId,rId);
                                    if(rId!=null && eId!=null && lId!=null && lId!=0){
                                        er.setEventId(eId);
                                        er.setRuleId(lId);
                                        er.setResponseId(rId);
                                        toSave.add(er);
                                    }
                                }
                            });
                        }
                    });
                    if(!toSave.isEmpty()){
                        eventRuleRelationRepository.saveAll(toSave);
                        eventRuleRelationRepository.flush();
                    }
                    if (!test) {
                        page++;
                        continue;
                    }
                }
                hasNext = false;
            }
            log.info("addEventRuleRelation: done");
        };
    }

    /**
     * 清除统计缓存
     */
    public Consumer<Map<String, String>> clearStatCache() {
        return data -> {
            stringRedisTemplate.delete("stat:all-keys");
            List<String> patterns = List.of(
                    "stat:event-rule:*",
                    "stat:indicator:*",
                    "stat:indicator-keys:*"
            );
            List<String> keys = new ArrayList<>();
            stringRedisTemplate.execute((RedisCallback<Object>) coon -> {
                patterns.forEach(pattern -> {
                    coon.scan(ScanOptions.scanOptions().match(pattern).build()).forEachRemaining(i -> {
                        String key = (String) stringRedisTemplate.getKeySerializer().deserialize(i);
                        if (StringUtils.isNotEmpty(key)) {
                            keys.add(key);
                        }
                        if (keys.size() >= 100) {
                            stringRedisTemplate.delete(keys);
                            log.info("已删除统计数据：{}", String.join(", ", keys));
                            keys.clear();
                        }
                    });
                });
                return null;
            });
            if (keys.size() > 0) {
                stringRedisTemplate.delete(keys);
                log.info("已删除统计数据：{}", String.join(", ", keys));
                keys.clear();
            }
        };
    }


}
