package cn.hanyi.ctm.service.update;

import cn.hanyi.ctm.repository.EventMonitorRulesRepository;
import cn.hanyi.ctm.repository.EventRepository;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.befun.auth.constant.ResourcePermissionType;
import org.befun.auth.entity.Department;
import org.befun.auth.entity.Organization;
import org.befun.auth.repository.OrganizationRepository;
import org.befun.auth.repository.UserRepository;
import org.befun.auth.service.DepartmentService;
import org.befun.auth.service.ResourcePermissionService;
import org.befun.core.service.ResourceCorporationService;
import org.befun.extension.entity.InboxMessage;
import org.befun.extension.repository.InboxMessageRepository;
import org.befun.extension.service.AbstractSystemUpdateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.befun.auth.constant.ResourcePermissionType.EVENT;

@Slf4j
@Service
public class SystemUpdateService_1_7_1 extends AbstractSystemUpdateService {

    @Autowired
    private OrganizationRepository organizationRepository;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private DepartmentService departmentService;
    @Autowired
    private EventMonitorRulesRepository eventMonitorRulesRepository;
    @Autowired
    private EventRepository eventRepository;
    @Autowired
    private ResourcePermissionService resourcePermissionService;
    @Autowired
    private InboxMessageRepository inboxMessageRepository;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private ResourceCorporationService resourceCorporationService;

    @Override
    public String getSecret() {
        return "460c67d9-0c28-6cdc-5e89-a600a7235a77";
    }

    @Override
    public Map<String, Consumer<Map<String, String>>> getUpdateTasks() {
        return Map.of(
                "addSuperAdminUserDepartment", addSuperAdminUserDepartment(),
                "addInboxMessageUserIdPermissions", addInboxMessageUserIdPermissions(),
                "addEventDepartment", addEventDepartment()
        );
    }

    /**
     * 补齐事件的部门，如果事件没有部门，则设置为根部门
     */
    public Consumer<Map<String, String>> addEventDepartment() {
        return data -> {
            boolean test = data != null && data.containsKey("test");
            boolean hasNext = true;
            int page = 0;
            int size = test ? 10 : 100;
            String sql = "select id, org_id orgId from event_result where department_id is null limit " + size;
            String update = "update event_result set department_id = %d where id = %d";
            Map<Long/*orgId*/, Long /*departmentId*/> map = new HashMap<>();
            while (hasNext) {
                // 每次处理100条
                String querySql = String.format(sql, page * size);
                log.info("addEventDepartment: querySql={}", querySql);
                List<IdOrgId> list = jdbcTemplate.query(querySql, new BeanPropertyRowMapper<>(IdOrgId.class));
                if (CollectionUtils.isNotEmpty(list)) {
                    list.forEach(i -> {
                        long departmentId = getRootDepartment(i.getOrgId(), map);
                        if (departmentId > 0) {
                            String updateSql = String.format(update, departmentId, i.getId());
                            log.info("addEventDepartment: updateSql={}", updateSql);
                            jdbcTemplate.update(updateSql);
                        }
                    });
                    if (!test) {
                        page++;
                        continue;
                    }
                } else {
                    log.info("addEventDepartment end, page = {}", page);
                }
                hasNext = false;
            }
        };
    }

    @Getter
    @Setter
    public static class IdOrgId {
        private Long id;
        private Long orgId;
    }


    private long getRootDepartment(Long orgId, Map<Long/*orgId*/, Long /*departmentId*/> map) {
        if (orgId == null) {
            return 0;
        }
        Long departmentId = map.get(orgId);
        if (departmentId == null) {
            Department department = departmentService.getRoot(orgId);
            if (department != null) {
                departmentId = department.getId();
                map.put(orgId, departmentId);
            }
        }
        return departmentId == null ? 0 : departmentId;
    }

    /**
     * 企业的超级管理员当前没有部门
     * 给所有的超级管理员加上根部门
     */
    public Consumer<Map<String, String>> addSuperAdminUserDepartment() {
        return data -> {
            boolean test = data != null && data.containsKey("test");
            boolean hasNext = true;
            int page = 0;
            int size = test ? 10 : 100;
            while (hasNext) {
                // 每次处理100条
                Page<Organization> list = organizationRepository.findAll(PageRequest.of(page, size));
                if (list.hasContent()) {
                    list.get().forEach(i -> {
                        Long userId = i.getOwnerId();
                        if (userId != null && userId > 0) {
                            userRepository.findById(userId).ifPresent(u -> {
                                if (u.getFormatDepartmentIds() == null) {
                                    Department root = departmentService.getRoot(i.getId());
                                    if (root != null) {
                                        u.appendDepartmentId(root.getId());
                                        userRepository.save(u);
                                    }
                                }
                            });
                        }
                    });
                    if (!test) {
                        page++;
                        continue;
                    }
                }
                hasNext = false;
            }
        };
    }

    /**
     * 事件列表根据userId展示
     * 需要添加站内信userId
     * 1、找到站内信的userId
     * 2、根据站内新的eventId
     * 3、写入预警事件里的userId
     *
     * @return
     */
    public Consumer<Map<String, String>> addInboxMessageUserIdPermissions() {
        return data -> {
            boolean test = data != null && data.containsKey("test");
            boolean hasNext = true;
            int page = 0;
            int size = test ? 10 : 100;
            while (hasNext) {
                Page<InboxMessage> list = inboxMessageRepository.findAll(PageRequest.of(page, size));
                if (list.hasContent()) {
                    list.get().forEach(i -> {
                        Long userId = i.getUserId();
                        Long eventId = regEventId(i.getTargetUrl());
                        if (eventId != null && eventId > 0) {
                            resourceCorporationService.shareToUser(eventId, EVENT.name(), null, i.getOrgId(), userId);
                        }
                    });
                    if (!test) {
                        page++;
                        continue;
                    }
                }
                hasNext = false;
            }
        };
    }

    public static Long regEventId(String url) {
        Pattern pattern = Pattern.compile(".*eventId=([0-9]+)");
        Matcher matcher = pattern.matcher(url);
        if (matcher.find()) {
            return Long.valueOf(matcher.group(1));
        }
        return null;
    }

}
