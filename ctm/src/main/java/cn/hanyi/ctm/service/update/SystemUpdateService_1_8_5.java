package cn.hanyi.ctm.service.update;

import lombok.extern.slf4j.Slf4j;
import org.befun.auth.pay.constant.OrderType;
import org.befun.auth.pay.dto.order.SmsOrderRequestDto;
import org.befun.auth.pay.service.OrganizationOrderService;
import org.befun.auth.pay.service.OrganizationRechargeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.function.Consumer;

@Slf4j
@Service
public class SystemUpdateService_1_8_5 extends SystemUpdateServiceHelper {

    @Autowired
    private OrganizationOrderService organizationOrderService;
    @Autowired
    private OrganizationRechargeService organizationRechargeService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public String getSecret() {
        return "7dec0f70-a878-4aa5-916b-73b3c5e45c74";
    }

    @Override
    public Map<String, Consumer<Map<String, String>>> getUpdateTasks() {
        return Map.of(
                "rechargeSms", rechargeSms(),
                "rechargeAmount", rechargeAmount(),
                "addLoginWhitelist", addLoginWhitelist(),
                "delLoginWhitelist", delLoginWhitelist()
        );
    }

    private Consumer<Map<String, String>> rechargeSms() {
        return data -> {
            Long orgId = parseLong(data, "orgId");
            Integer amount = parseInt(data, "amount");
            int price = parseInt(data, "price", 8);
            if (orgId != null && orgId > 0 && amount != null && amount > 0) {
                getOrgAndOwnerUserAndApply(orgId, (org, user) -> {
                    int rechargeAmount = amount * price;

                    organizationRechargeService.rechargeByPlatform(orgId, user.getId(), rechargeAmount, "账户充值-平台内部充值");

                    SmsOrderRequestDto smsOrderRequestDto = new SmsOrderRequestDto();
                    smsOrderRequestDto.setSms(amount);
                    smsOrderRequestDto.setCostAmount(rechargeAmount);
                    organizationOrderService.placeOrder(orgId, user.getId(), OrderType.order_sms, smsOrderRequestDto);
                });
            }
        };
    }

    private Consumer<Map<String, String>> rechargeAmount() {
        return data -> {
            Long orgId = parseLong(data, "orgId");
            Integer amount = parseInt(data, "amount");
            if (orgId != null && orgId > 0 && amount != null && amount > 0) {
                getOrgAndOwnerUserAndApply(orgId, (org, user) -> {
                    organizationRechargeService.rechargeByPlatform(orgId, user.getId(), amount, "账户充值-平台内部充值");
                });
            }
        };
    }

    private Consumer<Map<String, String>> addLoginWhitelist() {
        return data -> {
            String userId = data.get("userId");
            if (userId != null) {
                stringRedisTemplate.opsForSet().add("user.session.whitelist", userId);
            }
        };
    }

    private Consumer<Map<String, String>> delLoginWhitelist() {
        return data -> {
            String userId = data.get("userId");
            if (userId != null) {
                stringRedisTemplate.opsForSet().remove("user.session.whitelist", userId);
            }
        };
    }
}
