package cn.hanyi.ctm.service.update;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.function.Consumer;

@Slf4j
@Service
public class SystemUpdateService_1_7_0 extends SystemUpdateServiceHelper {

    @Override
    public String getSecret() {
        return "6d4ab3b4-cbbe-ee92-08be-008b2183973a";
    }

    @Override
    public Map<String, Consumer<Map<String, String>>> getUpdateTasks() {
        return Map.of(
                "initJourneyMapOwner", initJourneyMapOwner()
        );
    }

    public Consumer<Map<String, String>> initJourneyMapOwner() {
        return data -> {
            // 不需要了，1.7.8 版本，在查询旅程列表时，如果发现没有拥有者，则会重新赋值给超管
        };
    }
}
