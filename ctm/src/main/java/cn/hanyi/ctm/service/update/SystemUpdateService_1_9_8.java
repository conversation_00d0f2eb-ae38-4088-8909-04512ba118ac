package cn.hanyi.ctm.service.update;

import cn.hanyi.ctm.constant.*;
import cn.hanyi.ctm.constant.connector.ConnectorType;
import cn.hanyi.ctm.dto.SendChannelConfigDto;
import cn.hanyi.ctm.dto.journey.PushChannelConfigDto;
import cn.hanyi.ctm.entity.SendManage;
import cn.hanyi.ctm.repository.ConnectorRepository;
import cn.hanyi.ctm.repository.ExperienceInteractionPublishRepository;
import cn.hanyi.ctm.repository.SendManageRepository;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.entity.Menu;
import org.befun.auth.entity.Organization;
import org.befun.auth.entity.Permission;
import org.befun.auth.repository.MenuRepository;
import org.befun.auth.repository.OrganizationRepository;
import org.befun.auth.repository.PermissionRepository;
import org.befun.auth.repository.ThirdPartyAuthRepository;
import org.befun.auth.service.RoleService;
import org.befun.extension.service.AbstractSystemUpdateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SystemUpdateService_1_9_8 extends AbstractSystemUpdateService {

    @Autowired
    private PermissionRepository permissionRepository;

    @Autowired
    private OrganizationRepository organizationRepository;

    @Autowired
    private ConnectorRepository connectorRepository;

    @Autowired
    private SendManageRepository sendManageRepository;

    @Autowired
    private ThirdPartyAuthRepository thirdPartyAuthRepository;

    @Autowired
    private ExperienceInteractionPublishRepository experienceInteractionPublishRepository;

    @Autowired
    private RoleService roleService;
    @Autowired
    private MenuRepository menuRepository;
    @Autowired
    @Lazy
    private SystemUpdateService_1_9_8 self;

    @Override
    public String getSecret() {
        return "c77e47ed-2b17-4306-97d7-93084960eef7";
    }

    @Override
    public Map<String, Consumer<Map<String, String>>> getUpdateTasks() {
        return Map.of(
                "addSendManager", addSendManagerMenu(),
                "addSendManagerPermission", addSendManagerPermission(),
                "changeSurveyMenuName", changeSurveyMenuName(),
                "sendManageMigrate", sendManageMigrate()
        );
    }


    public Consumer<Map<String, String>> changeSurveyMenuName() {
        return data -> {
            String name = "数据收集";
            String path = "/TouchManager";

            menuRepository.findAll(Specification.where(
                    (root, query, criteriaBuilder) -> criteriaBuilder.and(
                            criteriaBuilder.like(root.get("fullPath"), path + "%"),
                            criteriaBuilder.equal(root.get("display"), 1))
            )).forEach(menu -> {
                if ("问卷管理".equals(menu.getFullName())) {
                    menu.setFullName(name);
                    menu.setName(name);
                } else if (menu.getFullName().startsWith("问卷管理")) {
                    menu.setFullName(menu.getFullName().replaceFirst("问卷管理", name));
                }
                menuRepository.save(menu);
            });
        };
    }

    public Consumer<Map<String, String>> addSendManagerMenu() {
        return data -> {
            String fullName = "收据收集/发送管理";
            String viewFullName = fullName + "/查看";
            String editFullName = fullName + "/编辑";

            String fullPath = "/TouchManager/SendManage";
            String viewFullPath = fullPath + "/view";
            String editFullPath = fullPath + "/edit";

            if(menuRepository.findAll(Specification.where(
                    (root, query, criteriaBuilder) -> criteriaBuilder.equal(root.get("fullPath"), fullPath)
            )).isEmpty()){
                Menu main = new Menu(15L, 2, "发送管理", fullName, "SendManage", fullPath, 1, 230);
                main.setId(75L);
                Menu view = new Menu(75L, 3, "查看", viewFullName, "view", viewFullPath, 1, 231);
                view.setId(76L);
                Menu edit = new Menu(75L, 3, "编辑", editFullName, "edit", editFullPath, 1, 232);
                edit.setId(77L);

                menuRepository.saveAll(List.of(main, view, edit));
                log.info("添加发送管理菜单完成");
            }
        };
    }

    public Consumer<Map<String, String>> addSendManagerPermission() {
        return data -> {
            boolean test = data != null && data.containsKey("test");
            boolean hasNext = true;
            int page = 0;
            int size = test ? 10 : 100;
            while (hasNext) {
                // 每次处理100条
                Page<Organization> list = organizationRepository.findAll(PageRequest.of(page, size));
                if (list.hasContent()) {
                    list.get().forEach(org -> {
                        log.info("添加发送管理权限，机构：{}", org.getId());
                        self.addPermission(org);
                    });
                    if (!test) {
                        page++;
                        continue;
                    }
                }
                hasNext = false;
            }
            log.info("添加发送管理权限完成");
        };
    }

    @Async
    public void addPermission(Organization org) {
        if (org == null || org.getId() == null || org.getId() <= 0) {
            return;
        }

        var role =  roleService.getSuperAdminByOrg(org.getId());

        if (role == null) {
            return;
        }

        String action1 = "/TouchManager/SendManage/view";
        String action2 = "/TouchManager/SendManage/edit";
        List<String> action = List.of(action1, action2);

        List<Permission> permissions = permissionRepository.findByRoleId(role.getId());

        if(permissions.stream().noneMatch(p -> action.contains(p.getPermission()))){

            permissionRepository.saveAll(
                    List.of(
                            new Permission(role.getId(), "Action", action1),
                            new Permission(role.getId(), "Action", action2)
                    )
            );
        }
    }


    public Consumer<Map<String, String>> sendManageMigrate() {
        return data -> {
            boolean test = data != null && data.containsKey("test");
            boolean hasNext = true;
            int page = 0;
            int size = test ? 10 : 100;
            while (hasNext) {
                // 每次处理100条
                Page<Organization> list = organizationRepository.findAll(
                        PageRequest.of(page, size));
                if (list.hasContent()) {
                    list.get().forEach(org -> {
                        log.info("sendManage迁移，机构：{}", org.getId());
                        self.migrateInteraction(org, experienceInteractionPublishRepository, sendManageRepository, connectorRepository);
                    });
                    if (!test) {
                        page++;
                        continue;
                    }
                }
                hasNext = false;
            }
            log.info("迁移完成");
        };
    }


    @Async
    private void migrateInteraction(Organization org, ExperienceInteractionPublishRepository eipr, SendManageRepository smr, ConnectorRepository cr) {

        eipr.findAll((r, q, c) -> c.equal(r.get("orgId"), org.getId())).forEach(publish -> {
            if (smr.findAll((r, q, c) -> c.equal(r.get("triggerId"), publish.getId())).isEmpty()) {
                log.info("迁移互动，Interaction：{}", publish.getId());
                SendManage sm = new SendManage();
                sm.setOrgId(org.getId());
                sm.setTitle(publish.getInteractionName());
                sm.setEnable(true);
                sm.setTriggerType(SendManageTriggerType.JOURNEY);
                sm.setTriggerId(publish.getJourneyId());
                sm.setSendSids(publish.getInteractionSids());
                sm.setSendMoment(publish.getInteractionMoment());
                sm.setSendType(SendType.MESSAGE);
                sm.setDisturbMoment(publish.getDisturbMoment());
                sm.setExpireMoment(publish.getExpireMoment());
                sm.setRepeatTimes(SendManageRepeatType.SINGLE);
                // 使用/分割
                sm.setSurveyTitles(publish.getSurveyTitles().stream().map(String::valueOf).collect(Collectors.joining("/")));
                List<PushChannelConfigDto> conf = publish.getConfig();
                if (conf != null) {
                    List<SendChannelConfigDto> sendChannelConfigDtos = conf.stream().map(c -> {
                        SendChannelConfigDto scc = new SendChannelConfigDto();
                        scc.setLevel(SendManageChannelLevel.LOW);
                        scc.setType(SendManageChannelType.valueOf(c.getType().name()));
                        scc.setThirdpartyTemplateId(c.getThirdpartyTemplateId());
                        scc.setWechatOpenTemplateId(c.getWechatOpenTemplateId());
                        scc.setContent(c.getContent());
                        if (InteractionCollectorType.APP.equals(c.getType())) {
                            cr.findOne((r, q, cb) -> cb.and(
                                    cb.equal(r.get("orgId"), org.getId()),
                                    cb.equal(r.get("type"), ConnectorType.API)
                            )).ifPresent(connector -> {
                                scc.setApiConnectorId(connector.getId());
                            });
                        }
                        return scc;
                    }).collect(Collectors.toList());
                    sm.setChannel(sendChannelConfigDtos);
                }
                sm.setEditorId(-1L);
                sm.setUserId(-1L);
                smr.save(sm);
            }
        });
    }

}
