package cn.hanyi.ctm.service.update;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.exception.BadRequestException;
import org.befun.core.utils.EnumHelper;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.constant.XPackAppType;
import org.befun.extension.dto.SmsTemplatePatternDto;
import org.befun.extension.entity.XpackConfig;
import org.befun.extension.repository.XpackConfigRepository;
import org.befun.extension.service.AbstractSystemUpdateService;
import org.befun.extension.service.XpackConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

@Slf4j
@Service
public class SystemUpdateService_1_9_4 extends AbstractSystemUpdateService {


    @Autowired
    private XpackConfigService xpackConfigService;
    @Autowired
    private XpackConfigRepository xpackConfigRepository;

    @Override
    public String getSecret() {
        return "e3ee21e3-8ff4-49fd-a88a-179843bc3571";
    }

    @Override
    public Map<String, Consumer<Map<String, String>>> getUpdateTasks() {
        return Map.of(
                "splitPlatformSmsTemplate", splitPlatformSmsTemplate()
        );
    }

    public Consumer<Map<String, String>> splitPlatformSmsTemplate() {
        return data -> {
            if (data == null || data.isEmpty()) {
                throw new BadRequestException("part: splitPlatformSmsTemplate 需要 smsType");
            }
            String smsType = data.get("smsType");
            if (StringUtils.isEmpty(smsType)) {
                throw new BadRequestException("part: addPlatformSmsTemplate 需要 smsType");
            }
            XPackAppType type = EnumHelper.parse(XPackAppType.values(), smsType, XPackAppType.SMS_TEMPLATE_CHUANGLAN);
            List<XpackConfig> list = xpackConfigService.getConfigsByType(type);
            if (CollectionUtils.isEmpty(list)) {
                return;
            }
            XpackConfig old = null;
            Map<String, XpackConfig> existMap = new HashMap<>();
            for (XpackConfig xpackConfig : list) {
                if (StringUtils.isEmpty(xpackConfig.getSubType())) {
                    if (old == null) {
                        old = xpackConfig;
                    }
                } else {
                    existMap.put(xpackConfig.getSubType(), xpackConfig);
                }
            }
            if (old == null) {
                return;
            }
            List<SmsTemplatePatternDto> templates = JsonHelper.toList(old.getConfig(), SmsTemplatePatternDto.class);
            if (templates == null) {
                return;
            }
            List<XpackConfig> add = new ArrayList<>();
            templates.forEach(i -> {
                if (!existMap.containsKey(i.getId())) {
                    XpackConfig entity = new XpackConfig();
                    entity.setType(type);
                    entity.setSubType(i.getId());
                    entity.setApp("cem");
                    entity.setEnabled(true);
                    entity.setConfig(JsonHelper.toJson(i));
                    add.add(entity);
                }
            });
            if (!add.isEmpty()) {
                xpackConfigRepository.saveAll(add);
            }
        };
    }
}
