package cn.hanyi.ctm.service.update;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.AppVersion;
import org.befun.auth.constant.AppVersionPermissions;
import org.befun.auth.constant.RoleType;
import org.befun.auth.dto.AppVersionDto;
import org.befun.auth.dto.OrganizationOptionalLimitDto;
import org.befun.auth.entity.*;
import org.befun.auth.repository.OrganizationRepository;
import org.befun.auth.repository.OrganizationWalletRepository;
import org.befun.auth.repository.PermissionRepository;
import org.befun.auth.service.DepartmentService;
import org.befun.auth.service.RoleService;
import org.befun.auth.service.UserRoleService;
import org.befun.auth.service.UserService;
import org.befun.auth.utils.PasswordHelper;
import org.befun.core.exception.BadRequestException;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.filter.OperateLogFilter;
import org.befun.extension.service.AbstractSystemUpdateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SystemUpdateService_1_8_6 extends AbstractSystemUpdateService {

    @Autowired
    private OrganizationRepository organizationRepository;
    @Autowired
    private OrganizationWalletRepository organizationWalletRepository;
    @Autowired
    private PermissionRepository permissionRepository;
    @Autowired
    private DepartmentService departmentService;
    @Autowired
    private UserService userService;
    @Autowired
    private RoleService roleService;
    @Autowired
    private UserRoleService userRoleService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public String getSecret() {
        return "f9f3de8b-2330-4e44-a188-df821faa1ea5";
    }

    @Override
    public Map<String, Consumer<Map<String, String>>> getUpdateTasks() {
        return Map.of(
                "initPrivatizationOrg", getInitPrivatizationOrgTask(),
                "clearOperateLogCache", getClearOperateLogCacheTask()
        );
    }

    public Consumer<Map<String, String>> getInitPrivatizationOrgTask() {
        return data -> {
            if (data == null || data.isEmpty()) {
                throw new BadRequestException("part: initOrg 需要 orgName, mobile, email, password, ");
            }
            String orgName = data.get("orgName");
            String password = data.get("password");
            String mobile = data.get("mobile");
            String email = data.get("email");
            if (StringUtils.isEmpty(orgName)
                    || StringUtils.isEmpty(mobile)
                    || StringUtils.isEmpty(email)
                    || StringUtils.isEmpty(password)) {
                throw new BadRequestException("part: initOrg 需要 orgName, mobile, email, password");
            }

            if (userService.existsByMobile(mobile)) {
                throw new BadRequestException("mobile 已存在");
            }
            if (userService.existsByEmail(email)) {
                throw new BadRequestException("email 已存在");
            }

            Date s = new Date();
            Date e = Date.from(LocalDateTime.now().plusYears(50).atZone(ZoneId.systemDefault()).toInstant());
            int created = (int) (System.currentTimeMillis() / 1000);
            String createdString = String.valueOf(created);

            // org wallet
            Organization org = new Organization();
            org.setId(1L);
            org.setName(orgName);
            org.setIndustryId(9L);
            org.setOwnerId(1L);
            org.setMaxUsers(Integer.MAX_VALUE);
            org.setIsBlock(0);
            org.setIsTemplate(0);
            org.setAvailableDateBegin(s);
            org.setAvailableDateEnd(e);
            org.setCreated(createdString);
            org.setUpdated(createdString);
            org.setOptionalLimit(JsonHelper.toJson(new OrganizationOptionalLimitDto(Integer.MAX_VALUE, Integer.MAX_VALUE, Integer.MAX_VALUE, Integer.MAX_VALUE, Integer.MAX_VALUE, Integer.MAX_VALUE, true, true, true, Integer.MAX_VALUE)));
            org.setVersion(JsonHelper.toJson(new AppVersionDto(AppVersion.PROFESSION.getText(), AppVersion.EMPTY.getText())));
            organizationRepository.save(org);
            OrganizationWallet wallet = new OrganizationWallet();
            wallet.setOrgId(1L);
            wallet.setMoney(0);
            wallet.setSms(Integer.MAX_VALUE);
            organizationWalletRepository.save(wallet);

            // department
            Department rootDepartment = new Department();
            rootDepartment.setId(1L);
            rootDepartment.setOrgId(1L);
            rootDepartment.setTitle(orgName);
            rootDepartment.setPid(0L);
            departmentService.save(rootDepartment);

            // user
            User user = new User();
            user.setId(1L);
            user.setOrgId(1L);
            user.appendDepartmentId(1L);
            user.setPassword(PasswordHelper.encrypt(password));
            user.setPasswordStrength(PasswordHelper.passwordStrength(password));
            user.setMobile(mobile);
            user.setEmail(email);
            user.setIsAdmin(true);
            user.setTruename("超级管理员");
            user.setNickname("超级管理员");
            user.setAvailableSystems("{\"login_surveyplus\":1,\"login_cem\":1}");
            user.setStatus(1);
            user.setIsFinishedGuide("N");
            user.setCreated(created);
            user.setUpdated(created);
            user.setIsDelete(0);
            userService.save(user);

            // role permission userRole
            // super admin
            Role superAdminRole = RoleType.SUPER_ADMIN.createRole(org.getId());
            superAdminRole.setId(1L);
            roleService.save(superAdminRole);
            List<Permission> adminPermissions = AppVersionPermissions.SUPER_ADMIN_PROFESSION.mapToString().stream().map((i) -> new Permission(1L, "Action", i)).collect(Collectors.toList());
            permissionRepository.saveAll(adminPermissions);
            userRoleService.addUserRole(user, superAdminRole);

            // member
            Role memberRole = RoleType.MEMBER.createRole(org.getId());
            memberRole.setId(2L);
            roleService.save(memberRole);
            List<Permission> memberPermissions = AppVersionPermissions.SUPER_ADMIN_PROFESSION.mapToString().stream().map((i) -> new Permission(2L, "Action", i)).collect(Collectors.toList());
            permissionRepository.saveAll(memberPermissions);

        };
    }

    public Consumer<Map<String, String>> getClearOperateLogCacheTask() {
        return data -> {
            stringRedisTemplate.delete(List.of(OperateLogFilter.KEY, OperateLogFilter.KEY_LOCK));
        };
    }

}
