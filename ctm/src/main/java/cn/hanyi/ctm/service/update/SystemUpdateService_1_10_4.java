package cn.hanyi.ctm.service.update;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.befun.auth.constant.AppVersion;
import org.befun.auth.entity.Permission;
import org.befun.auth.repository.PermissionRepository;
import org.befun.auth.service.OrganizationService;
import org.befun.auth.service.RoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

@Slf4j
@Service
public class SystemUpdateService_1_10_4 extends SystemUpdateServiceHelper {

    @Autowired
    private RoleService roleService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private PermissionRepository permissionRepository;

    @Override
    public String getSecret() {
        return "ea63d198-8b99-45b1-b182-fbcc595cbc46";
    }

    @Override
    public Map<String, Consumer<Map<String, String>>> getUpdateTasks() {
        return Map.of(
                "updateBiDataAnalysisPermission", updateBiDataAnalysisPermission()
        );
    }

    public Consumer<Map<String, String>> updateBiDataAnalysisPermission() {
        List<String> paths = List.of("/Bi/DataAnalysis/view", "/Bi/DataAnalysis/edit");
        return data -> consumerAllOrganization(data, org -> {
            AppVersion version = organizationService.parseOrgVersion2(org);
            if (version == AppVersion.UPDATE) {
                var role = roleService.getSuperAdminByOrg(org.getId());
                if (role != null) {
                    List<Permission> permissions = permissionRepository.findByRoleIdAndPermissionIn(role.getId(), paths);
                    if (CollectionUtils.isEmpty(permissions) || permissions.size() < 2) {
                        permissionRepository.saveAll(List.of(
                                new Permission(role.getId(), "Action", "/Bi/DataAnalysis/view"),
                                new Permission(role.getId(), "Action", "/Bi/DataAnalysis/edit")));
                    }
                }
            }
        });
    }


}
