package cn.hanyi.ctm.service.update;

import cn.hanyi.ctm.constant.connector.ConnectorProviderType;
import cn.hanyi.ctm.constant.connector.ConnectorType;
import cn.hanyi.ctm.entity.Connector;
import cn.hanyi.ctm.entity.ThirdPartyTemplate;
import cn.hanyi.ctm.repository.ThirdPartyTemplateRepository;
import cn.hanyi.ctm.service.ConnectorConsumerService;
import cn.hanyi.ctm.service.ConnectorService;
import cn.hanyi.ctm.service.EventRuleService;
import cn.hanyi.ctm.service.ThirdPartyTemplateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.befun.auth.entity.Organization;
import org.befun.auth.repository.OrganizationRepository;
import org.befun.core.exception.BadRequestException;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.constant.XPackAppType;
import org.befun.extension.dto.SmsTemplatePatternDto;
import org.befun.extension.entity.XpackConfig;
import org.befun.extension.property.SmsProperty;
import org.befun.extension.service.AbstractSystemUpdateService;
import org.befun.extension.service.XpackConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.function.Consumer;

@Slf4j
@Service
public class SystemUpdateService_1_8_9 extends AbstractSystemUpdateService {

    @Autowired
    private SmsProperty smsProperty;
    @Autowired
    private XpackConfigService xpackConfigService;
    @Autowired
    private ThirdPartyTemplateService thirdPartyTemplateService;
    @Autowired
    private ThirdPartyTemplateRepository thirdPartyTemplateRepository;
    @Autowired
    private OrganizationRepository organizationRepository;

    @Autowired
    private ConnectorService connectorService;

    @Autowired
    private ConnectorConsumerService connectorConsumerService;

    @Autowired
    private EventRuleService eventRuleService;


    @Override
    public String getSecret() {
        return "72e95fef-7269-4b6b-a50f-7a2d7a410f95";
    }

    @Override
    public Map<String, Consumer<Map<String, String>>> getUpdateTasks() {
        return Map.of(
                "addPlatformSmsTemplate", addPlatformSmsTemplate(),
                "grantPlatformSmsTemplate", grantPlatformSmsTemplate(),
                "removePlatformSmsTemplate", removePlatformSmsTemplate(),
                "updateWebhookEnable", updateWebhookEnable()
        );
    }

    public Consumer<Map<String, String>> addPlatformSmsTemplate() {
        return data -> {
            if (data == null || data.isEmpty()) {
                throw new BadRequestException("part: addPlatformSmsTemplate 需要 templateId, templateContent, templatePattern, templateVariables ");
            }
            String update = data.get("update");
            String templateId = data.get("templateId");
            String templatePattern = data.get("templatePattern");
            String originTemplate = data.get("originTemplate");
            String templateVariables = data.get("templateVariables");
            if (StringUtils.isEmpty(templateId)
                    || StringUtils.isEmpty(originTemplate)
                    || StringUtils.isEmpty(templatePattern)
                    || StringUtils.isEmpty(templateVariables)
            ) {
                throw new BadRequestException("part: addPlatformSmsTemplate 需要 templateId, templateContent, templatePattern, templateVariables");
            }
            XpackConfig entity = xpackConfigService.getConfigByTypeAndSubType(XPackAppType.SMS_TEMPLATE_CHUANGLAN, templateId);
            if (entity == null) {
                entity = new XpackConfig();
                entity.setType(XPackAppType.SMS_TEMPLATE_CHUANGLAN);
                entity.setSubType(templateId);
                entity.setApp("cem");
                entity.setEnabled(true);
            } else {
                if (!"true".equals(update)) {
                    throw new BadRequestException("part: grantPlatformSmsTemplate 平台模版已配置配置，传入参数update覆盖配置信息: " + templateId);
                }
            }
            SmsTemplatePatternDto config = new SmsTemplatePatternDto();
            config.setId(templateId);
            config.setOriginTemplate(originTemplate);
            config.setPattern(templatePattern);
            config.setVariables(templateVariables);
            entity.setConfig(JsonHelper.toJson(config));
            xpackConfigService.save(entity);
        };
    }

    public Consumer<Map<String, String>> grantPlatformSmsTemplate() {
        return data -> {
            String orgId = data.get("orgId");
            String signId = data.get("signId");
            String templateId = data.get("templateId");
            String templateName = data.get("templateName");
            String templateContent = data.get("templateContent");
            if (!NumberUtils.isDigits(orgId)
                    || StringUtils.isEmpty(templateId)
                    || StringUtils.isEmpty(templateName)
                    || StringUtils.isEmpty(templateContent)
            ) {
                throw new BadRequestException("part: grantPlatformSmsTemplate 需要 orgId, signId, templateId, templateName");
            }
            XpackConfig entity = xpackConfigService.getConfigByTypeAndSubType(XPackAppType.SMS_TEMPLATE_CHUANGLAN, templateId);
            if (entity != null) {
                SmsTemplatePatternDto config = JsonHelper.toObject(entity.getConfig(), SmsTemplatePatternDto.class);
                if (config != null) {
                    thirdPartyTemplateService.addPlatformSmsTemplate(Long.valueOf(orgId), templateName, templateContent, templateId, signId);
                    return;
                }
            }
            throw new BadRequestException("part: grantPlatformSmsTemplate 平台模版未配置: " + templateId);
        };
    }

    public Consumer<Map<String, String>> removePlatformSmsTemplate() {
        return data -> {
            String orgId = data.get("orgId");
            String templateId = data.get("templateId");
            if (!NumberUtils.isDigits(orgId)
                    || StringUtils.isEmpty(templateId)
            ) {
                throw new BadRequestException("part: removePlatformSmsTemplate 需要 orgId, templateId");
            }
            Connector connector = connectorService.getOrCreateSmsLocalConnector(Long.valueOf(orgId));
            ThirdPartyTemplate template = thirdPartyTemplateRepository.findByConnectorAndOpenId(connector, templateId).orElse(null);
            if (template != null) {
                thirdPartyTemplateRepository.delete(template);
                return;
            }
            throw new BadRequestException("part: removePlatformSmsTemplate 平台模版未配置: " + templateId);
        };
    }

    public Consumer<Map<String, String>> updateWebhookEnable() {
        return data -> {
            boolean test = data != null && data.containsKey("test");
            boolean hasNext = true;
            int page = 0;
            int size = test ? 10 : 100;
            while (hasNext) {
                // 每次处理100条
                Page<Organization> list = organizationRepository.findAll(PageRequest.of(page, size));
                if (list.hasContent()) {
                    list.get().forEach(org -> {
                        log.info("修改webhook开关，机构：{}", org.getId());
                        updateConnector(org.getId());
                    });
                    if (!test) {
                        page++;
                        continue;
                    }
                }
                hasNext = false;
            }
            log.info("修改webhook开关完成");
        };
    }

    public void updateConnector(Long orgId) {
        connectorService.getRepository().findAll((r, q, b) -> b.and(
                b.equal(r.get("orgId"), orgId),
                b.equal(r.get("type"), ConnectorType.WEBHOOK),
                b.equal(r.get("providerType"), ConnectorProviderType.WEBHOOK)
        )).forEach(connector -> {
            connectorConsumerService.getRepository().findAll((r, q, b) -> b.and(
                    b.equal(r.get("orgId"), orgId),
                    b.equal(r.get("connector"), connector),
                    b.equal(r.get("enable"), true)
            )).forEach(connectorConsumer -> {
                if (connectorConsumer.getRelationId() != null) {
                    eventRuleService.getRepository().findById(connectorConsumer.getRelationId()).ifPresent(eventRule -> {
                        eventRule.setNotifyConsumer(true);
                        log.info("修改webhook开关，机构：{}，规则：{}", orgId, eventRule.getId());
                        eventRuleService.save(eventRule);
                    });
                }
            });
        });
    }
}
