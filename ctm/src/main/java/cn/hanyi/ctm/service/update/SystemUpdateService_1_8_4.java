package cn.hanyi.ctm.service.update;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.AppVersion;
import org.befun.auth.entity.Organization;
import org.befun.auth.entity.Permission;
import org.befun.auth.entity.Role;
import org.befun.auth.repository.OrganizationRepository;
import org.befun.auth.repository.PermissionRepository;
import org.befun.auth.repository.RoleRepository;
import org.befun.auth.service.DepartmentService;
import org.befun.auth.service.OrganizationService;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.service.AbstractSystemUpdateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SystemUpdateService_1_8_4 extends AbstractSystemUpdateService {

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private PermissionRepository permissionRepository;

    @Autowired
    private OrganizationRepository organizationRepository;

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private JdbcTemplate jdbcTemplate;


    @Autowired
    private OrganizationService organizationService;


    @Override
    public String getSecret() {
        return "f476d824-0d37-4b4f-9d90-bf436d7dece4";
    }

    @Override
    public Map<String, Consumer<Map<String, String>>> getUpdateTasks() {
        return Map.of(
                "addSurveyVerifyAndLimit", addSurveyVerify(),
                "versionDegrade", versionDegrade()
        );
    }

    /**
     * 老用户降级
     */
    public Consumer<Map<String, String>> versionDegrade() {
        return data -> {
            boolean test = data != null && data.containsKey("test");
            boolean hasNext = true;
            int page = 0;
            int size = test ? 10 : 100;
            while (hasNext) {
                // 每次处理100条
                Page<Organization> list = organizationRepository.findAll(PageRequest.of(page, size));
                if (list.hasContent()) {
                    list.get().forEach(org -> {
                        degrade(org);
                    });
                    if (!test) {
                        page++;
                        continue;
                    }
                }
                hasNext = false;
            }
        };
    }

    /**
     * 添加问卷发布权限
     */
    public Consumer<Map<String, String>> addSurveyVerify() {
        return data -> {
            boolean test = data != null && data.containsKey("test");
            boolean hasNext = true;
            int page = 0;
            int size = test ? 10 : 100;
            while (hasNext) {
                // 每次处理100条
                Page<Organization> list = organizationRepository.findAll(PageRequest.of(page, size));
                if (list.hasContent()) {
                    list.get().forEach(org -> {
                        addVerify(org);
                        addSurveyLimit(org);
                    });
                    if (!test) {
                        page++;
                        continue;
                    }
                }
                hasNext = false;
            }
        };
    }


    /**
     * 添加问卷审核权限
     */
    public void addVerify(Organization org) {
        if (org == null || org.getId() == null || org.getId() <= 0) {
            return;
        }
        List<Role> roles = roleRepository.findByOrgId(org.getId());
        if (CollectionUtils.isEmpty(roles)) {
            return;
        }
        List<Long> roleIds = roles.stream().map(Role::getId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(roleIds)) {
            return;
        }
        String action1 = "/TouchManager/Survey/verify";
        String action2 = "/TouchManager/Survey/edit";
        List<String> action = List.of(action1, action2);
        long count = permissionRepository.count((root, query, builder) -> builder.and(root.get("roleId").in(roleIds), builder.equal(root.get("permission"), action1)));
        if (count > 0) {
            return;
        }
        roleIds.forEach(roleId -> {
            List<Permission> permissions = permissionRepository.findByRoleIdAndPermissionIn(roleId, action);
            Permission permission;
            // 角色只有 action2 的时候，添加 action1
            if (CollectionUtils.isNotEmpty(permissions)
                    && permissions.size() == 1
                    && (permission = permissions.get(0)) != null
                    && StringUtils.isNotEmpty(permission.getPermission())
                    && permission.getPermission().equals(action2)) {
                permissionRepository.save(new Permission(roleId, "Action", action1));
            }
        });
    }

    /**
     * 设置问卷数目1000000
     */
    public void addSurveyLimit(Organization org) {
        if (org == null || org.getId() == null || org.getId() <= 0) {
            return;
        }

        var limit = organizationService.parseOrgOptionalLimit(org);
        limit.setSurveysLimit(1000000);
        org.setOptionalLimit(JsonHelper.toJson(limit));
        organizationRepository.save(org);

    }

    /**
     * 屏蔽免费版、基础版、专业版的老帐号模块==【文本分析】【数据源】
     */
    public void degrade(Organization org) {
        var actions = List.of("/Bi/DataAnalysis", "/Bi/DataAnalysis/view", "/Bi/DataAnalysis/edit", "/Bi/Datasource", "/Bi/Datasource/view", "/Bi/Datasource/edit");
        var appVersion = organizationService.parseOrgVersion2(org);

        if (List.of(AppVersion.EMPTY, AppVersion.FREE, AppVersion.BASE, AppVersion.UPDATE).contains(appVersion)) {

            List<Role> roles = roleRepository.findAll((root, query, builder) -> builder.equal(root.get("orgId"), org.getId()));
            if (CollectionUtils.isEmpty(roles)) {
                return;
            }
            var permissions = permissionRepository.findByRoleIdIn(roles.stream().map(Role::getId).collect(Collectors.toList()));

            var acp = permissions.stream().filter(p -> actions.contains(p.getPermission())).collect(Collectors.toList());
            log.info("org:{} - 降级", org.getId());

            permissionRepository.deleteAll(acp);
        }
    }

}
