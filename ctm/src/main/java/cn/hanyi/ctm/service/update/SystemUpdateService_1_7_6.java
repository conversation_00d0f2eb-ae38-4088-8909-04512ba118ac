package cn.hanyi.ctm.service.update;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.entity.Organization;
import org.befun.auth.entity.Permission;
import org.befun.auth.entity.Role;
import org.befun.auth.repository.OrganizationRepository;
import org.befun.auth.repository.PermissionRepository;
import org.befun.auth.repository.RoleRepository;
import org.befun.auth.service.DepartmentService;
import org.befun.extension.service.AbstractSystemUpdateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SystemUpdateService_1_7_6 extends AbstractSystemUpdateService {

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private PermissionRepository permissionRepository;

    @Autowired
    private OrganizationRepository organizationRepository;

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public String getSecret() {
        return "274142ce-aa0f-43b8-97fa-168d2037a59d";
    }

    @Override
    public Map<String, Consumer<Map<String, String>>> getUpdateTasks() {
        return Map.of(
                "addSurveyPublish", addSurveyPublish(),
                "addDataAnalysis", addDataAnalysis()
        );
    }


    /**
     * 添加问卷发布权限
     */
    public Consumer<Map<String, String>> addSurveyPublish() {
        return data -> {
            boolean test = data != null && data.containsKey("test");
            boolean hasNext = true;
            int page = 0;
            int size = test ? 10 : 100;
            while (hasNext) {
                // 每次处理100条
                Page<Organization> list = organizationRepository.findAll(PageRequest.of(page, size));
                if (list.hasContent()) {
                    list.get().forEach(this::addSurveyPublish2);
                    if (!test) {
                        page++;
                        continue;
                    }
                }
                hasNext = false;
            }
        };
    }


    /**
     * 添加问卷发布权限
     */
    public void addSurveyPublish2(Organization org) {
        if (org == null || org.getId() == null || org.getId() <= 0) {
            return;
        }
        List<Role> roles = roleRepository.findByOrgId(org.getId());
        if (CollectionUtils.isEmpty(roles)) {
            return;
        }
        List<Long> roleIds = roles.stream().map(Role::getId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(roleIds)) {
            return;
        }
        String action1 = "/TouchManager/Survey/publish";
        String action2 = "/TouchManager/Survey/edit";
        List<String> action = List.of(action1, action2);
        // 如果企业里有 /TouchManager/Survey/publish 则直接忽略
        long count = permissionRepository.count((root, query, builder) -> builder.and(root.get("roleId").in(roleIds), builder.equal(root.get("permission"), action1)));
        if (count > 0) {
            return;
        }
        roleIds.forEach(roleId -> {
            List<Permission> permissions = permissionRepository.findByRoleIdAndPermissionIn(roleId, action);
            Permission permission;
            // 角色只有 action2 的时候，添加 action1
            if (CollectionUtils.isNotEmpty(permissions)
                    && permissions.size() == 1
                    && (permission = permissions.get(0)) != null
                    && StringUtils.isNotEmpty(permission.getPermission())
                    && permission.getPermission().equals(action2)) {
                permissionRepository.save(new Permission(roleId, "Action", action1));
            }
        });
    }


    /**
     * 添加文本分析全新
     */
    public Consumer<Map<String, String>> addDataAnalysis() {
        List<String> action = List.of("/Bi/DataAnalysis/view", "/Bi/DataAnalysis/edit");

        return data -> {
            boolean test = data != null && data.containsKey("test");
            boolean hasNext = true;
            int page = 0;
            int size = test ? 10 : 100;
            while (hasNext) {
                // 每次处理100条
                Page<Role> list = roleRepository.findAll(
                        (Specification<Role>) (root, query, cb) -> cb.and(cb.equal(root.get("type"), 1)),
                        PageRequest.of(page, size));
                if (list.hasContent()) {
                    list.get().forEach(i -> {
                        boolean exist = permissionRepository.findByRoleId(i.getId())
                                .stream()
                                .anyMatch(p -> action.contains(p.getPermission()));
                        if (!exist) {
                            log.info("添加文本分析权限 RoleId：{}", i.getId());
                            permissionRepository.save(new Permission(i.getId(), "Action", action.get(0)));
                            permissionRepository.save(new Permission(i.getId(), "Action", action.get(1)));
                        }
                    });
                    if (!test) {
                        page++;
                        continue;
                    }
                }
                hasNext = false;
            }
        };
    }

}
