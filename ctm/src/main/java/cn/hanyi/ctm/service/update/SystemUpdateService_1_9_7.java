package cn.hanyi.ctm.service.update;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.befun.auth.entity.Organization;
import org.befun.auth.entity.Permission;
import org.befun.auth.repository.OrganizationRepository;
import org.befun.auth.repository.PermissionRepository;
import org.befun.auth.service.RoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Consumer;

@Slf4j
@Service
public class SystemUpdateService_1_9_7 extends SystemUpdateServiceHelper {

    @Autowired
    private PermissionRepository permissionRepository;
    @Autowired
    private RoleService roleService;
    @Autowired
    private OrganizationRepository organizationRepository;

    @Override
    public String getSecret() {
        return "ad185d92-ca1e-4ce9-9435-d7d26f3bc0bb";
    }

    @Override
    public Map<String, Consumer<Map<String, String>>> getUpdateTasks() {
        return Map.of(
                "addJourneyEditPermission", addJourneyEditPermission()
        );
    }

    public Consumer<Map<String, String>> addJourneyEditPermission() {
        return data -> {
            String path = "/CustomerLife/CustomerLife/edit";
            List<String> paths = List.of(path);
            ExecutorService executorService = Executors.newFixedThreadPool(10);
            boolean hasNext = true;
            int page = 0;
            int size = 100;
            while (hasNext) {
                // 每次处理100条
                Page<Organization> list = organizationRepository.findAll(PageRequest.of(page, size));
                if (list.hasContent()) {
                    executorService.execute(() -> {
                        list.get().forEach(org -> {
                            var role = roleService.getSuperAdminByOrg(org.getId());
                            if (role != null) {
                                List<Permission> permissions = permissionRepository.findByRoleIdAndPermissionIn(role.getId(), paths);
                                if (CollectionUtils.isEmpty(permissions)) {
                                    permissionRepository.save(new Permission(role.getId(), "Action", path));
                                }
                            }
                        });
                    });
                    page++;
                    continue;
                }
                hasNext = false;
            }
        };
    }

}
