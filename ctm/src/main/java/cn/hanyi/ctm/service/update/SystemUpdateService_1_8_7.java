package cn.hanyi.ctm.service.update;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.AppVersion;
import org.befun.auth.entity.Organization;
import org.befun.auth.entity.Permission;
import org.befun.auth.entity.Role;
import org.befun.auth.repository.OrganizationRepository;
import org.befun.auth.repository.PermissionRepository;
import org.befun.auth.repository.RoleRepository;
import org.befun.auth.service.DepartmentService;
import org.befun.auth.service.OrganizationService;
import org.befun.auth.service.RoleService;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.service.AbstractSystemUpdateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SystemUpdateService_1_8_7 extends AbstractSystemUpdateService {

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private PermissionRepository permissionRepository;

    @Autowired
    private OrganizationRepository organizationRepository;

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private RoleService roleService;

    @Autowired
    private JdbcTemplate jdbcTemplate;


    @Autowired
    private OrganizationService organizationService;


    @Override
    public String getSecret() {
        return "0d1c509d-9e0b-4e14-b146-1210eb3d1dd9";
    }

    @Override
    public Map<String, Consumer<Map<String, String>>> getUpdateTasks() {
        return Map.of(
                "addCustomerPortrait", addCustomerPortrait()
        );
    }

    /**
     * 添加客户画像权限
     */
    public Consumer<Map<String, String>> addCustomerPortrait() {
        return data -> {
            boolean test = data != null && data.containsKey("test");
            boolean hasNext = true;
            int page = 0;
            int size = test ? 10 : 100;
            while (hasNext) {
                // 每次处理100条
                Page<Organization> list = organizationRepository.findAll(PageRequest.of(page, size));
                if (list.hasContent()) {
                    list.get().forEach(org -> {
                        log.info("添加客户画像权限，机构：{}", org.getId());
                        addVerify(org);
                    });
                    if (!test) {
                        page++;
                        continue;
                    }
                }
                hasNext = false;
            }
            log.info("添加客户画像权限完成");
        };
    }


    /**
     * 给超管添加客户画像权限
     */
    public void addVerify(Organization org) {
        if (org == null || org.getId() == null || org.getId() <= 0) {
            return;
        }

        var role =  roleService.getSuperAdminByOrg(org.getId());

        if (role == null) {
            return;
        }

        String action1 = "/CustomerLife/CustomerPortrait/view";
        String action2 = "/CustomerLife/CustomerPortrait/edit";
        List<String> action = List.of(action1, action2);

        List<Permission> permissions = permissionRepository.findByRoleId(role.getId());

        if(!permissions.stream().anyMatch(p -> action.contains(p.getPermission()))){

            permissionRepository.saveAll(
                    List.of(
                            new Permission(role.getId(), "Action", action1),
                            new Permission(role.getId(), "Action", action2)
                    )
            );
        }
    }
}
