package cn.hanyi.ctm;

import cn.hanyi.cem.core.CemCoreAutoConfiguration;
import com.jayway.jsonpath.spi.cache.CacheProvider;
import com.jayway.jsonpath.spi.cache.NOOPCache;
import org.befun.auth.AuthAutoConfiguration;
import org.befun.core.repository.impl.BaseRepositoryImpl;
import org.befun.extension.XPackAutoConfiguration;
import org.befun.task.TaskAutoConfiguration;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.TimeZone;

@SpringBootApplication(scanBasePackages = {"cn.hanyi", "org.befun.core", "org.befun.nlp.core"})
@EnableAsync
@EnableScheduling
public class CTMApplication implements CommandLineRunner {

    @Override
    public void run(String... args) throws Exception {
        TimeZone.setDefault(TimeZone.getTimeZone("Asia/Shanghai"));
    }

    @Configuration
    @EntityScan({
            "cn.hanyi.ctm.entity",
            "org.befun.core.entity",
            XPackAutoConfiguration.PACKAGE_ENTITY,
            TaskAutoConfiguration.PACKAGE_ENTITY,
            AuthAutoConfiguration.PACKAGE_ENTITY,
            CemCoreAutoConfiguration.PACKAGE_ENTITY,
    })
    @EnableJpaRepositories(basePackages = {
            "cn.hanyi.ctm.repository",
            "org.befun.core.repo",
            XPackAutoConfiguration.PACKAGE_REPOSITORY,
            TaskAutoConfiguration.PACKAGE_REPOSITORY,
            AuthAutoConfiguration.PACKAGE_REPOSITORY,
            CemCoreAutoConfiguration.PACKAGE_REPOSITORY,
    }, repositoryBaseClass = BaseRepositoryImpl.class)
    public class BaseJPAConfig {
    }

    public static void main(String[] args) {
        // 多线程消费时，禁用jsonpath缓存，否则会出现线程安全问题
        CacheProvider.setCache(new NOOPCache());
        SpringApplication.run(CTMApplication.class, args);
    }
}