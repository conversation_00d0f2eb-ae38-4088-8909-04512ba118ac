package cn.hanyi.ctm;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

public class AssertController extends SetupAuth {

    @Autowired
    protected MockMvc mvc;
    @Autowired
    protected ObjectMapper objectMapper;

    protected void postOk(String url, Object content, String token, ConsumerThrowable<ResultActions> consumer) throws Exception {
        MockHttpServletRequestBuilder builder = post(url)
                .header(HttpHeaders.AUTHORIZATION, token)
                .contentType(MediaType.APPLICATION_JSON);
        if (content != null) {
            if (content instanceof String) {
                builder.content((String) content);
            } else {
                builder.content(objectMapper.writeValueAsString(content));
            }
        }
        ResultActions actions = mvc.perform(builder)
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.code").value(200));
        if (consumer != null) {
            consumer.accept(actions);
        }
        MvcResult res = actions.andReturn();
        System.out.println(res.getResponse().getContentAsString());
    }

    protected void post400(String url, Object content, String token, int code) throws Exception {
        MockHttpServletRequestBuilder builder = post(url)
                .header(HttpHeaders.AUTHORIZATION, token)
                .contentType(MediaType.APPLICATION_JSON);
        if (content != null) {
            if (content instanceof String) {
                builder.content((String) content);
            } else {
                builder.content(objectMapper.writeValueAsString(content));
            }
        }
        MvcResult res = mvc.perform(builder)
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value(code))
                .andReturn();
        System.out.println(res.getResponse().getContentAsString());
    }

    protected void post403(String url, Object content) throws Exception {
        MockHttpServletRequestBuilder builder = post(url)
                .header(HttpHeaders.AUTHORIZATION, "")
                .contentType(MediaType.APPLICATION_JSON);
        if (content != null) {
            if (content instanceof String) {
                builder.content((String) content);
            } else {
                builder.content(objectMapper.writeValueAsString(content));
            }
        }
        MvcResult res = mvc.perform(builder)
                .andExpect(status().is(403))
                .andReturn();
        System.out.println(res.getResponse().getContentAsString());
    }

    protected void getOk(String url, String token, ConsumerThrowable<ResultActions> consumer) throws Exception {
        ResultActions actions = mvc.perform(get(url)
                        .header(HttpHeaders.AUTHORIZATION, token)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON));
        if (consumer != null) {
            consumer.accept(actions);
        }
        MvcResult res = actions.andReturn();
        System.out.println(res.getResponse().getContentAsString());
    }

    protected void get400(String url, String token, int code) throws Exception {
        MvcResult res = mvc.perform(get(url)
                        .header(HttpHeaders.AUTHORIZATION, token)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value(code))
                .andReturn();
        System.out.println(res.getResponse().getContentAsString());
    }

    protected void get403(String url) throws Exception {
        MvcResult res = mvc.perform(get(url)
                        .header(HttpHeaders.AUTHORIZATION, "")
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().is(403))
                .andReturn();
        System.out.println(res.getResponse().getContentAsString());
    }

    @FunctionalInterface
    public interface ConsumerThrowable<T> {
        void accept(T t) throws Exception;
    }
}
