package cn.hanyi.ctm.controller;

import cn.hanyi.ctm.AssertController;
import cn.hanyi.ctm.MockServiceConfiguration;
import cn.hanyi.ctm.TestRedisConfiguration;
import cn.hanyi.ctm.constant.EventActionType;
import cn.hanyi.ctm.dto.PushResponseDto;
import cn.hanyi.ctm.dto.event.EventActionCooperationRequestDto;
import cn.hanyi.ctm.dto.event.EventActionRemarkRequestDto;
import cn.hanyi.ctm.dto.event.EventActionWechatRequestDto;
import cn.hanyi.ctm.entity.Event;
import cn.hanyi.ctm.entity.EventAction;
import cn.hanyi.ctm.repository.EventActionRepository;
import cn.hanyi.ctm.repository.EventRepository;
import cn.hanyi.ctm.service.PushService;
import com.google.common.collect.Lists;
import org.befun.auth.constant.NotificationType;
import org.befun.auth.service.SystemNotificationService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.jdbc.Sql;

import java.util.List;

import static cn.hanyi.ctm.constant.EventActionType.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.BDDMockito.given;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {MockServiceConfiguration.class, TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class EventControllerTest extends AssertController {

    @MockBean
    private PushService pushService;
    @Autowired
    private EventRepository eventRepository;
    @Autowired
    private EventActionRepository eventActionRepository;
    @MockBean
    private SystemNotificationService systemNotificationService;

    private static final String URL_COUNT_ALL = "/events/count-all";
    private static final String URL_COUNT = "/events/count";
    private static final String URL_ACTION_SMS = "/events/%d/action-sms";
    private static final String URL_ACTION_WECHAT = "/events/%d/action-wechat";
    private static final String URL_ACTION_COOPERATION = "/events/%d/action-cooperation";
    private static final String URL_ACTION_REMARK = "/events/%d/action-remark";
    private static final String URL_ACTION_CLOSE = "/events/%d/action-close";

    //    @Test
//    @Sql(statements = INIT_SQL)
    public void count() throws Exception {
        getOk(URL_COUNT + "?_by=status", mock_token_org1_user1, actions ->
                actions.andExpect(jsonPath("$.data.total").value(12))
                        .andExpect(jsonPath("$.data.items[0].count").value(2))
                        .andExpect(jsonPath("$.data.items[1].count").value(1))
                        .andExpect(jsonPath("$.data.items[2].count").value(6))
                        .andExpect(jsonPath("$.data.items[3].count").value(3))
        );
        getOk(URL_COUNT + "?_by=warningLevel", mock_token_org1_user1, actions ->
                actions.andExpect(jsonPath("$.data.total").value(12))
                        .andExpect(jsonPath("$.data.items[0].count").value(1))
                        .andExpect(jsonPath("$.data.items[1].count").value(4))
                        .andExpect(jsonPath("$.data.items[2].count").value(4))
                        .andExpect(jsonPath("$.data.items[3].count").value(3))
        );
    }

    private void test(Long id, EventActionType type) {
        Event event = eventRepository.findById(id).orElse(null);
        assertNotNull(event);
        EventAction action = new EventAction();
        action.setEvent(event);
        action.setActionType(type);
        List<EventAction> list = eventActionRepository.findAll();
        assertEquals(1, list.size());
    }

//    @Test
    @Sql(statements = INIT_SQL)
    @Sql(statements = INIT_SQL_USER)
    @Sql(statements = INIT_SQL_THIRD_USER)
    @Sql(statements = INIT_SQL_DEPARTMENT)
    public void actionSms() throws Exception {
        mockPushSms();

//        EventActionSmsRequestDto dto = new EventActionSmsRequestDto();
//        dto.setTemplate("template");
//        dto.setTemplateContent("content{username}");
//        Map<String, Object> map = new HashMap<>();
//        map.put("username", "haha");
//        dto.setParameters(map);

        // org1_user1 给 org1 的事件添加 action 成功
        postOk(String.format(URL_ACTION_SMS, 1), null, mock_token_org1_user1, actions ->
                actions.andExpect(jsonPath("$.data").value(true))
        );
        // org1_user2 给 org1 的事件（该用户的部门看不到此事件）添加 action 失败 400
//        post400(String.format(URL_ACTION_SMS, 1), dto, mock_token_org1_user2, 20001);
        // org1_user1 给 org2 的事件添加 action 失败 400
//        post400(String.format(URL_ACTION_SMS, 13), dto, mock_token_org1_user1, 20001);

        // check one action message
        test(1L, ACTION_TYPE_MESSAGE);
    }

//    @Test
    @Sql(statements = INIT_SQL)
    @Sql(statements = INIT_SQL_USER)
    @Sql(statements = INIT_SQL_THIRD_USER)
    @Sql(statements = INIT_SQL_DEPARTMENT)
    public void actionWechat() throws Exception {
        mockPushKefu();

        EventActionWechatRequestDto dto = new EventActionWechatRequestDto();
        dto.setContent("template");
//        Map<String, Object> map = new HashMap<>();
//        map.put("username", "haha");
//        dto.setParameters(map);

        // org1_user1 给 org1 的事件添加 action 成功
        postOk(String.format(URL_ACTION_WECHAT, 1), dto, mock_token_org1_user1, actions ->
                actions.andExpect(jsonPath("$.data").value(true))
        );
        // org1_user2 给 org1 的事件（该用户的部门看不到此事件）添加 action 失败 400
//        post400(String.format(URL_ACTION_WECHAT, 1), dto, mock_token_org1_user2, 20001);
        // org1_user1 给 org2 的事件添加 action 失败 400
//        post400(String.format(URL_ACTION_WECHAT, 13), dto, mock_token_org1_user1, 20001);

        // check one action message
        test(1L, ACTION_TYPE_WECHAT);
    }


    @Test
    @Sql(statements = INIT_SQL)
    @Sql(statements = INIT_SQL_USER)
    @Sql(statements = INIT_SQL_THIRD_USER)
    @Sql(statements = INIT_SQL_DEPARTMENT)
    public void actionCooperation() throws Exception {
        mockPushWechatAndEmail();

        EventActionCooperationRequestDto dto = new EventActionCooperationRequestDto();
        dto.setTargetUserIds("1");
        dto.setContent("template");
        dto.setTypes(Lists.newArrayList(NotificationType.WECHAT, NotificationType.EMAIL));

        // org1_user1 给 org1 的事件添加 action 成功
        postOk(String.format(URL_ACTION_COOPERATION, 2), dto, mock_token_org1_user1, actions ->
                actions.andExpect(jsonPath("$.data").value(true))
        );
        // org1_user2 给 org1 的事件（该用户的部门看不到此事件）添加 action 失败 400
//        post400(String.format(URL_ACTION_COOPERATION, 1), dto, mock_token_org1_user2, 20001);
        // org1_user1 给 org2 的事件添加 action 失败 400
//        post400(String.format(URL_ACTION_COOPERATION, 13), dto, mock_token_org1_user1, 20001);

        // check one action message
        test(2L, ACTION_TYPE_COOPERATION);
    }


    @Test
    @Sql(statements = INIT_SQL)
    @Sql(statements = INIT_SQL_USER)
    @Sql(statements = INIT_SQL_THIRD_USER)
    @Sql(statements = INIT_SQL_DEPARTMENT)
    public void actionRemark() throws Exception {
        EventActionRemarkRequestDto dto = new EventActionRemarkRequestDto();
        dto.setContent("remark");

        // org1_user1 给 org1 的事件添加 action 成功
        postOk(String.format(URL_ACTION_REMARK, 2), dto, mock_token_org1_user1, actions ->
                actions.andExpect(jsonPath("$.data").value(true))
        );
        // org1_user2 给 org1 的事件（该用户的部门看不到此事件）添加 action 失败 400
//        post400(String.format(URL_ACTION_REMARK, 1), dto, mock_token_org1_user2, 20001);
        // org1_user1 给 org2 的事件添加 action 失败 400
//        post400(String.format(URL_ACTION_REMARK, 13), dto, mock_token_org1_user1, 20001);

        // check one action message
        test(2L, ACTION_TYPE_NOTE);
    }


    @Test
    @Sql(statements = INIT_SQL)
    @Sql(statements = INIT_SQL_USER)
    @Sql(statements = INIT_SQL_THIRD_USER)
    @Sql(statements = INIT_SQL_DEPARTMENT)
    public void actionClose() throws Exception {
        mockPushWechatAndEmail();

        // org1_user1 给 org1 的事件添加 action 成功
        postOk(String.format(URL_ACTION_CLOSE, 2), null, mock_token_org1_user1, actions ->
                actions.andExpect(jsonPath("$.data").value(true))
        );
        // org1_user2 给 org1 的事件（该用户的部门看不到此事件）添加 action 失败 400
//        post400(String.format(URL_ACTION_CLOSE, 1), null, mock_token_org1_user2, 20001);
        // org1_user1 给 org2 的事件添加 action 失败 400
//        post400(String.format(URL_ACTION_CLOSE, 13), null, mock_token_org1_user1, 20001);

        // check one action message
        test(2L, ACTION_TYPE_CLOSE);
    }

    private static final String INIT_SQL = "INSERT INTO `event_result` " +
            "(`id`, `org_id` ,`warning_level`, `status`, `deleted`, `create_time`, `survey_name`, `warning_title`) VALUES " +
            "(1, 1, 0, 0 , 0, now(), '测试问卷', '预警规则1')," +
            "(2, 1, 1, 1 , 0, now(), '测试问卷', '预警规则1')," +
            "(3, 1, 1, 2 , 0, now(), '测试问卷', '预警规则1')," +
            "(4, 1, 2, 2 , 0, now(), '测试问卷', '预警规则1')," +
            "(5, 1, 2, 2 , 0, now(), '测试问卷', '预警规则1')," +
            "(6, 1, 2, 2 , 0, now(), '测试问卷', '预警规则1')," +
            "(7, 1, 3, 3 , 0, now(), '测试问卷', '预警规则1')," +

            "(8, 1, 1, 0 , 0, now(), '测试问卷', '预警规则1')," +
            "(9, 1, 1, 2 , 0, now(), '测试问卷', '预警规则1')," +
            "(10,1, 2, 2 , 0, now(), '测试问卷', '预警规则1')," +
            "(11,1, 3, 3 , 0, now(), '测试问卷', '预警规则1')," +
            "(12,1, 3, 3 , 0, now(), '测试问卷', '预警规则1')," +

            "(13,2, 0, 0 , 0, now(), '测试问卷', '预警规则1')," +
            "(14,2, 1, 1 , 0, now(), '测试问卷', '预警规则1')," +
            "(15,2, 2, 2 , 0, now(), '测试问卷', '预警规则1')," +
            "(16,2, 2, 2 , 0, now(), '测试问卷', '预警规则1')," +
            "(17,2, 3, 3 , 0, now(), '测试问卷', '预警规则1')," +
            "(18,2, 3, 3 , 0, now(), '测试问卷', '预警规则1')," +
            "(19,2, 3, 3 , 0, now(), '测试问卷', '预警规则1')," +

            "(20,2, 1, 2 , 0, now(), '测试问卷', '预警规则1')," +
            "(21,2, 3, 3 , 0, now(), '测试问卷', '预警规则1');";

    private static final String INIT_SQL_USER = "INSERT INTO `user`" +
            "(id, org_id, is_admin, department_ids, role_id, username, truename, mobile, email, status, is_delete, create_time, modify_time)VALUES" +
            "(1, 1, 1, '[[1]]', '1', 'user1', 'truename1', '18681506437', '<EMAIL>', 1, 0, now(),now())";

    private static final String INIT_SQL_THIRD_USER = "insert into thirdparty_user" +
            "(id,`create_time`, `modify_time`,org_id, user_id, app, open_id) values" +
            "(1,now(), now(),1, 1, 'cem', 'outRR6G0baO_qUR-_lA3SQQq_gQU')";

    private static final String INIT_SQL_DEPARTMENT = "INSERT INTO `department` " +
            "(`id`, `create_time`, `modify_time`, `org_id`, `pid`, `title`, `level_id`) VALUES" +
            "('1', now(), now(), '1', 0, '广东', '1');";

    private void mockPushSms() {
        given(pushService.sendMessageByApp(any())).willReturn(new PushResponseDto(1, 1, 1, null, null, null));
    }

    private void mockPushWechatAndEmail() {
        given(systemNotificationService.notifyToUser(any(), any(), any(), any(), any())).willReturn(true);
    }

    private void mockPushKefu() {
        given(pushService.sendTextMessageByApp(any())).willReturn(new PushResponseDto(1, 1, 1, null, null, null));
    }
}