package cn.hanyi.ctm.controller;

import cn.hanyi.ctm.BaseTest;
import cn.hanyi.ctm.MockServiceConfiguration;
import cn.hanyi.ctm.TestRedisConfiguration;
import cn.hanyi.ctm.repository.EventMonitorRulesRepository;
import cn.hanyi.survey.core.constant.survey.SurveyStatus;
import cn.hanyi.survey.core.entity.Survey;
import cn.hanyi.survey.core.repository.SurveyRepository;
import org.befun.auth.constant.ResourcePermissionRelationType;
import org.befun.auth.constant.ResourcePermissionType;
import org.befun.core.entity.ResourcePermission;
import org.befun.core.repo.ResourcePermissionRepository;
import org.befun.core.repository.ResourceRepository;
import org.befun.core.utils.JsonHelper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import java.util.Map;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {MockServiceConfiguration.class, TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class EventMonitorRulesControllerTest extends BaseTest {

    @Autowired
    protected MockMvc mvc;

    @Autowired
    protected EventMonitorRulesRepository eventMonitorRulesRepository;

    @Autowired
//    @Qualifier("surveyJdbcTemplate")
    private JdbcTemplate jdbcTemplate;

    @Autowired


    private static final String CREATE_TABLE = "CREATE TABLE `survey_question` (\n" +
            " `id`   BIGINT  PRIMARY KEY AUTO_INCREMENT,\n" +
            " `name` VARCHAR(50) NOT NULL,\n" +
            " `s_id` BIGINT  NOT NULL\n" +
            ");";

    @Autowired
    ResourcePermissionRepository resourcePermissionRepository;
    @Autowired
    SurveyRepository surveyRepository;

    @Test
    public void curdEventMonitorRulesTest() throws Exception {
//        jdbcTemplate.execute(CREATE_TABLE);

        Survey survey = new Survey();
        survey.setId(1L);
        survey.setTitle(String.format("survey_%d", 1));
        survey.setStatus(SurveyStatus.COLLECTING);
        survey.setOrgId(1L);
        survey.setUserId(1L);
        survey.setWelcomingRemark(String.format("survey_%d welcome", 1));
        surveyRepository.save(survey);
        ResourcePermission resourcePermission = new ResourcePermission(1L, 1L, null, null, ResourcePermissionType.SURVEY.name(), ResourcePermissionRelationType.ADMIN.name());
        resourcePermission.setOrgId(1L);
        resourcePermissionRepository.save(resourcePermission);

        MvcResult res = mvc.perform(post("/event-monitor-rules")
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(JsonHelper.toJson(rule1))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.surveyId").value(rule1.getSurveyId()))
                .andReturn();

        res.getResponse().setCharacterEncoding("utf-8");
        Map data = JsonHelper.toMap(res.getResponse().getContentAsString());
        Map newRule = (Map) data.get("data");
        Long newRuleId = (Long) newRule.get("id");

        String newTitle = "update";

        mvc.perform(put(String.format("/event-monitor-rules/%d", newRuleId))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(JsonHelper.toJson(Map.of("title", newTitle)))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.surveyId").value(rule1.getSurveyId()))
                .andExpect(jsonPath("$.data.title").value(newTitle));

        mvc.perform(delete(String.format("/event-monitor-rules/%d", newRuleId))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        mvc.perform(get(String.format("/event-monitor-rules/%d", newRuleId))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value(20001));
    }
}