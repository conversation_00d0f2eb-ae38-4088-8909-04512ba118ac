package cn.hanyi.ctm.controller;

import cn.hanyi.ctm.BaseTest;
import cn.hanyi.ctm.MockServiceConfiguration;
import cn.hanyi.ctm.TestRedisConfiguration;
import cn.hanyi.ctm.entity.EventMonitorThesaurus;
import org.befun.core.utils.JsonHelper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import java.util.Map;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {MockServiceConfiguration.class, TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class EventMonitorThesaurusControllerTest extends BaseTest {

    @Autowired
    protected MockMvc mvc;

    @Test
    public void curdEventMonitorRulesTest() throws Exception {

        EventMonitorThesaurus thesaurus = new EventMonitorThesaurus();
        thesaurus.setTitle("test");
        thesaurus.setThesaurus("难吃/差评");

        MvcResult res = mvc.perform(post("/event-monitor-thesaurus")
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(JsonHelper.toJson(thesaurus))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andReturn();

        res.getResponse().setCharacterEncoding("utf-8");
        Map data = JsonHelper.toMap(res.getResponse().getContentAsString());
        Map newRule = (Map) data.get("data");
        Long newRuleId = (Long) newRule.get("id");

        String newTitle = "update";

        mvc.perform(put(String.format("/event-monitor-thesaurus/%d", newRuleId))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(JsonHelper.toJson(Map.of("title", newTitle)))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.title").value(newTitle));

        mvc.perform(delete(String.format("/event-monitor-thesaurus/%d", newRuleId))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200));

        mvc.perform(get(String.format("/event-monitor-thesaurus/%d", newRuleId))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.code").value(20001));
    }
}