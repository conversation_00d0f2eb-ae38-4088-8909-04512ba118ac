package cn.hanyi.ctm.controller;

import cn.hanyi.ctm.BaseTest;
import cn.hanyi.ctm.MockServiceConfiguration;
import cn.hanyi.ctm.TestRedisConfiguration;
import cn.hanyi.ctm.connector.ApiConnectorManager;
import cn.hanyi.ctm.constant.MomentType;
import cn.hanyi.ctm.constant.PushStrategy;
import cn.hanyi.ctm.dto.AppMassPushRequestDto;
import cn.hanyi.ctm.dto.journey.JourneyTriggerRequestDto;
import cn.hanyi.ctm.dto.journey.PushMomentDto;
import cn.hanyi.ctm.entity.journey.JourneyPublish;
import cn.hanyi.ctm.entity.Template;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import java.util.*;

import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {MockServiceConfiguration.class, TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
class OpenControllerTest extends BaseTest {
    @Autowired
    protected MockMvc mvc;

    @MockBean
    private ApiConnectorManager apiConnectorManager;

    @Autowired
    private StringRedisTemplate redisTemplate;

//    @Test
    public void triggerJourney() throws Exception{
        List<JourneyPublish> journeyPublishes = journeyPublishRepository.findAllByOrgId(mock_org_1_id);
        JourneyPublish journeyPublish = journeyPublishes.get(0);
        Long journeyId = journeyPublish.getId();
        JourneyTriggerRequestDto requestDto = new JourneyTriggerRequestDto();
        requestDto.setSceneId(journeyId);
        requestDto.setCustomerId(1L);
        doNothing().when(apiConnectorManager).sendApiMessage(any(),any(),any(),any(),any(),any(),any());

        MvcResult res = mvc.perform(post("/open/journey/trigger")
                .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                .content(objectMapper.writeValueAsString(requestDto))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        System.out.println(res.getResponse().getContentAsString());

    }

//    @Test
    public void sendFeigeMessage() throws Exception{
        ArrayList<Long> customerIds = new ArrayList<>();
        Map<String, Object> parameters = new HashMap<>();
        Long tid = 1L;
        customerIds.add(ct2.getId());
        PushMomentDto momentDto = new PushMomentDto();
        momentDto.setMomentType(MomentType.LATER);
        AppMassPushRequestDto requestDto = new AppMassPushRequestDto();

        requestDto.setMomentDto(momentDto);
        requestDto.setCustomerIds(customerIds);
        requestDto.setParameters(parameters);
        requestDto.setIsDetail(true);
        requestDto.setStrategy(PushStrategy.BATCH);
        requestDto.setOrgId(mock_org_1_id);
        Optional<Template> template = templateRepository.findByName("fake_template_2");
        if (template.isPresent()) {
            tid = template.get().getId();
            requestDto.setTemplateId(tid);
        } else {
            fail();
        }
        sendMassage();
        MvcResult res = mvc.perform(post("/admin/send")
                .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                .content(objectMapper.writeValueAsString(requestDto))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.actualNumber").value(1))
                .andExpect(jsonPath("$.data.template.id").value(tid))
                .andExpect(jsonPath("$.data.template.connector.name").value("c3"))
                .andReturn();
        System.out.println(res.getResponse().getContentAsString());
    }
}