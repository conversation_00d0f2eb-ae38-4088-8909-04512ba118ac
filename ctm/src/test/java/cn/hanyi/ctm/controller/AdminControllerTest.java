package cn.hanyi.ctm.controller;

import cn.hanyi.ctm.BaseTest;
import cn.hanyi.ctm.MockServiceConfiguration;
import cn.hanyi.ctm.TestRedisConfiguration;
import cn.hanyi.ctm.constant.connector.ConnectorProviderType;
import cn.hanyi.ctm.constant.connector.ConnectorType;
import cn.hanyi.ctm.constant.PushStrategy;
import cn.hanyi.ctm.dto.AppMassPushRequestDto;
import cn.hanyi.ctm.dto.TextPushRequestDto;
import cn.hanyi.ctm.dto.journey.PushMomentDto;
import cn.hanyi.ctm.entity.Template;
import org.befun.auth.entity.OrganizationWallet;
import org.junit.Assert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.BDDMockito.given;
import static org.mockito.Mockito.mock;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {MockServiceConfiguration.class, TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
class AdminControllerTest extends BaseTest {
    @Autowired
    protected MockMvc mvc;

    @Autowired
    private StringRedisTemplate redisTemplate;

//    @Test
    public void sendFeigeMessage() throws Exception{
        ArrayList<Long> customerIds = new ArrayList<>();
        Map<String, Object> parameters = new HashMap<>();
        Long tid = 1L;
        customerIds.add(ct2.getId());
        AppMassPushRequestDto requestDto = new AppMassPushRequestDto();
        requestDto.setMomentDto(new PushMomentDto());
        requestDto.setCustomerIds(customerIds);
        requestDto.setParameters(parameters);
        requestDto.setIsDetail(true);
        requestDto.setStrategy(PushStrategy.BATCH);
        requestDto.setOrgId(mock_org_1_id);
        Optional<Template> template = templateRepository.findByName("fake_template_2");
        if (template.isPresent()) {
            tid = template.get().getId();
            requestDto.setTemplateId(tid);
        } else {
            fail();
        }
        sendMassage();
        MvcResult res = mvc.perform(post("/admin/send")
                .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                .content(objectMapper.writeValueAsString(requestDto))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.actualNumber").value(1))
                .andExpect(jsonPath("$.data.template.id").value(tid))
                .andExpect(jsonPath("$.data.template.connector.name").value("c3"))
                .andReturn();
        System.out.println(res.getResponse().getContentAsString());
    }

//    @Test
    public void sendFeigeMessageWithoutTemplateId() throws Exception{
        ArrayList<Long> customerIds = new ArrayList<>();
        Map<String, Object> parameters = new HashMap<>();

        customerIds.add(ct2.getId());
        AppMassPushRequestDto requestDto = new AppMassPushRequestDto();
        requestDto.setCustomerIds(customerIds);
        requestDto.setParameters(parameters);
        requestDto.setIsDetail(true);
        requestDto.setStrategy(PushStrategy.BATCH);
        requestDto.setTemplateName(mock_default_template);
        requestDto.setOrgId(mock_org_1_id);

        sendMassage();
        MvcResult res = mvc.perform(post("/admin/send")
                .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                .content(objectMapper.writeValueAsString(requestDto))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.actualNumber").value(1))
                .andExpect(jsonPath("$.data.template.name").value("default_sms_customer"))
                .andExpect(jsonPath("$.data.template.connector.name").value("c3"))
                .andReturn();
        System.out.println(res.getResponse().getContentAsString());

    }

//    @Test
    public void sendFeigeMessageOverLimit() throws Exception{
        ArrayList<Long> customerIds = new ArrayList<>();
        Map<String, Object> parameters = new HashMap<>();
        customerIds.add(ct2.getId());
        AppMassPushRequestDto requestDto = new AppMassPushRequestDto();
        requestDto.setCustomerIds(customerIds);
        requestDto.setParameters(parameters);
        requestDto.setIsDetail(true);
        requestDto.setStrategy(PushStrategy.BATCH);
        Optional<Template> template = templateRepository.findByName("fake_template_2");
        if (template.isPresent()) {
            Long tid = template.get().getId();
            requestDto.setTemplateId(tid);
        } else {
            fail();
        }
        requestDto.setOrgId(mock_org_1_id);

        sendMassage();
        this.redisTemplate.opsForValue().set(String.format("limiter.smsbalance.%d",mock_org_1_id),"1");


        MvcResult res = mvc.perform(post("/admin/send")
                .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                .content(objectMapper.writeValueAsString(requestDto))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.actualNumber").value(1))
                .andExpect(jsonPath("$.data.template.name").value("fake_template_2"))
                .andExpect(jsonPath("$.data.template.connector.name").value("c3"))
                .andReturn();
        System.out.println(res.getResponse().getContentAsString());


        MvcResult res2= mvc.perform(post("/admin/send")
                .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                .content(objectMapper.writeValueAsString(requestDto))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().is4xxClientError())
                .andReturn();
        System.out.println(res2.getResponse().getContentAsString());
        Optional<OrganizationWallet> organizationWallet = organizationWalletRepository.findByOrgId(mock_org_1_id);
        if (organizationWallet.isPresent()){
            Assert.assertTrue(organizationWallet.get().getSms()==0);
        } else {
            fail();
        }
    }

    //    @Test todo 2022-03-30
//    public void sendWeChatOpenTemplateMessage() throws Exception{
//        Long tid = 1L;
//        ArrayList<Long> customerIds = new ArrayList<>();
//        Map<String, Object> parameters = new HashMap<>();
//        customerIds.add(tct1_ct1.getId());
//        AppMassPushRequestDto requestDto = new AppMassPushRequestDto();
//        requestDto.setCustomerIds(customerIds);
//        requestDto.setParameters(parameters);
//        requestDto.setStrategy(PushStrategy.BATCH);
//        requestDto.setParameters(new HashMap<>());
//        requestDto.setOrgId(mock_org_1_id);
//        Optional<Template> template = templateRepository.findByName("fake_template_3");
//        if (template.isPresent()) {
//            tid = template.get().getId();
//            requestDto.setTemplateId(tid);
//        } else {
//            fail();
//        }
//        given(wechatProvider.sendTemplateMessage(any(),any(),any(),any(),any(), any())).willReturn(true);
//        sendMassage();
//        MvcResult res = mvc.perform(post("/admin/send")
//                .header(HttpHeaders.AUTHORIZATION, mock_token_1)
//                .content(objectMapper.writeValueAsString(requestDto))
//                .contentType(MediaType.APPLICATION_JSON))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.data.actualNumber").value(1))
//                .andExpect(jsonPath("$.data.template.id").value(tid))
//                .andExpect(jsonPath("$.data.template.connector.name").value("c1"))
//                .andReturn();
//        System.out.println(res.getResponse().getContentAsString());
//        System.out.println(res.getResponse().getContentAsString());
//    }

//    @Test
    public void sentTextMessage() throws Exception{
        TextPushRequestDto requestDto = new TextPushRequestDto();
        Map<String, Object> content = new HashMap<>();
        content.put("content","asd");
        ArrayList<Long> customerIds = new ArrayList<>();
        Map<String, Object> parameters = new HashMap<>();
        customerIds.add(tct1_ct1.getId());

        requestDto.setContent(content);
        requestDto.setCustomerIds(customerIds);
        requestDto.setOrgId(mock_org_1_id);
        requestDto.setParameters(parameters);
        requestDto.setIsDetail(true);
        requestDto.setProviderType(ConnectorProviderType.WECHATOPEN);
        requestDto.setConnectorType(ConnectorType.PLATFORM);

        Map<String, Object> textContent = new HashMap<>();
        textContent.put("content","true");
        sendMassage();
        MvcResult res = mvc.perform(post("/admin/text")
                .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                .content(objectMapper.writeValueAsString(requestDto))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.actualNumber").value(1))
                .andExpect(jsonPath("$.data.detailInformation.*").value(textContent))
                .andReturn();
        System.out.println(res.getResponse().getContentAsString());
        System.out.println(res.getResponse().getContentAsString());
    }

    //    @Test todo 2022-03-30
//    public void evaluateWeChatOpenTemplateMessage() throws Exception{
//        ArrayList<Long> customerIds = new ArrayList<>();
//        Map<String, Object> parameters = new HashMap<>();
//        Long tid = 1L;
//        customerIds.add(tct1_ct1.getId());
//        AppMassPushRequestDto requestDto = new AppMassPushRequestDto();
//        requestDto.setCustomerIds(customerIds);
//        requestDto.setParameters(parameters);
//        requestDto.setStrategy(PushStrategy.BATCH);
////        requestDto.setTemplateId(3L);
//        requestDto.setParameters(new HashMap<>());
//        requestDto.setOrgId(mock_org_1_id);
//        Optional<Template> template = templateRepository.findByName("fake_template_3");
//        if (template.isPresent()) {
//            tid = template.get().getId();
//            requestDto.setTemplateId(tid);
//        } else {
//            fail();
//        }
//
//        given(wechatProvider.sendTemplateMessage(any(),any(),any(),any(),any(), any())).willReturn(true);
//        sendMassage();
//        MvcResult res = mvc.perform(post("/admin/evaluate")
//                .header(HttpHeaders.AUTHORIZATION, mock_token_1)
//                .content(objectMapper.writeValueAsString(requestDto))
//                .contentType(MediaType.APPLICATION_JSON))
//                .andExpect(status().isOk())
//                .andExpect(jsonPath("$.data.actualNumber").value(1))
//                .andExpect(jsonPath("$.data.template.id").value(tid))
//                .andExpect(jsonPath("$.data.template.connector.name").value("c1"))
//                .andReturn();
//        System.out.println(res.getResponse().getContentAsString());
//    }


}