package cn.hanyi.ctm.controller;

import cn.hanyi.ctm.BaseTest;
import cn.hanyi.ctm.MockServiceConfiguration;
import cn.hanyi.ctm.TestRedisConfiguration;
import cn.hanyi.ctm.connector.PlatformConnectorManager;
import cn.hanyi.ctm.constant.RecordType;
import cn.hanyi.ctm.constant.SendStatus;
import cn.hanyi.ctm.entity.Customer;
import cn.hanyi.ctm.entity.CustomerAnswers;
import cn.hanyi.ctm.entity.CustomerHistoryRecord;
import cn.hanyi.ctm.entity.CustomerJourneyRecord;
import cn.hanyi.ctm.repository.*;
import cn.hanyi.ctm.service.CustomerRecordService;
import cn.hanyi.ctm.service.CustomerService;
import cn.hanyi.ctm.service.CustomerUpdateService;
import cn.hanyi.survey.core.dto.message.SurveyResponseMessageDto;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import javax.persistence.EntityManager;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * The class description
 *
 * <AUTHOR>
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {MockServiceConfiguration.class, TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class CustomerControllerTest extends BaseTest {
    @Autowired
    protected MockMvc mvc;
    @Autowired
    private ConnectorRepository repository;

    @Autowired
    private PushRepository pushRepository;

    @Autowired
    CustomerJourneyRecordsRepository customerJourneyRecordsRepository;

    @Autowired
    CustomerHistoryRecordsRepository customerHistoryRecordsRepository;

    @Autowired
    CustomerAnswersRepository customerAnswersRepository;

    @Autowired
    CustomerRecordService customerRecordService;

    @Autowired
    CustomerService customerService;

    @Autowired
    CustomerUpdateService customerUpdateService;

    @Autowired
    protected EntityManager entityManager;

    @Autowired
    protected PlatformConnectorManager connectorManager;

    @Test
    @Disabled("事件驱动，不再回调使用")
    void addCustomerAwareEvent() throws Exception {
        List<CustomerHistoryRecord> customerHistoryRecords = customerHistoryRecordsRepository.findAllByCustomerIdAndRecordType(tct1_ct1.getId(), RecordType.ADDCUSTOMER);

        Assertions.assertTrue(customerHistoryRecords.size() > 0);
    }

    @Test
    void updateCustomerAwareEvent() throws Exception {
        Map<String, Object> requestDto = new HashMap<>();
        requestDto.put("username", "tct1_ct1_1_update");
        MvcResult res = mvc.perform(put(String.format("/customers/%d", tct1_ct1.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(requestDto))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andReturn();
        System.out.println(res.getResponse().getContentAsString());
//      eventlistener 更改为异步处理
//      List<CustomerHistoryRecord> customerHistoryRecords = customerHistoryRecordsRepository.findAllByCustomer(tct1_ct1);
//      Assertions.assertTrue(customerHistoryRecords.size()==3);
    }

    @Test
    public void addCustomerJourneyRecord() throws Exception {
        CustomerJourneyRecord requestDto = new CustomerJourneyRecord();
        requestDto.setDepartmentId(1L);
        requestDto.setJourneyId(j1.getId());
        requestDto.setDetails("bar");
        requestDto.setCreatedByUid(0L);
        MvcResult res = mvc.perform(post(String.format("/customers/%d/journey-records", tct1_ct1.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(requestDto))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andReturn();
        System.out.println(res.getResponse().getContentAsString());
//        Assertions.assertTrue(pushRepository.findAll().size()>0);
//        Assertions.assertEquals(2,
//                customerHistoryRecordsRepository.findAllByCustomer(tct1_ct1).size());
//        Assertions.assertEquals(1,
//                customerJourneyRecordsRepository.findAll().size());
    }

    @Test
    @Disabled("事件驱动，不再回调使用")
    public void batchAddCusotmerJourneyRecords() throws Exception {
        CustomerJourneyRecord requestDto = new CustomerJourneyRecord();
        requestDto.setDepartmentId(1L);
        requestDto.setJourneyId(j1.getId());
        requestDto.setDetails("foo");
        requestDto.setCreatedByUid(mock_user_1.getId());

        sendMassage();
        MvcResult res = mvc.perform(post(String.format("/customers/%d/journey-records", ct2.getId()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .content(objectMapper.writeValueAsString(requestDto))
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
                .andReturn();
        System.out.println(res.getResponse().getContentAsString());
        List<CustomerHistoryRecord> customerHistoryRecords = customerHistoryRecordsRepository.findAllByCustomerId(ct2.getId());
        Assertions.assertTrue(customerHistoryRecords.size() > 0);
    }

    @Test
    public void sendCustomerAnswer() throws Exception {
        CustomerAnswers customerAnswers = new CustomerAnswers();
        customerAnswers.setCustomer(ct2);
        customerAnswers.setJourneyRecordId(1L);
        customerAnswers.setSendStatus(SendStatus.SUCCESS);
        customerAnswers.setAnswerId(0L);
        customerAnswers.setSid("12345");
        customerAnswersRepository.save(customerAnswers);
        SurveyResponseMessageDto requestDto = new SurveyResponseMessageDto();
        requestDto.setCustomerId(ct2.getId());
        requestDto.setTrackId(String.format("record::%d", 1L));
        requestDto.setSurveyId(12345L);
        requestDto.setDurationInSeconds(13);
        requestDto.setResponseId(1111L);
        requestDto.setFinishTime(123456L);
        requestDto.setDepartmentId(1234L);
        requestDto.setDepartmentName("TEST");
        Customer customer = customerService.getCustomerFromResponse(
                requestDto.getOrgId(),
                requestDto.getExternalUserId(),
                requestDto.getCustomerId(),
                requestDto.getDepartmentId(),
                requestDto.getDepartmentName(),
                null,
                requestDto.getCustomerName(),
                requestDto.getCustomerGender(),
                null,
                requestDto.getTags()
        );
        customerUpdateService.updateCutomerAnswers(requestDto, customer);
        List<CustomerAnswers> customerAnswersList = customerAnswersRepository.findAllByCustomerIdAndSidAndJourneyRecordId(ct2.getId(), "12345", 1L);
        List<CustomerHistoryRecord> customerHistoryRecords = customerHistoryRecordsRepository.findAllByCustomerIdAndRecordType(ct2.getId(), RecordType.FILLEDSURVEY);
        Assertions.assertEquals(1, customerHistoryRecords.size());
        Assertions.assertTrue(
                customerAnswersList.get(0).getAnswerId().equals(requestDto.getResponseId()));
    }

}
