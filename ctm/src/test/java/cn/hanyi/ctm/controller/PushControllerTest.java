package cn.hanyi.ctm.controller;

import cn.hanyi.ctm.BaseTest;
import cn.hanyi.ctm.MockServiceConfiguration;
import cn.hanyi.ctm.TestRedisConfiguration;
import cn.hanyi.ctm.constant.PushStrategy;
import cn.hanyi.ctm.dto.MassPushRequestDto;
import cn.hanyi.ctm.entity.Template;
import cn.hanyi.ctm.repository.PushRepository;
import org.befun.auth.entity.OrganizationWallet;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {MockServiceConfiguration.class, TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class PushControllerTest extends BaseTest {
    @Autowired
    protected MockMvc mvc;

    @Autowired
    private PushRepository pushRepository;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Test
    public void sendFeigeMessage() throws Exception{
        ArrayList<Long> customerIds = new ArrayList<>();
        customerIds.add(ct2.getId());
        MassPushRequestDto requestDto = new MassPushRequestDto();
        requestDto.setCustomerIds(customerIds);
        requestDto.setParameters(null);
        requestDto.setStrategy(PushStrategy.BATCH);
        requestDto.setParameters(new HashMap<>());
        Long tid;
        Optional<Template> template = templateRepository.findByName("fake_template_2");
        if (template.isPresent()) {
            tid = template.get().getId();
            requestDto.setTemplateId(tid);
        } else {
            fail();
        }

        sendMassage();
        MvcResult res = mvc.perform(post("/pushes/send")
                .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                .content(objectMapper.writeValueAsString(requestDto))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        System.out.println(res.getResponse().getContentAsString());
//        Assert.assertTrue(pushRepository.findAll().size()>0);
    }

    @Test
    public void sendFeigeMessageOverLimit() throws Exception{
        ArrayList<Long> customerIds = new ArrayList<>();
        Map<String, Object> parameters = new HashMap<>();
        customerIds.add(ct2.getId());
        MassPushRequestDto requestDto = new MassPushRequestDto();
        requestDto.setCustomerIds(customerIds);
        requestDto.setParameters(parameters);
        requestDto.setIsDetail(true);
        requestDto.setStrategy(PushStrategy.BATCH);
        Long tid;
        Optional<Template> template = templateRepository.findByName("fake_template_2");
        if (template.isPresent()) {
            tid = template.get().getId();
            requestDto.setTemplateId(tid);
        } else {
            fail();
        }
        this.redisTemplate.opsForValue().set("limiter.smsbalance.1","1");

        sendMassage();
        MvcResult res = mvc.perform(post("/pushes/send")
                .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                .content(objectMapper.writeValueAsString(requestDto))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        System.out.println(res.getResponse().getContentAsString());


        MvcResult res2 = mvc.perform(post("/pushes/send")
                .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                .content(objectMapper.writeValueAsString(requestDto))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().is4xxClientError())
                .andReturn();
        System.out.println(res2.getResponse().getContentAsString());
        Optional<OrganizationWallet> organizationWallet = organizationWalletRepository.findByOrgId(mock_org_1_id);
        if (organizationWallet.isPresent()){
            Assert.assertTrue(organizationWallet.get().getSms()==(0));
        } else {
            fail();
        }
    }

//    @Test todo 2022-03-30
    public void sendWeChatOpenTemplateMessage() throws Exception{
        ArrayList<Long> customerIds = new ArrayList<>();
        Map<String, Object> parameters = new HashMap<>();
        customerIds.add(tct1_ct1.getId());
        MassPushRequestDto requestDto = new MassPushRequestDto();
        requestDto.setCustomerIds(customerIds);
        requestDto.setParameters(parameters);
        requestDto.setStrategy(PushStrategy.BATCH);
        requestDto.setParameters(new HashMap<>());

        Long tid;
        Optional<Template> template = templateRepository.findByName("fake_template_3");
        if (template.isPresent()) {
            tid = template.get().getId();
            requestDto.setTemplateId(tid);
        } else {
            fail();
        }

        sendMassage();
        MvcResult res = mvc.perform(post("/pushes/send")
                .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                .content(objectMapper.writeValueAsString(requestDto))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andReturn();
        System.out.println(res.getResponse().getContentAsString());
//        Assert.assertTrue(pushRepository.findAll().size()>0);
    }

    @Test
    public void evaluateFeigeMessage() throws Exception{
        Long tid;
        ArrayList<Long> customerIds = new ArrayList<>();
        customerIds.add(ct2.getId());
        MassPushRequestDto requestDto = new MassPushRequestDto();
        requestDto.setCustomerIds(customerIds);
        requestDto.setParameters(null);
        requestDto.setStrategy(PushStrategy.BATCH);
        requestDto.setIsDetail(true);
        requestDto.setParameters(new HashMap<>());
        Optional<Template> template = templateRepository.findByName("fake_template_2");
        if (template.isPresent()) {
            tid = template.get().getId();
            requestDto.setTemplateId(tid);
        } else {
            fail();
        }


        sendMassage();
        MvcResult res = mvc.perform(post("/pushes/evaluate")
                .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                .content(objectMapper.writeValueAsString(requestDto))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.actualNumber").value(1))
                .andReturn();
        System.out.println(res.getResponse().getContentAsString());
    }

    //    @Test todo 2022-03-30
    public void evaluateWeChatOpenTemplateMessage() throws Exception{
        ArrayList<Long> customerIds = new ArrayList<>();
        Map<String, Object> parameters = new HashMap<>();
        customerIds.add(tct1_ct1.getId());
        MassPushRequestDto requestDto = new MassPushRequestDto();
        requestDto.setCustomerIds(customerIds);
        requestDto.setParameters(parameters);
        requestDto.setStrategy(PushStrategy.BATCH);
        requestDto.setParameters(new HashMap<>());

        Long tid;
        Optional<Template> template = templateRepository.findByName("fake_template_3");
        if (template.isPresent()) {
            tid = template.get().getId();
            requestDto.setTemplateId(tid);
        } else {
            fail();
        }

        sendMassage();
        MvcResult res = mvc.perform(post("/pushes/evaluate")
                .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                .content(objectMapper.writeValueAsString(requestDto))
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.actualNumber").value(1))
                .andReturn();
        System.out.println(res.getResponse().getContentAsString());
    }
}