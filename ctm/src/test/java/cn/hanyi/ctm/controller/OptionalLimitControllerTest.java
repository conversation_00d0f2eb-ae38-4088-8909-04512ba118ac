package cn.hanyi.ctm.controller;

import cn.hanyi.ctm.BaseTest;
import cn.hanyi.ctm.MockServiceConfiguration;
import cn.hanyi.ctm.TestRedisConfiguration;
import cn.hanyi.ctm.constant.OrganizationOptionalLimitType;
import cn.hanyi.ctm.entity.journey.JourneyMap;
import cn.hanyi.ctm.service.JourneyMapService;
import org.befun.auth.dto.AppVersionDto;
import org.befun.auth.dto.OrganizationOptionalLimitDto;
import org.befun.auth.entity.Organization;
import org.befun.auth.entity.User;
import org.befun.auth.repository.OrganizationRepository;
import org.befun.auth.repository.UserRepository;
import org.befun.core.utils.JsonHelper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.web.servlet.MockMvc;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {MockServiceConfiguration.class, TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
class OptionalLimitControllerTest extends BaseTest {
    @Autowired
    protected MockMvc mvc;
    @Autowired
    protected OrganizationRepository organizationRepository;
    @Autowired
    protected UserRepository userRepository;
    @Autowired
    protected JourneyMapService journeyMapService;

    @Test
    public void childUserLimitCheck() throws Exception {
        Organization organization = new Organization();
        organization.setId(1L);
        organization.setName("test");
        organization.setOptionalLimit(JsonHelper.toJson(new OrganizationOptionalLimitDto(1, 1, 1, 1, 1, 1, false, false, false, 1)));
        organizationRepository.save(organization);

        mvc.perform(get(String.format("/optional-limit/%s", OrganizationOptionalLimitType.child_user_limit.name()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.size").value(1));

        User user = new User();
        user.setOrgId(1L);
        userRepository.save(user);

        mvc.perform(get(String.format("/optional-limit/%s", OrganizationOptionalLimitType.child_user_limit.name()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.size").value(1));

    }

    @Test
    public void CustomerLifecycleLimitCheck() throws Exception {
        Organization organization = new Organization();
        organization.setId(1L);
        organization.setName("test");
        organization.setOptionalLimit(JsonHelper.toJson(new OrganizationOptionalLimitDto(1, 2, 1, 1, 1, 1, false, false, false, 1)));
        organization.setVersion(JsonHelper.toJson(new AppVersionDto("base", "empty")));
        organizationRepository.save(organization);

        mvc.perform(get(String.format("/optional-limit/%s", OrganizationOptionalLimitType.customer_lifecycle_limit.name()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.size").value(1));

        JourneyMap journeyMap = new JourneyMap();
        journeyMap.setOrgId(1L);
        journeyMap.setTitle("test");
        journeyMap.setDefaultDateFilter("last_one_month");

        journeyMapRepository.save(journeyMap);

        mvc.perform(get(String.format("/optional-limit/%s", OrganizationOptionalLimitType.customer_lifecycle_limit.name()))
                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
                        .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.data.size").value(0));

    }

}