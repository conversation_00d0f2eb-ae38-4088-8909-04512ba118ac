package cn.hanyi.ctm.controller;

import cn.hanyi.ctm.AssertController;
import cn.hanyi.ctm.MockServiceConfiguration;
import cn.hanyi.ctm.TestRedisConfiguration;
import cn.hanyi.ctm.constant.journeymap.ElementTextBoxType;
import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import cn.hanyi.ctm.entity.journey.*;
import cn.hanyi.ctm.repository.*;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {MockServiceConfiguration.class, TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class JourneyMapPublishControllerTest extends AssertController {

    @Autowired
    private JourneyMapRepository journeyMapRepository;
    @Autowired
    private JourneyRepository journeyRepository;
    @Autowired
    private JourneyComponentRepository journeyComponentRepository;
    @Autowired
    private ElementTextBoxRepository elementTextBoxRepository;
    @Autowired
    private ElementCurveRepository elementCurveRepository;
    @Autowired
    private ExperienceIndicatorRepository experienceIndicatorRepository;
    @Autowired
    private ExperienceInteractionRepository experienceInteractionRepository;
    @Autowired
    private JourneyPublishRepository journeyPublishRepository;
    @Autowired
    private JourneyComponentPublishRepository journeyComponentPublishRepository;
    @Autowired
    private ElementTextBoxPublishRepository elementTextBoxPublishRepository;
    @Autowired
    private ElementCurvePublishRepository elementCurvePublishRepository;
    @Autowired
    private ExperienceIndicatorPublishRepository experienceIndicatorPublishRepository;
    @Autowired
    private ExperienceInteractionPublishRepository experienceInteractionPublishRepository;

//    @Test
    public void publish() throws Exception {
        JourneyMap journeyMap = initJourney();

        getOk("/journey-maps/get", mock_token_org1_user1, actions ->
                actions.andExpect(jsonPath("$.data.id").value(journeyMap.getId())));

        postOk("/journey-maps/publish", null, mock_token_org1_user1, actions ->
                actions.andExpect(jsonPath("$.data").value(true))
        );
        assertEquals(1, journeyMapRepository.count());
        assertEquals(7, journeyComponentPublishRepository.count());
        assertEquals(3, journeyPublishRepository.count());
        assertEquals(2, elementTextBoxPublishRepository.count());
        assertEquals(3, elementCurvePublishRepository.count());

    }


    private JourneyMap initJourney() {
        JourneyMap journeyMap = new JourneyMap();
        journeyMap.setOrgId(1L);
        journeyMap.setTitle("test");
        journeyMap.setDefaultDateFilter("last_one_month");
        journeyMapRepository.save(journeyMap);
        JourneyComponent journey = new JourneyComponent(1L, journeyMap.getId(), 0, "场景", JourneyComponentType.journey);
        JourneyComponent journeyDescriptions = new JourneyComponent(1L, journeyMap.getId(), 1, "场景说明", JourneyComponentType.journey_descriptions);
        JourneyComponent textbox = new JourneyComponent(1L, journeyMap.getId(), 2, "触点", JourneyComponentType.textbox);
        JourneyComponent experienceIndicator = new JourneyComponent(1L, journeyMap.getId(), 3, "体验指标", JourneyComponentType.experience_indicator);
        JourneyComponent curve = new JourneyComponent(1L, journeyMap.getId(), 4, "情绪曲线", JourneyComponentType.curve);
        JourneyComponent experienceMatrix = new JourneyComponent(1L, journeyMap.getId(), 5, "体验矩阵", JourneyComponentType.experience_matrix);
        JourneyComponent experienceInteraction = new JourneyComponent(1L, journeyMap.getId(), 6, "体验互动", JourneyComponentType.experience_interaction);
        List<JourneyComponent> journeyComponents = Lists.newArrayList(
                journey, journeyDescriptions, textbox, experienceIndicator, curve, experienceMatrix, experienceInteraction
        );
        journeyComponentRepository.saveAll(journeyComponents);
        Journey journeyOne = new Journey(1L, journey.getId(), "售前", "#4b8cf5", 0);
        Journey journeyTwo = new Journey(1L, journey.getId(), "售中", "#e04944", 1);
        Journey journeyThree = new Journey(1L, journey.getId(), "售后", "#F1B405", 2);
        journeyRepository.saveAll(Lists.newArrayList(journeyOne, journeyTwo, journeyThree));
        List<ElementTextBox> elementTextBoxes = Lists.newArrayList(
                new ElementTextBox(journeyDescriptions.getId(), 1L, ElementTextBoxType.description, new ArrayList<>()),
                new ElementTextBox(textbox.getId(), 1L, ElementTextBoxType.touch_point, new ArrayList<>()));
        elementTextBoxRepository.saveAll(elementTextBoxes);
        List<ElementCurve> elementCurves = Lists.newArrayList(
                new ElementCurve(curve.getId(), 1L, journeyOne.getId()),
                new ElementCurve(curve.getId(), 1L, journeyTwo.getId()),
                new ElementCurve(curve.getId(), 1L, journeyThree.getId()));
        elementCurveRepository.saveAll(elementCurves);
        return journeyMap;
    }

}