package cn.hanyi.ctm.controller;

import cn.hanyi.ctm.BaseTest;
import cn.hanyi.ctm.MockServiceConfiguration;
import cn.hanyi.ctm.TestRedisConfiguration;
import cn.hanyi.ctm.connector.PlatformConnectorManager;
import cn.hanyi.ctm.dto.connector.ThirdPartyCustomerDto;
import cn.hanyi.ctm.dto.connector.ThirdPartyTemplateDto;
import cn.hanyi.ctm.entity.Customer;
import cn.hanyi.ctm.entity.ThirdPartyCustomer;
import cn.hanyi.ctm.entity.ThirdPartyTemplate;
import cn.hanyi.ctm.repository.ConnectorRepository;
import cn.hanyi.ctm.repository.TemplateRepository;
import cn.hanyi.ctm.repository.ThirdPartyTemplateRepository;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import javax.persistence.EntityManager;
import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.BDDMockito.given;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

/**
 * The class description
 *
 * <AUTHOR>
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {MockServiceConfiguration.class, TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class ConnectorControllerTest extends BaseTest {
    @Autowired
    protected MockMvc mvc;
    @Autowired
    private ConnectorRepository repository;

    @Autowired
    private TemplateRepository templateRepository;

    @Autowired
    private ThirdPartyTemplateRepository thirdPartyTemplateRepository;

    @Autowired
    protected EntityManager entityManager;

    @Autowired
    protected PlatformConnectorManager connectorManager;

//    @Test
//    public void findAllConnectors() throws Exception {
//        MvcResult res = mvc.perform(get("/connectors")
//                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
//                        .contentType(MediaType.APPLICATION_JSON))
//                .andExpect(status().isOk())
//                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
//                .andExpect(jsonPath("$.meta.total").value(6))
//                .andExpect(jsonPath("$.items[0].name").value("c1"))
//                .andExpect(jsonPath("$.items[1].name").value("c2")).andReturn();
//        System.out.println(res.getResponse().getContentAsString());
//    }

//    @Test
//    public void findConnector() throws Exception {
//        MvcResult res = mvc.perform(get(String.format("/connectors/%d", c1.getId()))
//                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
//                        .contentType(MediaType.APPLICATION_JSON))
//                .andExpect(status().isOk())
//                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
//                .andExpect(jsonPath("$.data.id").value(c1.getId()))
//                .andExpect(jsonPath("$.data.authorizeStatus").value("AUTHORIZED"))
//                .andExpect(jsonPath("$.data.status").value("READY"))
//                .andReturn();
//        System.out.println(res.getResponse().getContentAsString());
//    }

    //    @Test
//    void syncCustomer() throws Exception {
//        List<ThirdPartyCustomerDto> customerDtoList = new ArrayList<>();
//        customerDtoList.add(ThirdPartyCustomerDto.builder()
//                .name("tpc_c_1")
//                .openId("tp_c_1_openId")
//                .build()
//        );
//        given(wechatProvider.fetchCustomer(any())).willReturn(customerDtoList);
//
//        MvcResult res = mvc.perform(post(String.format("/connectors/%d/sync-customer", c1.getId()))
//                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
//                        .contentType(MediaType.APPLICATION_JSON))
//                .andExpect(status().isOk())
//                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
//                .andReturn();
//        System.out.println("result");
//        System.out.println(res.getResponse().getContentAsString());
//        Thread.sleep(5000);
//        Optional<ThirdPartyCustomer> response = thirdPartyCustomerRepository.findThirdPartyCustomerByConnectorAndOpenId(c1, "tp_c_1_openId");
//        System.out.println(response);
//        Assert.assertTrue(response.isPresent());
//    }

//    @Test
//    void syncTemplate() throws Exception {
//        List<ThirdPartyTemplateDto> templateList = new ArrayList<>();
//        templateList.add(ThirdPartyTemplateDto.builder()
//                .title("tpt_c_1")
//                .content("third template t1")
//                .build()
//        );
//        given(wechatProvider.fetchTemplate(any()))
//                .willReturn(templateList);
//
//        MvcResult res = mvc.perform(post(String.format("/connectors/%d/sync-template", c1.getId()))
//                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
//                        .contentType(MediaType.APPLICATION_JSON))
//                .andExpect(status().isOk())
//                .andReturn();
//        System.out.println(res.getResponse().getContentAsString());
//        List<ThirdPartyTemplate> thirdPartyTemplateList = thirdPartyTemplateRepository.findAllByConnector(c1);
//        Assert.assertEquals(thirdPartyTemplateList.size(), 2);
//    }

//    @Test
//    void findAllThirdPartyTemplates() throws Exception {
//        MvcResult res = mvc.perform(get(String.format("/thirdparty-templates?connector=%d", c1.getId()))
//                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
//                        .contentType(MediaType.APPLICATION_JSON))
//                .andExpect(status().isOk())
//                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
//                .andExpect(jsonPath("$.meta.total").value(1))
//                .andExpect(jsonPath("$.items[0].name").value("fake_thirdTemplate_1"))
//                .andReturn();
//        System.out.println(res.getResponse().getContentAsString());
//    }

//    @Test
//    void findAllTemplates() throws Exception {
//        MvcResult res = mvc.perform(get(String.format("/templates?connector=%d", c1.getId()))
//                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
//                        .contentType(MediaType.APPLICATION_JSON))
//                .andExpect(status().isOk())
//                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
//                .andExpect(jsonPath("$.meta.total").value(2))
//                .andExpect(jsonPath("$.items[0].name").value("fake_template_1"))
//                .andReturn();
//        System.out.println(res.getResponse().getContentAsString());
//    }

//    @Test
//    void deleteConnector() throws Exception {
//        MvcResult res = mvc.perform(delete(String.format("/connectors/%d?delete_user=false", c1.getId()))
//                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
//                        .contentType(MediaType.APPLICATION_JSON))
//                .andExpect(status().isOk())
//                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
//                .andReturn();
//        Optional<Customer> response = customerRepository.findCustomerByThirdPartyCustomerId(tct1.getId());
//        List<ThirdPartyTemplate> thirdPartyTemplateList = thirdPartyTemplateRepository.findAllByConnector(c1);
//        Assert.assertTrue(response.isPresent());
//        Assert.assertEquals(thirdPartyTemplateList.size(), 0);
//    }

//    @Test
//    void deleteConnectorAndCustomer() throws Exception {
//        MvcResult res = mvc.perform(delete(String.format("/connectors/%d?delete_user=true", c1.getId()))
//                        .header(HttpHeaders.AUTHORIZATION, mock_token_1)
//                        .contentType(MediaType.APPLICATION_JSON))
//                .andExpect(status().isOk())
//                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
//                .andReturn();
//        Optional<Customer> response = customerRepository.findCustomerByThirdPartyCustomerId(tct1.getId());
//        List<ThirdPartyTemplate> thirdPartyTemplateList = thirdPartyTemplateRepository.findAllByConnector(c1);
//        Assert.assertTrue(response.isEmpty());
//        Assert.assertEquals(thirdPartyTemplateList.size(), 0);
//
//    }
}
