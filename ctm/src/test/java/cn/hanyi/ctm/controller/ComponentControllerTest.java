package cn.hanyi.ctm.controller;//package cn.hanyi.ctm.controller;
//
//import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete;
//import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
//import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
//import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
//import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
//import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;
//
//import cn.hanyi.ctm.BaseTest;
//import cn.hanyi.ctm.MockServiceConfiguration;
//import cn.hanyi.ctm.TestRedisConfiguration;
//import cn.hanyi.ctm.connector.PlatformConnectorManager;
//import cn.hanyi.ctm.constant.RecordType;
//import cn.hanyi.ctm.constant.SendStatus;
//import cn.hanyi.ctm.constant.journeymap.ElementTextBoxType;
//import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
//import cn.hanyi.ctm.dto.journey.JourneyComponentDto;
//import cn.hanyi.ctm.dto.journey.JourneyOrderDto;
//import cn.hanyi.ctm.dto.survey.SurveyResponseMessageDto;
//import cn.hanyi.ctm.entity.CustomerAnswers;
//import cn.hanyi.ctm.entity.CustomerHistoryRecord;
//import cn.hanyi.ctm.entity.CustomerJourneyRecord;
//import cn.hanyi.ctm.entity.journey.ElementCurve;
//import cn.hanyi.ctm.entity.journey.ExperienceInteraction;
//import cn.hanyi.ctm.entity.journey.Journey;
//import cn.hanyi.ctm.entity.journey.JourneyComponent;
//import cn.hanyi.ctm.repository.ConnectorRepository;
//import cn.hanyi.ctm.repository.PushRepository;
//import cn.hanyi.ctm.repository.customer.CustomerAnswersRepository;
//import cn.hanyi.ctm.repository.customer.CustomerHistoryRecordsRepository;
//import cn.hanyi.ctm.repository.customer.CustomerJourneyRecordsRepository;
//import cn.hanyi.ctm.repository.journeyMap.ElementCurveRepository;
//import cn.hanyi.ctm.repository.journeyMap.ExperienceInteractionRepository;
//import cn.hanyi.ctm.service.CustomerService;
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.Optional;
//import javax.persistence.EntityManager;
//import org.assertj.core.api.AssertionsForClassTypes;
//import org.junit.jupiter.api.Assertions;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.http.HttpHeaders;
//import org.springframework.http.MediaType;
//import org.springframework.test.annotation.DirtiesContext;
//import org.springframework.test.util.AssertionErrors;
//import org.springframework.test.web.servlet.MockMvc;
//import org.springframework.test.web.servlet.MvcResult;
//import org.springframework.transaction.annotation.Transactional;
//import org.w3c.dom.Element;
//
///**
// * The class description
// *
// * <AUTHOR>
// */
//@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {MockServiceConfiguration.class, TestRedisConfiguration.class})
//@AutoConfigureMockMvc
//@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
//public class ComponentControllerTest extends BaseTest {
//    @Autowired
//    protected MockMvc mvc;
//    @Autowired
//    protected ExperienceInteractionRepository experienceInteractionRepository;
//    @Autowired
//    protected ElementCurveRepository elementCurveRepository;
//
//    @Transactional
//    @Test void addComponent() throws Exception {
//        JourneyComponentDto journeyComponentDto = new JourneyComponentDto();
//        //        添加文本框组件生成默认元素
//        journeyComponentDto.setTitle("foo");
//        journeyComponentDto.setElementType(ElementTextBoxType.description);
//        journeyComponentDto.setType(JourneyComponentType.textbox);
//        journeyComponentDto.setOrder(1);
//        MvcResult res = mvc.perform(post("/components")
//                .header(HttpHeaders.AUTHORIZATION, mock_token_1)
//                .content(objectMapper.writeValueAsString(journeyComponentDto))
//                .contentType(MediaType.APPLICATION_JSON))
//                .andExpect(status().isOk())
//                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
//                .andExpect(jsonPath("$.data.elements").isNotEmpty())
//                .andReturn();
//        System.out.println(res.getResponse().getContentAsString());
////        添加情绪曲线组件生成默认元素
//        journeyComponentDto.setTitle("bar");
//        journeyComponentDto.setType(JourneyComponentType.curve);
//        MvcResult res2 = mvc.perform(post("/components")
//                .header(HttpHeaders.AUTHORIZATION, mock_token_1)
//                .content(objectMapper.writeValueAsString(journeyComponentDto))
//                .contentType(MediaType.APPLICATION_JSON))
//                .andExpect(status().isOk())
//                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
//                .andExpect(jsonPath("$.data.elements").isNotEmpty())
//                .andReturn();
//        System.out.println(res2.getResponse().getContentAsString());
//    }
//
//    @Test void updateComponentsOrder() throws Exception {
//        JourneyOrderDto dto = new JourneyOrderDto();
//        dto.setOrder(1);
//        dto.setId(jm1_jc1.getId());
//        MvcResult res = mvc.perform(post("/components/update-order")
//                .header(HttpHeaders.AUTHORIZATION, mock_token_1)
//                .content(objectMapper.writeValueAsString(new ArrayList<>(){{add(dto);}}))
//                .contentType(MediaType.APPLICATION_JSON))
//                .andExpect(status().isOk())
//                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
//                .andReturn();
//        System.out.println(res.getResponse().getContentAsString());
//        Optional<JourneyComponent> journeyComponent = journeyComponentRepository.findById(jm1_jc1.getId());
//        Assertions.assertTrue(journeyComponent.isPresent());
//        Assertions.assertEquals(1, (int) journeyComponent.get().getOrder());
//    }
//
//    @Test void createExperienceInteraction() throws Exception {
//        JourneyComponent journeyComponent = new JourneyComponent();
//        journeyComponent.setJourneyMap(jm1);
//        journeyComponent.setOrgId(mock_org_1_id);
//        journeyComponent.setTitle("foo");
//        journeyComponent.setType(JourneyComponentType.experience_interaction);
//        journeyComponentRepository.save(journeyComponent);
//        ExperienceInteraction experienceInteraction = new ExperienceInteraction();
//        experienceInteraction.setComponent(journeyComponent);
//        experienceInteraction.setJourneyId(jm1_jc1_j1.getId());
//        experienceInteractionRepository.save(experienceInteraction);
//        MvcResult res = mvc.perform(post(String.format("/components/%s/experience-interaction", jm1_jc1.getId()))
//                .header(HttpHeaders.AUTHORIZATION, mock_token_1)
//                .content(objectMapper.writeValueAsString(experienceInteraction))
//                .contentType(MediaType.APPLICATION_JSON))
//                .andExpect(status().isOk())
//                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
//                .andReturn();
//        System.out.println(res.getResponse().getContentAsString());
//        Optional<Journey> journey = journeyRepository.findById(jm1_jc1_j1.getId());
//        Assertions.assertTrue(journey.isPresent());
//        Assertions.assertEquals(experienceInteraction.getId(), journey.get().getExperienceInteractionId());
//    }
//
//    @Transactional
//    @Test void createJourney() throws Exception {
//        Journey jm1_jc1_j1_1_1 = new Journey();
//        jm1_jc1_j1_1_1.setOrgId(mock_org_1_id);
//        jm1_jc1_j1_1_1.setParentId(jm1_jc1_j1_1.getId());
//        MvcResult res = mvc.perform(post(String.format("/components/%d/journeys", jm1_jc1.getId()))
//                .header(HttpHeaders.AUTHORIZATION, mock_token_1)
//                .content(objectMapper.writeValueAsString(jm1_jc1_j1_1_1))
//                .contentType(MediaType.APPLICATION_JSON))
//                .andExpect(status().isOk())
//                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
//                .andReturn();
//        System.out.println(res.getResponse().getContentAsString());
//        Optional<Journey> journey = journeyRepository.findById(jm1_jc1_j1_1.getId());
//        journey.ifPresentOrElse(j -> Assertions.assertEquals(0, j.getIsLeaf()), Assertions::fail);
//        Assertions.assertTrue(elementCurveRepository.findAll().size() > 0);
//    }
//
//    @Test void deleteJourney() throws Exception {
//        MvcResult res = mvc.perform(delete(String.format("/components/%d/journeys/%d", jm1_jc1.getId(), jm1_jc1_j1.getId()))
//                .header(HttpHeaders.AUTHORIZATION, mock_token_1)
//                .contentType(MediaType.APPLICATION_JSON))
//                .andExpect(status().isOk())
//                .andExpect(content().contentTypeCompatibleWith(MediaType.APPLICATION_JSON))
//                .andReturn();
//        System.out.println(res.getResponse().getContentAsString());
//        Optional<Journey> journey = journeyRepository.findById(jm1_jc1_j1.getId());
//        Optional<Journey> journeyChildren = journeyRepository.findById(jm1_jc1_j1_1.getId());
//        Assertions.assertFalse(journey.isPresent());
//        Assertions.assertFalse(journeyChildren.isPresent());
//    }
//}
