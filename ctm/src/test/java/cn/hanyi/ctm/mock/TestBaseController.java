package cn.hanyi.ctm.mock;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.befun.core.entity.BaseEntity;
import org.befun.core.repository.ResourceRepository;
import org.junit.jupiter.api.Assertions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.repository.support.Repositories;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.util.Assert;
import org.springframework.web.context.WebApplicationContext;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;


public class TestBaseController {

    @Autowired
    private WebApplicationContext appContext;
    @Autowired
    protected MockMvc mvc;
    private Repositories repositories;
    protected static ObjectMapper objectMapper;


    static {
        objectMapper = new ObjectMapper();
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
    }

    public <E extends BaseEntity> E findById(Class<E> eClass, long id) {
        return findFirst(eClass, Map.of("id", id));
    }

    public <E extends BaseEntity> E findFirst(Class<E> eClass, Map<String, Object> search) {
        return findAll(eClass, search).stream().findFirst().orElse(null);
    }

    public <E extends BaseEntity> List<E> findAll(Class<E> eClass, Map<String, Object> search) {
        return getRepository(eClass).findAll((root, query, builder) -> {
            List<Predicate> list = new ArrayList<>();
            search.forEach((k, v) -> list.add(builder.equal(root.get(k), v)));
            return builder.and(list.toArray(new Predicate[0]));
        });
    }

    public <AE extends BaseEntity, ID> ResourceRepository<AE, ID> getRepository(Class<AE> entityClass) {
        if (this.repositories == null) {
            Assert.notNull(this.appContext, "generic repository should be access in mvc context");
            this.repositories = new Repositories(this.appContext);
        }

        Assert.notNull(this.repositories, "missing repositories injected");
        Optional<Object> a = this.repositories.getRepositoryFor(entityClass);
        if (a.isPresent()) {
            return (ResourceRepository) a.get();
        } else {
            throw new RuntimeException("Missing Repository for " + entityClass.getName());
        }
    }
}
