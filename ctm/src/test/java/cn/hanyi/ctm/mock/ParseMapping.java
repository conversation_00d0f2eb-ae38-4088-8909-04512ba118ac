package cn.hanyi.ctm.mock;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.befun.core.utils.JsonHelper;
import org.springframework.cglib.proxy.Enhancer;
import org.springframework.cglib.proxy.MethodInterceptor;
import org.springframework.cglib.proxy.MethodProxy;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.net.URI;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

public class ParseMapping<C> implements MethodInterceptor {

    private MockMvc mvc;
    private ObjectMapper objectMapper;
    private Class<C> controllerClass;
    private Consumer<C> callMethod;
    private List<String> pathParams;
    private String query;
    private Object body;
    private String token;
    private ConsumerThrowable<ResultActions> consumer;
    private String response;
    private boolean exec = false;

    private ParseMapping() {
    }

    private ParseMapping<C> cloneOrThis() {
        if (this.exec) {
            return ParseMapping.builder(mvc, objectMapper, controllerClass).token(token);
        }
        return this;
    }

    public static <C> ParseMapping<C> builder(MockMvc mvc, ObjectMapper objectMapper, Class<C> controllerClass) {
        return new ParseMapping<C>().mvc(mvc).objectMapper(objectMapper).controllerClass(controllerClass);
    }

    public ParseMapping<C> isOk() {
        return isOk(null);
    }

    public ParseMapping<C> isOk(ConsumerThrowable<ResultActions> appendConsumer) {
        return exec(results -> {
            results.andExpect(status().isOk())
                    .andExpect(jsonPath("$.code").value(200));
            if (appendConsumer != null) {
                appendConsumer.accept(results);
            }
        });
    }

    public ParseMapping<C> is400() {
        return is400(null);
    }

    public ParseMapping<C> is400(ConsumerThrowable<ResultActions> appendConsumer) {
        return exec(results -> {
            results.andExpect(status().is(400));
            if (appendConsumer != null) {
                appendConsumer.accept(results);
            }
        });
    }

    @SuppressWarnings("unchecked")
    public ParseMapping<C> exec(ConsumerThrowable<ResultActions> consumer) {
        exec = true;
        consumer(consumer);
        Enhancer enhancer = new Enhancer();
        enhancer.setSuperclass(controllerClass);
        enhancer.setCallback(this);
        C controller = (C) enhancer.create();
        callMethod.accept(controller);
        return this;
    }

    public <V> V parseResponse(String key) {
        Object value = null;
        Map<String, Object> map = JsonHelper.toMap(response);
        String[] keys = key.split("\\.");
        if (keys.length > 1) {
            Iterator<String> iterable = Arrays.stream(keys).iterator();
            while (iterable.hasNext()) {
                String n = iterable.next();
                value = map.get(n);
                if (iterable.hasNext()) {
                    map = (Map<String, Object>) value;
                }
            }
        }
        return (V) value;
    }

    @FunctionalInterface
    public interface ConsumerThrowable<T> {
        void accept(T t) throws Exception;
    }

    @Override
    public Object intercept(Object o, Method method, Object[] objects, MethodProxy methodProxy) throws Throwable {
        String[] paths = {""};
        HttpMethod httpMethod = parseMethod(method, paths);
        String path = paths[0];
        if (path != null && httpMethod != null) {
            String url = concatUrl(path);
            MockHttpServletRequestBuilder builder = MockMvcRequestBuilders.request(httpMethod, URI.create(url));
            if (StringUtils.hasText(token)) {
                builder.header(HttpHeaders.AUTHORIZATION, token);
            }
            if (body != null) {
                builder.contentType(MediaType.APPLICATION_JSON).content(objectMapper.writeValueAsString(body));
            }
            ResultActions actions = mvc.perform(builder);
            if (consumer != null) {
                consumer.accept(actions);
            }
            response = actions.andReturn().getResponse().getContentAsString();
        }
        return null;
    }

    private HttpMethod parseMethod(Method method, String[] paths) {
        String path = null;
        HttpMethod httpMethod = null;
        for (Annotation a : method.getAnnotations()) {
            if (a.annotationType() == GetMapping.class) {
                GetMapping mapping = ((GetMapping) a);
                path = mapping.value().length == 0 ? "" : mapping.value()[0];
                httpMethod = HttpMethod.GET;
                break;
            } else if (a.annotationType() == PostMapping.class) {
                PostMapping mapping = ((PostMapping) a);
                path = mapping.value().length == 0 ? "" : mapping.value()[0];
                httpMethod = HttpMethod.POST;
                break;
            } else if (a.annotationType() == PutMapping.class) {
                PutMapping mapping = ((PutMapping) a);
                path = mapping.value().length == 0 ? "" : mapping.value()[0];
                httpMethod = HttpMethod.PUT;
                break;
            } else if (a.annotationType() == DeleteMapping.class) {
                DeleteMapping mapping = ((DeleteMapping) a);
                path = mapping.value().length == 0 ? "" : mapping.value()[0];
                httpMethod = HttpMethod.DELETE;
                break;
            } else if (a.annotationType() == RequestMapping.class) {
                RequestMapping mapping = ((RequestMapping) a);
                path = mapping.value().length == 0 ? "" : mapping.value()[0];
                httpMethod = HttpMethod.resolve(((RequestMapping) a).method()[0].name());
                break;
            }
        }
        paths[0] = path;
        return httpMethod;
    }

    private String concatUrl(String path) {
        String root = "/";
        RequestMapping requestMapping = controllerClass.getAnnotation(RequestMapping.class);
        if (requestMapping != null) {
            root = requestMapping.value()[0];
            if (!root.isEmpty() && !root.equals("/")) {
                if (!root.startsWith("/")) {
                    root = "/" + root;
                }
                if (!root.endsWith("/")) {
                    root = root + "/";
                }
            }
        }
        if (path.startsWith("/")) {
            path = path.substring(1);
        }
        String url = root + path;
        if (url.contains("{")) {
            Pattern pattern = Pattern.compile("(\\{[\\w-$]+})");
            Matcher matcher = pattern.matcher(url);
            int i = 0;
            StringBuilder ss = new StringBuilder();
            while (matcher.find()) {
                matcher.appendReplacement(ss, pathParams.get(i));
                i++;
            }
            matcher.appendTail(ss);
            url = ss.toString();
        }
        return query == null ? url : (url + "?" + query);
    }

    private ParseMapping<C> mvc(MockMvc mvc) {
        this.mvc = mvc;
        return this;
    }

    private ParseMapping<C> objectMapper(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
        return this;
    }

    private ParseMapping<C> controllerClass(Class<C> controllerClass) {
        this.controllerClass = controllerClass;
        return this;
    }

    public ParseMapping<C> callMethod(Consumer<C> callMethod) {
        ParseMapping<C> cloneOrThis = cloneOrThis();
        cloneOrThis.callMethod = callMethod;
        return cloneOrThis;
    }

    public ParseMapping<C> pathParams(List<String> pathParams) {
        this.pathParams = pathParams;
        return this;
    }

    public ParseMapping<C> query(String query) {
        this.query = query;
        return this;
    }

    public ParseMapping<C> body(Object body) {
        this.body = body;
        return this;
    }

    public ParseMapping<C> token(String token) {
        this.token = token;
        return this;
    }

    public ParseMapping<C> consumer(ConsumerThrowable<ResultActions> consumer) {
        this.consumer = consumer;
        return this;
    }

}
