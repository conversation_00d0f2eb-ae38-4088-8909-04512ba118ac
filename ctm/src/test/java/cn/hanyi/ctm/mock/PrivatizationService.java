package cn.hanyi.ctm.mock;

import org.befun.auth.constant.AppVersion;
import org.befun.auth.constant.RoleType;
import org.befun.auth.dto.AppVersionDto;
import org.befun.auth.dto.OrganizationOptionalLimitDto;
import org.befun.auth.entity.*;
import org.befun.auth.repository.*;
import org.befun.auth.service.UserRoleService;
import org.befun.auth.utils.PasswordHelper;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

@Service
public class PrivatizationService {

    @Autowired
    private OrganizationRepository organizationRepository;
    @Autowired
    private OrganizationWalletRepository organizationWalletRepository;
    @Autowired
    private DepartmentRepository departmentRepository;
    @Autowired
    private RoleRepository roleRepository;
    @Autowired
    private UserRepository userRepository;
    @Autowired
    private UserRoleService userRoleService;

    public void init(String orgName, String orgCode, String email, String mobile, String password) {
        // init org & wallet
        // init role (admin & member)
        // init department
        // init user (super admin & user role)

        Date s = new Date();
        Date e = Date.from(LocalDateTime.now().plusYears(20).atZone(ZoneId.systemDefault()).toInstant());
        int created = (int) (System.currentTimeMillis() / 1000);

        String optionalLimit = JsonHelper.toJson(new OrganizationOptionalLimitDto(1000000, 1000000, 1000000, 1000000, 1000000, 1000000, true, true, true, 1000));
        String appVersion = JsonHelper.toJson(new AppVersionDto(AppVersion.PROFESSION.getText(), "base"));
        // org wallet
        Organization org = new Organization();
        org.setId(1L);
        org.setName(orgName);
        org.setCode(orgCode);
        org.setIndustryId(9L);
        org.setMaxUsers(Integer.MAX_VALUE);
        org.setOwnerId(1L);
        org.setAvailableDateBegin(s);
        org.setAvailableDateEnd(e);
        org.setIsBlock(0);
        org.setIsTemplate(0);
        org.setOptionalLimit(optionalLimit);
        org.setVersion(appVersion);
        org.setCreated(String.valueOf(created));
        org.setUpdated(String.valueOf(created));
        organizationRepository.save(org);
        OrganizationWallet wallet = new OrganizationWallet();
        wallet.setId(1L);
        wallet.setOrgId(org.getId());
        wallet.setMoney(0);
        wallet.setSms(Integer.MAX_VALUE);
        wallet.setOrgId(org.getId());
        organizationWalletRepository.save(wallet);

        // department
        Department department = new Department();
        department.setOrgId(1L);
        department.setTitle(orgName);
        department.setPid(0L);
        department.setParentIds("1");
        department.setParentNames(orgName);
        departmentRepository.save(department);

        // user
        User user = new User();
        user.setPassword(PasswordHelper.encrypt(password));
        user.setPasswordStrength(PasswordHelper.passwordStrength(password));
        user.setMobile(mobile);
        user.setEmail(email);
        user.setIsAdmin(true);
        user.setTruename("超级管理员");
        user.setNickname("超级管理员");
        user.setAvailableSystems("{\"login_surveyplus\":1,\"login_cem\":1}");
        user.setStatus(1);
        user.setIsFinishedGuide("N");
        user.setCreated(created);
        user.setUpdated(created);
        user.setIsDelete(0);
        user.setOrgId(1L);
        user.appendDepartmentId(1L);
        userRepository.save(user);

        // role permission userRole
        Role superAdmin = RoleType.SUPER_ADMIN.createRole(1L);
        superAdmin.setId(1L);
        roleRepository.save(superAdmin);
        userRoleService.addUserRole(user, superAdmin);

        Role memberRole = RoleType.MEMBER.createRole(1L);
        memberRole.setId(2L);
        roleRepository.save(memberRole);
    }

}
