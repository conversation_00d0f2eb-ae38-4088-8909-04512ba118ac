package cn.hanyi.ctm.mock;

import io.swagger.v3.oas.annotations.Parameter;
import org.befun.auth.dto.LoginPasswordRequestDto;
import org.befun.auth.dto.LoginResponseDto;
import org.befun.auth.service.AuthService;
import org.befun.core.dto.ResourceResponseDto;
import org.befun.core.rest.context.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;


@RestController
@Validated
public class LoginController {

    @Autowired
    private AuthService authService;

    @PostMapping("/login/password/{app}")
    public ResourceResponseDto<LoginResponseDto> loginPassword(
            @Parameter(name = "app", description = "默认cem") @PathVariable("app") String app,
            @Valid @NotNull @RequestBody LoginPasswordRequestDto dto) {
        TenantContext.clear();
        return new ResourceResponseDto<>((LoginResponseDto) authService.login("password", app, dto));
    }
}
