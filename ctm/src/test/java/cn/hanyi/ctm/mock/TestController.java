package cn.hanyi.ctm.mock;

import cn.hanyi.ctm.TestRedisConfiguration;
import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import cn.hanyi.ctm.controller.journey.*;
import cn.hanyi.ctm.entity.EventMonitorRules;
import cn.hanyi.ctm.entity.journey.*;
import cn.hanyi.ctm.service.stat.CacheStatEventService;
import cn.hanyi.ctm.service.stat.CacheStatIndicatorService;
import org.befun.auth.workertrigger.IAuthEventTrigger;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.annotation.DirtiesContext;
import org.springframework.test.context.jdbc.Sql;

import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.BDDMockito.given;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

@Sql(scripts = "classpath:init.sql")
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class TestController extends TestBaseController {

    @Autowired
    protected PrivatizationService privatizationService;
    private String token = null;

    @MockBean
    private CacheStatIndicatorService cacheStatIndicatorService;
    @MockBean
    private CacheStatEventService cacheStatEventService;

    private void mockBean() {
        given(cacheStatIndicatorService.getData(any(), any(), any(), any())).willReturn(List.of());
        given(cacheStatIndicatorService.getData(any(), any(), any(), anyBoolean(), any())).willReturn(List.of());
        given(cacheStatEventService.getData(any(), any(), any(), any())).willReturn(List.of());
        given(cacheStatEventService.getData(any(), any(), any(), anyBoolean(), any())).willReturn(List.of());
    }

    public <C> ParseMapping<C> q(Class<C> cClass) {
        return ParseMapping.builder(mvc, objectMapper, cClass).token(token);
    }

    private void login() {
        token = q(LoginController.class)
                .callMethod(c -> c.loginPassword(null, null))
                .pathParams(List.of("cem"))
                .body(Map.of("username", "13000000000", "password", "123456"))
                .isOk().parseResponse("data.token");
    }

    @BeforeEach
    public void setup() {
        privatizationService.init("test", "test", "<EMAIL>", "13000000000", "123456");
        login();
    }

    @Test
    public void testAll() {
        mockBean();
        journeyMap();
    }

    private void journeyMap() {
        Optional.of(q(JourneyMapController.class)).ifPresent(i -> {
            // 创建旅程
            i.callMethod(c -> c.create(null))
                    .body(Map.of("title", "我的旅程"))
                    .isOk(r ->
                            r.andExpect(jsonPath("$.data.success").value(0))
                    );
        });
        JourneyMap journeyMap = findFirst(JourneyMap.class, Map.of());
        assertNotNull(journeyMap);

        Optional.of(q(ComponentController.class)).ifPresent(i -> {
            // 添加事件统计组件
            i.callMethod(c -> c.create(0L, null))
                    .pathParams(List.of(journeyMap.getId().toString()))
                    .body(Map.of("order", 5, "title", "事件统计", "type", "event_stat"))
                    .isOk(r ->
                            r.andExpect(jsonPath("$.data.id").isNumber())
                    );

            // 添加连接器组件
            i.callMethod(c -> c.create(0L, null))
                    .pathParams(List.of(journeyMap.getId().toString()))
                    .body(Map.of("order", 6, "title", "连接器", "type", "linker"))
                    .isOk(r ->
                            r.andExpect(jsonPath("$.data.id").isNumber())
                    );
        });


        JourneyComponent eventStatComponent = findFirst(JourneyComponent.class, Map.of("journeyMapId", journeyMap.getId(), "type", JourneyComponentType.event_stat));
        assertNotNull(eventStatComponent);

        JourneyComponent linkerComponent = findFirst(JourneyComponent.class, Map.of("journeyMapId", journeyMap.getId(), "type", JourneyComponentType.linker));
        assertNotNull(linkerComponent);

        List<Journey> journeys = findAll(Journey.class, Map.of());
        assertNotNull(journeys);
        assertEquals(journeys.size(), 10);
        Journey journey1 = journeys.get(5);
        Journey journey2 = journeys.get(6);
        Journey journey3 = journeys.get(7);

        Long eventStatId1 = addEventStat(journeyMap, eventStatComponent, journey1.getId());
        Long eventStatId2 = addEventStat(journeyMap, eventStatComponent, journey2.getId());
        Long eventStatId3 = addEventStat(journeyMap, eventStatComponent, journey3.getId());

        Long linerId1 = addLinker(journeyMap, linkerComponent, journey1.getId());
        Long linerId2 = addLinker(journeyMap, linkerComponent, journey2.getId());
        Long linerId3 = addLinker(journeyMap, linkerComponent, journey3.getId());


        JourneyComponent journeyComponent = findFirst(JourneyComponent.class, Map.of("journeyMapId", journeyMap.getId(), "type", JourneyComponentType.journey));
        assertNotNull(journeyComponent);
        // 添加第一个子场景
        Long journeyId1_1 = addJourney(journeyMap, journeyComponent, journey1.getId());

        // journey1 的事件统计,连接器没有了
        assertNull(findFirst(ElementEventStat.class, Map.of("journeyId", journey1.getId())));
        assertNull(findFirst(ElementLinker.class, Map.of("journeyId", journey1.getId())));

        // 在刚刚新添加的场景上，并且还是之前第一个添加的事件统计,连接器
        Optional.ofNullable(findFirst(ElementEventStat.class, Map.of("journeyId", journeyId1_1)))
                .ifPresentOrElse(i -> assertEquals(i.getId(), eventStatId1), Assertions::fail);
        Optional.ofNullable(findFirst(ElementLinker.class, Map.of("journeyId", journeyId1_1)))
                .ifPresentOrElse(i -> assertEquals(i.getId(), linerId1), Assertions::fail);


        // 添加第二个子场景
        Long journeyId1_2 = addJourney(journeyMap, journeyComponent, journey1.getId());
        // 这个子场景没有事件统计，连接器
        assertNull(findFirst(ElementEventStat.class, Map.of("journeyId", journeyId1_2)));
        assertNull(findFirst(ElementLinker.class, Map.of("journeyId", journeyId1_2)));

        // 第二个子场景 也加上一个事件统计,连接器
        Long eventStatId1_2 = addEventStat(journeyMap, eventStatComponent, journeyId1_2);
        Long linkerId1_2 = addLinker(journeyMap, linkerComponent, journeyId1_2);

        // 有了
        Optional.ofNullable(findFirst(ElementEventStat.class, Map.of("journeyId", journeyId1_2)))
                .ifPresentOrElse(i -> assertEquals(i.getId(), eventStatId1_2), Assertions::fail);
        Optional.ofNullable(findFirst(ElementLinker.class, Map.of("journeyId", journeyId1_2)))
                .ifPresentOrElse(i -> assertEquals(i.getId(), linkerId1_2), Assertions::fail);


        // 删除第一个子场景
        deleteJourney(journeyMap, journeyComponent, journeyId1_1);

        // 事件统计,连接器也被删了
        assertNull(findFirst(ElementEventStat.class, Map.of("journeyId", journeyId1_1)));
        assertNull(findFirst(ElementLinker.class, Map.of("journeyId", journeyId1_1)));

        // 删除第二个子场景
        deleteJourney(journeyMap, journeyComponent, journeyId1_2);

        // 事件统计，连接器没有被删除，并且被转移到，journey1 了
        assertNull(findFirst(ElementEventStat.class, Map.of("journeyId", journeyId1_2)));
        assertNull(findFirst(ElementLinker.class, Map.of("journeyId", journeyId1_2)));
        assertEquals(findById(ElementEventStat.class, eventStatId1_2).getJourneyId(), journey1.getId());
        assertEquals(findById(ElementLinker.class, linkerId1_2).getJourneyId(), journey1.getId());

        // 给第2，3场景都加上子场景
        Long journeyId2_1 = addJourney(journeyMap, journeyComponent, journey2.getId());
        assertNull(findFirst(ElementEventStat.class, Map.of("journeyId", journey2.getId())));
        assertNull(findFirst(ElementLinker.class, Map.of("journeyId", journey2.getId())));
        assertNotNull(findFirst(ElementEventStat.class, Map.of("journeyId", journeyId2_1)));
        assertNotNull(findFirst(ElementLinker.class, Map.of("journeyId", journeyId2_1)));
        Long journeyId3_1 = addJourney(journeyMap, journeyComponent, journey3.getId());
        assertNull(findFirst(ElementEventStat.class, Map.of("journeyId", journey3.getId())));
        assertNull(findFirst(ElementLinker.class, Map.of("journeyId", journey3.getId())));
        assertNotNull(findFirst(ElementEventStat.class, Map.of("journeyId", journeyId3_1)));
        assertNotNull(findFirst(ElementLinker.class, Map.of("journeyId", journeyId3_1)));
        Long journeyId3_2 = addJourney(journeyMap, journeyComponent, journey3.getId());
        assertNull(findFirst(ElementEventStat.class, Map.of("journeyId", journeyId3_2)));
        assertNull(findFirst(ElementLinker.class, Map.of("journeyId", journeyId3_2)));

        Long journeyId4 = addJourney(journeyMap, journeyComponent, 0L);


        // publish
        q(JourneyMapController.class).callMethod(c -> c.publish(0L))
                .pathParams(List.of(journeyMap.getId().toString()))
                .isOk(r -> r.andExpect(jsonPath("$.data").value(true)));

        // publish components
        List<Map<String, Object>> components = q(ComponentPublishController.class).callMethod(c -> c.findAll(0L))
                .pathParams(List.of(journeyMap.getId().toString()))
                .isOk(r -> r.andExpect(jsonPath("$.data.journeyMap.id").value(journeyMap.getId()))
                ).parseResponse("data.components");
        components.forEach(i -> {
            if (i.get("type") == JourneyComponentType.journey.name()) {
                assertEquals(4, ((List<Object>) i.get("elements")).size());
            } else if (i.get("type") == JourneyComponentType.event_stat.name()) {
                assertEquals(3, ((List<Object>) i.get("elements")).size());
            } else if (i.get("type") == JourneyComponentType.linker.name()) {
                assertEquals(3, ((List<Object>) i.get("elements")).size());
            }
        });

        List<JourneyPublish> journeyPublishes = findAll(JourneyPublish.class, Map.of("componentId", journeyComponent.getId()));
        assertEquals(14, journeyPublishes.size());

//        assertNotNull(findFirst(ElementEventStat.class, Map.of("journeyId", journeyId1_2)));

    }

    private Long addEventStat(JourneyMap journeyMap, JourneyComponent component, Long journeyId) {
        EventMonitorRules eventRule = findFirst(EventMonitorRules.class, Map.of());
        assertNotNull(eventRule);
        return q(JourneyMapControllerProxy.class)
                .callMethod(c -> c.createJourneyComponentElementEventStat(0L, 0L, null))
                .pathParams(List.of(journeyMap.getId().toString(), component.getId().toString()))
                .body(Map.of("statType", 1, "journeyId", journeyId, "eventRuleId", eventRule.getId()))
                .isOk(r -> r.andExpect(jsonPath("$.data.id").isNumber()))
                .parseResponse("data.id");
    }

    private Long addLinker(JourneyMap journeyMap, JourneyComponent component, Long journeyId) {
        Map<String, Object> linkerConfig = Map.of("baiduTongji", Map.of(
                "visitor", "",
                "gran", "day",
                "metrics", "pv_count",
                "method", "trend/time/a"
        ));
        return q(JourneyMapControllerProxy.class).callMethod(c -> c.createJourneyComponentElementLinker(0L, 0L, null))
                .pathParams(List.of(journeyMap.getId().toString(), component.getId().toString()))
                .body(Map.of("journeyId", journeyId, "name","连接器","linkerType", "baiduTongji", "linkerConfig", linkerConfig))
                .isOk(r -> r.andExpect(jsonPath("$.data.id").isNumber()))
                .parseResponse("data.id");
    }

    private Long addJourney(JourneyMap journeyMap, JourneyComponent component, Long parentId) {
        return q(ComponentJourneyController.class)
                .callMethod(c -> c.createJourney(0L, 0L, null))
                .pathParams(List.of(journeyMap.getId().toString(), component.getId().toString()))
                .body(Map.of("parentId", parentId, "journeyName", "1-1", "color", "#cccccc"))
                .isOk(r -> r.andExpect(jsonPath("$.data.journey.id").isNumber()))
                .parseResponse("data.journey.id");
    }


    private void deleteJourney(JourneyMap journeyMap, JourneyComponent component, Long journeyId) {
        q(ComponentJourneyController.class)
                .callMethod(c -> c.deleteJourney(0L, 0L, 0L))
                .pathParams(List.of(journeyMap.getId().toString(), component.getId().toString(), journeyId.toString()))
                .isOk(r -> r.andExpect(jsonPath("$.data.journey.id").isNumber()));
    }

}
