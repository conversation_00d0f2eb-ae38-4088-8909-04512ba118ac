package cn.hanyi.ctm.connector.wechatopen;

import org.befun.core.dto.TemplateParameter;
import cn.hanyi.ctm.connector.impl.wechatopen.TemplateParser;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * The class description
 *
 * <AUTHOR>
 */
public class TemplateParserTest {

    private String template = "" +
            "{{result.DATA}}\n" +
            "     领奖金额：{ {withdrawMoney.DATA} }\n" +
            "     领奖时间：{ {withdrawTime.DATA} }\n" +
            "     银行信息：{ {cardInfo.DATA} }\n" +
            "     到账时间： { {arrivedTime.DATA} }\n" +
            "{{remark.DATA}}";

    @Test
    @DisplayName("parse_wechat_template")
    public void parseWechatTemplate() {
        List<Map<String, Object>> params = TemplateParser.parseWechatTemplate(template);

        assertEquals(4, params.size());
        assertEquals("领奖金额", params.get(0).get("title"));
        assertEquals("withdrawMoney", params.get(0).get("name"));
        assertEquals("领奖时间", params.get(1).get("title"));
        assertEquals("withdrawTime", params.get(1).get("name"));
        assertEquals("银行信息", params.get(2).get("title"));
        assertEquals("cardInfo", params.get(2).get("name"));
        assertEquals("到账时间", params.get(3).get("title"));
        assertEquals("arrivedTime", params.get(3).get("name"));

    }
}
