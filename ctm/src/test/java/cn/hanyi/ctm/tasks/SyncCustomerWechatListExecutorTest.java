package cn.hanyi.ctm.tasks;

import cn.hanyi.ctm.AssertController;
import cn.hanyi.ctm.MockServiceConfiguration;
import cn.hanyi.ctm.TestRedisConfiguration;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.bean.result.WxMpUserList;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;

import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;


@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {MockServiceConfiguration.class, TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class SyncCustomerWechatListExecutorTest extends AssertController {

//    @MockBean
//    private WechatOpenConenctorProvider wechatOpenConenctorProvider;

    //    @Test
    public void sync() throws WxErrorException {
        mockWechat();
    }

    private void mockWechat() throws WxErrorException {
        List<String> openIds1 = IntStream.of(0, 10000).mapToObj(Objects::toString).collect(Collectors.toList());
        List<String> openIds2 = IntStream.of(10000, 20000).mapToObj(Objects::toString).collect(Collectors.toList());
        List<String> openIds3 = IntStream.of(20000, 23456).mapToObj(Objects::toString).collect(Collectors.toList());
        Function<String, WxMpUserList> get = nextId -> {
            WxMpUserList list = new WxMpUserList();
            list.setTotal(23456);
            list.setCount(10000);
            switch (nextId) {
                case "":
                    list.setNextOpenid("1");
                    list.setOpenids(openIds1);
                    break;
                case "1":
                    list.setNextOpenid("2");
                    list.setOpenids(openIds2);
                    break;
                case "2":
                    list.setNextOpenid(null);
                    list.setOpenids(openIds3);
                    break;
            }
            return list;
        };
//        given(wechatOpenConenctorProvider.getMpUserIds("aaa", "")).willReturn(get.apply(""));
//        given(wechatOpenConenctorProvider.getMpUserIds("aaa", "1")).willReturn(get.apply("1"));
//        given(wechatOpenConenctorProvider.getMpUserIds("aaa", "2")).willReturn(get.apply("2"));
//
//        given(wechatOpenConenctorProvider.getMpUsers(any(), any())).willReturn(new ArrayList<>());
    }
}