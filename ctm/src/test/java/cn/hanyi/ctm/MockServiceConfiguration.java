package cn.hanyi.ctm;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * The class description
 *
 * <AUTHOR>
 */
@TestConfiguration
@Slf4j
@EnableAsync
@ConditionalOnProperty(name = "async.enabled", havingValue = "false")
public class MockServiceConfiguration {
//    @Bean
//    @Primary
//    public WechatOpenConenctorProvider mockWechatProvider() {
//        return Mockito.mock(WechatOpenConenctorProvider.class);
//    }
}
