package cn.hanyi.ctm.service;

import cn.hanyi.ctm.BaseTest;
import cn.hanyi.ctm.TestRedisConfiguration;
import cn.hanyi.ctm.connector.PlatformConnectorManager;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;

/**
 * The class description
 *
 * <AUTHOR>
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class CustomerServiceTest extends BaseTest {

    @Autowired
    private PlatformConnectorManager manager;

    @Test
    public void updateAnswer() throws Exception {
    }
}