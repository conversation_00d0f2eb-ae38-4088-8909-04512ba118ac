package cn.hanyi.ctm.service;

import cn.hanyi.ctm.BaseTest;
import cn.hanyi.ctm.TestRedisConfiguration;
import cn.hanyi.ctm.connector.PlatformConnectorManager;
import cn.hanyi.ctm.constant.connector.ConnectorProviderType;
import lombok.SneakyThrows;
import org.junit.Assert;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;

import static org.mockito.BDDMockito.given;

/**
 * The class description
 *
 * <AUTHOR>
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class ConnectorServiceTest extends BaseTest {

    @Autowired
    private  PlatformConnectorManager manager;

//    @SneakyThrows
//    @Test
//    public void simpleMockTest() {
//        given(wechatProvider.preAuthenticate()).willReturn("authCode");
//        String result = manager.preAuthenticate(ConnectorProviderType.WECHATOPEN);
//        Assert.assertEquals("authCode", result);
//    }
}