package cn.hanyi.ctm.service;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;


public class EventServiceTest {

    @Test
    public void testTargetUrl() {
        testTargetUrl("https://test.xmplus.cn/", "/cem/event/operateData?eventId=1");
        testTargetUrl("https://test.xmplus.cn/", "cem/event/operateData?eventId=1");
        testTargetUrl("https://test.xmplus.cn", "/cem/event/operateData?eventId=1");
        testTargetUrl("https://test.xmplus.cn", "cem/event/operateData?eventId=1");
    }

    private void testTargetUrl(String domain, String path) {
        if (domain.endsWith("/")) {
            domain = domain.substring(0, domain.length() - 1);
        }
        if (path.startsWith("/")) {
            path = path.substring(1);
        }
        Assertions.assertEquals(String.format("%s/%s", domain, path), "https://test.xmplus.cn/cem/event/operateData?eventId=1");
    }
}
