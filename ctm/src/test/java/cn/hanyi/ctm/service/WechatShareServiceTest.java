package cn.hanyi.ctm.service;

import cn.hanyi.ctm.TestRedisConfiguration;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import org.befun.auth.provider.wechat.mp.WechatMpAuthProvider;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.DirtiesContext;

/**
 * <AUTHOR>
 * @Description
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = {TestRedisConfiguration.class})
@AutoConfigureMockMvc
@DirtiesContext(classMode = DirtiesContext.ClassMode.AFTER_EACH_TEST_METHOD)
public class WechatShareServiceTest {

    @Autowired
    WechatMpAuthProvider wechatMpAuthProvider;




    @Test
    public void getWxSignature() {
        String url = "http://localhost:8080/cem/wechat-share?url=https://test.xmplus.cn/cem/dashboard?code=1&state=wxb2c4b6d25d89c719&bindFlag=true";
        WxMpService mpService = wechatMpAuthProvider.getWxMpService("cem");
        WxJsapiSignature jsapiSignature = null;
        try {
            jsapiSignature = mpService.createJsapiSignature(url);
            System.out.println(jsapiSignature.toString());
//            return;
        } catch (WxErrorException e) {
            e.printStackTrace();
        }
        System.out.println(jsapiSignature.toString());
    }
}
