package cn.hanyi.ctm.service;

import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.time.Period;

class IndicatorDataServiceTest {

    private Pair<LocalDate, LocalDate> relativeDate(LocalDate s, LocalDate e) {
        Period period = Period.between(s, e);
        return Pair.of(s.minus(period), e.minus(period));
    }

    @Test
    public void testRelativeDate() {
        LocalDate s = LocalDate.of(2021, 10, 1);
        LocalDate e = LocalDate.of(2021, 10, 7);
        Pair<LocalDate, LocalDate> pair = relativeDate(s, e);
        System.out.println(pair.toString());

    }

    private static Double formatDouble(Double value) {
        if (value == null) {
            return null;
        }
        return Long.valueOf(Math.round(value * 100)).doubleValue() / 100;
    }

    @Test
    public void testFormatDouble() {
        System.out.println(formatDouble(null));

        System.out.println(3.0 / 2);
        System.out.println(formatDouble(3.0 / 2));

        System.out.println(7.0 / 3);
        System.out.println(formatDouble(7.0 / 3));

        System.out.println(10.0 / 3);
        System.out.println(formatDouble(10.0 / 3));

        System.out.println(11.0 / 3);
        System.out.println(formatDouble(11.0 / 3));

    }


}