package cn.hanyi.ctm;

import cn.hanyi.ctm.connector.impl.sms.FeigeSmsProvider;
import cn.hanyi.ctm.constant.EventWarningType;
import cn.hanyi.ctm.constant.MessageType;
import cn.hanyi.ctm.constant.SourceType;
import cn.hanyi.ctm.constant.connector.ConnectorAuthorizeStatus;
import cn.hanyi.ctm.constant.connector.ConnectorProviderType;
import cn.hanyi.ctm.constant.connector.ConnectorStatus;
import cn.hanyi.ctm.constant.connector.ConnectorType;
import cn.hanyi.ctm.constant.customer.CustomerSourceType;
import cn.hanyi.ctm.constant.event.EventMonitorCondition;
import cn.hanyi.ctm.constant.event.EventMonitorStatus;
import cn.hanyi.ctm.constant.event.EventNotifyDelayUnit;
import cn.hanyi.ctm.constant.event.EventNotifyMoment;
import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import cn.hanyi.ctm.dto.MassPushRequestDto;
import cn.hanyi.ctm.dto.event.EventReceiverDto;
import cn.hanyi.ctm.dto.journey.PushMomentDto;
import cn.hanyi.ctm.entity.*;
import cn.hanyi.ctm.entity.journey.*;
import cn.hanyi.ctm.repository.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.SneakyThrows;
import org.befun.auth.constant.NotificationType;
import org.befun.auth.entity.Organization;
import org.befun.auth.entity.OrganizationWallet;
import org.befun.auth.repository.OrganizationRepository;
import org.befun.auth.repository.OrganizationWalletRepository;
import org.befun.core.dto.UserDto;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.redis.core.RedisTemplate;

import javax.sql.DataSource;
import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.BDDMockito.given;

/**
 * The class description
 *
 * <AUTHOR>
 */
public class BaseTest {
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    protected DataSource dataSource;

    @Autowired
    protected ObjectMapper objectMapper;

    @Autowired
    protected ConnectorRepository connectorRepository;

    @Autowired
    protected ThirdPartyTemplateRepository thirdPartyTemplateRepository;

    @Autowired
    protected EventMonitorRulesRepository eventMonitorRulesRepository;

    @Autowired
    protected TemplateRepository templateRepository;

    @Autowired
    protected ThirdPartyCustomerRepository thirdPartyCustomerRepository;

    @Autowired
    protected CustomerRepository customerRepository;

    @Autowired
    protected OrganizationWalletRepository organizationWalletRepository;

    @Autowired
    protected JourneyPublishRepository journeyPublishRepository;

    @Autowired
    protected ExperienceInteractionPublishRepository experienceInteractionPublishRepository;

    @MockBean
    public FeigeSmsProvider feigeProvider;

//    @MockBean
//    public WechatOpenConenctorProvider wechatProvider;

    @Autowired
    protected JourneyMapRepository journeyMapRepository;

    @Autowired
    protected JourneyComponentRepository journeyComponentRepository;

    @Autowired
    protected JourneyRepository journeyRepository;

    @Autowired
    protected OrganizationRepository organizationRepository;


    protected Organization org1;

    // c1 绑定全民问答
    protected Connector c1;
    protected Connector c2;
    protected Connector c4;
    protected Connector c5;
    protected Connector c6;

    protected Connector c3;
    protected Long mock_org_1_id = 1L;
    protected Long mock_org_2_id = 2L;
    protected String mock_api_key = "fake_api_key";
    protected String mock_api_secret = "fake_api_secret";
    protected String mock_token_1 = "fake_token_1";
    protected String mock_token_1_1 = "fake_token_1_1";
    protected String mock_token_2 = "fake_token_2";
    protected String mock_t1_example = "fake_third_template_example";
    protected List<Map<String, Object>> mock_t1_parameters = new ArrayList<>();
    protected String mock_t1_open_id = "-SHleOivuU-dmWJBC8lKAm_0AuGrSuT5X519X-5eupU";
    protected UserDto mock_user_1;
    protected UserDto mock_user_org1_u2;
    protected UserDto mock_user_2;
    protected String mock_ct1_phone = "12345";
    protected String mock_ct1_name = "fake_customer_1";
    protected String mock_default_template = "default_sms_customer";
    protected String survey_url = "https://test.surveyplus.cn/lite/504";

    // t1 审核结果通知
    protected ThirdPartyTemplate tt1;
    protected ThirdPartyTemplate tt2;

    protected Template t1;
    protected Template t2;
    protected Template t3;
    protected Template t4;

    protected MassPushRequestDto m1;
    protected ThirdPartyCustomer tct1;
    protected Customer tct1_ct1;
    protected Customer ct2;
    protected Map<String, Object> t2_content = new HashMap<>();
    protected Map<String, Object> t3_content = new HashMap<>();

    protected OrganizationWallet org1_wallet;

    // 预警规则
    protected EventMonitorRules rule1;
    protected EventMonitorRules rule2;

    // 预警接受者
    protected EventReceiverDto receiver1;
    protected EventReceiverDto receiver2;


    protected ExperienceInteractionPublish interaction_1;
    protected JourneyPublish j1;
    protected JourneyMap jm1;
    protected JourneyComponent jm1_jc1;
    protected JourneyComponent jm1_jc2;
    protected Journey jm1_jc1_j1;
    protected Journey jm1_jc1_j1_1;

    @BeforeEach
    @SneakyThrows
    public void setup() {
        org1 = new Organization();
        org1.setId(mock_org_1_id);
        org1.setOwnerId(1L);
        organizationRepository.save(org1);

        mock_user_1 = UserDto
                .builder()
                .orgId(mock_org_1_id)
                .id(1L)
                .username("u1")
                .build();
        mock_user_org1_u2 = UserDto
                .builder()
                .orgId(mock_org_1_id)
                .id(3L)
                .username("u2")
                .build();
        mock_user_2 = UserDto
                .builder()
                .orgId(mock_org_2_id)
                .id(2L)
                .username("u2")
                .build();
        saveToken(mock_token_1, mock_user_1, "1,2");
        saveToken(mock_token_1_1, mock_user_org1_u2, "2");
        saveToken(mock_token_2, mock_user_2, "2");
        saveLimiter(mock_org_1_id);
        saveLimiter(mock_org_2_id);

        c1 = new Connector().toBuilder()
                .appId("app1")
                .type(ConnectorType.PLATFORM)
                .providerType(ConnectorProviderType.WECHATOPEN)
                .appId("wx38eb115cf5014c74")
                .type(ConnectorType.PLATFORM)
                .name("c1")
                .status(ConnectorStatus.READY)
                .source(SourceType.USER)
                .providerType(ConnectorProviderType.WECHATOPEN)
                .authorizeStatus(ConnectorAuthorizeStatus.AUTHORIZED)
                .build();
        c1.setOrgId(mock_org_1_id);
        connectorRepository.save(c1);

        c2 = new Connector().toBuilder()
                .appId("app2")
                .name("c2")
                .build();
        c2.setOrgId(mock_org_1_id);
        connectorRepository.save(c2);

        c3 = new Connector().toBuilder()
                .appId("app3")
                .name("c3")
                .build();
        c3.setOrgId(mock_org_2_id);
        connectorRepository.save(c3);

        c4 = new Connector().toBuilder()
                .appId("app3")
                .name("c3")
                .providerType(ConnectorProviderType.FEIGE)
                .type(ConnectorType.SMS)
                .build();
        c4.setOrgId(mock_org_1_id);
        connectorRepository.save(c4);

        c5 = new Connector().toBuilder()
                .appId("app3")
                .name("c3")
                .providerType(ConnectorProviderType.FEIGE)
                .type(ConnectorType.SMS)
                .source(SourceType.SYSTEM)
                .build();
        c5.setOrgId(mock_org_1_id);
        connectorRepository.save(c5);

        c6 = new Connector().toBuilder()
                .appId("app6")
                .name("api_connector")
                .providerType(ConnectorProviderType.FEIGE)
                .type(ConnectorType.API)
                .source(SourceType.USER)
                .build();
        c6.setOrgId(mock_org_1_id);
        connectorRepository.save(c6);

        tt1 = new ThirdPartyTemplate();
        tt1.setConnector(c1);
        tt1.setOrgId(mock_org_1_id);
        tt1.setName("fake_thirdTemplate_1");
        tt1.setExample(mock_t1_example);
        tt1.setParameters(mock_t1_parameters);
        tt1.setOpenId(mock_t1_open_id);
        tt1.setProviderType(ConnectorProviderType.WECHATOPEN);
        tt1.setConnectorType(ConnectorType.PLATFORM);
        tt1.setCreateUserId(mock_user_1.getId());
        tt1.setUpdateUserId(mock_user_1.getId());
        tt2 = new ThirdPartyTemplate();
        tt2.setConnector(c1);
        tt2.setOrgId(mock_org_1_id);
        tt2.setName("fake_thirdTemplate_2");
        tt2.setCreateUserId(mock_user_1.getId());
        tt2.setUpdateUserId(mock_user_1.getId());
        thirdPartyTemplateRepository.save(tt1);

        t1 = new Template();
        t1.setConnector(c1);
        t1.setName("fake_template_1");
        t1.setOrgId(mock_org_1_id);
        templateRepository.save(t1);

        // feigeTemplate
        t2_content.put("content", "尊敬的${username}");
        t2 = new Template();
        t2.setConnector(c4);
        t2.setName("fake_template_2");
        t2.setContent(t2_content);
        t2.setOrgId(mock_org_1_id);
        templateRepository.save(t2);

        // wechatopenTemplate
        t3 = new Template();
        t3_content.put("keyword1", "${keyword1}");
        t3.setMessageType(MessageType.TEMPLATE);
        t3.setConnector(c1);
        t3.setName("fake_template_3");
        t3.setContent(t3_content);
        t3.setOrgId(mock_org_1_id);
        templateRepository.save(t3);

        t4 = new Template();
        t4.setContent(t2_content);
        t4.setName(mock_default_template);
        t4.setSourceType(SourceType.SYSTEM);
        t4.setConnector(c5);
        t4.setOrgId(mock_org_1_id);
        templateRepository.save(t4);


        tct1 = new ThirdPartyCustomer();
        tct1.setAuthorizeStatus(ConnectorAuthorizeStatus.AUTHORIZED);
        tct1.setConnector(c1);
        tct1.setOpenId("1111");
        tct1.setOrgId(mock_org_1_id);
        tct1.setSubscribed(tct1.isSubscribed());
        thirdPartyCustomerRepository.save(tct1);

        tct1_ct1 = new Customer();
        tct1_ct1.setMobile(mock_ct1_phone);
        tct1_ct1.setUsername(mock_ct1_name);
        tct1_ct1.setOrgId(mock_org_1_id);
        tct1_ct1.setThirdPartyCustomerId(tct1.getId());
        tct1_ct1.setSource(CustomerSourceType.WECHATOPEN);
        tct1_ct1.setCreatedByUid(0L);
        customerRepository.save(tct1_ct1);

        ct2 = new Customer();
        ct2.setMobile(mock_ct1_phone);
        ct2.setUsername("ct2");
        ct2.setOrgId(mock_org_1_id);
        ct2.setCreatedByUid(0L);
        customerRepository.save(ct2);

        org1_wallet = new OrganizationWallet();
        org1_wallet.setOrgId(mock_org_1_id);
        org1_wallet.setSms(100);
        org1_wallet.setMoney(0);
        organizationWalletRepository.save(org1_wallet);

        m1 = new MassPushRequestDto();

        // 构造预警规则
        rule1 = new EventMonitorRules();
        rule1.setExpression("NeG20.selected(\"TWjXn\") and NH8bX.value < 5");
        rule1.setLevel(EventWarningType.HIGH);
        rule1.setStatus(EventMonitorStatus.OPEN);
        rule1.setSurveyId(1L);
        rule1.setTitle("预警命中");
        rule1.setOrgId(mock_user_1.getOrgId());
        rule1.setUserId(mock_user_1.getId());
        rule1.setSurveyName("问卷名称1");
        rule1.setModifyTime(new Date());
        rule1.setCreateTime(new Date());

        receiver1 = new EventReceiverDto();
        receiver1.setNotifyMoment(EventNotifyMoment.IMMEDIATE);
        receiver1.setDelayCondition(null);
        receiver1.setNotifyChannel(List.of(NotificationType.WECHAT, NotificationType.EMAIL));

        rule1.setReceiver(List.of(receiver1));

        rule2 = new EventMonitorRules();
        rule2.setExpression("NH8bX.value > 6");
        rule2.setLevel(EventWarningType.HIGH);
        rule2.setStatus(EventMonitorStatus.OPEN);
        rule2.setSurveyId(1L);
        rule2.setTitle("预警");
        rule2.setOrgId(mock_user_2.getOrgId());
        rule2.setUserId(mock_user_2.getId());
        rule2.setSurveyName("问卷名称2");
        rule2.setModifyTime(new Date());
        rule2.setCreateTime(new Date());

        receiver2 = new EventReceiverDto();
        receiver2.setNotifyMoment(EventNotifyMoment.DELAY);
        receiver2.setDelayInterval(1);
        receiver2.setDelayUnit(EventNotifyDelayUnit.MINUTE);
        receiver2.setDelayCondition(EventMonitorCondition.UNCLOSE);
        receiver2.setNotifyChannel(List.of(NotificationType.EMAIL));
        rule2.setReceiver(List.of((receiver2)));

        eventMonitorRulesRepository.save(rule1);
        eventMonitorRulesRepository.save(rule2);

//        messageData = (SurveyResponseMessageDto) SurveyResponseMessageDto.builder()
//                .surveyId(1L)
//                .surveyName("全类型")
//                .responseId(1L)
//                .finishTime(new Date().getTime())
//                .clientId("client001")
//                .externalUserId("userId001")
//                .customerId(1L)
//                .departmentId(1L)
//                .sceneId(1L)
//                .trackId("trackId001")
//                .data(
//                        List.of(
//                                SurveyResponseCellMessageRuleDto.builder()
//                                        .questionId(1L)
//                                        .type(QuestionType.SINGLE_CHOICE)
//                                        .title("单选题")
//                                        .value("TWjXn")
//                                        .questionsItems(
//                                                Map.of(
//                                                        "NeG20", new QuestionsItemsDto(
//                                                                "TWjXn",
//                                                                List.of(
//                                                                        new QuestionsItemDto("TWjXn", "单1"),
//                                                                        new QuestionsItemDto("TQeKr", "单2")
//                                                                ),
//                                                                List.of()
//                                                        )
//                                                )
//                                        )
//                                        .build(),
//                                SurveyResponseCellMessageRuleDto.builder()
//                                        .questionId(2L)
//                                        .type(QuestionType.SCORE)
//                                        .title("量表题")
//                                        .value(3)
//                                        .questionsItems(
//                                                Map.of(
//                                                        "NH8bX", new QuestionsItemsDto(
//                                                                3, null, null
//                                                        )
//                                                )
//                                        )
//                                        .build()
//                        )
//                )
//                .build();
//        message = JsonHelper.toJson(messageData);

        j1 = new JourneyPublish();
        j1.setOrgId(mock_org_1_id);
        j1.setJourneyName("fake_journey_1");
        journeyPublishRepository.save(j1);
        PushMomentDto momentDto = new PushMomentDto();

//        interaction_1: template:2
        interaction_1 = new ExperienceInteractionPublish();
        interaction_1.setInteractionName("test_interaction_name");
        interaction_1.setInteractionMoment(momentDto);
//        interaction_1.setJourney(j1);
        interaction_1.setCtmTemplateId(new ArrayList<>() {{
            add(t2.getId());
        }});
        interaction_1.setInteractionSids(new ArrayList<>() {{
            add(1L);
        }});
        interaction_1.setSurveyTitles(new ArrayList<>() {{
            add("1");
        }});
        interaction_1.setSurveyUrls(new ArrayList<>() {{
            add(survey_url);
        }});
        interaction_1.setInteractionsCollectors(new ArrayList<>() {{
            add(3L);
        }});
        experienceInteractionPublishRepository.save(interaction_1);

        // mock_org_1_id的客户旅程-wakanda forever
        jm1 = new JourneyMap();
        jm1.setTitle("foo");
        jm1.setOrgId(mock_org_1_id);
        journeyMapRepository.save(jm1);
        jm1_jc1 = new JourneyComponent();
        jm1_jc1.setOrgId(mock_org_1_id);
        jm1_jc1.setJourneyMapId(jm1.getId());
        jm1_jc1.setType(JourneyComponentType.journey);
        jm1_jc1.setTitle("foo_jc");
        jm1_jc1.setOrder(1);
        journeyComponentRepository.save(jm1_jc1);
        jm1_jc1_j1 = new Journey();
        jm1_jc1_j1.setOrder(0);
        jm1_jc1_j1.setJourneyName("foo_j");
        jm1_jc1_j1.setOrgId(mock_org_1_id);
        jm1_jc1_j1.setParentId(0L);
        jm1_jc1_j1.setComponentId(jm1_jc1.getId());
        jm1_jc1_j1.setIsLeaf(0);
        journeyRepository.save(jm1_jc1_j1);
        jm1_jc1_j1_1 = new Journey();
        jm1_jc1_j1_1.setOrgId(mock_org_1_id);
        jm1_jc1_j1_1.setComponentId(jm1_jc1.getId());
        jm1_jc1_j1_1.setParentId(jm1_jc1_j1.getId());
        jm1_jc1_j1_1.setIsLeaf(1);
        journeyRepository.save(jm1_jc1_j1_1);
        jm1_jc2 = new JourneyComponent();
        jm1_jc2.setType(JourneyComponentType.curve);
        jm1_jc2.setJourneyMapId(jm1.getId());
        jm1_jc2.setOrgId(mock_org_1_id);
        journeyComponentRepository.save(jm1_jc2);


    }

    /**
     * saveToken
     *
     * @param token
     * @param user
     */
    private void saveToken(String token, UserDto user, String subDepartmentIds) {
        redisTemplate.opsForHash().put("session:" + token, "orgId", user.getOrgId().toString());
        redisTemplate.opsForHash().put("session:" + token, "userId", user.getId().toString());
        redisTemplate.opsForHash().put("session:" + token, "username", user.getUsername());
        redisTemplate.opsForHash().put("session:" + token, "departments", subDepartmentIds);
    }

    private void saveLimiter(Long orgId) {
        redisTemplate.opsForValue().set("limiter.smsbalance." + orgId, "1");
    }

    public void sendMassage() {
        Map<String, Object> textContent = new HashMap<>();
        textContent.put("content", "true");
        given(feigeProvider.sendMessage(any(), any(), any(), any(), any(), any())).willReturn(true);
//        given(wechatProvider.sendTextMessage(any(), any(), any(), any(), any())).willReturn(textContent);
//        given(wechatProvider.sendTemplateMessage(any(), any(), any(), any(), any(), any())).willReturn(true);


    }
}
