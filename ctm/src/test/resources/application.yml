
spring:
  config:
    import:
      - classpath:befun-xpack-wechatopen.yml
      - classpath:file.yml
  flyway:
    enabled: false
  datasource:
    driverClassName: org.h2.Driver
    jdbc-url: jdbc:h2:mem:cem_platform;DB_CLOSE_DELAY=-1
  datasource-survey:
    driver-class-name: org.h2.Driver
    jdbc-url: jdbc:h2:mem:cem_platform;DB_CLOSE_DELAY=-1
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: create
      generate-ddl: true
  data:
    redis.repositories.enabled: false
    jdbc.repositories.enabled: false
  redis:
    host: localhost
    port: 6380

hanyi:
  shorturl: ${SHORTURL:https://test-t.xmplus.cn}
  open:
    key: Xxff8ft35h77qD8VDYWQh4sW9H5kcdCd

wechat:
  open:
    app_id: ${WECHAT_OPEN_APP_ID:wxbb2e0ad30fee2502}
    app_secret: ${WECHAT_OPEN_APP_SECRET:372012a24728ce35610b37e8eb539538}
    token: ${WECHAT_OPEN_TOKEN:surveyplus}
    aes_key: ${WECHAT_OPEN_AES_KEY:0F68wpOxqNJs4bL5xUoCyrlWinYF6QNPtmYVx8Ex0BB}
    callback: ${WECHAT_OPEN_CALLBACK:https://dev.xmplus.cn/cem/setting/account?from=wechat_open}

logging:
  level:
    root: ${LOG_LEVEL:info}
    state: error
    org:
      hibernate:
        SQL: info
#        type: trace

ctm:
  enable-mock-sync-customer: ${ENABLE_MOCK_SYNC_CUSTOMER:false}
  info:
    domain: ${CTM_INFO_DOMAIN:https://dev.xmplus.cn/}
  event:
    default-group: ctm
    target-url: /cem/event/operateData?eventId=${id}
    notify-topic: ctm_notify_event
    survey-response-topic: survey_response
    survey-change-topic: survey_change
    user-create-topic: queuing-user-create
    ctm-message-topic: ctm_message
  notify:
    app: cem
    warning: warning
    cooperation: cooperation
    close: close
    default-sms-template: default_sms_event

  journey:
    init:
      components:
        - order: 0
          title: 场景
          type: journey
          subType:
        - order: 1
          title: 客户期望
          type: textbox
          subType: customer_expection
        - order: 2
          title: 触点
          type: textbox
          subType: touch_point
        - order: 3
          title: 情绪曲线
          type: curve
          subType:
        - order: 4
          title: 体验指标
          type: experience_indicator
          subType:
        - order: 5
          title: 场景推送
          type: experience_interaction
          subType:
      journeys:
        - order: 0
          title: 感知
          color: '#3181F6'
        - order: 1
          title: 决策
          color: '#FA6056'
        - order: 2
          title: 购买
          color: '#FDAB3D'
        - order: 3
          title: 使用
          color: '#4286F4'
        - order: 4
          title: 推荐
          color: '#35A853'

befun:
#  task:
#    prefix: befun.task.test
#    trim:
#      enabled: true
#      cron: "*/1 * * * * *"
#    scheduler:
#      enabled: true
#      cron: "*/1 * * * * *"
#    worker:
#      enabled: true
#      interval-seconds: 1
  extension:
    upload:
      customer-return-size: ${UPLOAD_CUSTOMER_RETUREN_SIZE:50}
      customer-template-path: ${UPLOAD_CUSTOMER_TEMPLATE_PATH:static/客户导入模板.xlsx}
      customer-file-max-size: ${UPLOAD_CUSTOMER_FILE_MAX_SIZE:5}
    mail:
      host: ${SMTP_HOST:smtp.partner.outlook.cn}
      port: ${SMTP_PORT:587}
      username: ${SMTP_USER:<EMAIL>}
      from: ${SMTP_FROM:<EMAIL>}
      password: ${SMTP_PASSWORD:Survey+0627}
      templates:
        - name: warning
          enable-html: true
          subject: 【${surveyName}】收到了新的${warningLevel}
          content:
            '
            <a href="${url}">【${surveyName}】收到了新的${warningLevel}</a><br/>
            触发的预警规则：${warningTitle}<br/>
            预警时间：${warningTime}<br/>
            所属问卷：${surveyName}<br/>
            所属来源：${departmentName}<br/>
            请尽快登录体验XM家处理
            '
        - name: cooperation
          enable-html: true
          subject: 您收到了一条${warningLevel}协作邀请
          content:
            '
            <a href="${url}">您收到了一条${warningLevel}协作邀请</a><br/>
            触发的预警规则：${warningTitle}<br/>
            预警时间：${warningTime}<br/>
            邀请人：${formUserName}<br/>
            所属问卷：${surveyName}<br/>
            所属来源：${departmentName}<br/>
            请尽快登录体验XM家处理
            '
        - name: close
          enable-html: true
          subject: 【${surveyName}】解除了一条${warningLevel}
          content:
            '
            <a href="${url}">【${surveyName}】解除了一条${warningLevel}</a><br/>
            触发的预警规则：${warningTitle}<br/>
            预警时间：${warningTime}<br/>
            解除时间：${closeTime}<br/>
            行动人：${formUserName}<br/>
            所属问卷：${surveyName}<br/>
            所属来源：${departmentName}<br/>
            请尽快登录体验XM家处理
            '
  auth:
    apps:
      - cem
    wechat-mp-sources:
      - name: cem
        app-id: ${WECHAT_MP_CEM_APP_ID:wxa3a9422f58e65a48}
        app-secret: ${WECHAT_MP_CEM_APP_SECRET:74794b288b734d7a43db94cdeb8acfa7}
        token: ${WECHAT_MP_CEM_TOKEN:hanyidata}
        templates:
          - id: TGoVGr6DIttyLBjZZSOBvN-7R5egGFfcotvGp6R4CS4
            name: warning
            parameters:
              - name: first
                value: 您收到了一条事件预警
              - name: keyword1
                value: ${warningTitle}
              - name: keyword2
                value: ${warningTime}
              - name: remark
                value: 请尽快登录体验家XM处理!
          - id: c7BGYasXScuAkLQpsx8bGYeg69rMpYXhNJa1AO8HDJI
            name: cooperation
            parameters:
              - name: first
                value: 您收到了一条协作邀请通知
              - name: keyword1
                value: ${content}
              - name: keyword2
                value: ${formUserName}
              - name: keyword3
                value: ${formUserPhone}
              - name: remark
                value: 可登录体验家XM处理!
          - id: 62VbC9NFyGj9RLA2OA6UC-P5L5XheWCNFzoMxQ8msB4
            name: close
            parameters:
              - name: first
                value: ${formUserName}关闭了一条预警事件
              - name: keyword1
                value: ${warningTitle}
              - name: keyword2
                value: ${closeTime}
              - name: remark
                value: 可登录体验家XM查看事件处理详情!

feige:
  template:
    customer: default_sms_customer

message:
  event:
    register:
      topic: ${REGISTER_TOPIC:queuing-user-create}
survey:
  survey-url-prefix:
    root: ${LITE_INFO_DOMAIN:https://dev.surveyplus.cn/lite/}
lite:
  info:
    domain: ${LITE_INFO_DOMAIN:https://dev.surveyplus.cn/lite/}
worker:
  producer:
    event:
      enabled:
        auth: false
        ctm: false
        survey: false
    task:
      enabled:
        auth: false
        ctm: false
        survey: false