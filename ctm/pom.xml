<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.hanyi</groupId>
        <artifactId>parent-ctm</artifactId>
        <version>${revision}.${sha1}-${changelist}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>ctm</artifactId>
    <version>1.0</version>
    <name>contact-mgnt</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>${java.version}</java.version>
        <java.version>${java.version}</java.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
            <version>2021.0.4.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hanyi</groupId>
            <artifactId>ctm-journey-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hanyi</groupId>
            <artifactId>ctm-customer-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hanyi</groupId>
            <artifactId>ctm-event-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hanyi</groupId>
            <artifactId>ctm-task</artifactId>
        </dependency>
        <dependency>
            <groupId>org.befun.auth</groupId>
            <artifactId>befun-auth-pay</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hanyi</groupId>
            <artifactId>cem-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.flywaydb</groupId>
            <artifactId>flyway-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>3.15.0</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.taobao.arthas</groupId>-->
        <!--            <artifactId>arthas-spring-boot-starter</artifactId>-->
        <!--            <version>3.6.7</version>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <version>1.4.200</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>it.ozimov</groupId>
            <artifactId>embedded-redis</artifactId>
            <version>0.7.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>2.21.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>knife4j-springdoc-ui</artifactId>
            <version>3.0.3</version>
        </dependency>
        <dependency>
            <groupId>cn.hanyi</groupId>
            <artifactId>ctm-data-core</artifactId>
        </dependency>
    </dependencies>
</project>
