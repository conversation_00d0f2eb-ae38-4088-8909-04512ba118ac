package cn.hanyi.ctm.constant;

import cn.hanyi.ctm.dto.event.EventOpenResourceParam;
import org.befun.auth.constant.IOpenResourceType;
import org.befun.auth.constant.OpenResourceBelong;

public enum EventOpenResourceType implements IOpenResourceType<EventOpenResourceParam> {
    event;

    @Override
    public String label() {
        return "事件";
    }

    @Override
    public OpenResourceBelong getBelong() {
        return OpenResourceBelong.USER;
    }

    @Override
    public Class<EventOpenResourceParam> getParamsClass() {
        return EventOpenResourceParam.class;
    }
}
