package cn.hanyi.ctm.dto.group;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EventGroupNameIdDto {

    @JsonView(ResourceViews.Detail.class)
    @Schema(description = "分组名")
    private String title;

    @JsonView(ResourceViews.Detail.class)
    @Schema(description = "分组id")
    private Long id;


}
