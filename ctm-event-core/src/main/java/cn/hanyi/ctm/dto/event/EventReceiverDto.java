package cn.hanyi.ctm.dto.event;

import cn.hanyi.ctm.constant.EventWarningType;
import cn.hanyi.ctm.constant.event.EventMonitorCondition;
import cn.hanyi.ctm.constant.event.EventNotifyDelayUnit;
import cn.hanyi.ctm.constant.event.EventNotifyMoment;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.auth.constant.NotificationType;
import org.befun.core.converter.LongCommaListConverter;
import org.befun.core.rest.view.ResourceViews;
import org.befun.task.BaseTaskDetailDto;

import javax.persistence.Convert;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EventReceiverDto extends BaseTaskDetailDto {

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "接受通知用户角色IDs")
    private List<Long> roleIds = new ArrayList<>();

    @JsonView(ResourceViews.Detail.class)
    @Schema(description = "接受通知用户角色ID")
    private Long roleId;

    @JsonView(ResourceViews.Basic.class)
    @Convert(converter = LongCommaListConverter.class)
    @Schema(description = "接受通知用户ID列表")
    private List<Long> userIds = new ArrayList<>();

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "通知方式")
    private EventNotifyMoment notifyMoment;

    @Schema(description = "延后时间")
    @JsonView(ResourceViews.Basic.class)
    private Integer delayInterval;

    @Schema(description = "延后时间单位")
    @JsonView(ResourceViews.Basic.class)
    private EventNotifyDelayUnit delayUnit;

    @Schema(description = "延后情况")
    @JsonView(ResourceViews.Basic.class)
    private EventMonitorCondition delayCondition;

    @Schema(description = "通知类型")
    @JsonView(ResourceViews.Basic.class)
    private List<NotificationType> notifyChannel;

    // hide 当事件触发的规则有重复的roleId时，用来排序
    private EventWarningType level;


    // 兼容旧数据
    public List<Long> getRoleIds() {
        Optional.ofNullable(roleId).ifPresent(r -> roleIds.add(r));
        return roleIds.stream().distinct().collect(Collectors.toList());
    }
}