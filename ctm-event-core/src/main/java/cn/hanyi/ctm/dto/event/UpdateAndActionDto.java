package cn.hanyi.ctm.dto.event;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;

import java.util.List;

@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class UpdateAndActionDto extends BaseDTO {
    private String tags;
    private Warning warning;
    private String groups;
    private String content;

    @Setter
    @Getter
    public class Warning {
        private String warningTitle;
        private List<Long> ids;
    }

}
