package cn.hanyi.ctm.dto.group;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.befun.auth.constant.NotificationType;
import org.befun.core.rest.view.ResourceViews;

import java.util.List;

@Getter
@Setter
public class EventGroupNotifyUserDto {

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "成员通知目标")
    private List<Long> notifyUserIds;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "成员通知时间")
    private EventGroupTimerDto notifyUserTimer;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "成员通知渠道")
    private List<NotificationType> notifyUserChannel;

    public boolean valid() {
        if (CollectionUtils.isEmpty(notifyUserIds) || notifyUserTimer == null || CollectionUtils.isEmpty(notifyUserChannel)) {
            return false;
        }
        try {
            notifyUserTimer.check();
        } catch (Throwable e) {
            return false;
        }
        return true;
    }
}
