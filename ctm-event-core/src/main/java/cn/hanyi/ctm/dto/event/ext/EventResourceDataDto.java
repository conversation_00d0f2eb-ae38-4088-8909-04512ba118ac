package cn.hanyi.ctm.dto.event.ext;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

/**
 * 事件详情存放额外数据
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class EventResourceDataDto {
    @Schema(description = "短信余额")
    @JsonView({ResourceViews.Basic.class})
    private Integer smsCount = 0;
}
