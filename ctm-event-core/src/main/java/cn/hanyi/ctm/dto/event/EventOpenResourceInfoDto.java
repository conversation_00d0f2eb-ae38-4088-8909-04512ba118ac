package cn.hanyi.ctm.dto.event;

import cn.hanyi.ctm.entity.EventActionDto;
import cn.hanyi.ctm.entity.EventDto;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.auth.projection.SimpleUser;
import org.befun.core.rest.view.ResourceViews;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EventOpenResourceInfoDto {

    @JsonView(ResourceViews.Basic.class)
    private EventDto event;
    @JsonView(ResourceViews.Basic.class)
    private List<EventActionDto> actions;
    @JsonView(ResourceViews.Basic.class)
    private List<SimpleUser> cooperationUsers;
}
