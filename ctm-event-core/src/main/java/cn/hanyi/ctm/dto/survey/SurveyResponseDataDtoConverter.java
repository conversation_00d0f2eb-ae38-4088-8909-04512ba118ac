package cn.hanyi.ctm.dto.survey;

import org.apache.commons.lang3.StringUtils;
import org.befun.core.utils.JsonHelper;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.List;

@Converter
public class SurveyResponseDataDtoConverter implements AttributeConverter<List<SurveyResponseCellMessageRuleDto>, String> {

    @Override
    public String convertToDatabaseColumn(List<SurveyResponseCellMessageRuleDto> list) {
        if (list == null) {
            return null;
        } else {
            return JsonHelper.toJson(list);
        }
    }

    @Override
    public List<SurveyResponseCellMessageRuleDto> convertToEntityAttribute(String dbData) {
        if (StringUtils.isEmpty(dbData)) {
            return null;
        } else {
            return JsonHelper.toList(dbData, SurveyResponseCellMessageRuleDto.class);
        }
    }
}