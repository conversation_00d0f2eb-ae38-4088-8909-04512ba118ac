package cn.hanyi.ctm.dto.event;

import cn.hanyi.ctm.constant.EventWarningType;
import cn.hanyi.ctm.constant.event.EventMonitorStatus;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class SimpleEventMonitorRules {

    @JsonView(ResourceViews.Basic.class)
    private Long id;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "预警名称")
    private String title;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "预警等级")
    private EventWarningType level;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "开关状态")
    private EventMonitorStatus status;
}
