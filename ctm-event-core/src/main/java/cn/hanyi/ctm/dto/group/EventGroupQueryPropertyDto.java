package cn.hanyi.ctm.dto.group;

import cn.hanyi.ctm.constant.group.QueryLogic;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EventGroupQueryPropertyDto {

    @Schema(description = "查询逻辑")
    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @Enumerated(EnumType.STRING)
    @JsonView(ResourceViews.Basic.class)
    private QueryLogic logic = QueryLogic.and;

    @Schema(description = "查询条件")
    @JsonView(ResourceViews.Basic.class)
    private List<EventGroupQueryItemsDto> items;

    @Schema(description = "tag 默认空字符串 前端使用")
    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @JsonView(ResourceViews.Basic.class)
    private String tag = "";

}
