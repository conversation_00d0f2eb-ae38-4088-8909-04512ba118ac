package cn.hanyi.ctm.dto.event;

import cn.hanyi.ctm.constant.EventStatusType;
import cn.hanyi.ctm.constant.EventWarningType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

@Data
@AllArgsConstructor
@Getter
@NoArgsConstructor
public class EventCountDto {
    private int allRule;
    private int allStatus;
    private int rule1;
    private int rule2;
    private int rule3;
    private int status0;
    private int status1;
    private int status2;
    private int status3;

    public EventCountDto(List<CountDto> list) {
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(i -> {
                if (i.status == EventStatusType.NONE) {
                    this.status0 += i.count;
                } else if (i.status == EventStatusType.WAIT) {
                    this.status1 += i.count;
                } else if (i.status == EventStatusType.APPLYING) {
                    this.status2 += i.count;
                } else if (i.status == EventStatusType.SUCCESS) {
                    this.status3 += i.count;
                }
                if (i.level == EventWarningType.LOW) {
                    this.rule1 += i.count;
                } else if (i.level == EventWarningType.MIDDLE) {
                    this.rule2 += i.count;
                } else if (i.level == EventWarningType.HIGH) {
                    this.rule3 += i.count;
                }
            });
        }
        this.allRule = this.rule1 + this.rule2 + this.rule3;
        this.allStatus = this.status0 + this.status1 + this.status2 + this.status3;
    }

    private static int unbox(Long i) {
        return i == null ? 0 : (int) i.longValue();
    }

    public static class CountDto {
        EventStatusType status;
        EventWarningType level;
        int count;

        public CountDto(EventStatusType status, EventWarningType level, Long count) {
            this.status = status;
            this.level = level;
            this.count = unbox(count);
        }
    }
}