package cn.hanyi.ctm.dto.group;

import cn.hanyi.ctm.constant.group.QueryOptType;
import cn.hanyi.ctm.constant.group.QueryValueType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EventGroupQueryBuilderDto {

    @Schema(description = "查询属性描述")
    private String propertyLabel;

    @Schema(description = "模版id")
    private Integer templateId;

    @Schema(description = "查询属性名称")
    private String propertyName;

    @JsonIgnore
    @Schema(description = "查询属性字段名")
    private String propertyColumn;

    @JsonIgnore
    @Schema(description = "查询属性来源：customer stat group extend")
    private String propertySource;

    @JsonIgnore
    @Schema(description = "查询属性类型：string date datetime long arrayString")
    private String propertyType;

    @Schema(description = "输入类型：TEXT，ARRAY_TEXT，NUMBER，DATE，SINGLE_CHOICE，MULTIPLE_CHOICE NONE(自定义属性) ")
    private String inputType;

    @Schema(description = "该属性支持的查询条件")
    private List<QueryBuilderItemDto> queryItems;

    public EventGroupQueryBuilderDto(String propertyLabel, String propertyName, List<QueryBuilderItemDto> queryItems) {
        this.propertyLabel = propertyLabel;
        this.propertyName = propertyName;
        this.queryItems = queryItems;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QueryBuilderItemDto {

        @Schema(description = "查询条件类型描述")
        private String queryTypeLabel;

        @Schema(description = "查询条件类型")
        private QueryOptType queryType;

        @Schema(description = "查询值类型")
        private QueryValueType queryValueType;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QueryBuilderValueOptionDto {

        @Schema(description = "选项值")
        private String value;

        @Schema(description = "选项描述")
        private String label;

    }

}
