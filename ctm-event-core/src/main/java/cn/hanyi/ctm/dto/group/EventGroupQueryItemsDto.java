package cn.hanyi.ctm.dto.group;

import cn.hanyi.ctm.constant.group.QueryOptType;
import cn.hanyi.ctm.constant.group.QueryValueType;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EventGroupQueryItemsDto {

        @Schema(description = "查询条件类型描述")
        @JsonView(ResourceViews.Basic.class)
        private String propertyName;

        @Schema(description = "模版id")
        @JsonView(ResourceViews.Basic.class)
        private Integer templateId;

        @Schema(description = "查询条件类型描述")
        @JsonView(ResourceViews.Basic.class)
        private String queryTypeLabel;

        @Schema(description = "查询条件类型")
        @JsonView(ResourceViews.Basic.class)
        private QueryOptType queryType;

        @Schema(description = "查询值类型")
        @JsonView(ResourceViews.Basic.class)
        private QueryValueType queryValueType;

        @Schema(description = "查询条件类型描述")
        @JsonView(ResourceViews.Basic.class)
        private String queryValue;

        public String tableAlias() {
                return templateId + "_" + propertyName;
        }

}
