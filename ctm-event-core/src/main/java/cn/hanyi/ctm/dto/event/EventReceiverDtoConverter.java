package cn.hanyi.ctm.dto.event;

import org.apache.commons.lang3.StringUtils;
import org.befun.core.utils.JsonHelper;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.List;

@Converter
public class EventReceiverDtoConverter implements AttributeConverter<List<EventReceiverDto>, String> {


    @Override
    public String convertToDatabaseColumn(List<EventReceiverDto> list) {
        if (list == null) {
            return null;
        } else {
            return JsonHelper.toJson(list);
        }
    }

    @Override
    public List<EventReceiverDto> convertToEntityAttribute(String dbData) {
        if (StringUtils.isEmpty(dbData)) {
            return null;
        } else {
            return JsonHelper.toList(dbData, EventReceiverDto.class);
        }
    }
}