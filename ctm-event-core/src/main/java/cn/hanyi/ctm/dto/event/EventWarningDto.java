package cn.hanyi.ctm.dto.event;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;
import org.befun.task.BaseTaskDetailDto;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EventWarningDto extends BaseTaskDetailDto{

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "答题ID")
    private Long responseId;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "预警规则ID")
    private Long ruleId;

}