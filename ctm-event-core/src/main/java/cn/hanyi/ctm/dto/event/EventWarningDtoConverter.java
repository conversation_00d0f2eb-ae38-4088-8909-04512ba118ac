package cn.hanyi.ctm.dto.event;

import org.apache.commons.lang3.StringUtils;
import org.befun.core.utils.JsonHelper;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.List;

@Converter
public class EventWarningDtoConverter implements AttributeConverter<List<EventWarningDto>, String> {

    @Override
    public String convertToDatabaseColumn(List<EventWarningDto> list) {
        if (list == null) {
            return null;
        } else {
            return JsonHelper.toJson(list);
        }
    }

    @Override
    public List<EventWarningDto> convertToEntityAttribute(String dbData) {
        if (StringUtils.isEmpty(dbData)) {
            return null;
        } else {
            return JsonHelper.toList(dbData, EventWarningDto.class);
        }
    }
}