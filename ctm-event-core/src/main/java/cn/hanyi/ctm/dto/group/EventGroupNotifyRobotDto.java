package cn.hanyi.ctm.dto.group;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.befun.core.rest.view.ResourceViews;

import java.util.List;

@Getter
@Setter
public class EventGroupNotifyRobotDto {

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "群组通知时间")
    private EventGroupTimerDto notifyRobotTimer;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "群组通知渠道")
    private List<EventGroupRobotChannelDto> notifyRobotChannel;

    public boolean valid() {
        if ( notifyRobotTimer == null || CollectionUtils.isEmpty(notifyRobotChannel)) {
            return false;
        }
        try {
            notifyRobotTimer.check();
        } catch (Throwable e) {
            return false;
        }
        return notifyRobotChannel.stream().anyMatch(EventGroupRobotChannelDto::valid);
    }

}
