package cn.hanyi.ctm.dto.event.download;

import cn.hanyi.ctm.entity.Event;
import cn.hanyi.ctm.entity.EventAction;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.befun.core.utils.DateHelper;
import org.befun.core.utils.JsonHelper;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class EventDownloadDto {
    @ExcelProperty(index = 0, value = "事件编号")
    private String id;
    @ExcelProperty(index = 1, value = "事件生成时间")
    private String createTime;
    @ExcelProperty(index = 2, value = "事件来源")
    private String source;
    @ExcelProperty(index = 3, value = "事件状态")
    private String status;
    @ExcelProperty(index = 4, value = "预警规则")
    private String warningTitle;
    @ExcelProperty(index = 5, value = "所属问卷")
    private String surveyName;
    @ExcelProperty(index = 6, value = "答卷ID")
    private String responseId;
    @ExcelProperty(index = 7, value = "答卷编号")
    private String responseSequence;
    @ExcelProperty(index = 8, value = "客户编号")
    private String customerId;
    @ExcelProperty(index = 9, value = "客户姓名")
    private String customerName;
    @ExcelProperty(index = 10, value = "最后一次处理人")
    private String lastActionUsername;
    @ExcelProperty(index = 11, value = "最后一次处理时间")
    private String lastActionTime;
    @ExcelProperty(index = 12, value = "事件全部备注")
    private String allRemark;
    @ExcelProperty(index = 13, value = "最后一次备注人")
    private String lastRemarkUsername;
    @ExcelProperty(index = 14, value = "最后一次备注时间")
    private String lastRemarkTime;
    @ExcelProperty(index = 15, value = "外部参数")
    private String parameters;
    @ExcelProperty(index = 16, value = "层级ID")
    private String departmentId;
    @ExcelProperty(index = 17, value = "部门名称")
    private String departmentName;
    @ExcelProperty(index = 18, value = "客户性别")
    private String customerGender;
    @ExcelProperty(index = 19, value = "外部组织编号")
    private String departmentCode;
    @ExcelProperty(index = 20, value = "外部企业ID")
    private String externalCompanyId;

    public EventDownloadDto(Event event, Long responseSequence, List<EventAction> remarkActions) {
        this.id = Objects.toString(event.getId(), "");
        this.createTime = Objects.toString(DateHelper.formatDateTime(event.getCreateTime()), "");
        this.source = Objects.toString(event.getSource(), "");
        this.status = Objects.toString(event.getStatus().desc, "");
        this.warningTitle = Objects.toString(event.getWarningTitle().replace(";", "/"), "");
        this.surveyName = Objects.toString(event.getSurveyName(), "");
        this.responseId = Objects.toString(event.getResponseId(), "");
        this.responseSequence = Objects.toString(responseSequence, "");
        this.customerId = Objects.toString(event.getExternalUserId(), "");
        this.customerName = Objects.toString(event.getCustomerName(), "");
        this.lastActionUsername = Objects.toString(event.getLastActionUsername(), "");
        this.lastActionTime = Objects.toString(DateHelper.formatDateTime(event.getLastActionTime()), "");
        this.departmentId = Objects.toString(event.getDepartmentId(), "");
        this.departmentName = Objects.toString(event.getDepartmentName(), "");
        this.customerGender = Objects.toString(event.getCustomerGender(), "");
        this.departmentCode = Objects.toString(event.getDepartmentCode(), "");
        this.externalCompanyId = Objects.toString(event.getExternalCompanyId(), "");
        if (MapUtils.isNotEmpty(event.getExternalParameters())) {
            this.parameters = JsonHelper.toJson(event.getExternalParameters());
        } else {
            this.parameters = "";
        }

        if (CollectionUtils.isEmpty(remarkActions)) {
            allRemark = "";
            lastRemarkUsername = "";
            lastRemarkTime = "";
        } else {
            allRemark = JsonHelper.toJson(remarkActions.stream().map(EventAction::getContent).filter(Objects::nonNull).collect(Collectors.toList()));
            EventAction lastRemark = remarkActions.get(remarkActions.size() - 1);
            lastRemarkUsername = Objects.toString(lastRemark.getActionUsername(), "");
            lastRemarkTime = Objects.toString(DateHelper.formatDateTime(lastRemark.getCreateTime()), "");
        }
    }

}
