package cn.hanyi.ctm.dto.event.ext;

import cn.hanyi.ctm.entity.CustomerDto;
import cn.hanyi.ctm.entity.Event;
import cn.hanyi.ctm.entity.EventGroupDto;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.entity.Department;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class EventExtDto extends BaseEntityDTO<Event> {

    public EventExtDto() {
    }

    public EventExtDto(Event entity) {
        super(entity);
    }

    @JsonView(ResourceViews.Basic.class)
    private CustomerDto customer;

    @JsonView(ResourceViews.Basic.class)
    private Department department;

    @JsonView(ResourceViews.Basic.class)
    private String pushIds;

    @JsonView(ResourceViews.Basic.class)
    private String tags;

    @JsonView(ResourceViews.Basic.class)
    private Long responseSequence;

    @JsonView(ResourceViews.Basic.class)
    private EventResourceDataDto resourceData;

    @JsonView(ResourceViews.Basic.class)
    private List<EventGroupDto> groups = new ArrayList<>();

    @JsonView(ResourceViews.Basic.class)
    private String channelType;

}
