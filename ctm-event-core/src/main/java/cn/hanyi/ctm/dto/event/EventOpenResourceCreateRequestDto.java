package cn.hanyi.ctm.dto.event;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.utils.DateHelper;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EventOpenResourceCreateRequestDto {

    @Schema(description = "事件id")
    private Long eventId;
    @Schema(description = "过期时间")
    private String expireTime;
    @Schema(description = "通过md5处理后密码")
    private String password;

    public Date parseExpireTime() {
        return StringUtils.isEmpty(expireTime) ? null : DateHelper.toDate(DateHelper.parseDateTime(expireTime));
    }
}
