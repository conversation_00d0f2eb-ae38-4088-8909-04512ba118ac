package cn.hanyi.ctm.dto.event;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.befun.auth.entity.User;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class EventUserDto {
    private Long userId;
    private Long orgId;
    private String realName;
    private String mobile;
    private List<Long> departmentIds;

    public static List<EventUserDto> mapList(List<User> list, Predicate<User> filter) {
        return Optional.ofNullable(list)
                .map(l -> l.stream()
                        .filter(i -> Objects.nonNull(i) && filter.test(i))
                        .map(EventUserDto::map)
                        .collect(Collectors.toList()))
                .orElse(null);
    }

    public static EventUserDto map(User user) {
        return EventUserDto.builder()
                .userId(user.getId())
                .orgId(user.getOrgId())
                .realName(user.getTruename())
                .mobile(user.getMobile())
                .departmentIds(user.parseDepartmentIds2())
                .build();
    }

    public String concatName() {
        return realName;
    }


}