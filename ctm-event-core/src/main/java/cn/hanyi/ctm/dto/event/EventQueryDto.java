package cn.hanyi.ctm.dto.event;

import cn.hanyi.ctm.constant.EventStatusType;
import cn.hanyi.ctm.constant.EventWarningType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.validation.ValidSearchText;

@Getter
@Setter
public class EventQueryDto {
    @Schema(defaultValue = "页码",name = "_page")
    public int _page = 1;
    @Schema(defaultValue = "每页数量",name = "_limit")
    public int _limit = 20;
    @Schema(defaultValue = "关键字",name = "_q")
    @ValidSearchText
    public String _q;
    @Schema(defaultValue = "预警级别")
    public EventWarningType warningLevel;
    @Schema(defaultValue = "预警级别!=")
    public EventWarningType warningLevel_neq;
    @Schema(defaultValue = "状态")
    public EventStatusType status;
    @Schema(defaultValue = "提交时间>=")
    public String responseTime_gte;
    @Schema(defaultValue = "提交时间<=")
    public String responseTime_lte;
    @Schema(defaultValue = "行动时间>=")
    public String lastActionTime_gte;
    @Schema(defaultValue = "行动时间<=")
    public String lastActionTime_lte;
    @Schema(defaultValue = "是否有预警推送:true|false")
    public Boolean hasPush;
}
