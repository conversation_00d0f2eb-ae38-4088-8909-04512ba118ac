package cn.hanyi.ctm.dto.group;

import cn.hanyi.ctm.constant.connector.ConnectorType;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.rest.view.ResourceViews;

import java.util.List;

import static cn.hanyi.ctm.constant.connector.ConnectorType.*;

@Getter
@Setter
public class EventGroupRobotChannelDto {

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "是否启用")
    private boolean enabled;
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "类型: WECHAT_WORK, DING_DING, FEI_SHU, WEBHOOK")
    private ConnectorType type;
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "地址")
    private String url;
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "自定义 webhook id")
    private Long webhookId;

    public boolean valid() {
        if (!enabled) {
            return false;
        }
        if (List.of(WECHAT_WORK, DING_DING, FEI_SHU).contains(type)) {
            return !StringUtils.isEmpty(url);
        } else if (WEBHOOK == type) {
            return webhookId != null && webhookId > 0;
        }
        return false;
    }
}
