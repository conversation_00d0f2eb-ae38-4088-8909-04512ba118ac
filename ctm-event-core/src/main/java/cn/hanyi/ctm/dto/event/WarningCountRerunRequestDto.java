package cn.hanyi.ctm.dto.event;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.math3.util.Pair;
import org.befun.core.dto.BaseDTO;
import org.befun.core.exception.BadRequestException;
import org.befun.core.utils.DateHelper;

import javax.validation.constraints.NotEmpty;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Setter
@Getter
@NoArgsConstructor
public class WarningCountRerunRequestDto extends BaseDTO {
    @NotEmpty
    @Schema(description = "数据范围：all 全部，waiting 待分析，dateRange 时间区间")
    private String dataScope;
    @Schema(description = "时间区间，包含两个日期（,）分隔")
    private List<String> dateRange;

    public WarningCountRerunRequestDto(String dataScope, String dateRange) {
        this.dataScope = dataScope;
        if (dateRange != null) {
            this.dateRange = Arrays.stream(dateRange.split(",")).collect(Collectors.toList());
        }
    }

    public boolean isValid() {
        if (List.of("all", "waiting", "dateRange").contains(dataScope)) {
            return !"dateRange".equals(dataScope) || parseDateRange() != null;
        }
        return false;
    }

    public Pair<LocalDateTime, LocalDateTime> parseDateRange() {
        if (dateRange != null && dateRange.size() == 2) {
            LocalDate start = DateHelper.parseDate(dateRange.get(0));
            LocalDate end = DateHelper.parseDate(dateRange.get(1));
            if (start != null && end != null && (start.equals(end) || start.isBefore(end))) {
                return Pair.create(start.atStartOfDay(), end.plusDays(1).atStartOfDay());
            }
        }
        return null;
    }

    public Pair<LocalDateTime, LocalDateTime> parseDateRangeThrowable() {
        return Optional.ofNullable(parseDateRange()).orElseThrow(() -> new BadRequestException("时间区间错误"));
    }
}
