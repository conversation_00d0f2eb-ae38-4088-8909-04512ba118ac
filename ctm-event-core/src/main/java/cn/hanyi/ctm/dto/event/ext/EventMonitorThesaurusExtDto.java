package cn.hanyi.ctm.dto.event.ext;

import cn.hanyi.ctm.dto.user.SimpleUserDto;
import cn.hanyi.ctm.entity.EventMonitorThesaurus;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;

@Getter
@Setter
public abstract class EventMonitorThesaurusExtDto extends BaseEntityDTO<EventMonitorThesaurus> {
    public EventMonitorThesaurusExtDto(EventMonitorThesaurus entity) {
        super(entity);
    }

    public EventMonitorThesaurusExtDto() {
    }

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "创建人")
    private SimpleUserDto creator;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "修改人")
    private SimpleUserDto editor;

}
