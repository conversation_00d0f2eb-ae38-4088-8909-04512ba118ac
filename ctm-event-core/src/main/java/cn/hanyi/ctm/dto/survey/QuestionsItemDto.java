package cn.hanyi.ctm.dto.survey;


import com.fasterxml.jackson.annotation.JsonView;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;
import org.befun.core.rest.view.ResourceViews;


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class QuestionsItemDto extends BaseDTO {
    @JsonView(ResourceViews.Basic.class)
    private String name;
    @JsonView(ResourceViews.Basic.class)
    private String text;
}
