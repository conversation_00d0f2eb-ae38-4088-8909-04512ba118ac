package cn.hanyi.ctm.dto.event.ext;

import cn.hanyi.ctm.dto.connector.ConsumerConnectorBotDto;
import cn.hanyi.ctm.entity.EventMonitorRules;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public class EventMonitorRulesExtDto extends BaseEntityDTO<EventMonitorRules> {

    public EventMonitorRulesExtDto(EventMonitorRules entity) {
        super(entity);
    }

    public EventMonitorRulesExtDto() {
    }

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "预警推送")
    private List<ConsumerConnectorBotDto> consumer = new ArrayList<>();

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "是否重跑")
    private Boolean rerun = false;
}
