package cn.hanyi.ctm.dto.survey;

import cn.hanyi.ctm.entity.EventMonitorRules;
import cn.hanyi.survey.core.dto.message.SurveyResponseCellMessageDto;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Setter
@Getter
public class SurveyResponseCellMessageRuleDto extends SurveyResponseCellMessageDto {
    @JsonView(ResourceViews.Basic.class)
    private EventMonitorRules eventMonitorRules;

    @JsonView(ResourceViews.Basic.class)
    private Integer max;

    @JsonView(ResourceViews.Basic.class)
    private String unit;

    @JsonView(ResourceViews.Basic.class)
    private String total;
}
