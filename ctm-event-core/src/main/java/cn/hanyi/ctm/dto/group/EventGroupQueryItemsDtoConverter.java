package cn.hanyi.ctm.dto.group;

import org.befun.core.utils.JsonHelper;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter
public class EventGroupQueryItemsDtoConverter implements AttributeConverter<EventGroupQueryItemsDto, String> {

    @Override
    public String convertToDatabaseColumn(EventGroupQueryItemsDto dto) {
        return JsonHelper.toJson(dto);
    }

    @Override
    public EventGroupQueryItemsDto convertToEntityAttribute(String dbData) {
        return JsonHelper.toObject(dbData, EventGroupQueryItemsDto.class);
    }
}