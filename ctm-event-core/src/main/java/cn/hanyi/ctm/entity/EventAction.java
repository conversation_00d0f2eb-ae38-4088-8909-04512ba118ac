package cn.hanyi.ctm.entity;

import cn.hanyi.ctm.constant.EventActionStatusType;
import cn.hanyi.ctm.constant.EventActionType;
import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.converter.LongCommaListConverter;
import org.befun.core.converter.StringListConverter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Entity
@Table(name = "event_action")
@DtoClass(includeAllFields = true)
public class EventAction extends EnterpriseEntity {

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "survey_id")
    private Long surveyId;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "content")
    @Size(max = 2000, message = "字数不能超过2000")
    private String content;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "action_type")
    @Enumerated
    private EventActionType actionType;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "action_user_id")
    private Long actionUserId;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "action_username")
    private String actionUsername;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "action_status")
    private EventActionStatusType actionStatus;

    @ManyToOne()
    @JoinColumn(name = "event_id", nullable = false)
    @JsonBackReference
    private Event event;

    @JoinColumn(name = "target_user_Ids")
    @Convert(converter = LongCommaListConverter.class)
    private List<Long> targetUserIds = new ArrayList<>();

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "attachments")
    @Schema(description = "附件")
    @Convert(converter = StringListConverter.class)
    private List<String> attachments;
}
