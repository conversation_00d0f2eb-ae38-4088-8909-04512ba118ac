package cn.hanyi.ctm.entity;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Getter
@Setter
@Entity
@Table(name = "event_result_rule_relation")
public class EventRuleRelation extends BaseEntity {

    @Column(name = "event_id")
    @JsonView(ResourceViews.Basic.class)
    private Long eventId;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "rule_id")
    private Long ruleId;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "response_id")
    private Long responseId;
}
