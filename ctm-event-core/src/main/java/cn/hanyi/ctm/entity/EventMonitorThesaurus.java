package cn.hanyi.ctm.entity;

import cn.hanyi.ctm.constant.event.EventMonitorStatus;
import cn.hanyi.ctm.dto.event.ext.EventMonitorThesaurusExtDto;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.entity.annotation.SoftDelete;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Entity
@Data
@Table(name = "event_monitor_thesaurus")
@SQLDelete(sql = "UPDATE event_monitor_thesaurus SET deleted = 1 WHERE id=?")
@Where(clause = "deleted=0")
@SoftDelete
@EntityScopeStrategy
@DtoClass(includeAllFields = true, superClass = EventMonitorThesaurusExtDto.class)
public class EventMonitorThesaurus extends EnterpriseEntity {

    @Schema(description = "用户id")
    @Column(name = "user_id")
    @JsonIgnore
    protected Long userId;

    @Schema(description = "修改者id")
    @JsonIgnore
    @Column(name = "editor_id")
    protected Long editorId;

    @JsonIgnore
    @Column(columnDefinition = "bit(1) default 0")
    private Boolean deleted = false;

    @Schema(description = "词库名称")
    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    private String title;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "是否启用")
    private EventMonitorStatus status = EventMonitorStatus.OPEN;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "词库内容 /分隔")
    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    private String thesaurus;


    @PrePersist
    public void prePersist() {
        this.setUserId(TenantContext.getCurrentUserId());
        this.setEditorId(TenantContext.getCurrentUserId());
    }

    @PreUpdate
    void preUpdate() {
        this.setEditorId(TenantContext.getCurrentUserId());
    }

    public List<String> toList() {
        return getThesaurus() == null ? new ArrayList<>() : List.of(getThesaurus().split("/"));
    }


}
