package cn.hanyi.ctm.entity;

import cn.hanyi.ctm.constant.group.GroupType;
import cn.hanyi.ctm.constant.group.QueryLogic;
import cn.hanyi.ctm.dto.group.EventGroupNotifyRobotDto;
import cn.hanyi.ctm.dto.group.EventGroupNotifyUserDto;
import cn.hanyi.ctm.dto.group.EventGroupQueryPropertyDto;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.constant.EntityScopeStrategyType;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.EnterpriseOwnerEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.hibernate.types.JsonColumn;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

@Entity
@Getter
@Setter
@Table(name = "event_result_group")
@AllArgsConstructor
@NoArgsConstructor
@EntityScopeStrategy(value = EntityScopeStrategyType.OWNER_CORPORATION, resource = "EVENT_GROUP")
@DtoClass
public class EventGroup extends EnterpriseOwnerEntity {

    @Schema(description = "顺序")
    @JsonView(ResourceViews.Basic.class)
    private int sequence = 0;

    @Size(max = 200, message = "组名长度超过限制")
    @Schema(description = "组名")
    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    private String title = "";

    @Schema(description = "类型")
    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @Enumerated(EnumType.STRING)
    private GroupType type = GroupType.AUTO;

//    @OneToMany(mappedBy = "group", cascade = {CascadeType.ALL}, orphanRemoval = true, fetch = FetchType.LAZY)
//    @OrderBy("sequence")
//    @DtoProperty(type = EventGroupQueryDto.class)
//    private List<EventGroupQuery> queries = new ArrayList<>();

    @Embedded
    @DtoProperty(jsonView = ResourceViews.Detail.class)
    private List<EventGroupQueryPropertyDto> groups = new ArrayList<>();

    @Schema(description = "查询逻辑")
    @DtoProperty(jsonView = ResourceViews.Detail.class)
    @Enumerated(EnumType.STRING)
    private QueryLogic logic = QueryLogic.and;

    @Schema(description = "按照当前用户的所属部门过滤数据")
    @DtoProperty(jsonView = ResourceViews.Basic.class)
    private boolean withDepartment = false;

    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @Schema(description = "成员通知")
    @Column(name = "notify_user")
    private Boolean notifyUser = false;

    @Type(type = JsonColumn.TYPE)
    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @Schema(description = "成员通知内容")
    @Column(name = "notify_user_content")
    private EventGroupNotifyUserDto notifyUserContent;

    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @Schema(description = "群组通知")
    @Column(name = "notify_robot")
    private Boolean notifyRobot = false;

    @Type(type = JsonColumn.TYPE)
    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @Schema(description = "群组通知内容")
    @Column(name = "notify_robot_content")
    private EventGroupNotifyRobotDto notifyRobotContent;


}
