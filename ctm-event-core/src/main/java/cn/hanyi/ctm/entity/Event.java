package cn.hanyi.ctm.entity;

import cn.hanyi.ctm.constant.EventStatusType;
import cn.hanyi.ctm.constant.EventWarningType;
import cn.hanyi.ctm.constant.survey.ResponseStatus;
import cn.hanyi.ctm.constant.survey.SurveyChangeStatus;
import cn.hanyi.ctm.dto.event.EventWarningDto;
import cn.hanyi.ctm.dto.event.EventWarningDtoConverter;
import cn.hanyi.ctm.dto.event.SimpleEventMonitorRules;
import cn.hanyi.ctm.dto.event.ext.EventExtDto;
import cn.hanyi.ctm.dto.survey.SurveyResponseCellMessageRuleDto;
import cn.hanyi.ctm.dto.survey.SurveyResponseDataDtoConverter;
import cn.hanyi.survey.core.constant.question.QuestionType;
import cn.hanyi.survey.core.dto.message.QuestionsItemDto;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.converter.HashMapConverter;
import org.befun.core.converter.MapListConverter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.entity.annotation.SoftDelete;
import org.befun.core.rest.view.ResourceViews;
import org.befun.core.utils.JsonHelper;
import org.hibernate.annotations.Filter;
import org.hibernate.annotations.FilterDef;
import org.hibernate.annotations.ParamDef;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

@Getter
@Setter
@Entity
@Table(name = "event_result")
@Where(clause = "deleted=0")
// 有问卷权限的，不用部门过滤，看到全部预警, 没有问卷权限，有被预警通知的，可以根据部门看到预警
@FilterDef(
        name = "eventCustomFilter",
        parameters = {
                @ParamDef(name = "orgId", type = "long"),
                @ParamDef(name = "userId", type = "long"),
                @ParamDef(name = "roleIds", type = "long"),
                @ParamDef(name = "departmentIds", type = "long"),
                @ParamDef(name = "subDepartmentIds", type = "long"),
        },
        defaultCondition = " org_id=:orgId and (" +
                " survey_id in (select s.id from survey s where s.org_id = :orgId and (" +
                "    s.user_id = :userId " +
                "    or s.id in (select rc.resource_id from resource_permission as rc where rc.org_id = :orgId and rc.resource_type = 'SURVEY' and (rc.user_id = :userId or rc.role_id in (:roleIds) or rc.department_id in (:departmentIds)))" +
                "    or s.group_id in (select rc.resource_id from resource_permission as rc where rc.org_id = :orgId and rc.resource_type = 'SURVEY_GROUP' and (rc.user_id = :userId or rc.role_id in (:roleIds) or rc.department_id in (:departmentIds)))" +
                " ))" +
                " or" +
                " (id in (select rp.resource_id from resource_permission rp where rp.org_id=:orgId and rp.resource_type='EVENT' and rp.user_id=:userId))" +
                " )"
)
@Filter(name = "eventCustomFilter")
@EntityScopeStrategy(value = {}, filterNames = "eventCustomFilter")
@DtoClass(includeAllFields = true, superClass = EventExtDto.class)
@SoftDelete
public class Event extends EnterpriseEntity {

    @Column(name = "customer_id")
    @JsonView(ResourceViews.Basic.class)
    private Long customerId;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "survey_id")
    private Long surveyId;

    @JsonIgnore
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "external_user_id")
    private String externalUserId;

    @Schema(description = "部门名称")
    @Column(name = "department_name")
    private String departmentName;

    @Schema(description = "客户名称")
    @Column(name = "customer_name")
    private String customerName;

    @Schema(description = "客户性别")
    @Column(name = "customer_gender")
    private String customerGender;

    @Schema(description = "外部组织编号")
    @Column(name = "department_code")
    private String departmentCode;

    @Schema(description = "外部企业ID")
    @Column(name = "external_company_id")
    private String externalCompanyId;

    @Schema(description = "默认参数")
    @Column(name = "default_pa")
    private String defaultPa;

    @Schema(description = "默认参数")
    @Column(name = "default_pb")
    private String defaultPb;

    @Schema(description = "默认参数")
    @Column(name = "default_pc")
    private String defaultPc;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "答卷状态")
    @Column(name = "response_status")
    private ResponseStatus responseStatus = ResponseStatus.FINAL_SUBMIT;

    @Column(name = "department_id")
    @JsonView(ResourceViews.Basic.class)
    private Long departmentId;

    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "survey_name")
    private String surveyName;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "survey_version")
    private Integer surveyVersion;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "warning_level")
    @Enumerated
    @Schema(description = "可通过_by条件分组统计数量")
    private EventWarningType warningLevel;

    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    @Column(name = "warning_title")
    private String warningTitle;

    @JsonView(ResourceViews.Basic.class)
    @Convert(converter = SurveyResponseDataDtoConverter.class)
    @Column(name = "questions", columnDefinition = "TEXT")
    private List<SurveyResponseCellMessageRuleDto> questions;

    @JsonView(ResourceViews.Basic.class)
    @Convert(converter = HashMapConverter.class)
    private HashMap parameters = new HashMap();

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "source")
    private String source;

    @Convert(converter = EventWarningDtoConverter.class)
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "warnings", columnDefinition = "TEXT")
    private List<EventWarningDto> warnings = new ArrayList<>();

    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    @Column(name = "last_action_username")
    private String lastActionUsername;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "last_action_time")
    private Date lastActionTime;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "status")
    @Schema(description = "可通过_by条件分组统计数量")
    private EventStatusType status = EventStatusType.NONE;

    @JsonIgnore
    @Column(columnDefinition = "bit(1) default 0")
    private Boolean deleted = false;

//    @OneToMany(mappedBy = "event", fetch = FetchType.EAGER)
//    @JsonManagedReference
//    @JsonView(ResourceViews.Detail.class)
//    private List<EventAction> actions;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "response_time")
    private Date responseTime;

    @Column(name = "response_id")
    @JsonView(ResourceViews.Basic.class)
    private Long responseId;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "push_ids")
    private String pushIds;

    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    @Column(name = "tags")
    private String tags;

    @Transient
    @DtoProperty(jsonView = ResourceViews.Basic.class, type = SurveyResponseCellMessageRuleDto.class)
    @Convert(converter = MapListConverter.class)
    private SurveyResponseCellMessageRuleDto question;


    @Transient
    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @Schema(description = "命中预警条件的题型id-map")
    private HashMap<Long, SimpleEventMonitorRules> questionIdRules;

    @Transient
    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @Schema(description = "问卷状态")
    private SurveyChangeStatus surveyStatus;

    @PostLoad
    private void postLoad() {
        // 需求需要前端随机展示一道题
        if (questions != null && !questions.isEmpty()) {
            // 过滤题组
            questions.removeIf(q -> QuestionType.GROUP.equals(q.getType()));

            // 评价题需要把value转成数字
            List<Integer> evaluationType = List.of(QuestionType.SCORE_EVALUATION.ordinal(), QuestionType.EVALUATION.ordinal());
            List<Integer> orderType = List.of(
                    QuestionType.RANKING.ordinal(),
                    QuestionType.MATRIX_SLIDER.ordinal(),
                    QuestionType.MATRIX_CHOICE.ordinal(),
                    QuestionType.MATRIX_SCORE.ordinal(),
                    QuestionType.BLANK.ordinal(),
                    QuestionType.MULTIPLE_BLANK.ordinal()
            );
            HashMap<Long, String> score_evaluation_value = new HashMap<>();
            questions.stream().filter(q -> q.getType() != null && evaluationType.contains(q.getType().ordinal())).forEach(q -> {
                q.getQuestionsItems().forEach((key, value) -> {
                    value.getItems().stream().filter(i -> i.getName().equals(value.getValue())).anyMatch(i -> {
                        score_evaluation_value.put(q.getQuestionId(), i.getText() + "/" + (value.getItems().indexOf(i) + 1));
                        return true;
                    });
                });
            });

            buildQuestions(questions);

            for (SurveyResponseCellMessageRuleDto q : questions) {
                if (q.getType() == null) {
                    continue;
                }
                if (evaluationType.contains(q.getType().ordinal())) {
                    q.setValue(score_evaluation_value.get(q.getQuestionId()));
                }
                if (orderType.contains(q.getType().ordinal())) {
                    List<HashMap> orderValue = new ArrayList<>();

                    q.getQuestionsItems().forEach((key, value) -> {
                        if (value != null && value.getItems() != null) {
                            value.getItems().stream().filter(i -> value.getValue() != null && i != null && StringUtils.isNotEmpty(Objects.toString(((Map) value.getValue()).get(i.getName())))).forEach(i -> {
                                HashMap<String, Object> map = new HashMap<>();
                                map.put("key", i.getText());
                                Object v;
                                if (QuestionType.MATRIX_CHOICE.ordinal() == q.getType().ordinal()) {
                                    v = value.getColumns().stream()
                                            .filter(c -> c.getName().equals(((Map) value.getValue()).get(i.getName())))
                                            .map(QuestionsItemDto::getText)
                                            .findFirst()
                                            .orElse("");
                                } else {
                                    v = ((Map) value.getValue()).get(i.getName());
                                }
                                map.put("value", v);
                                orderValue.add(map);
                            });
                        }
                    });

                    if (!orderValue.isEmpty()) {
                        q.setValue(orderValue);
                    }
                }
            }
        }

        warnings = warnings == null ? new ArrayList<>() : warnings;

        if (!warnings.isEmpty() && responseId == null) {
            responseId = warnings.get(0).getResponseId();
        }

        responseStatus = responseStatus == null ? ResponseStatus.FINAL_SUBMIT : responseStatus;
    }

    private void buildQuestions(List<SurveyResponseCellMessageRuleDto> questions) {
        buildChoiceComment(questions);

        // 将questions name转成文字
        questions.forEach(question -> {
            question.convertText(false);
        });
    }

    private void buildChoiceComment(List<SurveyResponseCellMessageRuleDto> questions) {
        questions.forEach(question -> {
            try {
                if (question.getType()!=null && List.of(QuestionType.SINGLE_CHOICE.name(), QuestionType.MULTIPLE_CHOICES.name()).contains(question.getType().name())) {
                    AtomicReference<String> comment = new AtomicReference<>("");
                    if (StringUtils.isNotEmpty((String) question.getComment())) {
                        JsonHelper.toMap((String) question.getComment()).forEach((k, v) -> {
                            question.getQuestionsItems().values().forEach(i -> {
                                i.getItems().stream().filter(item -> item.getName().equals(k)).forEach(item -> {
                                    comment.set(comment.get() + item.getText() + "：" + v + ";");
                                });
                            });

                        });
                    }
                    question.setComment(comment.get());
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    public HashMap getParameters() {

        HashMap<String, Object> parametersMap = new HashMap<>();
        IdentityHashMap<Object, String> fixedParametersMap = new IdentityHashMap<>() {
            {
                put(externalUserId, "外部客户编号");
                put(departmentCode, "外部组织编号");
                put(externalCompanyId, "外部企业ID");
                put(departmentName, "部门名称");
                put(customerName, "客户名称");
                put(customerGender, "客户性别");
                put(defaultPa, "默认参数a");
                put(defaultPb, "默认参数b");
                put(defaultPc, "默认参数c");
            }
        };

        fixedParametersMap.entrySet().stream().filter(entry -> entry.getKey() != null && !"null".equals(entry.getKey())).forEach(entry -> {
            parametersMap.put(entry.getValue(), entry.getKey());
        });
        Optional.ofNullable(parameters).ifPresent(parametersMap::putAll);
        return parametersMap;
    }

    public HashMap getExternalParameters() {
        return parameters;
    }

}
