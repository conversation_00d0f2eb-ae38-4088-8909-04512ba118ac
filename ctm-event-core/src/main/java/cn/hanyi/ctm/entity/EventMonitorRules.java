package cn.hanyi.ctm.entity;

import cn.hanyi.ctm.constant.EventWarningType;
import cn.hanyi.ctm.constant.event.EventMonitorStatus;
import cn.hanyi.ctm.constant.event.EventSurveyStatus;
import cn.hanyi.ctm.constant.event.EventSurveyType;
import cn.hanyi.ctm.dto.event.EventReceiverDto;
import cn.hanyi.ctm.dto.event.EventReceiverDtoConverter;
import cn.hanyi.ctm.dto.event.ext.EventMonitorRulesExtDto;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.befun.core.converter.LongCommaListConverter;
import org.befun.core.converter.StringCommaListConverter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.entity.annotation.SoftDelete;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.*;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Entity
@Data
@Table(name = "event_monitor_rules")
@SQLDelete(sql = "UPDATE event_monitor_rules SET deleted = 1 WHERE id=?")
@Where(clause = "deleted=0")
@FilterDef(
        name = "eventRuleCustomFilter",
        parameters = {
                @ParamDef(name = "orgId", type = "long"),
                @ParamDef(name = "userId", type = "long"),
                @ParamDef(name = "departmentIds", type = "long"),
                @ParamDef(name = "roleIds", type = "long")
        },
        defaultCondition = " org_id = :orgId and s_id in" +
                " (select s.id from survey s where s.org_id = :orgId and (" +
                "    s.user_id = :userId " +
                "    or s.id in (select rc.resource_id from resource_permission as rc where rc.org_id = :orgId and rc.resource_type = 'SURVEY' and (rc.user_id = :userId or rc.role_id in (:roleIds) or rc.department_id in (:departmentIds)))" +
                "    or s.group_id in (select rc.resource_id from resource_permission as rc where rc.org_id = :orgId and rc.resource_type = 'SURVEY_GROUP' and (rc.user_id = :userId or rc.role_id in (:roleIds) or rc.department_id in (:departmentIds)))" +
                " ))"
)
@Filter(name = "eventRuleCustomFilter")
@EntityScopeStrategy(value = {}, filterNames = "eventRuleCustomFilter")
@DtoClass(includeAllFields = true, superClass = EventMonitorRulesExtDto.class)
@SoftDelete
public class EventMonitorRules extends EnterpriseEntity {

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "user_id")
    private Long userId;

    @JsonIgnore
    @Column(columnDefinition = "bit(1) default 0")
    private Boolean deleted = false;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "问卷ID")
    @Column(name = "s_id")
    private Long surveyId;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "题型id列表")
    @Column(name = "question_ids")
    @Convert(converter = LongCommaListConverter.class)
    private List<Long> questionIds;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "题库id列表")
    @Column(name = "thesaurus_ids")
    @Convert(converter = LongCommaListConverter.class)
    private List<Long> thesaurusIds;

    @JsonView(ResourceViews.Detail.class)
    @Column(name = "role_id")
    @Schema(description = "登录用户角色ID")
    private Long roleId;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "survey_name")
    @Schema(description = "问卷名称")
    private String surveyName;

    @JsonView(ResourceViews.Basic.class)
    @Size(max = 20, message = "预警名称长度超过限制")
    @Schema(description = "预警名称")
    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class, description = "预警名称")
    private String title;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "预警等级")
    private EventWarningType level;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "开关状态")
    private EventMonitorStatus status = EventMonitorStatus.OPEN;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "SEL表达式")
    private String expression;

    @Convert(converter = EventReceiverDtoConverter.class)
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "接受预警用户列表")
    private List<EventReceiverDto> receiver;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "是否需要预警用户")
    @Column(name = "notify_user")
    private Boolean notifyUser = true;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "开启推送")
    @Column(name = "notify_consumer")
    private Boolean notifyConsumer = false;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "创建人")
    @Size(max = 20, message = "创建人名称长度超过限制")
    private String creator;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "修改人")
    @Size(max = 20, message = "编辑者名称长度超过限制")
    private String editor;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "问卷状态")
    @Column(name = "survey_status")
    private EventSurveyStatus surveyStatus = EventSurveyStatus.NONE;


    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "预警问卷类型")
    @Column(name = "survey_type")
    private EventSurveyType surveyType;

    @Convert(converter = StringCommaListConverter.class)
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "tags")
    private List<String> tags;

    @Schema(description = "最后关闭时间")
    @Column(name = "last_close_time")
    private Date lastCloseTime;

}
