package cn.hanyi.ctm.repository;

import cn.hanyi.ctm.entity.EventMonitorThesaurus;
import org.befun.core.repository.ResourceRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface EventMonitorThesaurusRepository extends ResourceRepository<EventMonitorThesaurus, Long> {
    List<EventMonitorThesaurus> findByIdIn(List<Long> ids);
}
