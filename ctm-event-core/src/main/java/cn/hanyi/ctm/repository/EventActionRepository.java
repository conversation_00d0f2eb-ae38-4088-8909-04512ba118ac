package cn.hanyi.ctm.repository;

import cn.hanyi.ctm.constant.EventActionType;
import cn.hanyi.ctm.entity.EventAction;
import java.util.Optional;
import org.befun.core.repository.ResourceRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public interface EventActionRepository extends ResourceRepository<EventAction, Long> {

    List<EventAction> findByEventIdAndActionType(Long eventId, EventActionType type);

    List<EventAction> findByEventIdInAndActionType(Collection<Long> ids, EventActionType actionType);

    List<EventAction> findByEventIdOrderByCreateTimeDesc(Long eventId);

    Optional<EventAction> findFirstByEventIdAndActionTypeOrderByCreateTimeDesc(Long eventId, EventActionType type);
}
