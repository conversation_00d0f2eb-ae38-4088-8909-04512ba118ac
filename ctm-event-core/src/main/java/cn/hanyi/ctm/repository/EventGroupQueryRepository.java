package cn.hanyi.ctm.repository;

import cn.hanyi.ctm.entity.EventGroupQuery;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EventGroupQueryRepository extends ResourceRepository<EventGroupQuery, Long> {

    List<EventGroupQuery> findByGroupId(Long groupId);

    @Modifying
    void deleteByGroupId(Long groupId);
}
