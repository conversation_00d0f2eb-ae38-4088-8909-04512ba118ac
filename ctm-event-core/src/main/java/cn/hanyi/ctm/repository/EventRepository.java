package cn.hanyi.ctm.repository;

import cn.hanyi.ctm.constant.EventStatusType;
import cn.hanyi.ctm.constant.EventWarningType;
import cn.hanyi.ctm.dto.event.EventIdOnlyDto;
import cn.hanyi.ctm.dto.event.EventWarningDto;
import cn.hanyi.ctm.dto.event.EventWarningId;
import cn.hanyi.ctm.entity.Event;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface EventRepository extends ResourceRepository<Event, Long> {
    boolean existsByIdAndStatusIn(Long id, List<EventStatusType> status);
    Optional<Event> findOneBySurveyId(Long surveyId);
    Optional<Event> findOneBySurveyIdAndResponseId(Long surveyId, Long responseId);
    Optional<List<EventIdOnlyDto>> findBySurveyId(Long surveyId);
    List<Event> findByIdInOrderByCreateTimeDesc(List<Long> ids);

    @Query(
            value = "select id,warnings from event_result  where length(warnings) > 2",
            countQuery = "select count(id) from event_result  where length(warnings) > 2",
            nativeQuery = true)
    Page<EventWarningId> queryEventWarnings(Pageable pageable);
    Optional<Event> findOneByResponseId(Long responseId);
}
