package cn.hanyi.ctm.repository;

import cn.hanyi.ctm.entity.EventGroup;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EventGroupRepository extends ResourceRepository<EventGroup, Long> {

    List<EventGroup> findByNotifyUserTrueOrNotifyRobotTrue(Pageable pageable);
}
