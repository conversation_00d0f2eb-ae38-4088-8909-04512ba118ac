package cn.hanyi.ctm.repository;

import cn.hanyi.ctm.constant.event.EventMonitorStatus;
import cn.hanyi.ctm.constant.event.EventSurveyStatus;
import cn.hanyi.ctm.entity.EventMonitorRules;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Repository
public interface EventMonitorRulesRepository extends ResourceRepository<EventMonitorRules, Long> {
    Optional<List<EventMonitorRules>> findBySurveyIdAndStatusAndSurveyStatus(Long surveyId, EventMonitorStatus eventMonitorStatus, EventSurveyStatus eventSurveyStatus);
    Optional<List<EventMonitorRules>> findBySurveyId(Long surveyId);
    @Query(
            nativeQuery = true,
            value = "select * from event_monitor_rules " +
                    "where deleted = 0 and thesaurus_ids like %?1% ")
    List<EventMonitorRules> findByThesaurusIdsContains(Long thesaurusId);

    @Transactional
    @Modifying
    @Query("update EventMonitorRules e set e.status = ?1, e.lastCloseTime = ?2 where e.id in ?3")
    int updateStatusAndLastCloseTimeByIdIn(EventMonitorStatus status, Date lastCloseTime, Collection<Long> ids);
}
