package cn.hanyi.ctm.repository;

import cn.hanyi.ctm.entity.EventRuleRelation;
import org.befun.core.repository.ResourceRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EventRuleRelationRepository extends ResourceRepository<EventRuleRelation, Long> {

    Boolean existsByResponseId(Long responseId);

    List<EventRuleRelation> findByResponseId(Long responseId);
}
