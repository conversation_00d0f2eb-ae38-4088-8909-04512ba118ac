package cn.hanyi.ctm.properites;

import cn.hanyi.ctm.constant.group.QueryOptType;
import cn.hanyi.ctm.constant.group.QueryValueType;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.persistence.Enumerated;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Configuration()
@ConfigurationProperties(prefix = "ctm.event-result.query-config")
@Getter
@Setter
public class EventResultQueryProperties {

    private List<Query> query = new ArrayList<>();
    private Map<String, List<QueryType>> queryType = new HashMap<>();


    public enum ColumnType {
        table,
        function,
        ;
    }

    @Getter
    @Setter
    public static class QueryType {
        private String queryTypeLabel;
        private QueryOptType queryType;
        private QueryValueType queryValueType;
    }

    @Getter
    @Setter
    public static class Query {
        private String propertyName;
        private Integer templateId;
        private String propertyLabel;
        private String propertyType = "string"; // string date datetime long arrayString
        private String propertySource;
        private String propertyOn;
        private String propertyColumn;
        private String queryItemType;
        private String inputType;
        @Enumerated(javax.persistence.EnumType.STRING)
        private ColumnType columnType = ColumnType.table;
    }


}
