package cn.hanyi.ctm.service;

import cn.hanyi.ctm.constant.connector.ConnectorProviderType;
import cn.hanyi.ctm.constant.connector.ConnectorType;
import cn.hanyi.ctm.constant.event.EventMonitorStatus;
import cn.hanyi.ctm.dto.connector.ConnectorConsumerBotContentDto;
import cn.hanyi.ctm.dto.connector.ConsumerConnectorBotDto;
import cn.hanyi.ctm.dto.event.TaskEventWarningDto;
import cn.hanyi.ctm.dto.event.WarningCountRerunRequestDto;
import cn.hanyi.ctm.dto.event.WarningCountRerunResponseDto;
import cn.hanyi.ctm.dto.event.WarningRerunDto;
import cn.hanyi.ctm.entity.Connector;
import cn.hanyi.ctm.entity.ConnectorConsumer;
import cn.hanyi.ctm.entity.EventMonitorRules;
import cn.hanyi.ctm.entity.EventMonitorRulesDto;
import cn.hanyi.ctm.repository.ConnectorConsumerRepository;
import cn.hanyi.ctm.repository.ConnectorRepository;
import cn.hanyi.ctm.repository.EventMonitorRulesRepository;
import cn.hanyi.ctm.utils.RegularExpressionUtils;
import cn.hanyi.ctm.workertrigger.ICtmEventTrigger;
import cn.hanyi.ctm.workertrigger.ICtmTaskTrigger;
import cn.hanyi.survey.core.repository.SurveyResponseRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.math3.util.Pair;
import org.befun.auth.constant.AiPointRecordType;
import org.befun.auth.constant.UserTaskType;
import org.befun.auth.entity.OrganizationAiPointRecord;
import org.befun.auth.repository.OrganizationAiPointRecordRepository;
import org.befun.auth.service.OrganizationService;
import org.befun.auth.service.UserService;
import org.befun.auth.service.UserTaskService;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.BaseService;
import org.befun.core.utils.JsonHelper;
import org.befun.task.entity.TaskProgress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiFunction;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class EventRuleService extends BaseService<EventMonitorRules, EventMonitorRulesDto, EventMonitorRulesRepository> {
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private UserService userService;
    @Autowired
    private ConnectorService connectorService;
    @Autowired
    private ConnectorConsumerService connectorConsumerService;

    @Autowired
    private ConnectorRepository connectorRepository;
    @Autowired
    private ConnectorConsumerRepository connectorConsumerRepository;
    @Autowired
    private EventMonitorRulesRepository eventMonitorRulesRepository;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private ICtmEventTrigger ctmEventTrigger;
    @Autowired
    private ICtmTaskTrigger ctmTaskTrigger;
    @Autowired
    private UserTaskService userTaskService;
    @Autowired
    private SurveyResponseRepository responseRepository;
    @Autowired
    private OrganizationAiPointRecordRepository organizationAiPointRecordRepository;

    @Transactional
    public boolean status(long id, boolean enable) {
        EventMonitorRules entity = require(id);
        checkIsCurrentOrg(entity);
        if (enable) {
            if (entity.getStatus() != EventMonitorStatus.OPEN) {
                entity.setStatus(EventMonitorStatus.OPEN);
                eventMonitorRulesRepository.save(entity);
            }
        } else {
            if (entity.getStatus() != EventMonitorStatus.CLOSE) {
                entity.setStatus(EventMonitorStatus.CLOSE);
                entity.setLastCloseTime(new Date());
                eventMonitorRulesRepository.save(entity);
            }
        }
        return true;
    }

    public WarningCountRerunResponseDto countRerun(Long ruleId, WarningCountRerunRequestDto dto) {
        EventMonitorRules rule = requireWithFilter(ruleId);
        return countRerun(rule, dto);
    }

    public WarningCountRerunResponseDto countRerun(EventMonitorRules rule, WarningCountRerunRequestDto dto) {
        String sql;
        Object[] args;
        boolean forceToAll = false;
        if ("waiting".equals(dto.getDataScope())) {
            // find organization_ai_point_record
            OrganizationAiPointRecord record = organizationAiPointRecordRepository.findFirstByOrgIdAndTypeAndSourceId(rule.getOrgId(), AiPointRecordType.warning, rule.getId());
            if (record != null) {
                // 统计待分析的答卷
                sql = "select count(distinct sr.id) count, min(sr.id) minId, max(sr.id) maxId from survey_response sr" +
                        " left join organization_ai_point_record_response oaprr on oaprr.org_id=? and oaprr.response_id=sr.id and oaprr.record_id=?" +
                        " where sr.s_id=? and sr.status=1 and sr.deleted=0 and oaprr.id is null ";
                args = new Object[]{rule.getOrgId(), record.getId(), rule.getSurveyId()};
                return afterCountRerun(rule, sql, args);
            } else {
                forceToAll = true;
            }
        }
        if (forceToAll || "all".equals(dto.getDataScope())) {
            // 统计全部有效答卷
            sql = "select count(id) count, min(id) minId, max(id) maxId from survey_response where s_id=? and status=1 and deleted=0";
            args = new Object[]{rule.getSurveyId()};
        } else if ("dateRange".equals(dto.getDataScope())) {
            // 统计时间范围内的答卷
            Pair<LocalDateTime, LocalDateTime> dateRange = dto.parseDateRangeThrowable();
            LocalDateTime start = dateRange.getFirst();
            LocalDateTime end = dateRange.getSecond();
            sql = "select count(id) count, min(id) minId, max(id) maxId from survey_response where s_id=? and finish_time>=? and finish_time<? and status=1 and deleted=0";
            args = new Object[]{rule.getSurveyId(), start, end};
        } else {
            throw new BadRequestException("不支持的类型:" + dto.getDataScope());
        }
        return afterCountRerun(rule, sql, args);
    }

    private WarningCountRerunResponseDto afterCountRerun(EventMonitorRules rule, String sql, Object[] args) {
        List<WarningCountRerunResponseDto> list = jdbcTemplate.query(sql, (rs, rowNum) -> {
            WarningCountRerunResponseDto item = new WarningCountRerunResponseDto();
            item.setCount(rs.getLong("count"));
            item.setMinId(rs.getLong("minId"));
            item.setMaxId(rs.getLong("maxId"));
            return item;
        }, args);
        WarningCountRerunResponseDto responseDto;
        if (CollectionUtils.isNotEmpty(list)) {
            responseDto = list.get(0);
        } else {
            responseDto = new WarningCountRerunResponseDto();
            responseDto.setCount(0L);
            responseDto.setMinId(0L);
            responseDto.setMaxId(0L);
        }

        responseDto.setLastCloseTime(rule.getLastCloseTime());
        return responseDto;
    }


    @Transactional
    public Boolean rerun(Long ruleId, WarningRerunDto dto) {
        status(ruleId, true);
        Long orgId = TenantContext.getCurrentTenant();
        Long userId = TenantContext.getCurrentUserId();
        TaskProgress progress = userTaskService.createTask(
                orgId,
                userId,
                UserTaskType.eventWarningRerun,
                0,
                new TaskEventWarningDto(ruleId, dto),
                ruleId);
        ctmTaskTrigger.eventRerun(
                orgId,
                userId,
                ruleId,
                progress.getId()
        );
        return true;
    }

    @Override
    public <S extends BaseEntityDTO<EventMonitorRules>> EventMonitorRulesDto create(S data) {
        EventMonitorRulesDto params = (EventMonitorRulesDto) data;
        params.setUserId(TenantContext.getCurrentUserId());
        EventMonitorRulesDto result = super.create(params);
        afterRule(result.getEntity(), params.getConsumer());
        result.setConsumer(params.getConsumer());
        result.setRerun(changeRuleNeedRerun(result));
        return result;
    }

    @Override
    public <S extends BaseEntityDTO<EventMonitorRules>> EventMonitorRulesDto updateOne(long id, S change) {
        EventMonitorRulesDto params = (EventMonitorRulesDto) change;
        EventMonitorRulesDto result = super.updateOne(id, change);
        updateRule(result.getEntity(), params.getConsumer());
        result.setConsumer(params.getConsumer());
        result.setRerun(params.getRerun() && changeRuleNeedRerun(result));
        ctmEventTrigger.eventRuleChange(TenantContext.getCurrentTenant(), TenantContext.getCurrentUserId(), id);
        return result;
    }

    @Override
    protected <S extends BaseEntityDTO<EventMonitorRules>> boolean beforeMapToEntity(ResourceMethod method, S data, EventMonitorRules entity) {
        if (method == ResourceMethod.UPDATE_ONE) {
            EventMonitorRulesDto params = (EventMonitorRulesDto) data;
            params.setRerun(ruleHasChange(entity, params));
        }
        return false;
    }

    private boolean ruleHasChange(EventMonitorRules oldRule, EventMonitorRulesDto newRule) {
        BiFunction<String, String, Boolean> compare = (o1, o2) -> (o1 == null && o2 == null) || (o1 != null && o1.equals(o2));
        String oldExpression = oldRule.getExpression();
        String newExpression = newRule.getExpression();
        String oldTags = oldRule.getTags() == null ? null : String.join(",", oldRule.getTags());
        String newTags = newRule.getTags() == null ? null : String.join(",", newRule.getTags());
        // 没有修改规则和标签和等级
        return oldRule.getLevel() != newRule.getLevel() || !compare.apply(oldExpression, newExpression) || !compare.apply(oldTags, newTags);
    }

    private boolean changeRuleNeedRerun(EventMonitorRulesDto newRule) {
        if (newRule == null || newRule.getStatus() != EventMonitorStatus.OPEN) {
            // 没有开启规则
            return false;
        }
        return countRerun(newRule.getEntity(), new WarningCountRerunRequestDto("all", null)).getCount() > 0;
    }

    /**
     * 追加预警规则数据
     *
     * @param rule
     */
    public void afterRule(EventMonitorRules rule, List<ConsumerConnectorBotDto> consumer) {
        setQuestionIds(rule);
        eventMonitorRulesRepository.save(rule);

        consumer.forEach(c -> {
            if (ObjectUtils.isNotEmpty(c.getConnectorId())) {
                // webHook 已经存在直接使用
                Connector connector = connectorService.require(c.getConnectorId());
                connectorConsumerService.consumer(connector, rule.getId(), c.getEnable(), c.getConnectorParams());
            } else {
                // bot 不存在需要新建
                var connector = connectorService.getOrCreateWarningBotLocalConnector(c.getConnectorId(), c.getUrl(), c.getType());
                AtomicReference<String> content = new AtomicReference<>();
                Optional.ofNullable(c.getTemplate()).ifPresent(t -> {
                    content.set(JsonHelper.toJson(t));
                });
                connectorConsumerService.consumer(connector, rule.getId(), c.getEnable(), content.get());
                c.setConnectorId(connector.getId());
            }
        });

    }

    /**
     * 预警规则设置问题id
     *
     * @param rule
     */
    public EventMonitorRules setQuestionIds(EventMonitorRules rule) {
        Set<String> names = RegularExpressionUtils.getQuestionNames(rule.getExpression());
        String namesString = "'" + String.join("','", names) + "'";
        String sql = String.format("select id,name from survey_question where name in (%s) and s_id = %s", namesString, rule.getSurveyId());
        List<Map<String, Object>> qIdNameMaps = jdbcTemplate.queryForList(sql);
        List<Long> qIds = new ArrayList<>();

        names.forEach(map -> qIdNameMaps.stream().filter(m -> map.equals(m.get("name"))).findFirst().ifPresent(m -> {
            qIds.add((Long) m.get("id"));
        }));

        if (qIds.size() > 0) {
            rule.setQuestionIds(qIds);
        } else {
            rule.setQuestionIds(null);
        }
        userService.getSimple(TenantContext.getCurrentUserId()).ifPresent(user -> {
            rule.setEditor(user.getTruename());
        });
        HashMap<Long, String> thesaurusIdMap = RegularExpressionUtils.getThesaurusIds(rule.getExpression());
        rule.setThesaurusIds(new ArrayList<>((thesaurusIdMap.keySet())));
        return rule;
    }

    /**
     * 更新预警规则
     */
    public void updateRule(EventMonitorRules rule, List<ConsumerConnectorBotDto> consumer) {
        setQuestionIds(rule);

        consumer.forEach(c -> {
            if (c.getType() == ConnectorType.WEBHOOK) {
                Connector connector = connectorService.require(c.getConnectorId());
                ConnectorConsumer connectorConsumer = connectorConsumerService.getConsumerByConnectorAndRelationId(connector, rule.getId());
                if (connectorConsumer == null) {
                    connectorConsumer = new ConnectorConsumer(connector, rule.getId(), c.getEnable());
                }

                if (!connectorConsumer.getConnector().getId().equals(connector.getId())) {
                    connectorConsumer.setConnector(connector);
                }

                connectorConsumer.setEnable(c.getEnable());
                connectorConsumer.setParams(c.getConnectorParams());
                connectorConsumerRepository.findByRelationId(rule.getId())
                        .stream()
                        .filter(cc -> connector != cc.getConnector() && cc.getConnector().getType() == ConnectorType.WEBHOOK)
                        .forEach(cc -> connectorConsumerRepository.delete(cc));
                connectorConsumerRepository.save(connectorConsumer);
            } else {
                Connector connector = connectorService.getOrCreateWarningBotLocalConnector(c.getConnectorId(), c.getUrl(), c.getType());
                connector.setGateway(c.getUrl());
                connectorService.save(connector);
                ConnectorConsumer connectorConsumer = connectorConsumerService.getOrCreateConsumer(connector, rule.getId(), c.getTemplate());
                connectorConsumer.setEnable(c.getEnable());
                connectorConsumerService.save(connectorConsumer);
            }
        });

        eventMonitorRulesRepository.save(rule);
    }

    @Override
    public void afterMapToDto(List<EventMonitorRules> entity, List<EventMonitorRulesDto> dto) {
        if (CollectionUtils.isNotEmpty(dto)) {
            dto.forEach(i -> enableConsumerConnector(i, false));
        }
    }

    public boolean enableConsumerConnector(EventMonitorRulesDto rule, boolean setEmpty) {
        if (rule != null) {
            List<ConnectorConsumer> connectorConsumer = connectorConsumerRepository.findByRelationId(rule.getId());

            // webhook connector
            if (rule.getConsumer().isEmpty() && !setEmpty) {
                ConnectorConsumer webhook = connectorConsumer.stream().filter(i -> i.getConnector() != null && i.getConnector().getType() == ConnectorType.WEBHOOK).findFirst().orElse(null);
                if (webhook != null) {
                    if (webhook.getConnector() != null) {
                        rule.getConsumer().add(new ConsumerConnectorBotDto(webhook.getConnector().getId(), webhook.getEnable(), ConnectorType.WEBHOOK, webhook.getParams()));
                    } else {
                        // connector删除后需要手动删除
                        connectorConsumerRepository.delete(webhook);
                    }
                }

                // bot机器人
                var bots = connectorConsumer.stream()
                        .filter(i -> i.getConnector() != null && List.of(ConnectorType.WECHAT_WORK, ConnectorType.DING_DING, ConnectorType.FEI_SHU).contains(i.getConnector().getType()))
                        .map(i -> new ConsumerConnectorBotDto(
                                        i.getConnector().getType(),
                                        i.getConnector().getId(),
                                        i.getEnable(),
                                        i.getConnector().getGateway(),
                                        JsonHelper.toObject(i.getContent(), ConnectorConsumerBotContentDto.class)
                                )
                        )
                        .collect(Collectors.toList());
                if (bots.size() > 0) {
                    rule.getConsumer().addAll(bots);
                }
            }
            return Optional.of(rule.getConsumer().stream().anyMatch(ConsumerConnectorBotDto::getEnable)).orElse(false);
        }
        return false;
    }

    @Override
    public Boolean deleteOne(long id) {
        // 删除预警规则关联的bot connector 和 consumer
        List<ConnectorConsumer> connectorConsumer = connectorConsumerRepository.findByRelationId(id);
        connectorConsumer.stream().forEach(cc -> {
            var connector = cc.getConnector();
            if (connector != null && connector.getProviderType() == ConnectorProviderType.BOT) {
                connectorRepository.delete(connector);
                connectorConsumerRepository.delete(cc);
            }
        });
        return super.deleteOne(id);
    }

    public static Pattern CALCULATE_COST_KEYWORDS = Pattern.compile("(sentiment|commentSentiment|topicIn|commentTopicIn)");

    public Map<Long, Integer> calculateRuleCost(List<EventMonitorRules> eventMonitorRules) {
        Map<Long, Integer> ruleCostMap = new HashMap<>();
        if (org.apache.commons.collections.CollectionUtils.isEmpty(eventMonitorRules)) {
            return ruleCostMap;
        }
        eventMonitorRules.forEach(i -> {
            ruleCostMap.put(i.getId(), calculateRuleCost(i));
        });
        return ruleCostMap;
    }

    public int calculateRuleCost(EventMonitorRules rule) {
        if (rule != null && StringUtils.isNotEmpty(rule.getExpression())) {
            int count = 0;
            Matcher matcher = CALCULATE_COST_KEYWORDS.matcher(rule.getExpression());
            while (matcher.find()) {
                log.debug("{} find cost keyword {}", rule.getExpression(), matcher.group());
                count++;
            }
            return count;
        }
        return 0;
    }

    @Transactional
    public Set<Long> closeRules(Map<Long, Integer> map) {
        Set<Long> ids = new HashSet<>();
        map.forEach((k, v) -> {
            if (v > 0) {
                ids.add(k);
            }
        });
        if (ids.isEmpty()) {
            return ids;
        }
        repository.updateStatusAndLastCloseTimeByIdIn(EventMonitorStatus.CLOSE, new Date(), ids);
        return ids;
    }
}
