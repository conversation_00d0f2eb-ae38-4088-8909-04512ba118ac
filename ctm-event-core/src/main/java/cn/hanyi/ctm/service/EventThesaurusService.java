package cn.hanyi.ctm.service;

import cn.hanyi.ctm.constant.event.EventSurveyStatus;
import cn.hanyi.ctm.dto.user.SimpleUserDto;
import cn.hanyi.ctm.entity.EventMonitorRules;
import cn.hanyi.ctm.entity.EventMonitorThesaurus;
import cn.hanyi.ctm.entity.EventMonitorThesaurusDto;
import cn.hanyi.ctm.repository.EventMonitorRulesRepository;
import cn.hanyi.ctm.repository.EventMonitorThesaurusRepository;
import cn.hanyi.ctm.utils.RegularExpressionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.UserService;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.befun.core.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class EventThesaurusService extends BaseService<EventMonitorThesaurus, EventMonitorThesaurusDto, EventMonitorThesaurusRepository> {

    @Autowired
    UserService userService;
    @Autowired
    EventMonitorThesaurusRepository eventMonitorThesaurusRepository;
    @Autowired
    EventMonitorRulesRepository eventMonitorRulesRepository;

    @Override
    public void afterMapToDto(List<EventMonitorThesaurus> entity, List<EventMonitorThesaurusDto> dto) {
        if (CollectionUtils.isNotEmpty(dto)) {
            insertUser(dto);
        }
    }

    /**
     * 添加创建者和编辑者
     *
     * @param thesaurus
     */
    public void insertUser(List<EventMonitorThesaurusDto> thesaurus) {
        Set<Long> createIds = thesaurus.stream().map(EventMonitorThesaurusDto::getUserId).filter(Objects::nonNull).collect(Collectors.toSet());
        Set<Long> editorIds = thesaurus.stream().map(EventMonitorThesaurusDto::getEditorId).filter(Objects::nonNull).collect(Collectors.toSet());

        createIds.addAll(editorIds);
        List<SimpleUser> user = userService.getSimpleByIds(createIds);
        if (user != null) {
            thesaurus.forEach(ths -> {
                SimpleUser creator = user.stream().filter(us -> us.getId().equals(ths.getUserId())).findFirst().orElse(null);
                SimpleUser editor = user.stream().filter(us -> us.getId().equals(ths.getEditorId())).findFirst().orElse(null);
                ths.setCreator(creator == null ? null : new SimpleUserDto(creator));
                ths.setEditor(editor == null ? new SimpleUserDto(creator) : new SimpleUserDto(editor));
            });
        }
    }

    /**
     * 构建包含词库的预警内容
     */
    public HashMap<String, Object> buildThesaurusContent(List<EventMonitorRules> rules) {
        var thesaurusContent = new HashMap<String, Object>();
        try {

            HashMap<Long, String> thesaurusIdMap = new HashMap<>();

            rules.forEach(rule -> {
                thesaurusIdMap.putAll(RegularExpressionUtils.getThesaurusIds(rule.getExpression()));
            });

            HashMap<String, List<String>> dictionaries = new HashMap<>();
            if (!thesaurusIdMap.isEmpty()) {
                List<EventMonitorThesaurus> thesaurusList = eventMonitorThesaurusRepository.findByIdIn(new ArrayList<>(thesaurusIdMap.keySet()));
                thesaurusList.forEach(ths -> dictionaries.put(ths.getId().toString(), ths.toList()));
                if (!dictionaries.isEmpty()) {
                    thesaurusContent.put("config", new HashMap<>() {{
                        put("dictionaries", dictionaries);
                    }});
                }

            }

        } catch (Exception e) {
            log.error("buildThesaurusContent error", e);
        }finally {
            return thesaurusContent;
        }

    }

    @Override
    protected void afterExecuteMethod(ResourceMethod method, EventMonitorThesaurus entity, EventMonitorThesaurusDto dto) {
        if (method == ResourceMethod.DELETE_ONE) {
            invalidRule(entity.getId());
        }
    }

    /**
     * 删除词库无效预警规则
     *
     * @param id
     */
    public void invalidRule(Long id) {
        eventMonitorRulesRepository.findByThesaurusIdsContains(id).forEach(rule -> {
            rule.setSurveyStatus(EventSurveyStatus.THESAURUS);
            eventMonitorRulesRepository.save(rule);
        });
    }
}
