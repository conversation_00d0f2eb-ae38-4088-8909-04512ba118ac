package cn.hanyi.ctm.service;

import cn.hanyi.ctm.constant.EventWarningType;
import cn.hanyi.ctm.dto.event.download.EventDownloadDto;
import cn.hanyi.ctm.entity.Event;
import cn.hanyi.ctm.entity.EventAction;
import cn.hanyi.ctm.entity.EventDto;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.alibaba.excel.write.style.row.SimpleRowHeightStyleStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.befun.core.constant.QueryOperator;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.befun.core.entity.BaseEntity;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.query.GenericSpecification;
import org.befun.core.utils.DateHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
public class EventDownloadService {

    @Autowired
    private EventQueryService eventQueryService;
    @Autowired
    private EventService eventService;
    @Autowired
    private EventActionService eventActionService;
    @Autowired
    private NamedParameterJdbcTemplate jdbcTemplate;
    private static final String TMP_FILE_PATH = "/tmp";


    public void download(ResourceEntityQueryDto<EventDto> params, HttpServletResponse response) throws Exception {
        response.addHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        Date date = new Date();
        String fileName = URLEncoder.encode(String.format("事件数据_%s.xlsx", DateHelper.formatDate(date)), StandardCharsets.UTF_8);
        response.addHeader("Content-Disposition", "attachment;fileName=" + fileName);

        String filePath = buildTmpFilePath(params);
        try (BufferedInputStream bis = new BufferedInputStream(new FileInputStream(filePath))) {
            byte[] buff = new byte[1024];
            OutputStream os = response.getOutputStream();
            int i;
            while ((i = bis.read(buff)) != -1) {
                os.write(buff, 0, i);
                os.flush();
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        } finally {
            new File(filePath).delete();
        }

    }

    private String buildTmpFilePath(ResourceEntityQueryDto<EventDto> params) {
        int pageSize = 500;
        String fileName = System.currentTimeMillis() + ".xlsx";
        String filePath = TMP_FILE_PATH + "/" + fileName;


        // 过滤无预警
        params.getQueryCriteriaList().add(new ResourceQueryCriteria("warningLevel", EventWarningType.NONE, QueryOperator.NOT_EQUAL));

        ExcelWriter writer = EasyExcel
                .write(filePath, EventDownloadDto.class)
                .registerWriteHandler(new SimpleRowHeightStyleStrategy((short) 30, (short) 20)) // 设置行高
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(30)) // 设置列宽
                .registerWriteHandler(defaultStyle())
                .build();
        WriteSheet sheet = EasyExcel.writerSheet("事件数据").build();

        GenericSpecification<Event> specification = new GenericSpecification<>(params);
        long count = eventService.customCount(params).getTotal();
        if (count == 0) {
            throw new BadRequestException("无符合预警事件的数据");
        }
        int maxPage = (int) (count % pageSize == 0 ? count / pageSize : (count / pageSize + 1));
        for (int page = 0; page < maxPage; page++) {
            writer.write(pageQuery(page, pageSize, params, specification), sheet);
        }
        writer.finish();
        return filePath;
    }


    private HorizontalCellStyleStrategy defaultStyle() {
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_80_PERCENT.getIndex());
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setBold(false);
        headWriteFont.setFontName("微软雅黑");
        headWriteFont.setColor(IndexedColors.WHITE.getIndex());
        headWriteFont.setFontHeightInPoints((short) 14);
        headWriteCellStyle.setWriteFont(headWriteFont);

        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        WriteFont contentWriteFont = new WriteFont();
        contentWriteFont.setFontName("微软雅黑");
        contentWriteFont.setFontHeightInPoints((short) 12);
        contentWriteCellStyle.setWriteFont(contentWriteFont);
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
        horizontalCellStyleStrategy.setHeadWriteCellStyle(headWriteCellStyle);

        return horizontalCellStyleStrategy;
    }

    private List<EventDownloadDto> pageQuery(int page, int pageSize, ResourceEntityQueryDto<EventDto> params, GenericSpecification<Event> specification) {
        List<EventDownloadDto> data = new ArrayList<>();
        params.setPage(page + 1);
        params.setLimit(pageSize);
        List<Event> list = eventService.customQuery(params).getItems().stream().map(BaseEntityDTO::getEntity).collect(Collectors.toList());
        Map<Long, List<EventAction>> remarkActionMap;
        Map<Long, Long> responseSequenceMap;
        if (CollectionUtils.isNotEmpty(list)) {
            remarkActionMap = eventActionService.getRemarkByEventsGroupByEvent(list.stream().map(BaseEntity::getId).collect(Collectors.toSet()));
            responseSequenceMap = getResponseSequenceMap(list.stream().map(Event::getResponseId).filter(Objects::nonNull).collect(Collectors.toSet()));
        } else {
            remarkActionMap = new HashMap<>();
            responseSequenceMap = new HashMap<>();
        }
        for (Event event : list) {
            EventDownloadDto dto = new EventDownloadDto(event, responseSequenceMap.get(event.getResponseId()), remarkActionMap.get(event.getId()));
            data.add(dto);
        }
        return data;
    }

    private Map<Long, Long> getResponseSequenceMap(Set<Long> responseIds) {
        Map<Long, Long> map = new HashMap<>();
        if (CollectionUtils.isEmpty(responseIds)) {
            return map;
        }
        String sql = "select id, sequence from survey_response where id in (:responseIds)";
        Map<String, Object> params = new HashMap<>();
        params.put("responseIds", responseIds);
        jdbcTemplate.query(sql, params, (rs, rowNum) -> {
            long id = rs.getLong("id");
            Long sequence = rs.getLong("sequence");
            map.put(id, sequence);
            return id;
        });
        return map;
    }

}
