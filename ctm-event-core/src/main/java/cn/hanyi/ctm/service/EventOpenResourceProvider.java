package cn.hanyi.ctm.service;

import cn.hanyi.ctm.constant.EventOpenResourceType;
import cn.hanyi.ctm.dto.event.EventOpenResourceInfoDto;
import cn.hanyi.ctm.dto.event.EventOpenResourceParam;
import cn.hanyi.ctm.entity.EventActionDto;
import cn.hanyi.ctm.entity.EventDto;
import cn.hanyi.ctm.properties.EventProperties;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.auth.constant.OrganizationConfigType;
import org.befun.auth.dto.OpenResourceInfo;
import org.befun.auth.dto.orgconfig.OrgConfigDto;
import org.befun.auth.entity.OpenResource;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.OrganizationConfigService;
import org.befun.auth.service.UserService;
import org.befun.auth.service.openresource.IOpenResourceProvider;
import org.befun.core.exception.BadRequestException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class EventOpenResourceProvider implements IOpenResourceProvider<EventOpenResourceParam, EventOpenResourceType, EventOpenResourceInfoDto> {

    @Autowired
    private EventProperties eventProperties;
    @Autowired
    private OrganizationConfigService organizationConfigService;
    @Autowired
    private EventActionService eventActionService;
    @Autowired
    private EventService eventService;
    @Autowired
    private UserService userService;

    @Override
    public EventOpenResourceType getType() {
        return EventOpenResourceType.event;
    }

    @Override
    public void checkEnabled() {
        OrgConfigDto config = organizationConfigService.getConfig(OrganizationConfigType.eventShare);
        if (config != null && config.getEventShare() != null && config.getEventShare()) {
            return;
        }
        throw new BadRequestException("链接已失效，请联系管理员开启");
    }

    @Override
    public EventOpenResourceInfoDto get(Long orgId, Long userId, EventOpenResourceParam params) {
        EventDto event = eventService.findOne(params.getEventId());
        List<EventActionDto> actions = eventActionService.getAllActionsByEvent(params.getEventId());
        List<SimpleUser> cooperationUsers = eventActionService.cooperationUsers(params.getEventId());
        if (CollectionUtils.isNotEmpty(cooperationUsers)) {
            cooperationUsers = cooperationUsers.stream().map(i -> new SimpleUser(null, i.getTruename(), null, null, null, null)).collect(Collectors.toList());
        }
        return new EventOpenResourceInfoDto(event, actions, cooperationUsers);
    }

    @Override
    public OpenResourceInfo build(OpenResource entity, String token) {
        SimpleUser user = userService.getSimple(entity.getUserId()).orElse(null);
        String url = String.format(eventProperties.getShareUrl(), token);
        String message = String.format(eventProperties.getShareMessage(), user == null ? "" : user.getTruename(), url);
        return new OpenResourceInfo(entity.getId(), url, message);
    }
}
