package cn.hanyi.ctm.service;

import cn.hanyi.ctm.constant.EventStatusType;
import cn.hanyi.ctm.constant.event.EventMonitorCondition;
import cn.hanyi.ctm.constant.event.EventType;
import cn.hanyi.ctm.dto.EventMqDto;
import cn.hanyi.ctm.entity.Event;
import cn.hanyi.ctm.entity.journey.*;
import cn.hanyi.ctm.properties.CtmProperties;
import cn.hanyi.ctm.properties.EventProperties;
import cn.hanyi.ctm.properties.JourneyProperties;
import cn.hanyi.ctm.properties.NotifyProperties;
import cn.hanyi.ctm.service.data.EventStatDataService;
import cn.hanyi.ctm.service.data.IndicatorDataService;
import cn.hanyi.ctm.service.journey.elements.scene.JourneyScenePublishService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.NotificationType;
import org.befun.auth.constant.ResourcePermissionType;
import org.befun.auth.entity.Department;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.DepartmentService;
import org.befun.auth.service.ResourcePermissionService;
import org.befun.auth.service.SystemNotificationService;
import org.befun.auth.service.UserService;
import org.befun.core.service.ResourceCorporationService;
import org.befun.extension.constant.InboxMessageType;
import org.befun.extension.entity.InboxMessage;
import org.befun.extension.service.InboxMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static org.befun.auth.constant.ResourcePermissionRelationType.ADMIN;
import static org.befun.auth.constant.ResourcePermissionType.*;
import static org.befun.core.constant.ResourceShareFlag.SHARE_FOUND_IGNORE;


@Slf4j
@Service
public class EventNotifyService {


    @Autowired
    private DepartmentService departmentService;
    @Autowired
    private InboxMessageService inboxMessageService;
    @Autowired
    private EventProperties eventProperties;
    @Autowired
    private NotifyProperties notifyProperties;

    @Autowired
    private JourneyProperties journeyProperties;
    @Autowired
    private CtmProperties ctmProperties;
    @Autowired
    private UserService userService;
    @Autowired
    private SystemNotificationService systemNotificationService;
    @Autowired
    private ResourcePermissionService resourcePermissionService;
    @Autowired
    private ResourceCorporationService resourceCorporationService;
    @Autowired
    private EventService eventService;
    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private JourneyMapService journeyMapService;

    @Value("${ctm.journey.journey-url:}")
    private String journeyUrl;

    @Autowired
    private JourneyWarningService journeyWarningService;

    @Autowired
    private IndicatorDataService indicatorService;

    @Autowired
    private EventStatDataService eventStatService;

    @Autowired
    private JourneyScenePublishService journeyScenePublishService;

    /**
     * 事件预警
     * 答题预警事件
     * 客户体验指标预警事件
     */
    public void notifyImmediately(EventMqDto eventDto) {

        Event e = null;
        boolean eventCondition;
        String targetUrl;
        String notifyTemplate;
        InboxMessage inboxMessage;
        Map<String, Object> notifyParams = new HashMap<>();
        Long orgId = eventDto.getOrgId();
        Double value = eventDto.getValue();

        Long departmentId = null;
        boolean grantPermission = false;

        Set<Long> roleIds = new HashSet<>();
        if (eventDto.getRoleId() != null) {
            roleIds.add(eventDto.getRoleId());
        }


        switch (eventDto.getEventType()) {
            case EVENT:
                e = eventService.get(eventDto.getEventId());
                departmentId = e.getDepartmentId();
                eventCondition = EventType.EVENT.equals(eventDto.getEventType()) && (
                        eventDto.getDelayCondition() == null
                                || (eventDto.getDelayCondition() == EventMonitorCondition.UNHANDLE && e.getStatus() != null && e.getStatus() == EventStatusType.WAIT)
                                || (eventDto.getDelayCondition() == EventMonitorCondition.UNCLOSE && e.getStatus() != null && e.getStatus() != EventStatusType.SUCCESS));
                targetUrl = getEventTargetUrl(e);
                String inboxEventTitle = String.format("%s:%s", warningLevelName(e), e.getWarningTitle());
                notifyTemplate = notifyProperties.getWarning();
                inboxMessage = buildWarningInboxMessage(orgId, targetUrl, inboxEventTitle, InboxMessageType.WARNING);
                notifyParams = buildNotifyParam(null, e, null);
                grantPermission = true;
                break;
            case JOURNEY:
                roleIds.addAll(
                        Arrays.stream(eventDto.getRoleIds().split(",")).map(Long::valueOf).collect(Collectors.toSet())
                );
                // EventId为设置规则的id
                JourneyWarningPublish journeyWarningPublish = journeyWarningService.getPublish(eventDto.getEventId());

                if (journeyWarningPublish == null) {
                    return;
                }

                JourneyPublish journeyPublish = journeyScenePublishService.get(journeyWarningPublish.getJourneyId());
                if (journeyPublish == null) {
                    return;
                }

                JourneyMap journeyMap = journeyMapService.get(journeyWarningPublish.getJourneyMapId());

                String warningTypeText = "";
                String methodText = "";
                String warningTitle = "";
                String valueText = "";
                String warningValue = "";
                switch (journeyWarningPublish.getRelationType()) {
                    case experience_indicator:

                        warningTypeText = "平均值";
                        ExperienceIndicatorBase indicator = indicatorService.getEntity(journeyWarningPublish.getRelationId(), true);
                        if (indicator == null) {
                            return;
                        }
                        warningTitle = indicator.getIndicatorName();
                        String calculatingMethod = indicator.getCalculatingMethod() == null ? "" : indicator.getCalculatingMethod().toLowerCase();
                        switch (calculatingMethod) {
                            case "average":
                                methodText = "平均值";
                                valueText = formatDouble(value, false);//String.format("%.2f", value);
                                warningValue = formatDouble(journeyWarningPublish.getWarningValue(), false);//String.valueOf(journeyWarningPublish.getWarningValue());
                                break;
                            case "weightavg":
                                methodText = "加权平均值";
                                valueText = formatDouble(value, false);//String.format("%.2f", value);
                                warningValue = formatDouble(journeyWarningPublish.getWarningValue(), false);//String.valueOf(journeyWarningPublish.getWarningValue());
                                break;
                            case "nps":
                                methodText = "净满意度";
                                valueText = formatDouble(value, true);//String.format("%.2f", value * 100) + "%";
                                warningValue = formatDouble(journeyWarningPublish.getWarningValue(), true);//String.valueOf(journeyWarningPublish.getWarningValue() * 100) + "%";
                                break;
                            case "nss":
                                methodText = "净推荐度";
                                valueText = formatDouble(value, true);//String.format("%.2f", value * 100) + "%";
                                warningValue = formatDouble(journeyWarningPublish.getWarningValue(), true);//String.valueOf(journeyWarningPublish.getWarningValue() * 100) + "%";
                                break;
                            case "percent":
                                methodText = "选项占比";
                                valueText = formatDouble(value, true);//String.format("%.2f", value * 100) + "%";
                                warningValue = formatDouble(journeyWarningPublish.getWarningValue(), true);//String.valueOf(journeyWarningPublish.getWarningValue() * 100) + "%";
                                break;
//                            default:
//                                break;
                        }

                        break;
                    case event_stat:
                        warningTypeText = "触发总数";
                        ElementEventStatBase eventStat = eventStatService.getEntity(journeyWarningPublish.getRelationId(), true);
                        if (eventStat == null) {
                            return;
                        }

                        switch (eventStat.getStatType()) {
                            case 1:
                                warningTypeText = "触发总数";
                                break;
                            case 2:
                                warningTypeText = "待处理数";
                                break;
                            default:
                                warningTypeText = "";
                                break;
                        }

                        Map<Long, String> eventRuleMap = journeyWarningService.getEventRuleTitleByIds(Collections.singleton(eventStat.getEventRuleId()));
                        warningTitle = eventRuleMap.getOrDefault(eventStat.getEventRuleId(), "");
                        methodText = warningTypeText;
                        valueText = String.format("%d", value.intValue());
                        warningValue = String.valueOf(journeyWarningPublish.getWarningValue().intValue());
                        break;
                    default:
                        log.error("暂不支持relation type: {}", journeyWarningPublish.getRelationType());
                        return;
                }

                // 需要单独处理 0 表示全部
                Integer warningRange = journeyWarningPublish.getWarningRange();
                String range = warningRange == 0 ? "全部" : String.format("%d", warningRange);
                // 指标名称+监测范围+计算方法+当前值+（过低/过高）
                String inboxJourneyTitle = String.format("指标预警: %s (最近%s%s) 的%s%s（%s）",
                        warningTitle,
                        range,
                        journeyWarningPublish.getWarningFrequency().text,
                        methodText,
                        valueText,
                        journeyWarningPublish.getWarningCompare().label
                );

                String JourneyWarningTitle = String.format("%s (最近%s%s) 的%s为%s，(%s)设定值%s",
                        warningTitle,
                        range,
                        journeyWarningPublish.getWarningFrequency().text,
                        methodText,
                        valueText,
                        journeyWarningPublish.getWarningCompare().text,
                        warningValue
                );

                eventCondition = true;
                targetUrl = getJourneyTargetUrl(journeyWarningPublish.getJourneyMapId());
                notifyTemplate = notifyProperties.getJourney();
                inboxMessage = buildWarningInboxMessage(orgId, targetUrl, inboxJourneyTitle, InboxMessageType.JOURNEY);

                String warningDateRange = journeyWarningService.getWarningDateRange(journeyWarningPublish.getWarningFrequency(), journeyWarningPublish.getWarningRange());
                String finalWarningTypeText = warningTypeText;
                String finalValueText = valueText;
                String finalWarningTitle = warningTitle;
                String finalWarningValue = warningValue;
                // sms      ${targetTruename}，您好！您收到一条指标预警：${indicatorName}的值为${currentValue}(${indicatorCompareLabel})，请尽快登录体验家XM处理！
                // wechat   ${warningTitle} ${warningTime}
                // wx_work  ${JourneyWarningTitle}
                // email    ${targetTruename} ${warningLevelSimple} ${warningTitle} ${currentValue} ${indicatorCompareLabel} ${url} ${indicatorName} ${warningRange} ${warningFrequency} ${warningTypeText} ${currentValue} ${indicatorCompareLabel} ${indicatorCompareText} ${warningValue} ${warningTime} ${journeyName}
                Map<String, Object> extra = new HashMap<>() {
                    {
                        put("warningLevelSimple", journeyWarningPublish.getRelationType().text);
                        put("warningTitle", finalWarningTitle);
                        put("JourneyWarningTitle", JourneyWarningTitle);
                        put("indicatorCompareLabel", journeyWarningPublish.getWarningCompare().label);
                        put("indicatorCompareText", journeyWarningPublish.getWarningCompare().text);
                        put("indicatorName", finalWarningTitle);
                        put("warningRange", range);
                        put("warningFrequency", journeyWarningPublish.getWarningFrequency().text);
                        put("warningTypeText", finalWarningTypeText);
                        put("currentValue", finalValueText);
                        put("warningValue", finalWarningValue);
                        put("url", targetUrl);
                        put("journeyName", journeyMap.getTitle());
                        put("warningTime", warningDateRange);
                    }
                };

                notifyParams.putAll(extra);
                break;
            default:
                log.error("不支持的事件类型：{}", eventDto.getEventType());
                return;
        }

        if (eventCondition) {
            // 查询出事件所在的部门的所有用户
            List<SimpleUser> targetUsers = getNotifyUsers(orgId, departmentId, roleIds);
            if (CollectionUtils.isEmpty(targetUsers)) {
                return;
            }
            // 如果当前层级有用户，则开始发送站内信和微信，邮件
            NotificationType[] types = parseNotificationType(eventDto.getNotifyChannel());
            String app = notifyProperties.getApp();

            Map<String, Object> finalNotifyParams = notifyParams;
            Event finalE = e;
            boolean finalGrantPermission = grantPermission;

            targetUsers.forEach(u -> {
                // inbox
                addInboxMessage(u.getId(), inboxMessage);
                // send wx/email/sms to user
                finalNotifyParams.put("targetTruename", u.getTruename() == null ? "" : u.getTruename());
                sendToUser(u.getId(), app, types, notifyTemplate, finalNotifyParams);
                // user add event permission
                if (finalGrantPermission) {
                    grantEventPermission(finalE, u.getId());
                }
            });
        } else {
            log.warn("异常消息，已忽略：{}", eventDto.summary());
        }

    }

    private static String formatDouble(Double value, boolean percent) {
        if (value == null || value == 0) {
            return percent ? "0" : "0%";
        }
        if (percent) {
            return (Math.round(value * 10000) / 100.0) + "%";
        } else {
            return (Math.round(value * 100) / 100.0) + "";
        }
    }

    /**
     * 查询事件需要通知的用户
     */
    public List<SimpleUser> getNotifyUsers(Long orgId, Long departmentId, Set<Long> roleIds) {
        if (roleIds == null) {
            return null;
        }
        List<Long> departmentIds = null;
        if (departmentId == null || departmentId <= 0) {
            // 事件没有部门（旧数据或者数据不完整，则查询所有的部门）
            departmentIds = departmentService.getSubDepartmentIdList(orgId, null, true);
        } else {
            // 事件由部门id，则查询这个部门及其所有的子部门
            departmentIds = departmentService.getSubDepartmentIdList(orgId, departmentId, false);
        }
        if (CollectionUtils.isEmpty(departmentIds)) {
            return null;
        }
        String sql = "select u.id from user u" +
                " inner join user_role ur on u.id=ur.user_id and ur.role_id in (" + roleIds.stream().map(Objects::toString).collect(Collectors.joining(",")) + ")" +
                " where u.org_id = " + orgId +
                " and u.department_id in (" + departmentIds.stream().map(Objects::toString).collect(Collectors.joining(",")) + ")";
        List<Long> userIds = jdbcTemplate.queryForList(sql, Long.class);
        if (CollectionUtils.isNotEmpty(userIds)) {
            Set<Long> ids = userIds.stream().filter(Objects::nonNull).collect(Collectors.toSet());
            if (!ids.isEmpty()) {
                return userService.getSimpleByIds(ids);
            }
        }
        return null;
    }

    public void grantEventPermission(Event event, Long userId) {
        resourceCorporationService.shareToUser(event.getId(), EVENT, ADMIN, event.getOrgId(), userId, SHARE_FOUND_IGNORE);
    }

    public void grantEventPermission(Long orgId, Long relationId, Long userId, ResourcePermissionType type) {
        switch (type) {
            case EVENT:
                resourceCorporationService.shareToUser(relationId, EVENT, ADMIN, orgId, userId, SHARE_FOUND_IGNORE);
                break;
            case JOURNEY:
                resourceCorporationService.shareToUser(relationId, JOURNEY, ADMIN, orgId, userId, SHARE_FOUND_IGNORE);
                break;
            case SURVEY:
                resourceCorporationService.shareToUser(relationId, SURVEY, ADMIN, orgId, userId, SHARE_FOUND_IGNORE);
                break;
            default:
                log.error("不支持的资源类型：{}", type);
                return;
        }
    }

    public Map<String, Object> buildNotifyParam(SimpleUser current, Event event, Map<String, Object> extParams) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        Map<String, Object> map = new HashMap<>();
        map.put("eventId", event.getId());
        map.put("surveyName", event.getSurveyName());
        map.put("url", getEventTargetUrl(event));
        map.put("warningLevel", event.getWarningLevel().getText());
        map.put("warningLevelSimple", event.getWarningLevel().getSimpleText());
        map.put("warningTitle", event.getWarningTitle());
        map.put("warningTime", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(event.getCreateTime()));
        map.put("departmentName", Optional.ofNullable(departmentService.get(event.getDepartmentId())).map(Department::getTitle).orElse(""));
        if (current != null) {
            map.put("formUserName", current.getTruename());
            map.put("formUserPhone", current.getMobile());
        }
        map.put("eventTime", sdf.format(event.getCreateTime()));
        map.put("closeTime", sdf.format(new Date()));

        if (MapUtils.isNotEmpty(extParams)) {
            map.putAll(extParams);
        }
        return map;
    }

    public NotificationType[] parseNotificationType(List<NotificationType> types) {
        if (CollectionUtils.isNotEmpty(types)) {
            return types.stream().filter(Objects::nonNull).toArray(NotificationType[]::new);
        }
        return null;
    }


    public void sendToUser(Long userId, String app, NotificationType[] types, String template, Map<String, Object> params) {
        if (eventProperties.getEnableNotify() && types != null && types.length > 0) {
            systemNotificationService.notifyToUser(app, userId, types, template, params, true);
        }
    }

    private String getEventTargetUrl(Event event) {
        if (StringUtils.isNotEmpty(eventProperties.getTargetUrl())) {
            return String.format("%s%s", eventProperties.getTargetUrl(), event.getId());
        }
        return "";
    }

    private String getJourneyTargetUrl(Long journeyId) {
        return String.format("%s/%s", journeyUrl, journeyId);
    }

    private String warningLevelName(Event event) {
        return event.getWarningLevel().getText();
    }

    public void addInboxMessage(Long targetUserId, InboxMessage entity) {
        InboxMessage add = new InboxMessage();
        add.setId(null);
        add.setUserId(targetUserId);
        add.setOrgId(entity.orgId);
        add.setType(entity.getType());
        add.setFromUserId(entity.getFromUserId());
        add.setFromUserName(entity.getFromUserName());
        add.setTitle(entity.getTitle());
        add.setDescription(entity.getDescription());
        add.setTargetUrl(entity.getTargetUrl());
        add.setReadStatus(entity.getReadStatus());
        inboxMessageService.addInboxMessage(add);
    }


    private InboxMessage buildWarningInboxMessage(Long orgId, String targetUrl, String title, InboxMessageType type) {


        InboxMessage entity = new InboxMessage();
        entity.setOrgId(orgId);
        entity.setType(type);
        entity.setFromUserId(0L);
        entity.setTitle(title);
        entity.setDescription(title);
        entity.setTargetUrl(targetUrl);
        return entity;
    }

    public InboxMessage buildActionCooperationInboxMessage(Long orgId, SimpleUser user, Event event) {
        String title = String.format("%s(%s):%s", user.getTruename(), user.getTruename(), event.getWarningTitle());

        InboxMessage entity = new InboxMessage();
        entity.setOrgId(orgId);
        entity.setType(InboxMessageType.COOPERATION);
        entity.setFromUserId(user.getId());
        entity.setFromUserName(user.getTruename());
        entity.setTitle(title);
        entity.setDescription(title);
        entity.setTargetUrl(getEventTargetUrl(event));
        return entity;
    }

    public InboxMessage buildActionCloseInboxMessage(Long orgId, SimpleUser user, Event event) {
        String title = String.format("%s(%s)关闭了一条%s:%s", user.getTruename(), user.getTruename(), warningLevelName(event), event.getWarningTitle());

        InboxMessage entity = new InboxMessage();
        entity.setOrgId(orgId);
        entity.setType(InboxMessageType.ACTION);
        entity.setFromUserId(user.getId());
        entity.setFromUserName(user.getTruename());
        entity.setTitle(title);
        entity.setDescription(title);
        entity.setTargetUrl(getEventTargetUrl(event));
        return entity;
    }


}
