package cn.hanyi.ctm.localworkerconsumer;

import cn.hanyi.ctm.service.persona.CustomerPersonaService;
import cn.hanyi.ctm.workertrigger.ICtmEventConsumer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Component
public class LocalJourneyCtmEventConsumer implements ICtmEventConsumer {

    @Autowired
    private CustomerPersonaService customerPersonaService;
    private final ExecutorService executorService = Executors.newCachedThreadPool();

    @Override
    public void journeyMapPublish(Long orgId, Long userId, Long journeyMapId) {
        executorService.execute(() -> customerPersonaService.updateByJourneyMapChange(orgId, journeyMapId));
    }

    @Override
    public void journeyMapDelete(Long orgId, Long userId, Long journeyMapId) {
        executorService.execute(() -> customerPersonaService.updateByJourneyMapChange(orgId, journeyMapId));
    }
}
