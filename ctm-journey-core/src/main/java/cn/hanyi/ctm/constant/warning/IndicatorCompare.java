package cn.hanyi.ctm.constant.warning;

/**
 * <AUTHOR>
 */

public enum IndicatorCompare {
    LT("过低", "低于"),
    LTE("过低", "低于等于"),
    EQ("持平", "等于"),
    GT("过高", "高于"),
    GTE("过高", "高于等于");

    public String label;
    public String text;

    IndicatorCompare(String label, String text) {
        this.label = label;
        this.text = text;
    }

    public Boolean compare(Double value1, Double value2) {
        switch (this) {
            case LT:
                return value1 < value2;
            case LTE:
                return value1 <= value2;
            case EQ:
                return value1.equals(value2);
            case GT:
                return value1 > value2;
            case GTE:
                return value1 >= value2;
            default:
                return false;
        }
    }
}
