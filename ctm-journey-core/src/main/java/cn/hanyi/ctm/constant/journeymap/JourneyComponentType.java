package cn.hanyi.ctm.constant.journeymap;

import lombok.Getter;

@Getter
public enum JourneyComponentType {
    journey("客户旅程"),
    journey_descriptions("场景说明", null, false),
    textbox("文本框", journey_descriptions, false),
    experience_indicator("体验指标"),
    experience_matrix("体验矩阵"),
    experience_interaction("问卷推送"),
    curve("情绪曲线"),
    event_stat("事件统计", true),
    linker("连接器", true),
    persona(true, "客户画像", null, false, false),
    persona_content("客户画像", true),
    ;

    public boolean hasChildren = false;
    public final String text;
    public JourneyComponentType sameAs = null;
    public boolean supportTransfer = false;
    public boolean hasRelationJourney = true;

    JourneyComponentType(String text) {
        this.text = text;
    }

    JourneyComponentType(String text, boolean supportTransfer) {
        this.text = text;
        this.supportTransfer = supportTransfer;
    }

    JourneyComponentType(String text, JourneyComponentType sameAs, boolean hasRelationJourney) {
        this.text = text;
        this.sameAs = sameAs;
        this.hasRelationJourney = hasRelationJourney;
    }

    JourneyComponentType(String text, JourneyComponentType sameAs, boolean supportTransfer, boolean hasRelationJourney) {
        this.text = text;
        this.sameAs = sameAs;
        this.supportTransfer = supportTransfer;
        this.hasRelationJourney = hasRelationJourney;
    }

    JourneyComponentType(boolean hasChildren, String text, JourneyComponentType sameAs, boolean supportTransfer, boolean hasRelationJourney) {
        this.hasChildren = hasChildren;
        this.text = text;
        this.sameAs = sameAs;
        this.supportTransfer = supportTransfer;
        this.hasRelationJourney = hasRelationJourney;
    }
}