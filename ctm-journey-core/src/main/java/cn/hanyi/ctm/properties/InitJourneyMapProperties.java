package cn.hanyi.ctm.properties;

import cn.hanyi.ctm.constant.journeymap.ElementTextBoxType;
import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Configuration()
@ConfigurationProperties(prefix = "ctm.journey.init")
public class InitJourneyMapProperties {

    private List<InitJourneyMapProperties.InitComponent> components = new ArrayList<>();
    private List<InitJourneyMapProperties.InitJourney> journeys = new ArrayList<>();
    private boolean hasJourneyFirstChild = true;

    @Getter
    @Setter
    public static class InitComponent {
        private int order;
        private String title;
        private String text;
        private JourneyComponentType type;
        private ElementTextBoxType subType;
    }

    @Getter
    @Setter
    public static class InitJourney {
        private int order;
        private String title;
        private String color;
        private boolean child = true;
    }
}
