package cn.hanyi.ctm.properties;

import cn.hanyi.ctm.constant.persona.PersonaComponentType;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Getter
@Setter
@Configuration()
@ConfigurationProperties(prefix = "ctm.persona")
public class PersonaProperties {

    private List<InitItem> items;

    @Getter
    @Setter
    public static class InitItem {
        private String name;
        private PersonaComponentType type;
        private String content;
        private Layout layout;
    }

    @Getter
    @Setter
    public static class Layout {
        private int w = 0;
        private int h = 0;
        private int x = 0;
        private int y = 0;
    }
}
