package cn.hanyi.ctm.repository;

import cn.hanyi.ctm.entity.journey.Journey;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface JourneyRepository extends ResourceRepository<Journey, Long> {
    @Modifying
    @Query("update Journey j set j.order = :order where j.id = :journeyId and j.orgId = :orgId")
    void updateOrderById(@Param(value = "order") int order, @Param(value = "journeyId") Long journeyId, @Param(value = "orgId") Long orgId);

    List<Journey> findAllByParentId(Long parentId);

    Optional<Journey> findByExperienceInteractionId(Long experienceInteractionId);

    Optional<Journey> findByExperienceIndicatorId(Long experienceInteractionId);

}
