package cn.hanyi.ctm.repository;

import cn.hanyi.ctm.entity.persona.CustomerPersona;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface CustomerPersonRepository extends ResourceRepository<CustomerPersona, Long> {

    @Modifying
    @Query(nativeQuery = true, value = "update customer_persona set category_id = null where category_id=:categoryId")
    void clearCustomerPersonaCategory(Long categoryId);

    @Modifying
    @Query(nativeQuery = true, value = "update customer_persona set journey_map_titles = :journeyMapTitles where id=:id")
    void updateCustomerPersonaJourneyTitles(Long id,String journeyMapTitles);

}
