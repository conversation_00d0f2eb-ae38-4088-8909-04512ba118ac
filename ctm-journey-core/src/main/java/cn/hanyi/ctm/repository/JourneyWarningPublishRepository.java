package cn.hanyi.ctm.repository;

import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import cn.hanyi.ctm.entity.journey.JourneyWarningPublish;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Set;

public interface JourneyWarningPublishRepository extends ResourceRepository<JourneyWarningPublish, Long> {
    JourneyWarningPublish findFirstByRelationTypeAndRelationId(JourneyComponentType relationType, Long relationId);

    List<JourneyWarningPublish> findByRelationTypeAndRelationIdIn(JourneyComponentType relationType, Set<Long> relationIds);

    List<JourneyWarningPublish> findByRelationTypeAndEnableWarning(JourneyComponentType relationType, Boolean enableWarning, Pageable page);

    List<JourneyWarningPublish> findByJourneyMapIdAndRelationTypeAndEnableWarning(Long journeyMapId, JourneyComponentType relationType, Boolean enableWarning, Pageable page);

}