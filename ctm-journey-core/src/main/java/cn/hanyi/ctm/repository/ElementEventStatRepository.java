package cn.hanyi.ctm.repository;

import cn.hanyi.ctm.entity.journey.ElementEventStat;
import org.befun.core.repository.ResourceRepository;

public interface ElementEventStatRepository extends ResourceRepository<ElementEventStat, Long>,
        IFindByOrgIdAndJourneyIdIn<ElementEventStat>,
        IFindByJourneyMapIdAndJourneyId<ElementEventStat>
{
    ElementEventStat findFirstByJourneyMapIdAndComponentIdAndJourneyId(Long journeyMapId, Long componentId, Long journeyId);
}