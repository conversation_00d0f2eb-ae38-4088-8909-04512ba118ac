package cn.hanyi.ctm.repository;

import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import cn.hanyi.ctm.entity.journey.JourneyComponent;
import cn.hanyi.ctm.projection.JourneyComponentOrder;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Repository
public interface JourneyComponentRepository extends ResourceRepository<JourneyComponent, Long> {
    @Modifying
    @Query("update JourneyComponent jc set jc.order = :order where jc.id = :componentId and jc.orgId= :orgId")
    void updateOrderById(@Param(value = "order") int order, @Param(value = "componentId") Long componentId, @Param(value = "orgId") Long orgId);

    List<JourneyComponent> findByJourneyMapIdAndTypeIn(Long journeyMapId, Collection<JourneyComponentType> types);

    List<JourneyComponent> findByJourneyMapIdAndParentId(Long journeyMapId, Long parentId);

    Optional<JourneyComponentOrder> findFirstByJourneyMapIdAndParentIdAndTypeOrderByOrderDesc(Long journeyMapId, Long parentId, JourneyComponentType type);

    List<JourneyComponent> findByJourneyMapIdAndParentIdAndTypeAndRelationIdIn(Long journeyMapId, Long parentId, JourneyComponentType type, List<Long> relationIds);

}
