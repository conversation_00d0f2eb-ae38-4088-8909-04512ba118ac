package cn.hanyi.ctm.repository;

import cn.hanyi.ctm.entity.journey.ElementLinker;
import org.befun.core.repository.ResourceRepository;

public interface ElementLinkerRepository extends ResourceRepository<ElementLinker, Long>,
        IFindByOrgIdAndJourneyIdIn<ElementLinker>,
        IFindByJourneyMapIdAndJourneyId<ElementLinker> {
    ElementLinker findFirstByJourneyMapIdAndComponentIdAndJourneyId(Long journeyMapId, Long componentId, Long journeyId);
}