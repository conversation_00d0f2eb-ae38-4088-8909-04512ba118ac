package cn.hanyi.ctm.repository;

import cn.hanyi.ctm.entity.journey.ExperienceIndicator;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface ExperienceIndicatorRepository extends ResourceRepository<ExperienceIndicator, Long>, IFindByOrgIdAndJourneyIdIn<ExperienceIndicator> {

    List<ExperienceIndicator> findAllByJourneyIdIn(Set<Long> journeyIds);

    @Query(nativeQuery = true,
            value = "select * from experience_indicator ei where ei.sids like %:sid% or ei.qids like %:qid%")
    List<ExperienceIndicator> findAllBySidOrQid(@Param("sid") String sid, @Param("qid") String qid);

    @Query(nativeQuery = true,
            value = "select * from experience_indicator ei where ei.sids like %:sid% ")
    List<ExperienceIndicator> findAllBySid(@Param("sid") String sid);

    @Query(nativeQuery = true,
            value = "select * from experience_indicator ei where ei.sids like %:sid% and ei.qids like %:qid%")
    List<ExperienceIndicator> findAllBySidAndQid(@Param("sid") String sid, @Param("qid") String qid);

}
