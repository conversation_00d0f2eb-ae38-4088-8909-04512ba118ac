package cn.hanyi.ctm.repository;

import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import cn.hanyi.ctm.entity.journey.JourneyWarning;
import org.befun.core.repository.ResourceRepository;

import java.util.List;
import java.util.Set;

public interface JourneyWarningRepository extends ResourceRepository<JourneyWarning, Long> {
    JourneyWarning findFirstByRelationTypeAndRelationId(JourneyComponentType relationType, Long relationId);

    List<JourneyWarning> findByRelationTypeAndRelationIdIn(JourneyComponentType relationType, Set<Long> relationIds);
}