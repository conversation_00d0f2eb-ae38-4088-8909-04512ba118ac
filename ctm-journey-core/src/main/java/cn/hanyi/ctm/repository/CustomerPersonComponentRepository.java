package cn.hanyi.ctm.repository;

import cn.hanyi.ctm.constant.persona.PersonaComponentType;
import cn.hanyi.ctm.entity.persona.CustomerPersonaComponent;
import org.befun.core.repository.ResourceRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface CustomerPersonComponentRepository extends ResourceRepository<CustomerPersonaComponent, Long> {
    Optional<CustomerPersonaComponent> findFirstByOrgIdAndPersonaIdAndType(Long orgId, Long personaId, PersonaComponentType type);
}
