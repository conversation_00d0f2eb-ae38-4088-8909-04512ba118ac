package cn.hanyi.ctm.repository;

import cn.hanyi.ctm.entity.journey.ExperienceInteraction;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ExperienceInteractionRepository extends ResourceRepository<ExperienceInteraction, Long>, IFindByOrgIdAndJourneyIdIn<ExperienceInteraction> {

    @Query(nativeQuery = true,
            value = "select * from experience_interaction ei where ei.interaction_sids like %:sid% ")
    List<ExperienceInteraction> findAllByInteractionSids(@Param("sid") String sid);

}
