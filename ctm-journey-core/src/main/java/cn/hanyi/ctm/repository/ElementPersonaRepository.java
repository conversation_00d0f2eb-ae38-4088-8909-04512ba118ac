package cn.hanyi.ctm.repository;

import cn.hanyi.ctm.entity.journey.ElementPersona;
import org.befun.core.repository.ResourceRepository;

public interface ElementPersonaRepository extends ResourceRepository<ElementPersona, Long>,
        IFindByOrgIdAndJourneyIdIn<ElementPersona>,
        IFindByJourneyMapIdAndJourneyId<ElementPersona> {
    ElementPersona findFirstByJourneyMapIdAndComponentIdAndJourneyId(Long journeyMapId, Long componentId, Long journeyId);
}