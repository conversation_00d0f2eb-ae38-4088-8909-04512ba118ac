package cn.hanyi.ctm.dto.journey;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.auth.constant.NotificationType;
import org.befun.core.rest.view.ResourceViews;
import org.befun.task.BaseTaskDetailDto;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class WarningReceiverDto extends BaseTaskDetailDto {

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "接受通知用户角色1,2,3")
    private List<Long> roleIds;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "接受通知用户1,2,3")
    private List<Long> userIds;

    @Schema(description = "通知类型")
    @JsonView(ResourceViews.Basic.class)
    private List<NotificationType> notifyChannel;
}