package cn.hanyi.ctm.dto.journey;

import cn.hanyi.ctm.entity.journey.*;

import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class JourneyCreateResponseDto extends BaseDTO {
    private JourneyDto journey;

    private List<JourneyComponentDto> components;
}
