package cn.hanyi.ctm.dto.journey.ext;

import cn.hanyi.ctm.entity.journey.JourneyMap;
import cn.hanyi.ctm.entity.journey.JourneyMapGroupDto;
import cn.hanyi.ctm.entity.persona.CustomerPersonaGroupDto;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.ResourcePermissionRelationType;
import org.befun.auth.projection.SimpleUser;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.EntityWithGroupDTO;
import org.befun.core.rest.view.ResourceViews;

import java.util.Optional;

@Getter
@Setter
public abstract class JourneyMapExtDto extends BaseEntityDTO<JourneyMap> implements EntityWithGroupDTO<JourneyMapGroupDto> {

    public JourneyMapExtDto(JourneyMap entity) {
        super(entity);
    }

    public JourneyMapExtDto() {
    }

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "项目类型: 1 旅程 2 文件夹")
    private int itemType = 1;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "项目目录")
    private JourneyMapGroupDto group;

    @Schema(description = "当前用户的类型：OWNER, ADMIN, MEMBER, NONE")
    @JsonView(ResourceViews.Basic.class)
    private ResourcePermissionRelationType currentType;

    @Schema(description = "拥有人用户信息")
    @JsonView(ResourceViews.Basic.class)
    private SimpleUser ownerUser;

    @Schema(description = "修改人用户信息")
    @JsonView(ResourceViews.Basic.class)
    private SimpleUser modifyUser;

    @Schema(description = "修改人")
    @JsonView(ResourceViews.Basic.class)
    private String modifyUserTruename;

    public String getModifyUserTruename() {
        return Optional.ofNullable(Optional.ofNullable(modifyUser).orElse(ownerUser)).map(SimpleUser::getTruename).orElse(null);
    }

}
