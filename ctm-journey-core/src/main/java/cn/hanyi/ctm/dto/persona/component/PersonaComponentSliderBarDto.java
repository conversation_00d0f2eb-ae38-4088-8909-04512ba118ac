package cn.hanyi.ctm.dto.persona.component;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Schema(description = "画像组件-滑动条（type=sliderBar 时有此项）")
public class PersonaComponentSliderBarDto {
    @JsonView(ResourceViews.Basic.class)
    private List<SliderBarItem> items = new ArrayList<>();

    @Getter
    @Setter
    @Schema(description = "滑动条配置项")
    public static class SliderBarItem {
        @JsonView(ResourceViews.Basic.class)
        private String name;
        @JsonView(ResourceViews.Basic.class)
        private Integer value = 0;
        @JsonView(ResourceViews.Basic.class)
        private Integer min = 0;
        @JsonView(ResourceViews.Basic.class)
        private Integer max = 100;
    }

    public static PersonaComponentSliderBarDto defaultSliderBar() {
        PersonaComponentSliderBarDto dto = new PersonaComponentSliderBarDto();
        dto.items.add(new SliderBarItem());
        dto.items.add(new SliderBarItem());
        dto.items.add(new SliderBarItem());
        return dto;
    }

}
