package cn.hanyi.ctm.dto.persona;

import cn.hanyi.ctm.entity.persona.CustomerPersona;
import cn.hanyi.ctm.entity.persona.CustomerPersonaDto;
import cn.hanyi.ctm.entity.persona.CustomerPersonaGroup;
import cn.hanyi.ctm.entity.persona.CustomerPersonaGroupDto;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.query.ResourceCustomQueryWithGroupDto;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.springframework.data.domain.Sort;

import java.util.Set;

@Getter
@Setter
public class CustomerPersonaQueryDto extends ResourceCustomQueryWithGroupDto<CustomerPersona, CustomerPersonaDto, CustomerPersonaGroup, CustomerPersonaGroupDto> {

    @Override
    public ResourceEntityQueryDto<CustomerPersonaDto> transformQueryResource(Set<Long> excludeGroupIds) {
        ResourceEntityQueryDto<CustomerPersonaDto> queryDto = super.transformQueryResource(excludeGroupIds);
        if (queryDto.getSorts() == null || queryDto.getSorts().isUnsorted()) {
            queryDto.setSorts(Sort.by(Sort.Direction.DESC, "createTime"));
        }
        return queryDto;
    }
}
