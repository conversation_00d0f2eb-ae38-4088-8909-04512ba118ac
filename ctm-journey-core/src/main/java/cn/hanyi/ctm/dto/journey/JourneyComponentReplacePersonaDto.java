package cn.hanyi.ctm.dto.journey;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;

import javax.validation.constraints.NotNull;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class JourneyComponentReplacePersonaDto extends BaseDTO {


    @Schema(description = "组件名称")
    private String title;

    @NotNull
    @Schema(description = "画像id")
    private Long personaId;

}
