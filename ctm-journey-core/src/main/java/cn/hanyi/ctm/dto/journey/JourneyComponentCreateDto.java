package cn.hanyi.ctm.dto.journey;

import cn.hanyi.ctm.constant.journeymap.ElementTextBoxType;
import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.type.TypeFactory;
import com.fasterxml.jackson.databind.util.Converter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.dto.BaseDTO;

import javax.validation.constraints.NotNull;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class JourneyComponentCreateDto extends BaseDTO {

    @NotNull
    @Schema(description = "组件类型", required = true)
    private JourneyComponentType type;

    @Schema(description = "组件名称")
    private String title;

    @Schema(description = "排序号")
    private Integer order = 0;

    @Schema(description = "文本组件子类型，type=textbox时，必需")
    @JsonDeserialize(converter = EnumDeserializeNullable.class)
    private ElementTextBoxType elementType;

    @Schema(description = "文本组件默认值，type=textbox时，可以传入文本组件的默认值，替换空字符串")
    private String text;

    @Schema(description = "父组件id，type=persona_content时，需要指定父组件id")
    private Long parentId = 0L;

    @Schema(description = "关联id，type=persona_content时，需要指定画像id")
    private Long relationId = 0L;

    public static class EnumDeserializeNullable implements Converter<String, ElementTextBoxType> {

        @Override
        public ElementTextBoxType convert(String value) {
            if (StringUtils.isEmpty(value)) {
                return null;
            } else {
                return ElementTextBoxType.valueOf(value);
            }
        }

        @Override
        public JavaType getInputType(TypeFactory typeFactory) {
            return typeFactory.constructType(String.class);
        }

        @Override
        public JavaType getOutputType(TypeFactory typeFactory) {
            return typeFactory.constructType(ElementTextBoxType.class);
        }
    }
}
