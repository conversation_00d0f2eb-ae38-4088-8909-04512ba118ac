package cn.hanyi.ctm.dto.journey;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;

import java.util.HashMap;
import java.util.Map;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class JourneyTriggerRequestDto extends BaseDTO {

    @Schema(description = "场景id：场景id和发送id必须存在一个，优先使用发送id")
    private Long sceneId;
    @Schema(description = "发送id：场景id和发送id必须存在一个，优先使用发送id")
    private String sendToken;
    @Schema(description = "如果客户不存在，是否创建客户") // 1.9.6
    private boolean createIfNotExist = true;
    @Schema(description = "查询目标客户(优先级1)：客户id，如果查询到了客户，并且其他参数都填写了，则其他参数会临时替换这个客户的信息")
    private Long customerId;
    @Schema(description = "查询目标客户(优先级2)：外部客户id，如果查询到了客户，并且其他参数都填写了，则其他参数会临时替换这个客户的信息")
    private String externalUserId;
    @Schema(description = "查询目标客户(优先级3)：微信公众号appId，默认值为第一个绑定的公众号appId") // 1.9.6
    private String weChatAppId;
    @Schema(description = "查询目标客户(优先级3)：微信用户openId") // 1.9.6
    private String weChatOpenId;
    @Schema(description = "客户名称")  // 1.9.6
    private String username;
    @Schema(description = "客户手机号")  // 1.9.6
    private String mobile;
    @Schema(description = "部门id")
    private Long departmentId;
    @Schema(description = "部门编号")
    private String departmentCode;
    @Schema(description = "外部企业id")
    private String externalCompanyId;
    @Schema(description = "自定义参数")
    private Map<String, Object> params = new HashMap<>();   // 接收自定义参数并将此参数用于解析url中${参数}

}
