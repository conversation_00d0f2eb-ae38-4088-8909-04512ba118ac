package cn.hanyi.ctm.dto.journey;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ElementCurveDynamicEmotionDto {
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "动态表情颜色")
    private String color;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "动态表情类型")
    private int emotionType;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "最小值")
    private Double min;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "最大值")
    private Double max;

}
