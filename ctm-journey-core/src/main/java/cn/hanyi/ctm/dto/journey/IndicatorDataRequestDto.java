package cn.hanyi.ctm.dto.journey;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Getter
@Setter
public class IndicatorDataRequestDto {

    @Schema(description = "部门id")
    private Long departmentId;
    @Schema(description = "开始日期，默认旅程设置的时间段")
    private String startDate;
    @Schema(description = "结束日期，默认旅程设置的时间段")
    private String endDate;
    @Schema(description = "是否已发布，默认未发布")
    private boolean publish = false;

    @NotNull
    @Min(1)
    @Schema(description = "体验指标id", required = true)
    private Long id;


}
