package cn.hanyi.ctm.dto.journey;

import cn.hanyi.ctm.entity.journey.ElementPersona;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.CustomParamDto;
import org.befun.core.rest.view.ResourceViews;

import javax.validation.constraints.NotNull;

@Setter
@Getter
public class ElementPersonaSaveDto extends CustomParamDto<ElementPersona> {

    @NotNull
    @Schema(description = "场景id")
    @JsonView(ResourceViews.Basic.class)
    private Long journeyId;

    @Schema(description = "内容")
    @JsonView(ResourceViews.Basic.class)
    private String content;
}
