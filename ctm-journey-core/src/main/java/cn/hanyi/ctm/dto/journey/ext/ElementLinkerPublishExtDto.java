package cn.hanyi.ctm.dto.journey.ext;

import cn.hanyi.ctm.dto.journey.ElementLinkerConfigDto;
import cn.hanyi.ctm.dto.journey.LinkerDataResponseDto;
import cn.hanyi.ctm.entity.journey.ElementLinkerPublish;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;

@Getter
@Setter
public class ElementLinkerPublishExtDto extends BaseEntityDTO<ElementLinkerPublish> {

    public ElementLinkerPublishExtDto(ElementLinkerPublish entity) {
        super(entity);
    }

    public ElementLinkerPublishExtDto() {
    }

    @Schema(description = "连接器配置")
    @JsonView(ResourceViews.Basic.class)
    private ElementLinkerConfigDto linkerConfig;

    @Schema(description = "连接器数据")
    @JsonView(ResourceViews.Basic.class)
    private LinkerDataResponseDto calLinker;
}

