package cn.hanyi.ctm.dto.persona.ext;

import cn.hanyi.ctm.entity.persona.CustomerPersona;
import cn.hanyi.ctm.entity.persona.CustomerPersonaComponentDto;
import cn.hanyi.ctm.entity.persona.CustomerPersonaGroupDto;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.constant.ResourcePermissionRelationType;
import org.befun.auth.projection.SimpleUser;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.BaseResourcePermissionEntityDto;
import org.befun.core.dto.EntityWithGroupDTO;
import org.befun.core.rest.view.ResourceViews;

import java.util.List;

@Getter
@Setter
public abstract class CustomerPersonaExtDto extends BaseResourcePermissionEntityDto<CustomerPersona> implements EntityWithGroupDTO<CustomerPersonaGroupDto> {

    public CustomerPersonaExtDto(CustomerPersona entity) {
        super(entity);
    }

    public CustomerPersonaExtDto() {
    }

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "项目类型: 1 画像 2 文件夹")
    private int itemType = 1;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "项目目录")
    private CustomerPersonaGroupDto group;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "关联旅程数")
    private Integer countJourneyMap;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "修改人")
    private SimpleUser modifyUser;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "创建人")
    private SimpleUser createUser;

    @Schema(description = "当前用户的类型：OWNER, ADMIN, MEMBER, NONE")
    @JsonView(ResourceViews.Basic.class)
    private ResourcePermissionRelationType currentType;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "组件列表")
    private List<CustomerPersonaComponentDto> components;

}
