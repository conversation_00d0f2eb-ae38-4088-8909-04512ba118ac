package cn.hanyi.ctm.dto.journey.ext;

import cn.hanyi.ctm.dto.journey.ElementLinkerConfigDto;
import cn.hanyi.ctm.dto.journey.LinkerDataResponseDto;
import cn.hanyi.ctm.entity.journey.ElementLinker;
import cn.hanyi.ctm.entity.journey.ElementPersona;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;

@Getter
@Setter
public class ElementPersonaExtDto extends BaseEntityDTO<ElementPersona> {

    public ElementPersonaExtDto(ElementPersona entity) {
        super(entity);
    }

    public ElementPersonaExtDto() {
    }

    @Schema(description = "文本框类型")
    @JsonView(ResourceViews.Basic.class)
    private String type = "persona_content_element";

}

