package cn.hanyi.ctm.dto.journey;


import cn.hanyi.ctm.entity.journey.JourneyMapDto;
import cn.hanyi.ctm.entity.journey.JourneyComponentDto;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class JourneyMapResponseDto {

    @JsonView(ResourceViews.Basic.class)
    private JourneyMapDto journeyMap;
    @JsonView(ResourceViews.Basic.class)
    private List<JourneyComponentDto> components;
}
