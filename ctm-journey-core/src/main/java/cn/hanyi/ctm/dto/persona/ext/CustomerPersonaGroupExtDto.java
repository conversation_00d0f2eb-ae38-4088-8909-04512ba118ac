package cn.hanyi.ctm.dto.persona.ext;

import cn.hanyi.ctm.entity.persona.CustomerPersonaGroup;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.projection.SimpleUser;
import org.befun.core.dto.BaseResourcePermissionEntityDto;
import org.befun.core.rest.view.ResourceViews;

@Getter
@Setter
public abstract class CustomerPersonaGroupExtDto extends BaseResourcePermissionEntityDto<CustomerPersonaGroup> {

    public CustomerPersonaGroupExtDto(CustomerPersonaGroup entity) {
        super(entity);
    }

    public CustomerPersonaGroupExtDto() {
    }

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "画像数量")
    private Integer countPersona;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "修改人")
    private SimpleUser modifyUser;
}
