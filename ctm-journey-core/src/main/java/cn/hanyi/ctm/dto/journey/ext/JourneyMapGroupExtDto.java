package cn.hanyi.ctm.dto.journey.ext;

import cn.hanyi.ctm.entity.journey.JourneyMapGroup;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.projection.SimpleUser;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;

@Getter
@Setter
public abstract class JourneyMapGroupExtDto extends BaseEntityDTO<JourneyMapGroup> {

    public JourneyMapGroupExtDto(JourneyMapGroup entity) {
        super(entity);
    }

    public JourneyMapGroupExtDto() {
    }

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "旅程数量")
    private Integer countJourneyMap;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "修改人")
    private SimpleUser modifyUser;
}
