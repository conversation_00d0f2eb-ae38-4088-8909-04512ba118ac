package cn.hanyi.ctm.dto.journey;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class JourneyInteractionDto extends BaseDTO {
    private String interactionName;

    private Long surveyId;

    private String surveyName;

    private PushMomentDto momentDto;

    private DisturbMomentDto disturbMomentDto;

    private LocalDateTime expireTime;

    private InteractionTemplate app;
    private InteractionTemplate sms;
    private InteractionTemplate wechat;

    @Getter
    @Setter
    public static class InteractionTemplate {

        private Map<String, Object> content;

        /**
         * 短信直接使用第三方配置的模版
         * @see cn.hanyi.ctm.entity.ThirdPartyTemplate
         */
        private Long thirdpartyTemplateId;
        /**
         * 微信使用特殊的模版
         */
        private Long wechatTemplateId;

        public static InteractionTemplate fromThirdpartyTemplateId(Long templateId, Map<String, Object> content) {
            InteractionTemplate template = new InteractionTemplate();
            template.setThirdpartyTemplateId(templateId);
            if (content == null) {
                template.setContent(new HashMap<>());
            } else {
                template.setContent(content);
            }
            return template;
        }

        public static InteractionTemplate fromWechatTemplateId(Long templateId, Map<String, Object> content) {
            InteractionTemplate template = new InteractionTemplate();
            template.setWechatTemplateId(templateId);
            if (content == null) {
                template.setContent(new HashMap<>());
            } else {
                template.setContent(content);
            }
            return template;
        }
    }

}
