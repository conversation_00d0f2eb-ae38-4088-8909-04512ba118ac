package cn.hanyi.ctm.dto.journey;

import org.apache.commons.lang3.StringUtils;
import org.befun.core.utils.JsonHelper;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter
public class WarningReceiverDtoConverter implements AttributeConverter<WarningReceiverDto, String> {


    @Override
    public String convertToDatabaseColumn(WarningReceiverDto dto) {
        if (dto == null) {
            return null;
        } else {
            return JsonHelper.toJson(dto);
        }
    }

    @Override
    public WarningReceiverDto convertToEntityAttribute(String dbData) {
        if (StringUtils.isEmpty(dbData)) {
            return null;
        } else {
            return JsonHelper.toObject(dbData, WarningReceiverDto.class);
        }
    }
}