package cn.hanyi.ctm.dto.journey;

import cn.hanyi.ctm.entity.ConnectorDto;
import cn.hanyi.ctm.entity.ThirdPartyTemplateDto;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;

import java.util.ArrayList;
import java.util.List;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class InteractionRelationDto extends BaseDTO {
    private Integer  sms = 0;
    private List<ConnectorDto> connectors = new ArrayList<>();
    private List<ThirdPartyTemplateDto> thirdPartyTemplates = new ArrayList<>();
}
