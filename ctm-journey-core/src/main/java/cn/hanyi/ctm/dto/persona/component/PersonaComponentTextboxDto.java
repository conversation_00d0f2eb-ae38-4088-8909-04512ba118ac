package cn.hanyi.ctm.dto.persona.component;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

@Getter
@Setter
@Schema(description = "画像组件-文本框（type=textbox 时有此项）")
public class PersonaComponentTextboxDto {
    @JsonView(ResourceViews.Basic.class)
    private String content;  // {"content":"<ul><li><p></p></li></ul>"}

}
