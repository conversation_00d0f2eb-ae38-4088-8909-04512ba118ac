package cn.hanyi.ctm.dto.journey;

import cn.hanyi.ctm.entity.journey.ElementLinker;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.constant.ThirdPartyAuthType;
import org.befun.core.dto.CustomParamDto;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.view.ResourceViews;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Setter
@Getter
public class ElementLinkerSaveDto extends CustomParamDto<ElementLinker> {

    @NotNull
    @Schema(description = "场景id")
    @JsonView(ResourceViews.Basic.class)
    private Long journeyId;

    @NotEmpty
    @Schema(description = "名称")
    @JsonView(ResourceViews.Basic.class)
    private String name;

    @NotEmpty
    @Schema(description = "启用的连接器类型：baiduTongji")
    @JsonView(ResourceViews.Basic.class)
    private String linkerType;

    @NotNull
    @Schema(description = "连接器配置")
    @JsonView(ResourceViews.Basic.class)
    private ElementLinkerConfigDto linkerConfig;

    public void checkParam() {
        if (ThirdPartyAuthType.BAIDU_TONGJI.getCamelName().equals(linkerType) && linkerConfig.getBaiduTongji() == null) {
            throw new BadRequestException("百度统计配置信息不能为空");
        } else if (ThirdPartyAuthType.SHEN_CE.getCamelName().equals(linkerType) && linkerConfig.getShenCe() == null) {
            throw new BadRequestException("神策数据配置信息不能为空");
        }
    }

}
