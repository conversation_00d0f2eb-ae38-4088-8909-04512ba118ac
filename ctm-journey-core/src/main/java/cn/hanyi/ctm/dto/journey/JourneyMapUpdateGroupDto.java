package cn.hanyi.ctm.dto.journey;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Set;

@Getter
@Setter
public class JourneyMapUpdateGroupDto {

    @NotNull
    @Schema(description = "移入的组id，0为未分组")
    private Long targetGroupId;

    @NotEmpty
    @Schema(description = "待移动的数据ids")
    private Set<Long> ids;


}
