package cn.hanyi.ctm.dto.journey;

import com.fasterxml.jackson.annotation.JsonView;
import com.google.common.collect.Lists;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Setter
@Getter
public class IndicatorDataResponseDto {

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "指标元素id")
    private Long id;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "指标计算方式")
    private String calculatingMethod;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "指标数值和目标的差值")
    private Double compareTarget;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "实际值")
    private Double currentValue;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "环比")
    private Double ratioValue;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "每日数值：二维数组，recentValue[0][0] 日期，一定存在; recentValue[0][1] 当日数值，可能为null")
    private List<List<Object>> recentValue = new ArrayList<>();

    public void setCompareTarget(Double compareTarget) {
        this.compareTarget = formatDouble(compareTarget);
    }

    public void setCurrentValue(Double currentValue) {
        this.currentValue = formatDouble(currentValue);
    }

    public void setRatioValue(Double ratioValue) {
        this.ratioValue = formatDouble(ratioValue);
    }

    public static IndicatorDataResponseDto emptyDto(LocalDate s, LocalDate e) {
//        IndicatorDataResponseDto dto = new IndicatorDataResponseDto();
//        dto.fillRecentValues(s, e, null);
//        return dto;
        return new IndicatorDataResponseDto();
    }

    public void fillRecentValues(LocalDate s, LocalDate e, Map<String, Double> map) {
        LocalDate step = s;
        while (step.isBefore(e) || step.isEqual(e)) {
            String date = step.toString();
            addRecentValue(date, map == null ? null : formatDouble(map.get(date)));
            step = step.plusDays(1);
        }
    }

    public void addRecentValue(String label, Double value) {
        recentValue.add(Lists.newArrayList(label, formatDouble(value)));
    }

    public static Double formatDouble(Double value) {
        if (value == null) {
            return null;
        }
        return Long.valueOf(Math.round(value * 10000)).doubleValue() / 10000;
    }

}
