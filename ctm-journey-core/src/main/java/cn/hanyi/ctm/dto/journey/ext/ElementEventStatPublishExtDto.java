package cn.hanyi.ctm.dto.journey.ext;

import cn.hanyi.ctm.dto.journey.EventStatDataResponseDto;
import cn.hanyi.ctm.dto.journey.JourneyWarningDto;
import cn.hanyi.ctm.entity.journey.ElementEventStatPublish;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;

@Getter
@Setter
public class ElementEventStatPublishExtDto extends BaseEntityDTO<ElementEventStatPublish> {

    public ElementEventStatPublishExtDto(ElementEventStatPublish entity) {
        super(entity);
    }

    public ElementEventStatPublishExtDto() {
    }

    @Schema(description = "预警规则名称")
    @JsonView(ResourceViews.Basic.class)
    private String eventRuleName;

    @Schema(description = "预警统计数据")
    @JsonView(ResourceViews.Basic.class)
    private EventStatDataResponseDto calEventStat;

    @Schema(description = "统计预警，可能为null")
    @JsonView(ResourceViews.Basic.class)
    private JourneyWarningDto warning;
}

