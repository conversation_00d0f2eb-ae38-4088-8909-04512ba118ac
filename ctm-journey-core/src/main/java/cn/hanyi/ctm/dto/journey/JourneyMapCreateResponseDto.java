package cn.hanyi.ctm.dto.journey;

import cn.hanyi.ctm.entity.journey.JourneyMapDto;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

@Getter
@Setter
public class JourneyMapCreateResponseDto {

    @Schema(description = "0 成功 1 超出额度")
    @JsonView(ResourceViews.Basic.class)
    private int success;
    @JsonView(ResourceViews.Basic.class)
    private JourneyMapDto journeyMap;


    public static JourneyMapCreateResponseDto overLimit() {
        JourneyMapCreateResponseDto dto = new JourneyMapCreateResponseDto();
        dto.setSuccess(1);
        return dto;
    }

    public static JourneyMapCreateResponseDto success(JourneyMapDto journeyMap) {
        JourneyMapCreateResponseDto dto = new JourneyMapCreateResponseDto();
        dto.setSuccess(0);
        dto.setJourneyMap(journeyMap);
        return dto;
    }
}
