package cn.hanyi.ctm.dto.journey;

import java.util.List;
import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.utils.JsonHelper;

@Converter
public class ElementCurveDynamicEmotionDtoConverter implements AttributeConverter<List<ElementCurveDynamicEmotionDto>, String> {


    @Override
    public String convertToDatabaseColumn(List<ElementCurveDynamicEmotionDto> dto) {
        if (dto == null) {
            return null;
        } else {
            return JsonHelper.toJson(dto);
        }
    }

    @Override
    public List<ElementCurveDynamicEmotionDto> convertToEntityAttribute(String dbData) {
        if (StringUtils.isEmpty(dbData)) {
            return null;
        } else {
            return JsonHelper.toList(dbData, ElementCurveDynamicEmotionDto.class);
        }
    }
}