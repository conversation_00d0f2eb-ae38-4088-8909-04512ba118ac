package cn.hanyi.ctm.dto.journey;

import cn.hanyi.ctm.constant.warning.IndicatorCompare;
import cn.hanyi.ctm.constant.warning.IndicatorWarningFrequency;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

@Getter
@Setter
public class JourneyWarningDto {

    @Schema(description = "预警规则是否开启：false true")
    @JsonView(ResourceViews.Basic.class)
    private Boolean enableWarning;

    @Schema(description = "预警频率")
    @JsonView(ResourceViews.Basic.class)
    private IndicatorWarningFrequency warningFrequency;

    @Schema(description = "预警范围：0(全部)，1，3，6，7，30")
    @JsonView(ResourceViews.Basic.class)
    private Integer warningRange;

    @Schema(description = "预警规则：LE GT EQ LT GE")
    @JsonView(ResourceViews.Basic.class)
    private IndicatorCompare warningCompare;

    @Schema(description = "设定值：整数或者小数，如果是百分比，先转换为小数在保存")
    @JsonView(ResourceViews.Basic.class)
    private Double warningValue;

    @Schema(description = "预警值：整数或者小数，如果是百分比，先转换为小数在保存")
    @JsonView(ResourceViews.Basic.class)
    private Double currentValue;

    @Schema(description = "通知是否开启：false true")
    @JsonView(ResourceViews.Basic.class)
    private Boolean enableNotify;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "接受预警用户列表")
    private WarningReceiverDto receiver = new WarningReceiverDto();


    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "日期区间")
    private String dateRange;
}
