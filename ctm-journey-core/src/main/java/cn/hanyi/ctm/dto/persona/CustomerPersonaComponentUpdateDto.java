package cn.hanyi.ctm.dto.persona;

import cn.hanyi.ctm.dto.persona.ext.CustomerPersonaComponentExtDto;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;

@Getter
@Setter
public class CustomerPersonaComponentUpdateDto extends CustomerPersonaComponentExtDto {


    @Override
    @Schema(hidden = true)
    public Long getId() {
        return super.getId();
    }

    @NotEmpty
    @Length(max = 40)
    @Schema(description = "组件名称")
    @JsonView(ResourceViews.Basic.class)
    private String name;

}
