package cn.hanyi.ctm.dto.journey.ext;

import cn.hanyi.ctm.entity.journey.Journey;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.dto.TreeDto;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public abstract class JourneyExtDto<T> extends BaseEntityDTO<Journey> implements TreeDto<T> {
    public JourneyExtDto(Journey entity) {
        super(entity);
    }

    public JourneyExtDto() {
    }

    @JsonView(ResourceViews.Basic.class)
    private List<T> children = new ArrayList<>();

    public abstract Long getParentId();

    @Override
    public Long getPid() {
        return getParentId();
    }

    @Override
    public List<T> children() {
        return children;
    }

    @Override
    public void addChild(T t) {
        children.add(t);
    }
}
