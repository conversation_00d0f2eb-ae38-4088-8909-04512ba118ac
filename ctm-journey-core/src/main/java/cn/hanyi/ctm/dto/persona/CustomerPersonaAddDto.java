package cn.hanyi.ctm.dto.persona;

import cn.hanyi.ctm.entity.persona.CustomerPersona;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.CustomParamDto;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Getter
@Setter
public class CustomerPersonaAddDto extends CustomParamDto<CustomerPersona> {

    @NotEmpty
    @Length(max = 20)
    private String name;

    @NotNull
    private Long groupId;

}
