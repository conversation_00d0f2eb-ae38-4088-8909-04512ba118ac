package cn.hanyi.ctm.dto.persona.component;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

@Getter
@Setter
@Schema(description = "画像组件-图片（type=image 时有此项）")
public class PersonaComponentImageDto {

    @Schema(description = "地址")
    @JsonView(ResourceViews.Basic.class)
    private String url;

}
