package cn.hanyi.ctm.dto.journey.ext;

import cn.hanyi.ctm.entity.journey.JourneyComponentPublish;
import cn.hanyi.ctm.entity.persona.CustomerPersonaDto;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
public abstract class JourneyComponentPublishExtDto<D extends JourneyComponentPublishExtDto<D>> extends BaseEntityDTO<JourneyComponentPublish> implements IJourneyComponentElement<D> {
    public JourneyComponentPublishExtDto(JourneyComponentPublish entity) {
        super(entity);
    }

    public JourneyComponentPublishExtDto() {
    }

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "组件关联数据：画像")
    private CustomerPersonaDto person;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "子组件关")
    private List<D> subComponents = new ArrayList<>();

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "组件元素")
    private List<Object> elements = new ArrayList<>();

}
