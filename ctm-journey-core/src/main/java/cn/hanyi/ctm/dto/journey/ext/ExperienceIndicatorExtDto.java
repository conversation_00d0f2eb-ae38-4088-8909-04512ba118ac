package cn.hanyi.ctm.dto.journey.ext;

import cn.hanyi.ctm.dto.journey.IndicatorDataResponseDto;
import cn.hanyi.ctm.dto.journey.JourneyWarningDto;
import cn.hanyi.ctm.entity.journey.ExperienceIndicator;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;

@Getter
@Setter
public abstract class ExperienceIndicatorExtDto extends BaseEntityDTO<ExperienceIndicator> implements IExperienceMatrix {

    public ExperienceIndicatorExtDto(ExperienceIndicator entity) {
        super(entity);
    }

    public ExperienceIndicatorExtDto() {
    }

    @Schema(description = "指标数据")
    @JsonView(ResourceViews.Basic.class)
    private IndicatorDataResponseDto calExperienceIndicator;

    @Schema(description = "指标预警，可能为null")
    @JsonView(ResourceViews.Basic.class)
    private JourneyWarningDto warning;
}
