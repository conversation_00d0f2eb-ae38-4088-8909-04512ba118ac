package cn.hanyi.ctm.dto.journey;

import cn.hanyi.ctm.entity.journey.JourneyMap;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.BaseEntityDTO;

import javax.validation.constraints.NotEmpty;

@Getter
@Setter
public class JourneyCreateRequestDto extends BaseEntityDTO<JourneyMap> {

    private Long groupId;
    @NotEmpty
    private String title;
    private String cover;
    private String defaultDateFilter;

}
