package cn.hanyi.ctm.dto.journey;

import cn.hanyi.ctm.constant.InteractionCollectorType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class InteractionDto extends BaseDTO {
    private String interactionName;

    private String sid;

    private String surveyName;

    private String surveyUrl;

    private PushMomentDto momentDto;

    private DisturbMomentDto disturbMomentDto;

    private LocalDateTime expireTime;

    private List<InteractionCollectorType> collectorList;

    private List<Long> ctmTemplateIdList;
}
