package cn.hanyi.ctm.dto.journey;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

@Setter
@Getter
@NoArgsConstructor
public class ElementLinkerConfigDto {

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "百度统计配置")
    private BaiduTongjiConfig baiduTongji;
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "神策配置")
    private ShenCeConfig shenCe;

    @Getter
    @Setter
    @NoArgsConstructor
    public static class BaiduTongjiConfig {
        @JsonView(ResourceViews.Basic.class)
        private String method;
        @JsonView(ResourceViews.Basic.class)
        private String metrics;
        @JsonView(ResourceViews.Basic.class)
        private String gran;
        @JsonView(ResourceViews.Basic.class)
        private String visitor;
        @Schema(description = "额外的需要保存的数据，由前端自定义格式和内容")
        @JsonView(ResourceViews.Basic.class)
        private String extra;

        public BaiduTongjiConfig(String method, String metrics, String gran, String visitor) {
            this.method = method;
            this.metrics = metrics;
            this.gran = gran;
            this.visitor = visitor;
        }
    }

    @Getter
    @Setter
    @NoArgsConstructor
    public static class ShenCeConfig {
        @Schema(description = "分析类型路径：事件分析(events/report)")
        @JsonView(ResourceViews.Basic.class)
        private String reportPath;
        @Schema(description = "分析内容: urlDecode 编码一次")
        @JsonView(ResourceViews.Basic.class)
        private String requestBody;

        public ShenCeConfig(String reportPath, String requestBody) {
            this.reportPath = reportPath;
            this.requestBody = requestBody;
        }
    }

    public ElementLinkerConfigDto(BaiduTongjiConfig baiduTongji) {
        this.baiduTongji = baiduTongji;
    }

    public ElementLinkerConfigDto(ShenCeConfig shenCe) {
        this.shenCe = shenCe;
    }

}
