package cn.hanyi.ctm.dto.journey.ext;

import cn.hanyi.ctm.entity.journey.ElementPersonaPublish;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;

@Getter
@Setter
public class ElementPersonaPublishExtDto extends BaseEntityDTO<ElementPersonaPublish> {

    public ElementPersonaPublishExtDto(ElementPersonaPublish entity) {
        super(entity);
    }

    public ElementPersonaPublishExtDto() {
    }

    @Schema(description = "文本框类型")
    @JsonView(ResourceViews.Basic.class)
    private String type = "persona_content_element";

}

