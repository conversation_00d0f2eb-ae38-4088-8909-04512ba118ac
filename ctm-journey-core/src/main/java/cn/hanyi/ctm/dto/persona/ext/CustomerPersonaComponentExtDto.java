package cn.hanyi.ctm.dto.persona.ext;

import cn.hanyi.ctm.dto.persona.component.*;
import cn.hanyi.ctm.entity.persona.CustomerPersonaComponent;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;

import java.util.Map;

@Getter
@Setter
public abstract class CustomerPersonaComponentExtDto extends BaseEntityDTO<CustomerPersonaComponent> {

    public CustomerPersonaComponentExtDto(CustomerPersonaComponent entity) {
        super(entity);
    }

    public CustomerPersonaComponentExtDto() {
    }

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "定位信息：{'w':1,'h':1,'x':0,'y':0}")
    private Map<String, Integer> layoutData;

    @Schema(description = "组件-个人信息")
    @JsonView(ResourceViews.Basic.class)
    private PersonaComponentProfileDto profile;

    @Schema(description = "组件-文本框(背景、期望、痛点)")
    @JsonView(ResourceViews.Basic.class)
    private PersonaComponentTextboxDto textbox;

    @Schema(description = "组件-滑动条(关键能力)")
    @JsonView(ResourceViews.Basic.class)
    private PersonaComponentSliderBarDto sliderBar;

    @Schema(description = "组件-标签")
    @JsonView(ResourceViews.Basic.class)
    private PersonaComponentTagDto tag;

    @Schema(description = "组件-图片")
    @JsonView(ResourceViews.Basic.class)
    private PersonaComponentImageDto image;
}
