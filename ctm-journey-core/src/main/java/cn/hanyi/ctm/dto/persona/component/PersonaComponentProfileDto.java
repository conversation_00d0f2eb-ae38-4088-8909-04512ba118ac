package cn.hanyi.ctm.dto.persona.component;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
@Schema(description = "画像组件-个人信息（type=profile 时有此项）")
public class PersonaComponentProfileDto {

    @Schema(description = "头像")
    @JsonView(ResourceViews.Basic.class)
    private String avatar;
    @Schema(description = "姓名")
    @JsonView(ResourceViews.Basic.class)
    private String name;
    @Schema(description = "职业")
    @JsonView(ResourceViews.Basic.class)
    private String professional;
    @Schema(description = "特征")
    @JsonView(ResourceViews.Basic.class)
    private String features;
    @Schema(description = "性别")
    @JsonView(ResourceViews.Basic.class)
    private String gender;
    @Schema(description = "年龄")
    @JsonView(ResourceViews.Basic.class)
    private Integer age;
    @Schema(description = "位置")
    @JsonView(ResourceViews.Basic.class)
    private String location;
    @Schema(description = "扩展字段")
    @JsonView(ResourceViews.Basic.class)
    private Map<String, Object> extraFields = new HashMap<>();

    public PersonaComponentProfileDto() {
    }

    public PersonaComponentProfileDto(String name) {
        this.name = name;
    }
}
