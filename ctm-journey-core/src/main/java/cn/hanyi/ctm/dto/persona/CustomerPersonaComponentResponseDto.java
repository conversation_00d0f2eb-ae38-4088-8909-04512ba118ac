package cn.hanyi.ctm.dto.persona;

import cn.hanyi.ctm.entity.persona.CustomerPersonaComponentDto;
import cn.hanyi.ctm.entity.persona.CustomerPersonaDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class CustomerPersonaComponentResponseDto {

    @Schema(description = "画像信息")
    private CustomerPersonaDto persona;
    @Schema(description = "组件列表")
    private List<CustomerPersonaComponentDto> components;

}
