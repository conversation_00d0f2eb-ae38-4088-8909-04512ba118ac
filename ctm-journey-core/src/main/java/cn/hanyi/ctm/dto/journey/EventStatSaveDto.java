package cn.hanyi.ctm.dto.journey;

import cn.hanyi.ctm.entity.journey.ElementEventStat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;

@Setter
@Getter
public class EventStatSaveDto extends BaseEntityDTO<ElementEventStat> {

    @NotNull
    @Schema(description = "场景id")
    @JsonView(ResourceViews.Basic.class)
    private Long journeyId;

    @NotNull
    @Schema(description = "预警规则id")
    @JsonView(ResourceViews.Basic.class)
    private Long eventRuleId;

    @NotNull
    @Range(min = 1, max = 2)
    @Schema(description = "统计类型：1 全部 2 待处理")
    @JsonView(ResourceViews.Basic.class)
    private Integer statType;

}
