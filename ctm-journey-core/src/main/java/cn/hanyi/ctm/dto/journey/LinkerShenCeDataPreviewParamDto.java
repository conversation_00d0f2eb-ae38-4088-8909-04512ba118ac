package cn.hanyi.ctm.dto.journey;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;

@Setter
@Getter
public class LinkerShenCeDataPreviewParamDto {

    @NotEmpty
    @Schema(description = "分析类型路径：事件分析(events/report)")
    private String reportPath;

    @NotEmpty
    @Schema(description = "分析内容: urlDecode 编码一次")
    private String requestBody;

}
