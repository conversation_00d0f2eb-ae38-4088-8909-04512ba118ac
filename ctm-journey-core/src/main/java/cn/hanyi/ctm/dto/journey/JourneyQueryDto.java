package cn.hanyi.ctm.dto.journey;


import cn.hanyi.ctm.entity.journey.JourneyMap;
import cn.hanyi.ctm.entity.journey.JourneyMapDto;
import cn.hanyi.ctm.entity.journey.JourneyMapGroup;
import cn.hanyi.ctm.entity.journey.JourneyMapGroupDto;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.query.ResourceCustomQueryWithGroupDto;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.springframework.data.domain.Sort;

import java.util.Set;

@Getter
@Setter
public class JourneyQueryDto extends ResourceCustomQueryWithGroupDto<JourneyMap, JourneyMapDto, JourneyMapGroup, JourneyMapGroupDto> {

    @Override
    public ResourceEntityQueryDto<JourneyMapDto> transformQueryResource(Set<Long> excludeGroupIds) {
        ResourceEntityQueryDto<JourneyMapDto> queryDto =  super.transformQueryResource(excludeGroupIds);
        if (queryDto.getSorts() == null || queryDto.getSorts().isUnsorted()) {
            queryDto.setSorts(Sort.by(Sort.Direction.DESC, "createTime"));
        }
        return queryDto;
    }
}
