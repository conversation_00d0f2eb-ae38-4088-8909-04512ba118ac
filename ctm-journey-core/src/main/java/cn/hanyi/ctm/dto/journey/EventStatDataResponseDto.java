package cn.hanyi.ctm.dto.journey;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

import java.util.ArrayList;
import java.util.List;

@Setter
@Getter
public class EventStatDataResponseDto {

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "事件统计元素id")
    private Long id;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "总数，可能为null")
    private Integer currentValue;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "环比：小数，可能为null，未进行百分比转化(0.8932)")
    private Double ratioValue;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "每日统计数量：二维数组，recentValue[0][0] 日期，一定存在; recentValue[0][1] 当日数量，可能为null")
    private List<List<Object>> recentValue = new ArrayList<>();
}
