package cn.hanyi.ctm.dto.persona.component;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@Schema(description = "画像组件-标签（type=tag 时有此项）")
public class PersonaComponentTagDto {
    @JsonView(ResourceViews.Basic.class)
    private List<TagItem> items = new ArrayList<>();

    @Getter
    @Setter
    @Schema(description = "标签项")
    public static class TagItem {
        @JsonView(ResourceViews.Basic.class)
        private String name;
    }
}
