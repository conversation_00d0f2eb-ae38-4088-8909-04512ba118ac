package cn.hanyi.ctm.dto.journey;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class JourneyCopyDto {
    @Schema(description = "旅程ids")
    private List<Long> sourceJourneyMapIds;
    private Long targetOrgId;
    private Long targetUserId;
    @Schema(description = "固定token，代码写死")
    private String copyToken;
}
