package cn.hanyi.ctm.dto.journey;

import cn.hanyi.ctm.constant.InteractionCollectorType;
import cn.hanyi.ctm.constant.MomentType;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;
import org.befun.core.rest.view.ResourceViews;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ExperienceMatrixDto extends BaseDTO {

    @JsonView(ResourceViews.Basic.class)
    private Long indicatorId;

    @JsonView(ResourceViews.Basic.class)
    private List<Long> surveyIds;

    @JsonView(ResourceViews.Basic.class)
    private List<String> surveyTitles;

    @JsonView(ResourceViews.Basic.class)
    private Long journeyId;
}
