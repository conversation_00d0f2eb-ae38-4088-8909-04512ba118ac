package cn.hanyi.ctm.dto.journey;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class JourneyComponentCreatePersonaDto extends BaseDTO {


    @Schema(description = "组件名称")
    private String title;

    @Schema(description = "父级画像组件id")
    private Long parentId = 0L;

    @NotEmpty
    @Schema(description = "画像id列表")
    private List<@NotNull @Min(1) Long> personaIds;

}
