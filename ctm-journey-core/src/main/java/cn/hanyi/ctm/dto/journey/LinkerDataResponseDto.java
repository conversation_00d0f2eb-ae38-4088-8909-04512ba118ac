package cn.hanyi.ctm.dto.journey;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Setter
@Getter
public class LinkerDataResponseDto {

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "连接器元素id")
    private Long id;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "连接器类型")
    private String linkerType;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "实际值")
    private Double currentValue;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "统计数据列类型：第一列为x轴，第二列开始为数据列")
    private List<String> recentLabel = new ArrayList<>();

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "统计数量")
    private List<List<Object>> recentValue = new ArrayList<>();

}
