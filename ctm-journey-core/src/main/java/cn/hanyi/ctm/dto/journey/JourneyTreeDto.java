package cn.hanyi.ctm.dto.journey;

import cn.hanyi.ctm.constant.SendManageChannelType;
import cn.hanyi.ctm.entity.journey.JourneyMap;
import cn.hanyi.ctm.entity.journey.JourneyPublish;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class JourneyTreeDto {

    private Long id;
    private String journeyName;
    private boolean enabled = false;
    private Long sendManageId = 0L;
    private List<SendManageChannelType> sendManageChannelTypes = new ArrayList<>();
    private Integer isLeaf = 1;
    private List<JourneyTreeDto> children = new ArrayList<>();

    @JsonIgnore
    private Date createTime;

    @JsonIgnore
    private Date modifyTime;

    public static JourneyTreeDto fromJourneyMap(JourneyMap journeyMap) {
        JourneyTreeDto dto = new JourneyTreeDto();
        dto.setId(journeyMap.getId());
        dto.setJourneyName(journeyMap.getTitle());
        dto.setIsLeaf(0);
        dto.setCreateTime(journeyMap.getCreateTime() == null ? new Date() : journeyMap.getCreateTime());
        dto.setModifyTime(journeyMap.getModifyTime() == null ? new Date() : journeyMap.getModifyTime());
        return dto;
    }

    public static JourneyTreeDto fromJourney(JourneyPublish journey) {
        JourneyTreeDto dto = new JourneyTreeDto();
        dto.setId(journey.getId());
        dto.setJourneyName(journey.getJourneyName());
        dto.setIsLeaf(journey.getIsLeaf());
        return dto;
    }
}
