package cn.hanyi.ctm.service.journey;

import cn.hanyi.ctm.dto.journey.ext.IElementJourneyId;
import cn.hanyi.ctm.entity.journey.JourneyComponentDto;
import cn.hanyi.ctm.repository.IFindByJourneyMapIdAndJourneyId;
import cn.hanyi.ctm.repository.IFindByOrgIdAndJourneyIdIn;
import org.apache.commons.collections.CollectionUtils;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.entity.BaseEntity;
import org.befun.core.repository.ResourceRepository;

import java.util.List;
import java.util.Set;

import static cn.hanyi.ctm.utils.ListHelper.validId;

/**
 * 场景元素（和场景绑定的）的公共功能
 * 1 删除场景时，会同时删除此场景和所有子场景下的所有元素
 * 2 新增场景和删除场景时的场景转移（保证元素永远在叶子节点上, {@link cn.hanyi.ctm.constant.journeymap.JourneyComponentType#supportTransfer} == true)
 */
public interface IComponentJourneyElement<E extends BaseEntity & IElementJourneyId, D extends BaseEntityDTO<E>, R extends ResourceRepository<E, Long>>
        extends IComponentElement<E, D, R> {
    @SuppressWarnings("unchecked")
    private IFindByOrgIdAndJourneyIdIn<E> castRepository1() {
        R repository = getRepository();
        if (repository instanceof IFindByOrgIdAndJourneyIdIn) {
            return (IFindByOrgIdAndJourneyIdIn<E>) repository;
        }
        return null;
    }

    @SuppressWarnings("unchecked")
    private IFindByJourneyMapIdAndJourneyId<E> castRepository2() {
        R repository = getRepository();
        if (repository instanceof IFindByJourneyMapIdAndJourneyId) {
            return (IFindByJourneyMapIdAndJourneyId<E>) repository;
        }
        return null;
    }


    /**
     * 删除场景后，需要把他的子场景所关联的组件都删除
     */
    default void deleteByJourneyIds(Long orgId, Set<Long> journeyIds) {
        IFindByOrgIdAndJourneyIdIn<E> repository;
        if (CollectionUtils.isNotEmpty(journeyIds) && (repository = castRepository1()) != null) {
            List<E> delete = repository.findByOrgIdAndJourneyIdIn(orgId, journeyIds);
            if (CollectionUtils.isNotEmpty(delete)) {
                getRepository().deleteAll(delete);
            }
        }
    }

    /**
     * 添加场景后，同时添加和场景相关的组件元素（情绪曲线需要立即添加一列）
     */
    default void addByJourney(Long orgId, Long journeyMapId, Long journeyId) {

    }

    /**
     * 增删场景后，返回的组件需要包含数据（事件统计，连接器）
     */
    default void fillDataByComponent(JourneyComponentDto componentDto) {

    }

    /**
     * 场景转移
     * *******
     * 添加场景（添加场景，永远是添加叶子节点）
     * 把父场景的事件统计，转移给新增的子场景（第一个子场景会得到父场景的事件统计，由于父场景的事件统计已被转移，其他的子场景就无法继承）
     */
    default void transferByAdd(Long journeyMapId, Long addSceneId, Long parentSceneId) {
        IFindByJourneyMapIdAndJourneyId<E> repository;
        if (validId(journeyMapId, addSceneId, parentSceneId) && (repository = castRepository2()) != null) {
            // 查询出所有父场景的事件统计
            List<E> list = repository.findByJourneyMapIdAndJourneyId(journeyMapId, parentSceneId);
            if (CollectionUtils.isNotEmpty(list)) {
                // 转移给子场景
                list.forEach(i -> i.setJourneyId(addSceneId));
                getRepository().saveAll(list);
            }
        }
    }

    /**
     * 场景转移
     * *******
     * 删除场景
     * 1 如果删除的是叶子场景，并且此叶子场景是最后一个子场景，则把叶子场景的事件统计转移到父场景
     * 2 如果删除的不是叶子场景，则不用转移场景（已和产品确认）
     */
    default void transferByDelete(Long journeyMapId, Long removeSceneId, Long parentSceneId, boolean isLeaf, boolean isLastChild) {
        IFindByJourneyMapIdAndJourneyId<E> repository;
        if (isLeaf && isLastChild && validId(journeyMapId, removeSceneId, parentSceneId) && (repository = castRepository2()) != null) {
            // 查询出所有子场景的事件统计
            List<E> list = repository.findByJourneyMapIdAndJourneyId(journeyMapId, removeSceneId);
            if (CollectionUtils.isNotEmpty(list)) {
                // 转移给父场景
                list.forEach(i -> i.setJourneyId(parentSceneId));
                getRepository().saveAll(list);
            }
        }
    }
}
