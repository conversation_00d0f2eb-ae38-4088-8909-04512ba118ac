package cn.hanyi.ctm.service;

import cn.hanyi.ctm.entity.journey.*;
import cn.hanyi.ctm.repository.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static cn.hanyi.ctm.constant.journeymap.JourneyComponentType.*;

@Service
public class JourneyMapCopyService {

    @Autowired
    private JourneyMapRepository journeyMapRepository;
    @Autowired
    private JourneyComponentRepository journeyComponentRepository;
    @Autowired
    private JourneyComponentPublishRepository journeyComponentPublishRepository;
    @Autowired
    private ElementTextBoxRepository elementTextBoxRepository;
    @Autowired
    private ElementTextBoxPublishRepository elementTextBoxPublishRepository;
    @Autowired
    private ElementCurveRepository elementCurveRepository;
    @Autowired
    private ElementCurvePublishRepository elementCurvePublishRepository;
    @Autowired
    private JourneyRepository journeyRepository;
    @Autowired
    private JourneyPublishRepository journeyPublishRepository;

    /**
     * 复制旅程的顺序
     * 1 JourneyMap
     * 2 JourneyComponentPublish
     * 3 JourneyPublish
     * 4 其他组件
     */
    @Transactional
    public JourneyMap copy(Long orgId, Long userId,Long groupId, String title, JourneyMap source) {
        JourneyMap target = new JourneyMap();
        BeanUtils.copyProperties(source, target);
        target.setId(null);
        target.setOrgId(orgId);
        target.setModifyUserId(null);
        target.setUserId(userId);
        target.setGroupId(groupId);
        target.setCreateTime(null);
        if (StringUtils.isEmpty(title)) {
            title = (StringUtils.isEmpty(source.getTitle()) ? "" : source.getTitle()) + "_复制";
        }
        if (title.length() > 20) {
            title = title.substring(0, 20);
        }
        target.setTitle(title);
        journeyMapRepository.save(target);

        copyAllComponents(source, target);
        return target;
    }

    //*******************************************************************************************
    //*********************************** Component *********************************************
    //*******************************************************************************************

    private void copyAllComponents(JourneyMap sourceParent, JourneyMap targetParent) {
        Map<Long/*sourceId*/, JourneyComponent/*target*/> componentMap = new HashMap<>();
        Map<Long/*sourceId*/, JourneyComponentPublish/*source*/> sourceComponentMap = new HashMap<>();
        MutablePair<Long/*sourceId*/, JourneyComponent> sceneComponent = new MutablePair<>();
        Map<JourneyComponent, JourneyComponent> childParentComponentMap = new HashMap<>();
        journeyComponentPublishRepository.findAll((root, query, builder) -> builder.equal(root.get("journeyMapId"), sourceParent.getId()))
                .forEach(source -> {
                    JourneyComponent target = copyComponent(source, targetParent);
                    if (target.getType() != null && target.getType() == journey) {
                        sceneComponent.setLeft(source.getId());
                        sceneComponent.setRight(target);
                    }
                    componentMap.put(source.getId(), target);
                    sourceComponentMap.put(source.getId(), source);
                });
        if (componentMap.isEmpty() || sceneComponent.getLeft() == null || sceneComponent.getRight() == null) {
            return;
        }
        // 计算出目标组件的父子关系
        sourceComponentMap.forEach((id, sc) -> {
            if (sc.getParentId() != null && sc.getParentId() > 0) {
                // 是子级
                JourneyComponentPublish parent = sourceComponentMap.get(sc.getParentId());
                if (parent != null) {
                    JourneyComponent tParent = componentMap.get(parent.getId());
                    JourneyComponent tChild = componentMap.get(id);
                    if (tChild != null && tParent != null) {
                        childParentComponentMap.put(tChild, tParent);
                    }
                }
            }
        });

        Map<Long/*sourceSceneId*/, Long/*targetSceneId*/> copyJourneyIdMap = Optional.of(sceneComponent).map(i -> {
            Long sourceComponentId = i.getLeft();
            journeyComponentRepository.save(i.getRight());
            return copyAllJourneys(sourceComponentId, i.getRight());
        }).orElse(null);
        if (MapUtils.isEmpty(copyJourneyIdMap)) {
            return;
        }
        List<JourneyComponent> savedComponent = new ArrayList<>();
        componentMap.forEach((sourceComponentId, component) -> {
//                journey,                  skip 上面已经复制过了
//                journey_descriptions,     textbox
//                experience_indicator,     skip
//                experience_matrix,        skip
//                experience_interaction,   skip
//                curve,                    curve
//                textbox;                  textbox
            if (component.getType() != null && component.getType() != journey) {
                journeyComponentRepository.save(component);
                savedComponent.add(component);
                if (component.getType() == journey_descriptions || component.getType() == textbox) {
                    copyAllElementTexts(sourceComponentId, component);
                } else if (component.getType() == curve) {
                    copyAllElementCurves(copyJourneyIdMap, sourceComponentId, component);
                }
            }
        });
        savedComponent.forEach(c -> {
            Optional.ofNullable(childParentComponentMap.get(c)).ifPresent(p -> {
                if (p.getId() != null) {
                    c.setParentId(p.getId());
                    journeyComponentRepository.save(c);
                }
            });
        });
    }

    private JourneyComponent copyComponent(JourneyComponentPublish source, JourneyMap targetParent) {
        JourneyComponent target = new JourneyComponent();
        target.setOrgId(targetParent.getOrgId());
        target.setJourneyMapId(targetParent.getId());
        target.setType(source.getType());
        target.setTitle(source.getTitle());
        target.setOrder(source.getOrder());
        target.setRelationId(source.getRelationId());
        target.setIsDisplay(source.getIsDisplay());
        target.setIsDelete(source.getIsDelete());
        return target;
    }


    //*******************************************************************************************
    //*********************************** Journey ***********************************************
    //*******************************************************************************************

    private Map<Long/*sourceId*/, Long/*targetId*/> copyAllJourneys(Long sourceComponentId, JourneyComponent targetParent) {
        List<JourneyPublish> journeys = journeyPublishRepository.findAll((root, query, builder) -> builder.equal(root.get("componentId"), sourceComponentId));
        if (journeys.isEmpty()) {
            return Map.of();
        }
        // 把原始的 journey tree 转换为 新的 tree (此时还没有进行存储，没有生成 id )
        Map<JourneyPublish/*source*/, Journey/*target*/> copyMap = new HashMap<>();
        Map<Long, JourneyPublish> sourceMap = journeys.stream().peek(i -> copyMap.put(i, copyJourney(i, targetParent))).collect(Collectors.toMap(JourneyPublish::getId, v -> v));
        List<Journey> root = new ArrayList<>();
        journeys.forEach(i -> {
            Journey targetJourney = copyMap.get(i);
            if (targetJourney != null) {
                if (i.getParentId() == null || i.getParentId() == 0L) {
                    targetJourney.setParentId(0L); // 根节点，直接设置父id为0
                    root.add(targetJourney);
                } else {
                    // 子节点，找到父节点，然后把自己加入到父节点的children中
                    JourneyPublish sourceParent = sourceMap.get(i.getParentId());
                    if (sourceParent != null) {
                        Optional.ofNullable(copyMap.get(sourceParent)).ifPresent(targetParentJourney -> {
                            if (targetParentJourney.getChildren() == null) {
                                targetParentJourney.setChildren(new ArrayList<>());
                            }
                            targetParentJourney.getChildren().add(targetJourney);
                        });
                    }
                }
            }

        });
        if (root.isEmpty()) {
            return Map.of();
        }
        // 先保存根节点
        journeyRepository.saveAll(root);
        List<Journey> nodes = root;
        do {
            List<Journey> childNodes = new ArrayList<>();
            nodes.forEach(i -> {
                // 设置当前节点（已保存） 所有子节点的 parentId
                Optional.ofNullable(i.getChildren()).ifPresent(children -> {
                    children.forEach(j -> j.setParentId(i.getId()));
                    childNodes.addAll(children);
                });
            });
            if (!childNodes.isEmpty()) {
                journeyRepository.saveAll(childNodes);
            }
            nodes = childNodes;
        } while (!nodes.isEmpty());


        Map<Long/*sourceId*/, Long/*targetId*/> copyIdMap = new HashMap<>();
        copyMap.forEach((k, v) -> {
            if (v.getId() != null) {
                copyIdMap.put(k.getId(), v.getId());
            }
        });
        return copyIdMap;
    }

    public Journey copyJourney(JourneyPublish source, JourneyComponent targetParent) {
        Journey target = new Journey();
        target.setOrgId(targetParent.getOrgId());
        target.setComponentId(targetParent.getId());
        target.setJourneyName(source.getJourneyName());
        target.setColor(source.getColor());
        target.setIsLeaf(source.getIsLeaf());
        target.setOrder(source.getOrder());
        return target;
    }


    //*******************************************************************************************
    //*********************************** ElementCurve ******************************************
    //*******************************************************************************************

    private void copyAllElementCurves(Map<Long/*sourceId*/, Long/*targetId*/> copyJourneyIdMap, Long componentId, JourneyComponent targetParent) {
        List<ElementCurve> elementCurves = elementCurvePublishRepository.findAll((root, query, builder) -> builder.equal(root.get("componentId"), componentId)).stream().filter(source -> copyJourneyIdMap.get(source.getJourneyId()) != null) // 过滤掉无效的journey
                .map(source -> copyElementCurve(copyJourneyIdMap.get(source.getJourneyId()), source, targetParent)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(elementCurves)) {
            elementCurveRepository.saveAll(elementCurves);
        }
    }


    public ElementCurve copyElementCurve(Long journeyId, ElementCurvePublish source, JourneyComponent targetParent) {
        ElementCurve target = new ElementCurve();
        target.setOrgId(targetParent.getOrgId());
        target.setComponentId(targetParent.getId());
        target.setJourneyId(journeyId);
        target.setDescription(source.getDescription());
        target.setLayout(source.getLayout());
        target.setColor(source.getColor());
        target.setEmotionType(source.getEmotionType());
        target.initElementCurveDynamicEmotion();
        return target;
    }

    //*******************************************************************************************
    //******************************** ElementTextBox *******************************************
    //*******************************************************************************************

    private void copyAllElementTexts(Long componentId, JourneyComponent targetParent) {
        List<ElementTextBox> elementTextBoxes = elementTextBoxPublishRepository.findAll((root, query, builder) -> builder.equal(root.get("componentId"), componentId)).stream().map(source -> {
            ElementTextBox target = new ElementTextBox();
            target.setOrgId(targetParent.getOrgId());
            target.setComponentId(targetParent.getId());
            target.setType(source.getType());
            target.setContent(source.getContent());
            return target;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(elementTextBoxes)) {
            elementTextBoxRepository.saveAll(elementTextBoxes);
        }
    }
}
