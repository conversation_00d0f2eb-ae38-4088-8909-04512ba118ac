package cn.hanyi.ctm.service.journey.elements.linker;

import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import cn.hanyi.ctm.entity.journey.ElementLinker;
import cn.hanyi.ctm.entity.journey.ElementLinkerPublish;
import cn.hanyi.ctm.entity.journey.ElementLinkerPublishDto;
import cn.hanyi.ctm.repository.ElementLinkerPublishRepository;
import cn.hanyi.ctm.service.journey.IComponentElement;
import cn.hanyi.ctm.service.journey.IJourneyMapPublish;
import org.befun.core.service.BaseService;
import org.springframework.stereotype.Service;

@Service
public class JourneyLinkerPublishService extends BaseService<ElementLinkerPublish, ElementLinkerPublishDto, ElementLinkerPublishRepository>
        implements
        IComponentElement<ElementLinkerPublish, ElementLinkerPublishDto, ElementLinkerPublishRepository>,
        IJourneyMapPublish<ElementLinker, ElementLinkerPublish, ElementLinkerPublishRepository> {

    @Override
    public boolean publishComponent() {
        return true;
    }

    @Override
    public JourneyComponentType componentType() {
        return JourneyComponentType.linker;
    }


    @Override
    public ElementLinkerPublish newInstance() {
        return new ElementLinkerPublish();
    }

}
