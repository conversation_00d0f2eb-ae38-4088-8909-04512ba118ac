package cn.hanyi.ctm.service;

import cn.hanyi.ctm.constant.SendManageTriggerType;
import cn.hanyi.ctm.dto.journey.JourneyTriggerRequestDto;
import cn.hanyi.ctm.entity.Customer;
import cn.hanyi.ctm.entity.SendManage;
import cn.hanyi.ctm.repository.SendManageRepository;
import cn.hanyi.ctm.sendmanage.SendMangeJourneyHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.entity.Department;
import org.befun.auth.service.DepartmentService;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Service
public class OpenService {

    @Autowired
    private DepartmentService departmentService;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private SendManageRepository sendManageRepository;
    @Autowired
    private SendMangeJourneyHelper sendMangeJourneyHelper;

    @Transactional
    public String triggerJourney(JourneyTriggerRequestDto dto) {
        final Long orgId = TenantContext.getCurrentTenant();
        final Long userId = TenantContext.getCurrentUserId();
        final Long sendManageId = checkSendManageId(orgId, dto);
        Customer customer = buildCustomer(dto, orgId);
        Map<String, Object> params = buildParams(dto);
        Long departmentId = buildDepartment(dto, orgId);
        sendMangeJourneyHelper.addCustomerJourneyByOpenApi(orgId, userId, sendManageId, dto.getSceneId(), customer.getId(), departmentId, params);
        return "Success";
    }

    private Long checkSendManageId(Long orgId, JourneyTriggerRequestDto dto) {
        SendManage sendManage;
        if (StringUtils.isNotEmpty(dto.getSendToken())) {
            sendManage = sendManageRepository.findFirstByOrgIdAndSendToken(orgId, dto.getSendToken());
            if (sendManage != null && sendManage.getTriggerType() == SendManageTriggerType.JOURNEY) {
                if (sendManage.getEnable() != null && sendManage.getEnable()) {
                    dto.setSceneId(sendManage.getTriggerId());
                    return sendManage.getId();
                } else {
                    throw new BadRequestException("发送已停用");
                }
            }
        } else if (dto.getSceneId() != null && dto.getSceneId() >= 0) {
            sendManage = sendMangeJourneyHelper.getSendManageByJourney(orgId, dto.getSceneId());
            if (sendManage != null) {
                return sendManage.getId();
            }
        }
        throw new BadRequestException("场景不存在");
    }

    private Customer buildCustomer(JourneyTriggerRequestDto dto, Long orgId) {
        if (dto.getCustomerId() == null && StringUtils.isEmpty(dto.getExternalUserId()) && StringUtils.isEmpty(dto.getWeChatOpenId())) {
            throw new BadRequestException("客户不存在");
        }
        Customer customer = customerService.findCustomer(orgId, dto.getCustomerId(), dto.getExternalUserId(), dto.getWeChatAppId(), dto.getWeChatOpenId());
        if (customer == null && dto.isCreateIfNotExist() && (StringUtils.isNotEmpty(dto.getExternalUserId()) || StringUtils.isNotEmpty(dto.getWeChatOpenId()))) {
            // 自动创建客户
            customer = customerService.createCustomer(orgId, dto.getExternalUserId(), dto.getUsername(), null, dto.getDepartmentId(), dto.getDepartmentCode(), dto.getMobile(), dto.getWeChatAppId(), dto.getWeChatOpenId());
        }
        if (customer == null) {
            throw new BadRequestException("客户不存在");
        }
        return customer;
    }

    private static Map<String, Object> buildParams(JourneyTriggerRequestDto dto) {
        Map<String, Object> params = new HashMap<>(dto.getParams());
        // 系统内置参数
        params.put("sceneId", dto.getSceneId());
        if (StringUtils.isNotEmpty(dto.getExternalUserId())) {
            params.put("externalUserId", dto.getExternalUserId());
        }
        if (StringUtils.isNotEmpty(dto.getDepartmentCode())) {
            params.put("departmentCode", dto.getDepartmentCode());
        }
        if (StringUtils.isNotEmpty(dto.getExternalCompanyId())) {
            params.put("externalCompanyId", dto.getExternalCompanyId());
        }
        // 临时替换客户信息
        if (StringUtils.isNotEmpty(dto.getUsername())) {
            params.put("username", dto.getUsername());
        }
        if (StringUtils.isNotEmpty(dto.getMobile())) {
            params.put("mobile", dto.getMobile());
        }
        if (StringUtils.isNotEmpty(dto.getExternalUserId())) {
            params.put("externalUserId", dto.getExternalUserId());
        }
        if (StringUtils.isNotEmpty(dto.getWeChatAppId())) {
            params.put("appId", dto.getWeChatAppId());
        }
        if (StringUtils.isNotEmpty(dto.getWeChatOpenId())) {
            params.put("openId", dto.getWeChatOpenId());
        }
        return params;
    }

    private Long buildDepartment(JourneyTriggerRequestDto dto, Long orgId) {
        Department department = departmentService.getByIdOrCodeOrRoot(orgId, dto.getDepartmentId(), dto.getDepartmentCode(), true);
        Long departmentId = department == null ? null : department.getId();
        return departmentId;
    }

}
