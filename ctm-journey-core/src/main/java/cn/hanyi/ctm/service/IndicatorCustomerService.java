package cn.hanyi.ctm.service;

import cn.hanyi.ctm.constant.SubTypeFilter;
import cn.hanyi.ctm.constant.TypeFilter;
import cn.hanyi.ctm.constant.survey.QuestionType;
import cn.hanyi.ctm.dto.journey.IndicatorCustomerDataResponseDto;
import cn.hanyi.ctm.dto.journey.TypeFilterSubFilterDto;
import cn.hanyi.ctm.entity.journey.JourneyMap;
import cn.hanyi.ctm.repository.JourneyMapRepository;
import cn.hanyi.survey.core.constant.channel.SurveyCollectorMethod;
import cn.hanyi.survey.core.repository.SurveyChannelRepository;
import cn.hanyi.survey.core.repository.SurveyQuestionRepository;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.befun.auth.dto.DepartmentTreeDto;
import org.befun.auth.service.DepartmentService;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.utils.DateHelper;
import org.befun.core.utils.JsonHelper;
import org.befun.core.utils.NumberHelper;
import org.befun.extension.service.NativeSqlHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Service
public class IndicatorCustomerService {

    @Autowired
    private JourneyMapRepository journeyMapRepository;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private NativeSqlHelper nativeSqlHelper;
    @Autowired
    private SurveyChannelRepository channelRepository;
    @Autowired
    private DepartmentService departmentService;
    @Autowired
    private CustomerExperienceIndicatorService customerExperienceIndicatorService;
    @Autowired
    private SurveyQuestionRepository surveyQuestionRepository;

    public List<JourneyMap> getCustomerJourneyMap(Long customerId) {
        Set<Long> journeyMapIds = joinIndicator(TenantContext.getCurrentTenant(), customerId);
        if (!journeyMapIds.isEmpty()) {
            return journeyMapRepository.findAllById(journeyMapIds);
        } else {
            return List.of();
        }
    }

    @Getter
    @Setter
    public static class JourneyMapIndicator {
        private Long journeyMapId;
        private String sid;
        private String qid;
    }

    public Set<Long> joinIndicator(Long orgId, Long customerId) {
        if (orgId == null || customerId == null) {
            return Set.of();
        }
        String sql = "select" +
                " jm.id journeyMapId," +
                " eip.sids sid," +
                " eip.qids qid" +
                " from journey_publish jp" +
                " inner join experience_indicator_publish eip on eip.is_valid=1 and jp.id=eip.journey_id and eip.calculating_method!='percent'" +
                " inner join journey_component_publish jcp on jp.component_id=jcp.id and jcp.type='journey'" +
                " inner join journey_map jm on jcp.journey_map_id=jm.id and jm.org_id=" + orgId;
        log.info("查询出企业({})的所有指标的sql: {}", orgId, sql);
        List<JourneyMapIndicator> indicators = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(JourneyMapIndicator.class));
        if (CollectionUtils.isEmpty(indicators)) {
            return Set.of();
        }
        Map<String, Set<Long>> map = new HashMap<>();
        Set<String> where = new HashSet<>();
        indicators.forEach(i -> {
            List<Long> sids, qids;
            if (i.getJourneyMapId() != null
                    && (sids = parseIds(i.getSid())) != null
                    && (qids = parseIds(i.getQid())) != null
                    && sids.size() == qids.size()) { // 问卷id和问题id的数量必须相等
                for (int k = 0; k < sids.size(); k++) {
                    Long sid = sids.get(k), qid = qids.get(k);
                    String key = String.format("%d,%d", sid, qid);
                    map.computeIfAbsent(key, j -> new HashSet<>()).add(i.getJourneyMapId());
                    where.add(String.format("(src.s_id=%d and src.q_id=%d)", sid, qid));
                }
            }
        });
        if (where.isEmpty()) {
            return Set.of();
        }
        sql = "select distinct concat(src.s_id,',',src.q_id)" +
                " from customer_answers ca" +
                " inner join survey_response_cell src on src.r_id=ca.answer_id" +
                " where ca.customer_id=" + customerId + "  and ca.answer_id>0 and (" + String.join(" or ", where) + ") ";
        log.info("查询出客户({})填答过和指标相关的问卷问题的sql: {}", customerId, sql);
        List<String> list = jdbcTemplate.queryForList(sql, String.class);
        if (CollectionUtils.isEmpty(list)) {
            return Set.of();
        }
        Set<Long> journeyMapIds = new HashSet<>();
        list.forEach(i -> {
            Set<Long> j = map.get(i);
            if (CollectionUtils.isNotEmpty(j)) {
                journeyMapIds.addAll(j);
            }
        });
        if (journeyMapIds.isEmpty()) {
            return Set.of();
        }
        return journeyMapIds;
    }

    public List<IndicatorCustomerDataResponseDto> data(Long customerId, Long journeyMapId) {
        List<IndicatorCustomerDataResponseDto> list = new ArrayList<>();
        if (journeyMapId == null || customerId == null) {
            return list;
        }
        // 查询客户统计数据 体验指标
        // 1 客户所有填答完成的问卷
        String sql1 = "select sid,answer_id rid,answer_time answerTime from customer_answers ca where ca.answer_status=1 and ca.customer_id=" + customerId + " order by ca.answer_time desc";
        log.info("查询客户统计数据, 指标数据, 客户填答记录 sql={}", sql1);
        List<CustomerAnswer> customerAnswers = nativeSqlHelper.queryListObject(sql1, CustomerAnswer.class);
        if (CollectionUtils.isEmpty(customerAnswers)) {
            return list;
        }
        // 2 所有旅程的体验指标的问卷
        String sql2 = "select " +
                " eip.journey_id journeyId," +
                " jp.journey_name journeyName," +
                " jp.color journeyColor," +
                " eip.indicator_name indicatorName," +
                " eip.calculating_method method," +
                " eip.sids," +
                " eip.qids," +
                " eip.item_names itemNames," +
                " eip.weights weights " +
                " from journey_publish jp" +
                " inner join experience_indicator_publish eip on eip.is_valid=1 and jp.id=eip.journey_id and eip.calculating_method!='percent'" +
                " inner join journey_component_publish jcp on jp.component_id=jcp.id and jcp.type='journey' and jcp.journey_map_id=" + journeyMapId;
        log.info("查询客户统计数据, 指标数据，所有指标 sql={}", sql2);
        List<JourneyIndicator> indicators = nativeSqlHelper.queryListObject(sql2, JourneyIndicator.class);
        List<JourneyIndicatorItem> indicatorSids = parseIndicators(indicators);
        if (CollectionUtils.isEmpty(indicatorSids)) {
            return list;
        }
        // 3 遍历填答过的问卷，如果是体验指标的问卷，则查询是否填答了体验指标的问题
        for (CustomerAnswer ca : customerAnswers) {
            if (NumberUtils.isDigits(ca.sid) && ca.rid != null && ca.rid > 0) {
                Long sid = Long.parseLong(ca.sid);
                List<JourneyIndicatorItem> items = indicatorSids.stream().filter(i -> i.getSid().equals(sid)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(items)) {
                    // 则查询是答卷否填答了体验指标的问题
                    String sql3 = "SELECT q_id qid, `type`, i_val ivalue, s_val svalue, j_val jvalue, cell_score score FROM survey_response_cell where s_id=%d and r_id=%d and q_id in (%s)";
                    log.info("查询客户统计数据, 指标数据，客户答题 sql={}", sql3);
                    sql3 = String.format(sql3, sid, ca.rid, items.stream().map(i -> i.qid + "").distinct().collect(Collectors.joining(",")));
                    List<CustomerSurveyResponse> responses = nativeSqlHelper.queryListObject(sql3, CustomerSurveyResponse.class);
                    if (CollectionUtils.isNotEmpty(responses)) {
                        Map<Long, CustomerSurveyResponse> responseMap = responses.stream().collect(Collectors.toMap(i -> i.qid, Function.identity(), (o1, o2) -> o1));
                        for (JourneyIndicatorItem item : items) {
                            CustomerSurveyResponse response = responseMap.get(item.qid);
                            if (response != null) {
                                if (List.of(QuestionType.SINGLE_CHOICE.ordinal(), QuestionType.SCORE_EVALUATION.ordinal()).contains(NumberHelper.unbox(response.type))) {
                                    if (response.score == null) {
                                        response.score = 0;
                                    }
                                    Double value = formatDouble(response.score * 100); // 去掉权重
                                    list.add(new IndicatorCustomerDataResponseDto(
                                            DateHelper.formatDate(ca.getAnswerTime()),
                                            value,
                                            item.indicatorName,
                                            item.journeyName,
                                            item.journeyColor,
                                            item.journeyId));

                                } else if (List.of(QuestionType.SCORE.ordinal(), QuestionType.NPS.ordinal()).contains(NumberHelper.unbox(response.type))) {
                                    if (response.ivalue != null) {
                                        Double value = formatDouble(response.ivalue * 100); // 去掉权重
                                        list.add(new IndicatorCustomerDataResponseDto(
                                                DateHelper.formatDate(ca.getAnswerTime()),
                                                value,
                                                item.indicatorName,
                                                item.journeyName,
                                                item.journeyColor,
                                                item.journeyId));
                                    }
                                } else if (QuestionType.MATRIX_SCORE.ordinal() == NumberHelper.unbox(response.type)) {
                                    if (!StringUtils.isEmpty(item.itemName)) {
                                        Map<String, Object> j;
                                        Object v;
                                        if (StringUtils.isNotEmpty(response.jvalue)
                                                && (j = JsonHelper.toMap(response.jvalue)) != null
                                                && (v = j.get(item.itemName)) != null
                                                && v instanceof Number) {
                                            Double value = formatDouble(((Number) v).intValue() * 100); // 去掉权重
                                            list.add(new IndicatorCustomerDataResponseDto(
                                                    DateHelper.formatDate(ca.getAnswerTime()),
                                                    value,
                                                    item.indicatorName,
                                                    item.journeyName,
                                                    item.journeyColor,
                                                    item.journeyId));
                                        }
                                    }
                                }
                                if (list.size() >= 10) {
                                    return list;
                                }
                            }
                        }
                    }
                }
            }
        }
        return list;
    }


    private Double formatDouble(Integer value) {
        if (value == null) {
            return 0.0;
        }
        return Long.valueOf(Math.round(value * 100)).doubleValue() / 10000;
    }

    private List<JourneyIndicatorItem> parseIndicators(List<JourneyIndicator> indicators) {
        if (CollectionUtils.isEmpty(indicators)) {
            return null;
        }
        List<JourneyIndicatorItem> list = new ArrayList<>();
        indicators.forEach(i -> {
            List<Long> sids = parseListLong(i.sids);
            List<Long> qids = parseListLong(i.qids);
            if (sids != null && qids != null && sids.size() == qids.size()) {
                List<Integer> weights = parseWeights(i.method, qids, parseListInt(i.weights));
                List<String> itemNames = alignItems(qids, parseListString(i.itemNames));
                IntStream.range(0, sids.size()).forEach(index -> {
                    Long sid = sids.get(index);
                    Long qid = qids.get(index);
                    String itemName = itemNames.get(index);
                    int weight = weights.get(index);
                    list.add(new JourneyIndicatorItem(i.journeyId, i.indicatorName, i.journeyColor, i.indicatorName, sid, qid, itemName, weight));
                });
            }
        });
        return list;
    }

    private List<Integer> parseWeights(String method, List<Long> qid, List<Integer> weights) {
        return IntStream.range(0, qid.size()).mapToObj(i -> {
            if (!"WeightAvg".equalsIgnoreCase(method) || weights == null || i >= weights.size()) {
                return 100;
            } else {
                return weights.get(i);
            }
        }).collect(Collectors.toList());
    }

    private List<Long> parseListLong(String s) {
        return StringUtils.isEmpty(s) ? null : Arrays.stream(s.replace("[", "").replace("]", "").replace("\"", "").split(","))
                .filter(NumberUtils::isDigits).map(Long::valueOf).collect(Collectors.toList());
    }

    private List<Integer> parseListInt(String s) {
        return StringUtils.isEmpty(s) ? null : Arrays.stream(s.replace("[", "").replace("]", "").replace("\"", "").split(","))
                .filter(NumberUtils::isDigits).map(Integer::valueOf).collect(Collectors.toList());
    }

    private List<String> parseListString(String s) {
        return StringUtils.isEmpty(s) ? null : Arrays.stream(s.replace("[", "").replace("]", "").split(","))
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    private List<String> alignItems(List<Long> qid, List<String> items) {
        return IntStream.range(0, qid.size()).mapToObj(i -> {
            if (items == null || i >= items.size() || StringUtils.isEmpty(items.get(i))) {
                return null;
            } else {
                return items.get(i);
            }
        }).collect(Collectors.toList());
    }


    @Getter
    @Setter
    public static class CustomerAnswer {
        private String sid;
        private Long rid;
        private Date answerTime;
    }

    @Getter
    @Setter
    public static class JourneyIndicator {
        private Long journeyId;
        private String journeyName;
        private String journeyColor;
        private String indicatorName;
        private String method;
        private String sids;
        private String qids;
        private String itemNames;
        private String weights;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class JourneyIndicatorItem {
        private Long journeyId;
        private String journeyName;
        private String journeyColor;
        private String indicatorName;
        private Long sid;
        private Long qid;
        private String itemName;
        private int weight;
    }

    @Getter
    @Setter
    public static class CustomerSurveyResponse {
        private Long qid;
        private Integer type;
        private Integer ivalue;
        private String svalue;
        private String jvalue;
        private Integer score;
    }

    //    /**
//     * 客户指定旅程的指标
//     * 1 查询出旅程的所有指标信息(journeyId,sid,qid,item)
//     * 2 从客户答卷中找到 问卷和问题都相等的答案
//     */
//    public List<IndicatorCustomerDataResponseDto> data(Long customerId, Long journeyMapId) {
//        if (journeyMapId == null || customerId == null) {
//            return List.of();
//        }
//        String sql = "select" +
//                " jp.id journeyId," +
//                " jp.journey_name journeyName," +
//                " jp.color journeyColor," +
//                " eip.indicator_name indicatorName," +
//                " eip.calculating_method method," +
//                " eip.sids sid," +
//                " eip.qids qid," +
//                " eip.item_names item," +
//                " eip.weights weights" +
//                " from journey_publish jp" +
//                " inner join experience_indicator_publish eip on jp.id=eip.journey_id and eip.calculating_method!='percent'" +
//                " where jp.component_id in (select id from journey_component_publish jcp where jcp.journey_map_id=" + journeyMapId + " and jcp.type='journey')";
//        log.info("查询出旅程({})的所有指标的sql:{} ", journeyMapId, sql);
//        List<JourneyIndicator> indicators = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(JourneyIndicator.class));
//        if (CollectionUtils.isEmpty(indicators)) {
//            return List.of();
//        }
//        AtomicInteger g = new AtomicInteger(0);
//        Map<Long, JourneyIndicator> map = new HashMap<>();
//        List<String> join = new ArrayList<>();
//        indicators.forEach(i -> {
//            List<Long> sids, qids;
//            if (i.getJourneyId() != null
//                    && (sids = parseIds(i.getSid())) != null
//                    && (qids = parseIds(i.getQid())) != null
//                    && sids.size() == qids.size()) { // 问问卷id和问题id的数量必须相等
//                List<String> items = parseItems(qids, parseItems(i.getItem()));
//                List<Integer> weights = parseWeights(i.getMethod(), qids, parseWeights(i.getWeights()));
//                for (int j = 0; j < sids.size(); j++) {
//                    join.add(String.format("select %d journeyId, %d sid, %d qid, '%s' item, %d sort, %d weight",
//                            i.getJourneyId(), sids.get(j), qids.get(j), items.get(j), g.incrementAndGet(), weights.get(j)));
//                }
//                map.put(i.getJourneyId(), i);
//            }
//        });
//        if (join.isEmpty()) {
//            return List.of();
//        }
//        String joins = String.join(" union all ", join);
//        sql = "select ANY_VALUE(tt.journeyId) journeyId,ANY_VALUE(tt.courseDate) courseDate,ANY_VALUE(tt.sort) sort," +
//                " sum((case tt.item when '' then if(tt.ivalue is null,tt.cell_score,tt.ivalue) else JSON_EXTRACT(tt.jvalue,tt.item) end)*tt.weight) value " +
//                " from (" +
//                " select concat(src.r_id,',',t.journeyId) groupby,t.journeyId,t.sid,t.item,src.i_val ivalue,src.cell_score,src.j_val jvalue,t.weight weight,t.sort,ca.answer_time courseDate " +
//                " from customer_answers ca " +
//                " inner join survey_response_cell src on src.r_id=ca.answer_id " +
//                " inner join" +
//                " (" + joins + " ) t " +
//                " on src.s_id=t.sid and src.q_id=t.qid" +
//                " where ca.answer_id>0  and ca.customer_id= " + customerId +
//                " ) tt group by tt.groupby order by courseDate desc,sort desc  limit 10";
//        log.info("查询出客户({})最近10次指标的sql: {}", customerId, sql);
//        List<RawValue> list = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(RawValue.class));
//        if (CollectionUtils.isEmpty(list)) {
//            return List.of();
//        }
//        return list.stream().map(i -> {
//            IndicatorCustomerDataResponseDto dto = new IndicatorCustomerDataResponseDto();
//            dto.setCourseDate(i.getCourseDate());
//            Optional.ofNullable(map.get(i.getJourneyId())).ifPresent(j -> {
//                dto.setJourneyId(j.getJourneyId());
//                dto.setJourneyName(j.getJourneyName());
//                dto.setJourneyColor(j.getJourneyColor());
//                dto.setIndicatorName(j.getIndicatorName());
//            });
//            dto.setCurrentValue(formatDouble(i.getValue()));
//            return dto;
//        }).collect(Collectors.toList());
//    }
//
//    private Double formatDouble(Integer value) {
//        if (value == null) {
//            return 0.0;
//        }
//        return Long.valueOf(Math.round(value * 100)).doubleValue() / 10000;
//    }
//
//    @Getter
//    @Setter
//    public static class RawValue {
//        private Long journeyId;
//        private Integer value;
//        private String courseDate;
//    }
//
//    private Integer parseJsonValue(String jvalue, String item) {
//        Map<String, Object> value = JsonHelper.toMap(jvalue);
//        if (value != null) {
//            Object i = value.get(item);
//            if (i != null) {
//                String j = i.toString();
//                if (NumberUtils.isDigits(j)) {
//                    return Integer.parseInt(j);
//                }
//            }
//        }
//        return null;
//    }
//
//    private List<String> parseItems(String s) {
//        if (StringUtils.isNotEmpty(s)) {
//            s = s.replace("[", "").replace("]", "").replace("\"", "");
//            return Arrays.stream(s.split(",")).collect(Collectors.toList());
//        }
//        return new ArrayList<>();
//    }
//
//    /**
//     * 把 item 和 qid 两个列表的元素数量对其
//     */
//    private List<String> parseItems(List<Long> qid, List<String> items) {
//        return IntStream.range(0, qid.size()).mapToObj(i -> {
//            if (items == null || i >= items.size() || StringUtils.isEmpty(items.get(i))) {
//                return "";
//            } else {
//                return "$." + items.get(i);
//            }
//        }).collect(Collectors.toList());
//    }
//
//    /**
//     * 把 weight 和 qid 两个列表的元素数量对其
//     */
//    private List<Integer> parseWeights(String method, List<Long> qid, List<Integer> weights) {
//        return IntStream.range(0, qid.size()).mapToObj(i -> {
//            if (!"WeightAvg".equalsIgnoreCase(method) || weights == null || i >= weights.size()) {
//                return 100;
//            } else {
//                return weights.get(i);
//            }
//        }).collect(Collectors.toList());
//    }
//
    private List<Long> parseIds(String s) {
        if (StringUtils.isNotEmpty(s)) {
            s = s.replace("[", "").replace("]", "").replace("\"", "");
            return Arrays.stream(s.split(",")).filter(NumberUtils::isDigits).map(Long::parseLong).collect(Collectors.toList());
        }
        return null;
    }
//
//    private List<Integer> parseWeights(String s) {
//        if (StringUtils.isNotEmpty(s)) {
//            s = s.replace("[", "").replace("]", "").replace("\"", "");
//            if (s.isEmpty()) {
//                return new ArrayList<>();
//            }
//            return Arrays.stream(s.split(",")).filter(NumberUtils::isDigits).map(Integer::parseInt).collect(Collectors.toList());
//        }
//        return new ArrayList<>();
//    }
//

    /**
     * 体验指标获过滤
     *
     * @param surveyId
     * @return
     */
    public List<TypeFilterSubFilterDto> typeFilter(Long surveyId, TypeFilter type) {
        var typeFilterList = new ArrayList<TypeFilterSubFilterDto>();

        switch (type) {
            case CHANNEL:
                // 顺序固定
                // 分享链接、短连接、手机短信、微信服务号、页面嵌入、调研家社区、场景推送、一店一码
                var channel = new ArrayList<SurveyCollectorMethod>() {{
                    add(SurveyCollectorMethod.LINK);
                    add(SurveyCollectorMethod.SHORT_LINK);
                    add(SurveyCollectorMethod.PHONE_MSG);
                    add(SurveyCollectorMethod.WECHAT_SERVICE);
                    add(SurveyCollectorMethod.EMBEDDED);
                    add(SurveyCollectorMethod.MP);
                    add(SurveyCollectorMethod.APP);
                    add(SurveyCollectorMethod.EMAIL);
                    add(SurveyCollectorMethod.SURVEY_PLUS);
                    add(SurveyCollectorMethod.IMPORT);
                    add(SurveyCollectorMethod.SCENE_INTERACTION);
                    add(SurveyCollectorMethod.SHOP_QRCODE);
                }};

                channel.forEach(method -> {
                    var channelType = new TypeFilterSubFilterDto();
                    channelType.setId((long) method.ordinal());
                    channelType.setName(method.getText());
                    channelType.setType(TypeFilter.CHANNEL);
                    channelType.setSubType(SubTypeFilter.CHANNEL);

                    typeFilterList.add(channelType);

                });
                break;
            case CHANNEL_NAME:
                // 实际回收渠道中是没有一店一码的
                // 但是response的回收方式中有一店一码的回收方式
                // 一店一码放到最后展示

                channelRepository.findAll((root, query, builder) -> builder.and(builder.equal(root.get("sid"), surveyId + "")), Sort.by("createTime").descending()).forEach(c -> {
                    typeFilterList.add(new TypeFilterSubFilterDto(c.getId(), TypeFilter.CHANNEL_NAME, c.getName(), SubTypeFilter.CHANNEL_NAME));
                });

                DepartmentTreeDto root = departmentService.treeByOrg(TenantContext.getCurrentTenant());
                Optional.ofNullable(departmentService.treeToList(root, d -> d)).ifPresent(dto -> {
                    dto.forEach(d -> {
                        typeFilterList.add(new TypeFilterSubFilterDto(d.getId(), TypeFilter.CHANNEL_NAME, d.getTitle(), SubTypeFilter.DEPARTMENT));
                    });
                });
                break;
        }
        return typeFilterList;
    }

}
