package cn.hanyi.ctm.service.persona.component;

import cn.hanyi.ctm.constant.persona.PersonaComponentType;
import cn.hanyi.ctm.dto.persona.ext.CustomerPersonaComponentExtDto;
import cn.hanyi.ctm.entity.persona.CustomerPersona;
import cn.hanyi.ctm.entity.persona.CustomerPersonaComponent;
import org.befun.core.utils.JsonHelper;

import java.util.function.BiConsumer;
import java.util.function.Function;

public interface IPersonaComponent<C> {

    void setDefaultContent(CustomerPersona persona, CustomerPersonaComponent entity);

    PersonaComponentType type();

    Class<C> componentClass();

    <D extends CustomerPersonaComponentExtDto> Function<D, C> getComponent();

    <D extends CustomerPersonaComponentExtDto> BiConsumer<D, C> setComponent();

    default <D extends CustomerPersonaComponentExtDto> CustomerPersonaComponentExtDto parseContent(CustomerPersonaComponent entity, D dto) {
        if (entity == null || dto == null) {
            return dto;
        }
        C component = parse(entity.getContent());
        setComponent().accept(dto, component);
        return dto;
    }

    default <D extends CustomerPersonaComponentExtDto> CustomerPersonaComponent formatContent(CustomerPersonaComponent entity, D dto) {
        if (entity == null || dto == null) {
            return entity;
        }
        String content = format(getComponent().apply(dto));
        entity.setContent(content);
        return entity;
    }

    default C parse(String content) {
        return JsonHelper.toObject(content, componentClass());
    }

    default String format(C c) {
        return JsonHelper.toJson(c);
    }
}
