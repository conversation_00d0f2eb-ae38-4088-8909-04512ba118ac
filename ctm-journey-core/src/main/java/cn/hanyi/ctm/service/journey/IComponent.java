package cn.hanyi.ctm.service.journey;

import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import cn.hanyi.ctm.dto.journey.ExperienceMatrixDto;
import cn.hanyi.ctm.dto.journey.ext.IExperienceMatrix;
import cn.hanyi.ctm.dto.journey.ext.IJourneyComponentElement;
import cn.hanyi.ctm.entity.journey.JourneyComponentBase;
import cn.hanyi.ctm.entity.journey.JourneyComponentPublish;
import cn.hanyi.ctm.entity.journey.JourneyComponentPublishDto;
import cn.hanyi.ctm.entity.persona.CustomerPersona;
import cn.hanyi.ctm.entity.persona.CustomerPersonaDto;
import cn.hanyi.ctm.repository.CtmSurveyRepository;
import cn.hanyi.ctm.service.persona.CustomerPersonaService;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.core.constant.EntityScopeStrategyType;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.entity.BaseEntity;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.domain.Sort;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


public interface IComponent<E extends JourneyComponentBase, D extends BaseEntityDTO<E> & IJourneyComponentElement<D>, R extends ResourceRepository<E, Long>> {

    R getRepository();

    CustomerPersonaService getCustomerPersonaService();

    CtmSurveyRepository getCtmSurveyRepository();

    IComponentElement<?, ?, ?> getComponentElement(JourneyComponentType type);

    List<D> mapToDto(List<E> entity);

    default List<D> findAll(Long journeyMapId) {
        List<E> entity = findAllEntity(journeyMapId);
        List<D> dto = mapToDto(entity);
        afterMapToDto(entity, dto);
        return dto;
    }

    default void afterMapToDto(List<E> entity, List<D> dto) {
        if (CollectionUtils.isEmpty(dto)) {
            return;
        }
        D matrixComponent = null;
        D indicatorComponent = null;
        Map<Long, List<D>> childrenMap = new HashMap<>();
        Map<JourneyComponentType, List<D>> relationMap = new HashMap<>();
        Iterator<D> iterator = dto.iterator();
        boolean singleChild = false;
        while (iterator.hasNext()) {
            D component = iterator.next();
            E e = component.getEntity();
            if (e == null) {
                continue;
            }
            // 体验问卷 组件的数据 是从 体验指标中提取的，这里把 体验问卷和体验指标这两个组件找出来
            if (e.getType() == JourneyComponentType.experience_indicator) {
                indicatorComponent = component;
            } else if (e.getType() == JourneyComponentType.experience_matrix) {
                matrixComponent = component;
            }
            component.getElements().addAll(getElementByComponent(e));
            // 处理组件关联的其他信息（客户画像）
            if (component.getRelationId() != null && component.getRelationId() > 0) {
                relationMap.computeIfAbsent(e.getType(), i -> new ArrayList<>()).add(component);
            }
            // 处理子组件
            if (component.getParentId() != null && component.getParentId() > 0) {
                // 如果是子组件，则先添加到map中，稍后在放入父组件中，并从列表中移除
                childrenMap.computeIfAbsent(component.getParentId(), i -> new ArrayList<>()).add(component);
                if (dto.size() == 1) {
                    singleChild = true;
                    // 如果列表只有一项，说明这个查询的是单个组件，直接查询子组件的时候，不去设置父子关系
                } else {
                    iterator.remove();
                }
            }
        }
        // 从map中取出子组件，放入父组件中
        if (!singleChild && !childrenMap.isEmpty()) {
            dto.forEach(i -> i.setSubComponents(childrenMap.getOrDefault(i.getId(), new ArrayList<>())));
        }
        // 加载关联信息
        if (!relationMap.isEmpty()) {
            loadRelationInfo(relationMap);
        }
        // 设置 体验问卷的数据
        if (matrixComponent != null && indicatorComponent != null) {
            matrixComponent.setElements(getExperienceMatrixElements(indicatorComponent.getElements()));
        }
    }

    default List<E> findAllEntity(Long journeyMapId) {
        return getRepository().findAll(((root, query, builder) -> builder.equal(root.get("journeyMapId"), journeyMapId)), Sort.by(Sort.Direction.ASC, "order"));
    }

    default void loadRelationInfo(Map<JourneyComponentType, List<D>> relationMap) {
        loadPersonaRelationInfo(relationMap.get(JourneyComponentType.persona_content));
    }

    private void loadPersonaRelationInfo(List<D> components) {
        if (CollectionUtils.isNotEmpty(components)) {
            Set<Long> personaIds = new HashSet<>();
            components.stream().filter(i -> i.getRelationId() != null && i.getRelationId() > 0).map(IJourneyComponentElement::getRelationId).forEach(personaIds::add);
            if (!personaIds.isEmpty()) {
                Map<Long, CustomerPersonaDto> map = getCustomerPersonaService().scopeQuery(EntityScopeStrategyType.ORGANIZATION, () -> getCustomerPersonaService().getGroupMapByIds(personaIds, CustomerPersona::getId));
//                Map<Long, CustomerPersonaDto> map = getCustomerPersonaService().getGroupMapByIds(personaIds, CustomerPersona::getId);
                components.forEach(i -> i.setPerson(map.get(i.getRelationId())));
            }
        }
    }

    /**
     * 删除组件同时删除元素
     */
    default void deleteElementByComponents(List<E> deleteList) {
        deleteList.forEach(this::deleteElementByComponent);
    }

    /**
     * 删除组件同时删除元素
     */
    default void deleteElementByComponent(E component) {
        if (component != null) {
            IComponentElement<?, ?, ?> componentElement = getComponentElement(component.getType());
            if (componentElement != null) {
                componentElement.deleteByComponent(component.getId());
            }
        }
    }

    /**
     * 通过组件获取组件的所有元素
     */
    @SuppressWarnings("unchecked")
    default List<Object> getElementByComponent(E component) {
        if (component != null) {
            IComponentElement<?, ?, ?> componentElement = getComponentElement(component.getType());
            if (componentElement != null) {
                return (List<Object>) componentElement.findAll(component.getId());
            }
        }
        return new ArrayList<>();
    }

    /**
     * 从体验指标中计算出体验问卷的数据
     */
    default List<Object> getExperienceMatrixElements(List<Object> objects) {
        if (CollectionUtils.isEmpty(objects)) {
            return new ArrayList<>();
        }
        List<IExperienceMatrix> indicators = new ArrayList<>();
        Set<Long> sIds = new HashSet<>();
        objects.forEach(a -> {
            IExperienceMatrix i = (IExperienceMatrix) a;
            indicators.add(i);
            Optional.of(i.getSids()).ifPresent(j -> j.forEach(k -> Optional.ofNullable(k).ifPresent(sIds::add)));
        });
        Map<Long, String> surveyTitleMap = new HashMap<>();
        if (!sIds.isEmpty()) {
            getCtmSurveyRepository().findAllById(sIds).forEach(i -> surveyTitleMap.put(i.getId(), i.getTitle()));
        }
        Function<List<Long>, List<String>> getTitle = ids -> {
            if (CollectionUtils.isNotEmpty(ids)) {
                return ids.stream().map(i -> surveyTitleMap.getOrDefault(i, "")).collect(Collectors.toList());
            }
            return List.of("");
        };
        return indicators.stream().map(i -> {
            List<String> titles = getTitle.apply(i.getSids());
            i.setSurveyTitles(titles);
            return new ExperienceMatrixDto(i.getId(), i.getSids(), titles, i.getJourneyId());
        }).collect(Collectors.toList());
    }

}
