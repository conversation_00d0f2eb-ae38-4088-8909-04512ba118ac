package cn.hanyi.ctm.service.persona.component;

import cn.hanyi.ctm.constant.persona.PersonaComponentType;
import cn.hanyi.ctm.dto.persona.component.PersonaComponentTagDto;
import cn.hanyi.ctm.dto.persona.ext.CustomerPersonaComponentExtDto;
import cn.hanyi.ctm.entity.persona.CustomerPersona;
import cn.hanyi.ctm.entity.persona.CustomerPersonaComponent;
import org.befun.core.utils.JsonHelper;
import org.springframework.stereotype.Service;

import java.util.function.BiConsumer;
import java.util.function.Function;

@Service
public class PersonaComponentTagService implements IPersonaComponent<PersonaComponentTagDto> {

    @Override
    public void setDefaultContent(CustomerPersona persona, CustomerPersonaComponent entity) {
        entity.setContent(JsonHelper.toJson(new PersonaComponentTagDto()));
    }

    @Override
    public PersonaComponentType type() {
        return PersonaComponentType.tag;
    }

    @Override
    public Class<PersonaComponentTagDto> componentClass() {
        return PersonaComponentTagDto.class;
    }

    @Override
    public <D extends CustomerPersonaComponentExtDto> Function<D, PersonaComponentTagDto> getComponent() {
        return CustomerPersonaComponentExtDto::getTag;
    }

    @Override
    public <D extends CustomerPersonaComponentExtDto> BiConsumer<D, PersonaComponentTagDto> setComponent() {
        return CustomerPersonaComponentExtDto::setTag;
    }
}
