package cn.hanyi.ctm.service;

import cn.hanyi.ctm.entity.journey.JourneyMap;
import cn.hanyi.ctm.entity.journey.JourneyMapDto;
import cn.hanyi.ctm.entity.journey.JourneyMapGroup;
import cn.hanyi.ctm.entity.journey.JourneyMapGroupDto;
import cn.hanyi.ctm.repository.JourneyMapGroupRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.UserService;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.BaseMixedGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class JourneyMapGroupService extends BaseMixedGroupService<
        JourneyMapGroup, JourneyMapGroupDto, JourneyMapGroupRepository,
        JourneyMap, JourneyMapDto, JourneyMapService> {

    @Autowired
    private UserService userService;
    @Autowired
    private JourneyMapService journeyMapService;

    @Override
    public void afterMapToDto(List<JourneyMapGroup> entity, List<JourneyMapGroupDto> dto) {
        if (CollectionUtils.isEmpty(dto)) {
            return;
        }
        Set<Long> userIds = new HashSet<>();
        Set<Long> ids = new HashSet<>();
        dto.forEach(i -> {
            Optional.ofNullable(i.getId()).ifPresent(ids::add);
            Optional.ofNullable(i.getModifyUserId()).ifPresent(userIds::add);
        });
        // 所有相关的用户
        Map<Long, SimpleUser> userMap = userService.getSimpleMapByIds(userIds);
        Map<Long, Integer> countMap = journeyMapService.getGroupCountMap(ids);
        dto.forEach(i -> {
            i.setModifyUser(userMap.get(i.getModifyUserId()));
            i.setCountJourneyMap(countMap.getOrDefault(i.getId(), 0));
        });
    }

    @Override
    public List<JourneyMapGroupDto> findAll(ResourceEntityQueryDto<JourneyMapGroupDto> queryDto) {
        List<JourneyMapGroupDto> list = super.findAll(queryDto);
        if (list == null) {
            list = new ArrayList<>();
        }
        list.add(0, getEmptyGroup());
        return list;
    }

    public JourneyMapGroupDto getEmptyGroup() {
        Long orgId = TenantContext.getCurrentTenant();
        JourneyMapGroupDto defaultGroup = new JourneyMapGroupDto();
        defaultGroup.setOrgId(orgId);
        defaultGroup.setId(0L);
        defaultGroup.setName("项目列表");
        return defaultGroup;
    }

}
