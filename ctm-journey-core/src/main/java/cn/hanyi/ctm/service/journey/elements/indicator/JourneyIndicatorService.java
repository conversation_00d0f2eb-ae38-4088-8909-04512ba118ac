package cn.hanyi.ctm.service.journey.elements.indicator;

import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import cn.hanyi.ctm.dto.journey.JourneyWarningDto;
import cn.hanyi.ctm.entity.journey.ExperienceIndicator;
import cn.hanyi.ctm.entity.journey.ExperienceIndicatorDto;
import cn.hanyi.ctm.entity.journey.Journey;
import cn.hanyi.ctm.repository.ExperienceIndicatorRepository;
import cn.hanyi.ctm.service.JourneyWarningService;
import cn.hanyi.ctm.service.data.IndicatorDataService;
import cn.hanyi.ctm.service.journey.IComponentJourneyElement;
import cn.hanyi.ctm.service.journey.elements.JourneyComponentService;
import cn.hanyi.ctm.service.journey.elements.scene.JourneySceneService;
import cn.hanyi.ctm.workertrigger.ICtmEventTrigger;
import org.apache.commons.collections.CollectionUtils;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.ResourceBatchUpdateRequestDto;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.annotation.processor.ResourceMethod;
import org.befun.core.rest.context.DeepEmbeddedFieldContext;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.CustomDeepEmbeddedService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Service
public class JourneyIndicatorService extends CustomDeepEmbeddedService<ExperienceIndicator, ExperienceIndicatorDto, ExperienceIndicatorRepository>
        implements IComponentJourneyElement<ExperienceIndicator, ExperienceIndicatorDto, ExperienceIndicatorRepository> {

    @Autowired
    private JourneyComponentService journeyComponentService;
    @Autowired
    private JourneySceneService journeySceneService;
    @Autowired
    private IndicatorDataService indicatorService;
    @Autowired
    private JourneyWarningService journeyWarningService;

    @Autowired
    private ICtmEventTrigger ctmEventTrigger;

    @Override
    public JourneyComponentType componentType() {
        return JourneyComponentType.experience_indicator;
    }

    @Override
    public void afterMapToDto(List<ExperienceIndicator> entity, List<ExperienceIndicatorDto> dto) {
        super.afterMapToDto(entity, dto);
        // set warning
        Set<Long> ids = new HashSet<>();
        if (CollectionUtils.isNotEmpty(dto)) {
            dto.forEach(i -> Optional.ofNullable(i.getId()).ifPresent(ids::add));
        }
        Map<Long, JourneyWarningDto> warningMap = journeyWarningService.groupByRelationId(JourneyComponentType.experience_indicator, ids, false);
        if (CollectionUtils.isNotEmpty(dto)) {
            dto.forEach(i -> Optional.ofNullable(warningMap.get(i.getId())).ifPresent(i::setWarning));
        }
    }

    @Override
    @Transactional
    public ExperienceIndicatorDto createDeepEmbeddedMany(Long journeyMapId, Long componentId, DeepEmbeddedFieldContext deepEmbeddedFieldContext, ExperienceIndicatorDto data) {
        var filter = data.getCalculatingFilter();
        if (filter != null && filter.getFilter().size() > 10) {
            throw new BadRequestException("筛选条数不能超过10条");
        }
        ExperienceIndicatorDto experienceIndicatorDto = super.createDeepEmbeddedMany(journeyMapId, componentId, deepEmbeddedFieldContext, data);
        updateJourneyExperienceIndicator(experienceIndicatorDto);
        experienceIndicatorDto.setCalExperienceIndicator(indicatorService.dataEdit(journeyMapId, componentId, experienceIndicatorDto.getEntity()));
        ctmEventTrigger.indicatorCreate(TenantContext.getCurrentTenant(), TenantContext.getCurrentUserId(), journeyMapId, componentId, experienceIndicatorDto.getId());
        return experienceIndicatorDto;
    }

    private void updateJourneyExperienceIndicator(ExperienceIndicatorDto dto) {
        Journey journey = journeySceneService.require(dto.getJourneyId());
        journey.setExperienceIndicatorId(dto.getId());
        if (dto.getIsDefaultDisplay() != null && dto.getIsDefaultDisplay() == 1) {
            setOffDefaultDisplay(dto);
        }
        journeySceneService.save(journey);
    }

    private void setOffDefaultDisplay(ExperienceIndicatorDto dto) {
        Journey journey = journeySceneService.require(dto.getJourneyId());
        Set<Long> fullJourneyIds = journeySceneService.getFullIds(journey.getComponentId(), journey.getId());
        if (!fullJourneyIds.isEmpty()) {
            List<ExperienceIndicator> experienceIndicators = repository.findAllByJourneyIdIn(fullJourneyIds);
            experienceIndicators.forEach(ei -> {
                ei.setIsDefaultDisplay(0);
            });
            repository.saveAll(experienceIndicators);
        }
    }

    @Override
    @Transactional
    public ExperienceIndicatorDto updateOneDeepEmbeddedMany(Long journeyMapId, String embeddedMapperBy, Long componentId, String deepEmbeddedMapperBy, Long deepId, ExperienceIndicatorDto change) {
        var filter = change.getCalculatingFilter();
        if (filter != null && filter.getFilter().size() > 10) {
            throw new BadRequestException("筛选条数不能超过10条");
        }
        ExperienceIndicatorDto experienceIndicatorDto = super.updateOneDeepEmbeddedMany(journeyMapId, embeddedMapperBy, componentId, deepEmbeddedMapperBy, deepId, change);
        if (filter == null && experienceIndicatorDto.getCalculatingFilter() != null) {
            experienceIndicatorDto.getEntity().setCalculatingFilter(null);
            experienceIndicatorDto.setCalculatingFilter(null);

        }
        updateJourneyExperienceIndicator(experienceIndicatorDto);
        experienceIndicatorDto.setCalExperienceIndicator(indicatorService.dataEdit(journeyMapId, componentId, experienceIndicatorDto.getEntity()));
        ctmEventTrigger.indicatorCreate(TenantContext.getCurrentTenant(), TenantContext.getCurrentUserId(), journeyMapId, componentId, experienceIndicatorDto.getId());
        return experienceIndicatorDto;
    }


    @Override
    protected <S extends BaseEntityDTO<ExperienceIndicator>> boolean beforeMapToEntity(ResourceMethod method, S data, ExperienceIndicator entity) {
        if (method == ResourceMethod.UPDATE_ONE) {
            crudService.mapToEntity(data, entity);
            if (data instanceof ExperienceIndicatorDto) {
                Double target = ((ExperienceIndicatorDto) data).getTargetValue();
                if (target == null) {
                    entity.setTargetValue(null);
                }
            }
            return true;
        }
        return super.beforeMapToEntity(method, data, entity);
    }

    @Override
    @Transactional
    public List<ExperienceIndicatorDto> batchUpdateDeepEmbeddedMany(Long journeyMapId, String embeddedMapperBy, Long componentId, String deepEmbeddedMapperBy, ResourceBatchUpdateRequestDto<ExperienceIndicatorDto> batchChangeDto) {
        List<ExperienceIndicatorDto> experienceIndicatorDtoList = super.batchUpdateDeepEmbeddedMany(componentId, embeddedMapperBy, componentId, deepEmbeddedMapperBy, batchChangeDto);
        experienceIndicatorDtoList.forEach(experienceIndicatorDto -> updateJourneyExperienceIndicator(experienceIndicatorDto));
        return experienceIndicatorDtoList;
    }

    @Override
    @Transactional
    public Boolean deleteOneDeepEmbeddedMany(Long entityId, String embeddedMapperBy, Long embeddedId, String deepEmbeddedMapperBy, Long deepId) {
        Boolean result = super.deleteOneDeepEmbeddedMany(entityId, embeddedMapperBy, embeddedId, deepEmbeddedMapperBy, deepId);
        journeySceneService.deleteIndicatorId(deepId);
        journeyWarningService.delete(JourneyComponentType.experience_indicator, deepId);
        return result;
    }
}
