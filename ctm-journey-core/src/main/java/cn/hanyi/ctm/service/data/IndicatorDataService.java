package cn.hanyi.ctm.service.data;

import cn.hanyi.ctm.constant.ExperienceInteractionStatus;
import cn.hanyi.ctm.constant.SentimentType;
import cn.hanyi.ctm.constant.survey.QuestionType;
import cn.hanyi.ctm.dto.journey.CalculatingFilterDto;
import cn.hanyi.ctm.dto.journey.IndicatorDataResponseDto;
import cn.hanyi.ctm.dto.stat.CacheStatIndicatorDataDto;
import cn.hanyi.ctm.dto.stat.CacheStatIndicatorParamDto;
import cn.hanyi.ctm.dto.survey.SurveyQuestionItemInfoDto;
import cn.hanyi.ctm.entity.journey.ExperienceIndicatorBase;
import cn.hanyi.ctm.service.JourneyMapService;
import cn.hanyi.ctm.service.journey.elements.indicator.JourneyIndicatorPublishService;
import cn.hanyi.ctm.service.journey.elements.indicator.JourneyIndicatorService;
import cn.hanyi.ctm.service.stat.CacheStatIndicatorService;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.befun.core.utils.EnumHelper;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static org.befun.core.utils.NumberHelper.division;
import static org.befun.core.utils.NumberHelper.unbox;

@Slf4j
@Service
public class IndicatorDataService {

    @Lazy
    @Autowired
    private CacheStatIndicatorService cacheStatIndicatorService;
    @Autowired
    private JourneyIndicatorService journeyIndicatorService;
    @Autowired
    private JourneyIndicatorPublishService journeyIndicatorPublishService;
    @Autowired
    private JourneyMapService journeyMapService;

    public List<IndicatorDataResponseDto> dataList(Long journeyMapId, Long componentId, boolean publish, String startDate, String endDate, Long departmentId) {
        List<? extends ExperienceIndicatorBase> list;
        if (publish) {
            list = journeyIndicatorPublishService.findAllEntity(componentId);
        } else {
            list = journeyIndicatorService.findAllEntity(componentId);
        }
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().parallel().map(i -> data(journeyMapId, componentId, i, startDate, endDate, departmentId)).collect(Collectors.toList());
        }
        return List.of();
    }

    public IndicatorDataResponseDto dataEdit(Long journeyMapId, Long componentId, ExperienceIndicatorBase entity) {
        return data(journeyMapId, componentId, entity, null, null, null);
    }

    public IndicatorDataResponseDto data(Long journeyMapId, Long componentId, Long indicatorId, boolean publish, String startDate, String endDate, Long departmentId) {
        ExperienceIndicatorBase entity = getEntity(indicatorId, publish);
        return data(journeyMapId, componentId, entity, startDate, endDate, departmentId);
    }

    /**
     * 1 平均值：总分数/次数
     * 2 加权平均：avg1*weight1 + avg2*weight2
     * 3 净推荐度：（大于8的次数 - 小于7的次数 ） / 总次数
     * 4 净满意度：（大于8的次数 - 小于7的次数 ） / 总次数
     * 5 选项占比：选项选中次数/总次数
     */
    public IndicatorDataResponseDto data(Long journeyMapId, Long componentId, ExperienceIndicatorBase entity /*nullable*/, String startDate, String endDate, Long departmentId) {
        IndicatorDataResponseDto result;
        Pair<LocalDate, LocalDate> currentDate = journeyMapService.parseJourneyMapDateRange(journeyMapId, componentId, startDate, endDate);
        LocalDate s = currentDate.getLeft();
        LocalDate e = currentDate.getRight();
        List<CacheStatIndicatorParamDto> params = parseParams(entity, departmentId);
        if (CollectionUtils.isEmpty(params) || ExperienceInteractionStatus.UNVALID.equals(entity.getIsValid())) {
            result = IndicatorDataResponseDto.emptyDto(s, e);
        } else {
            Pair<LocalDate, LocalDate> relativeDate = journeyMapService.relativeDate(s, e);
            LocalDate sp = relativeDate.getLeft();
            LocalDate ep = relativeDate.getRight();
            String type = entity.getCalculatingMethod();
            CalculatingFilterDto calculatingFilterDto = entity.getCalculatingFilter();
            DataValue current = null, previous = null;
            if (type.equalsIgnoreCase("average")) {
                current = calculateAvg(params.get(0), s, e, calculatingFilterDto);
                previous = calculateAvg(params.get(0), sp, ep, calculatingFilterDto);
            } else if (type.equalsIgnoreCase("NSS")) {
                current = calculateNps(params.get(0), s, e, calculatingFilterDto);
                previous = calculateNps(params.get(0), sp, ep, calculatingFilterDto);
            } else if (type.equalsIgnoreCase("NPS")) {
                current = calculateNps(params.get(0), s, e, calculatingFilterDto);
                previous = calculateNps(params.get(0), sp, ep, calculatingFilterDto);
            } else if (type.equalsIgnoreCase("percent")) {
                current = calculatePercent(params.get(0), s, e, calculatingFilterDto);
                previous = calculatePercent(params.get(0), sp, ep, calculatingFilterDto);
            } else if (type.equalsIgnoreCase("WeightAvg")) {
                current = calculateWeightAvg(params, s, e, calculatingFilterDto);
                previous = calculateWeightAvg(params, sp, ep, calculatingFilterDto);
            }
            result = compare(params.get(0).getQuestionId(), entity.getChartStyle(), s, e, entity.getTargetValue(), current, previous);
        }
        result.setCalculatingMethod(entity.getCalculatingMethod());
        result.setId(entity.getId());
        return result;
    }

    public ExperienceIndicatorBase getEntity(Long indicatorId, boolean publish) {
        if (publish) {
            return journeyIndicatorPublishService.get(indicatorId);
        } else {
            return journeyIndicatorService.get(indicatorId);
        }
    }

    public List<CacheStatIndicatorParamDto> parseParams(ExperienceIndicatorBase entity, Long departmentId) {
        if (entity == null) {
            return null;
        }
        String type = entity.getCalculatingMethod();
        List<Long> sid = entity.getSids();
        List<Long> qid = entity.getQids();
        CalculatingFilterDto filter = entity.getCalculatingFilter();
        BigInteger filterHash = filter == null ? null : new BigInteger(1, DigestUtils.md5(JsonHelper.toJson(filter)));
        if (StringUtils.isEmpty(type) || CollectionUtils.isEmpty(sid) || CollectionUtils.isEmpty(qid) || sid.size() != qid.size()) {
            return null;
        }
        List<String> itemNames = parseQuestionItems(qid, entity.getItemNames());
        boolean single = !type.equalsIgnoreCase("WeightAvg");
        if (single) {
            return List.of(
                    new CacheStatIndicatorParamDto(sid.get(0), qid.get(0), itemNames.get(0), entity.getPercentItem(), 1.0, departmentId, filterHash)
            );
        } else {
            List<Double> weights = parseWeights(qid, entity.getWeights());
            return IntStream.range(0, sid.size()).mapToObj(i -> {
                return new CacheStatIndicatorParamDto(sid.get(i), qid.get(i), itemNames.get(i), null, weights.get(i), departmentId, filterHash);
            }).collect(Collectors.toList());
        }
    }

    /**
     * 把 itemNames 和 qid 两个列表的元素数量对其
     */
    private List<String> parseQuestionItems(List<Long> qid, List<String> itemNames) {
        return IntStream.range(0, qid.size()).mapToObj(i -> {
            if (itemNames == null || i >= itemNames.size()) {
                return null;
            } else {
                return itemNames.get(i);
            }
        }).collect(Collectors.toList());
    }

    /**
     * 把 weight 和 qid 两个列表的元素数量对其
     */
    private List<Double> parseWeights(List<Long> qid, List<Long> weights) {
        int weightSum = (CollectionUtils.isEmpty(weights) ? 0 : weights.stream().filter(Objects::nonNull).mapToInt(Long::intValue).sum());
        Function<Integer, Double> getWeight = i -> {
            if (weightSum > 0 && CollectionUtils.isNotEmpty(weights) && i < weights.size()) {
                return weights.get(i).doubleValue() / weightSum;
            } else {
                return 0.0;
            }
        };
        return IntStream.range(0, qid.size()).mapToObj(getWeight::apply).collect(Collectors.toList());
    }

    private IndicatorDataResponseDto compare(Long qId, String chartStyle, LocalDate s, LocalDate e, Double targetValue, DataValue current, DataValue previous) {
        if (current == null) {
            // 本期无数据，不用计算和上期的的比例
            if (!"pie".equalsIgnoreCase(chartStyle)) {
                return IndicatorDataResponseDto.emptyDto(s, e);
            } else {
                return piePercent(qId, new IndicatorDataResponseDto(), Map.of());
            }
        }

        IndicatorDataResponseDto dto = new IndicatorDataResponseDto();
        dto.setCurrentValue(current.allData);
        if (current.allData != null) {
            if (targetValue != null) {
                dto.setCompareTarget(current.allData - targetValue);
            }
            if (previous != null && previous.allData != null && previous.allData != 0) {
                dto.setRatioValue((current.allData - previous.allData) / previous.allData);
            }
        }

        // day data or percent data
        if (!"pie".equalsIgnoreCase(chartStyle)) {
            dto.fillRecentValues(s, e, current.dayData);
        } else {
            dto = piePercent(qId, dto, current.percent);
        }
        return dto;
    }

    private IndicatorDataResponseDto piePercent(Long qId, IndicatorDataResponseDto dto, Map<String, Double> percentMap) {
        Pair<QuestionType, List<SurveyQuestionItemInfoDto>> questionInfo = cacheStatIndicatorService.questionInfo(qId);
        if (questionInfo == null) {
            return new IndicatorDataResponseDto();
        }
        dto.getRecentValue().clear();
        boolean score = questionInfo.getLeft() == QuestionType.SCORE || questionInfo.getLeft() == QuestionType.MATRIX_SCORE || questionInfo.getLeft() == QuestionType.NPS;
        boolean text = questionInfo.getLeft() == QuestionType.TEXT;
        questionInfo.getRight().forEach(i -> {
            Double v = percentMap.get(i.getValue());
            if (score) {
                dto.addRecentValue(i.getValue() + "分", v);
            } else if (text) {
                Arrays.stream(SentimentType.values()).filter(s -> i.getValue().equals(String.valueOf(s.getFlag()))).findFirst().ifPresentOrElse(s -> {
                    dto.addRecentValue(s.getDesc(), v);
                }, () -> {
                    dto.addRecentValue(i.getValue(), v);
                });
            } else {
                dto.addRecentValue(i.getText(), v);
            }
        });
        return dto;
    }

    public DataValue calculateWeightAvg(List<CacheStatIndicatorParamDto> params, LocalDate s, LocalDate e, CalculatingFilterDto calculatingFilterDto) {
        DataValue result = new DataValue();
        for (CacheStatIndicatorParamDto param : params) {
            DataValue j = calculateAvg(param, s, e, calculatingFilterDto);
            if (j.allData != null) {
                if (result.allData == null) {
                    result.allData = 0.0;
                }
                result.allData += j.allData; // 平均值已经乘了权重了，这里直接相加
            }
            j.dayData.forEach((k, v) -> result.dayData.merge(k, v, Double::sum));
        }
        return result;
    }


    public DataValue calculateAvg(CacheStatIndicatorParamDto param, LocalDate start, LocalDate end, CalculatingFilterDto calculatingFilterDto) {
        DataValue r = new DataValue();
        List<CacheStatIndicatorDataDto> values = cacheStatIndicatorService.getData(param, start, end, false, calculatingFilterDto);
        if (CollectionUtils.isNotEmpty(values)) {
            int sum = 0, count = 0;
            boolean hasData = false;
            for (CacheStatIndicatorDataDto value : values) {
                if (value.hasData) {
                    hasData = true;
                    int itemSun = unbox(value.avgSum);
                    int itemCount = value.realCount();
                    sum += itemSun;
                    count += itemCount;
                    r.dayData.put(value.day, division(itemSun, itemCount) * param.getWeight());
                }
            }
            if (hasData) {
                r.allData = division(sum, count) * param.getWeight();
            }
        }
        return r;
    }

    public DataValue calculateNps(CacheStatIndicatorParamDto param, LocalDate start, LocalDate end, CalculatingFilterDto calculatingFilterDto) {
        DataValue r = new DataValue();
        List<CacheStatIndicatorDataDto> values = cacheStatIndicatorService.getData(param, start, end, false, calculatingFilterDto);
        if (CollectionUtils.isNotEmpty(values)) {
            int gt8 = 0, lt7 = 0, count = 0;
            boolean hasData = false;
            for (CacheStatIndicatorDataDto value : values) {
                if (value.hasData) {
                    hasData = true;
                    int itemCount = value.realCount();
                    int itemGt8 = unbox(value.npsGt8);
                    int itemLt7 = unbox(value.npsLt7);
                    gt8 += itemGt8;
                    lt7 += itemLt7;
                    count += itemCount;
                    r.dayData.put(value.day, division(itemGt8 - itemLt7, itemCount));
                }
            }
            if (hasData) {
                r.allData = division((gt8 - lt7), count);
            }
        }
        return r;
    }

    public DataValue calculatePercent(CacheStatIndicatorParamDto param, LocalDate start, LocalDate end, CalculatingFilterDto calculatingFilterDto) {
        DataValue r = new DataValue();
        if (StringUtils.isEmpty(param.getPercentItem())) {
            log.warn("体验指标计算选项占比，选项不能为空");
            return r;
        }
        List<CacheStatIndicatorDataDto> values = cacheStatIndicatorService.getData(param, start, end, false, calculatingFilterDto);
        if (CollectionUtils.isNotEmpty(values)) {
            int selected = 0, count = 0;
            boolean hasData = false;
            Map<String, Integer> percent = new HashMap<>();
            for (CacheStatIndicatorDataDto value : values) {
                if (value.hasData) {
                    hasData = true;
                    int itemCount = value.realCount();
                    // 合并每个选项的选中次数
                    value.itemCountMap.forEach((k, v) -> {
                        int percentCount = unbox(percent.get(k)) + v.getItemCount();
                        percent.put(k, percentCount);
                    });
                    // 获得指定选项的选中次数
                    // v1.9.1 percentItem 可是保存多个选项
                    String percentItem = param.getPercentItem();
                    List<String> percentItems;
                    List<SentimentType> sentimentTypes = EnumHelper.parseList(SentimentType.values(), percentItem);
                    if (CollectionUtils.isNotEmpty(sentimentTypes)) {
                        percentItems = sentimentTypes.stream().map(i -> i.getFlag() + "").collect(Collectors.toList());
                    } else {
                        percentItems = Arrays.stream(percentItem.split(",")).collect(Collectors.toList());
                    }

                    int countPercent = 0;
                    for (String item : percentItems) {
                        countPercent += Optional.ofNullable(value.itemCountMap.get(item)).map(i -> i.getItemCount()).orElse(0);
                    }
                    selected += countPercent;
                    count += itemCount;
                    r.dayData.put(value.day, division(countPercent, itemCount));
                }
            }
            if (hasData) {
                r.allData = division(selected, count) * param.getWeight();
                // 计算每个选项的占比
                final int copyCount = count;
                percent.forEach((item, itemCount) -> r.percent.put(item, division(itemCount, copyCount)));
            }
        }
        return r;
    }

    @Getter
    @Setter
    public static class DataValue {
        private Double allData;
        private Map<String, Double> dayData = new HashMap<>();
        private Map<String, Double> percent = new HashMap<>();
    }
}
