package cn.hanyi.ctm.service;

import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import cn.hanyi.ctm.constant.warning.IndicatorWarningFrequency;
import cn.hanyi.ctm.dto.journey.JourneyWarningDto;
import cn.hanyi.ctm.entity.journey.JourneyWarning;
import cn.hanyi.ctm.entity.journey.JourneyWarningBase;
import cn.hanyi.ctm.entity.journey.JourneyWarningPublish;
import cn.hanyi.ctm.repository.*;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.befun.core.exception.BadRequestException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@Slf4j
@Service
public class JourneyWarningService {

    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private JourneyWarningRepository journeyWarningRepository;
    @Autowired
    private JourneyWarningPublishRepository journeyWarningPublishRepository;
    @Autowired
    private JourneyComponentPublishRepository journeyComponentPublishRepository;
    @Autowired
    private JourneyPublishRepository journeyPublishRepository;
    @Autowired
    private JourneyMapRepository journeyMapRepository;

    public String getWarningDateRange(IndicatorWarningFrequency frequency, Integer range) {
        LocalDate[] rangeDate = getWarningDate(frequency, range);
        if (rangeDate == null || rangeDate.length != 2 || rangeDate[1] == null) {
            return "";
        } else if (rangeDate[0] == null) {
            return "全部";
        } else {
            // 同一天就使用相同日期
            return rangeDate[0].equals(rangeDate[1]) ? rangeDate[1].toString() : rangeDate[0] + "至" + rangeDate[1];
        }
    }

    public LocalDate[] getWarningDate(IndicatorWarningFrequency frequency, Integer range) {
        LocalDate start = null, end = null;

        if (frequency != null && range != null) {
            end = LocalDate.now();
            switch (frequency) {
                case DAY:
                    if (range > 0) {
                        start = end.minusDays(range); // 过去n天
                    }
                    break;
                case MONTH:
                    if (range > 0) {
                        start = end.minusMonths(range).with(TemporalAdjusters.firstDayOfMonth());
                    }
                    break;
                default:
                    return null;
            }
            end = end.minusDays(1);
        }
        return new LocalDate[]{start, end};
    }

    public List<JourneyWarningPublish> getPublishEnableWarnings(Long journeyMapId, JourneyComponentType relationType, int page, int size) {
        if (journeyMapId != null && journeyMapId > 0) {
            return journeyWarningPublishRepository.findByJourneyMapIdAndRelationTypeAndEnableWarning(journeyMapId, relationType, true, PageRequest.of(page, size));
        } else {
            return journeyWarningPublishRepository.findByRelationTypeAndEnableWarning(relationType, true, PageRequest.of(page, size));
        }
    }

    public Map<Long, JourneyWarningDto> groupByRelationId(JourneyComponentType relationType, Set<Long> relationIds, boolean publish) {
        if (CollectionUtils.isNotEmpty(relationIds)) {
            if (publish) {
                List<JourneyWarningPublish> warnings = journeyWarningPublishRepository.findByRelationTypeAndRelationIdIn(relationType, relationIds);
                if (CollectionUtils.isNotEmpty(warnings)) {
                    return warnings.stream().collect(Collectors.toMap(JourneyWarningBase::getRelationId, this::mapToDto, (o1, o2) -> o1));
                }
            } else {
                List<JourneyWarning> warnings = journeyWarningRepository.findByRelationTypeAndRelationIdIn(relationType, relationIds);
                if (CollectionUtils.isNotEmpty(warnings)) {
                    return warnings.stream().collect(Collectors.toMap(JourneyWarningBase::getRelationId, this::mapToDto, (o1, o2) -> o1));
                }
            }
        }
        return new HashMap<>();
    }

    public Map<Long, String> getEventRuleTitleByIds(Set<Long> ids) {
        Map<Long, String> result = new HashMap<>();
        List<EventRule> list = getEventRules(ids);
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        list.forEach(i -> result.put(i.getId(), i.getTitle()));
        return result;
    }

    private List<EventRule> getEventRules(Set<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return null;
        }
        String in = ids.stream().filter(Objects::nonNull).map(Objects::toString).collect(Collectors.joining(",", "(", ")"));
        if (StringUtils.isEmpty(in)) {
            return null;
        }
        String sql = "select id, org_id orgId, title from event_monitor_rules where id in " + in;
        return jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(EventRule.class));
    }

    @Getter
    @Setter
    public static class EventRule {
        private Long id;
        private Long orgId;
        private String title;
    }

    public void checkRuleOrg(Long ruleId, Long orgId) {
        List<EventRule> list = getEventRules(Set.of(ruleId));
        if (CollectionUtils.isNotEmpty(list) && orgId != null && orgId.equals(list.get(0).getOrgId())) {
            return;
        }
        throw new BadRequestException("预警规则不存在");
    }

    public JourneyWarningDto mapToDto(JourneyWarning entity) {
        JourneyWarningDto dto = new JourneyWarningDto();
        BeanUtils.copyProperties(entity, dto);
        dto.setDateRange(getWarningDateRange(entity.getWarningFrequency(), entity.getWarningRange()));
        return dto;
    }

    public JourneyWarningDto mapToDto(JourneyWarningPublish entity) {
        JourneyWarningDto dto = new JourneyWarningDto();
        BeanUtils.copyProperties(entity, dto);
        dto.setCurrentValue(getWarningValue(entity.getId()));
        dto.setDateRange(getWarningDateRange(entity.getWarningFrequency(), entity.getWarningRange()));
        return dto;
    }

    public JourneyWarningDto saveIndicatorWarning(Long journeyMapId, Long componentId, Long journeyId, Long indicatorId, JourneyWarningDto dto) {
        return saveWarning(getOrCreate(journeyMapId, componentId, journeyId, JourneyComponentType.experience_indicator, indicatorId), dto);
    }

    public JourneyWarningDto saveEventStatWarning(Long journeyMapId, Long componentId, Long journeyId, Long eventStatId, JourneyWarningDto dto) {
        return saveWarning(getOrCreate(journeyMapId, componentId, journeyId, JourneyComponentType.event_stat, eventStatId), dto);
    }

    private JourneyWarning get(JourneyComponentType relationType, Long relationId) {
        return journeyWarningRepository.findFirstByRelationTypeAndRelationId(relationType, relationId);
    }

    public JourneyWarningPublish getPublish(JourneyComponentType relationType, Long relationId) {
        return journeyWarningPublishRepository.findFirstByRelationTypeAndRelationId(relationType, relationId);
    }

    public JourneyWarningPublish getPublish(Long id) {
        return journeyWarningPublishRepository.findById(id).orElse(null);
    }

    private JourneyWarning getOrCreate(Long journeyMapId, Long componentId, Long journeyId, JourneyComponentType relationType, Long relationId) {
        JourneyWarning entity = get(relationType, relationId);
        if (entity == null) {
            entity = new JourneyWarning();
            entity.setJourneyMapId(journeyMapId);
            entity.setComponentId(componentId);
            entity.setJourneyId(journeyId);
            entity.setRelationType(relationType);
            entity.setRelationId(relationId);
        }
        return entity;
    }

    private JourneyWarningDto saveWarning(JourneyWarning warning, JourneyWarningDto dto) {
        BeanUtils.copyProperties(dto, warning);
        journeyWarningRepository.save(warning);
        return mapToDto(warning);
    }

    public void delete(JourneyComponentType relationType, Long relationId) {
        JourneyWarning entity = get(relationType, relationId);
        if (entity != null) {
            journeyWarningRepository.delete(entity);
        }
    }

    public void deletePublish(JourneyComponentType relationType, Set<Long> relationIds) {
        List<JourneyWarningPublish> deleteList = journeyWarningPublishRepository.findByRelationTypeAndRelationIdIn(relationType, relationIds);
        if (CollectionUtils.isNotEmpty(deleteList)) {
            journeyWarningPublishRepository.deleteAll(deleteList);
        }
    }

    public boolean isValid(JourneyWarningPublish entity) {
        return entity.getJourneyMapId() != null && entity.getJourneyMapId() > 0 && journeyMapRepository.existsById(entity.getJourneyMapId())
                && entity.getComponentId() != null && entity.getComponentId() > 0 && journeyComponentPublishRepository.existsById(entity.getComponentId())
                && entity.getJourneyId() != null && entity.getJourneyId() > 0 && journeyPublishRepository.existsById(entity.getJourneyId());
    }

    /**
     * 旅程发布之后，编辑态的旅程预警规则，也要复制到发布态
     */
    public void publish(JourneyComponentType relationType, Long relationId) {
        JourneyWarning source = get(relationType, relationId);
        JourneyWarningPublish target;
        if (source == null) {
            // delete
            target = getPublish(relationType, relationId);
            if (target != null) {
                journeyWarningPublishRepository.delete(target);
            }
            return;
        }
        target = journeyWarningPublishRepository.findById(source.getId()).orElse(null);
        if (target == null) {
            target = new JourneyWarningPublish();
        }
        BeanUtils.copyProperties(source, target);
        journeyWarningPublishRepository.save(target);
    }

    /**
     * 缓存计算出的预警值
     */
    public void cacheWarningValue(Long id, Double value) {
        if (value != null && id != null) {
            String key = warningValueKey();
            String hashKey = id.toString();
            hashOpt().put(key, hashKey, value.toString());
            stringRedisTemplate.expire(key, Duration.ofDays(1));
        }
    }

    /**
     * 清空缓存计算出的预警值
     */
    public void clearWarningValue() {
        String key = warningValueKey();
        stringRedisTemplate.delete(key);
    }

    /**
     * 从缓存中获取预警值
     */
    public Double getWarningValue(Long id) {
        if (id != null) {
            String key = warningValueKey();
            String hashKey = id.toString();
            String value = hashOpt().get(key, hashKey);
            if (value != null) {
                return Double.valueOf(value);
            }
        }
        return null;
    }

    /**
     * 缓存需要通知的预警
     */
    public void cacheWarningNotify(Long id, Double value, JourneyComponentType relationType) {
        if (value != null && id != null) {
            String key = warningNotifyKey(relationType);
            String v = id + "," + value;
            listOpt().rightPush(key, v);
            stringRedisTemplate.expire(key, Duration.ofDays(1));
        }
    }

    /**
     * 清空缓存需要通知的预警
     */
    public void clearWarningNotify(JourneyComponentType relationType) {
        String key = warningNotifyKey(relationType);
        stringRedisTemplate.delete(key);
    }


    /**
     * 遍历所有的满足预警条件的预警，然后发送通知
     */
    public void consumerWarningNotify(JourneyComponentType relationType, BiConsumer<JourneyWarningPublish, Double> notifyCallback) {
        consumerWarningNotify(relationType, i -> true, notifyCallback);
    }

    /**
     * 遍历所有的满足预警条件的预警，然后发送通知
     */
    public void consumerWarningNotify(JourneyComponentType relationType, Predicate<JourneyWarningPublish> filter, BiConsumer<JourneyWarningPublish, Double> notifyCallback) {
        String key = warningNotifyKey(relationType);
        int page = 0;
        int size = 100;
        boolean hasNext = true;
        while (hasNext) {
            int start = page * size;
            int end = start + size - 1;
            List<String> list = listOpt().range(key, start, end);
            if (CollectionUtils.isNotEmpty(list)) {
                Map<Long, Double> map = new HashMap<>();
                list.forEach(i -> {
                    String[] s = i.split(",");
                    if (s.length == 2 && NumberUtils.isDigits(s[0]) && NumberUtils.isParsable(s[1])) {
                        map.put(Long.parseLong(s[0]), Double.parseDouble(s[1]));
                    }
                });
                if (!map.isEmpty()) {
                    journeyWarningPublishRepository.findAllById(map.keySet()).forEach(j -> {
                        if (filter.test(j)) {
                            Double value = map.get(j.getId());
                            if (value != null) {
                                try {
                                    notifyCallback.accept(j, value);
                                } catch (Throwable e) {
                                    log.error("发送预警通知失败，id={}, value={} msg={}", j.getId(), value, e.getMessage());
                                }
                            }
                        }
                    });
                }
            } else {
                hasNext = false;
            }
            page++;
        }
    }

    private String warningValueKey() {
        return "journey-warning:value";
    }

    private String warningNotifyKey(JourneyComponentType relationType) {
        return String.format("journey-warning:notify:%s", relationType.name());
    }

    private ListOperations<String, String> listOpt() {
        return stringRedisTemplate.opsForList();
    }

    private HashOperations<String, String, String> hashOpt() {
        return stringRedisTemplate.opsForHash();
    }
}
