//package cn.hanyi.ctm.service;
//
//import cn.hanyi.ctm.constant.InteractionCollectorType;
//import cn.hanyi.ctm.constant.MomentType;
//import cn.hanyi.ctm.constant.connector.ConnectorType;
//import cn.hanyi.ctm.dto.SurveyLinkDto;
//import cn.hanyi.ctm.dto.TemplateInfoDto;
//import cn.hanyi.ctm.dto.customer.CustomerAddJourneyDto;
//import cn.hanyi.ctm.dto.customer.DisturbStatus;
//import cn.hanyi.ctm.dto.journey.DisturbMomentDto;
//import cn.hanyi.ctm.dto.journey.JourneyInteractionDto;
//import cn.hanyi.ctm.dto.journey.PushMomentDto;
//import cn.hanyi.ctm.entity.Connector;
//import cn.hanyi.ctm.entity.Customer;
//import cn.hanyi.ctm.entity.CustomerJourneyRecord;
//import cn.hanyi.ctm.entity.ThirdPartyCustomer;
//import cn.hanyi.ctm.entity.journey.ExperienceInteractionPublish;
//import cn.hanyi.ctm.repository.ConnectorRepository;
//import cn.hanyi.ctm.repository.ExperienceInteractionPublishRepository;
//import cn.hanyi.ctm.repository.SendManageRepository;
//import cn.hanyi.ctm.workertrigger.CtmTaskTrigger;
//import cn.hanyi.ctm.workertrigger.dto.CustomerBatchAddJourneyDto;
//import cn.hanyi.ctm.workertrigger.dto.CustomerSendApiDto;
//import cn.hanyi.ctm.workertrigger.dto.CustomerSendSmsDto;
//import cn.hanyi.ctm.workertrigger.dto.CustomerSendWechatDto;
//import cn.hanyi.survey.core.projection.SimpleSurvey;
//import cn.hanyi.survey.core.repository.SurveyRepository;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.befun.auth.constant.UserTaskType;
//import org.befun.auth.service.UserTaskService;
//import org.befun.auth.service.auth.config.WechatOpenConfig;
//import org.befun.core.exception.BadRequestException;
//import org.befun.core.rest.context.TenantContext;
//import org.befun.core.utils.JsonHelper;
//import org.befun.core.utils.RegHelper;
//import org.befun.extension.sms.ISmsAccountService;
//import org.befun.task.entity.TaskProgress;
//import org.befun.task.utils.TimeUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.time.Duration;
//import java.time.Instant;
//import java.util.*;
//import java.util.concurrent.atomic.AtomicReference;
//
//@Slf4j
//@Service
//public class CustomerJourneyPushService {
//
//    @Autowired
//    private CustomerService customerService;
//    @Autowired
//    private CustomerRecordService customerRecordService;
//    @Autowired
//    private CtmTaskTrigger ctmTaskTrigger;
//    @Autowired
//    private ExperienceInteractionPublishRepository experienceInteractionPublishRepository;
//    @Autowired
//    private ConnectorRepository connectorRepository;
//    @Autowired
//    private CustomerAnswersService customerAnswersService;
//    @Autowired
//    private CustomerJourneyRecordService customerJourneyRecordService;
//    @Autowired
//    private ISmsAccountService smsAccountService;
//    @Autowired
//    private UserTaskService userTaskService;
//    @Autowired
//    private CustomerMessageService customerMessageService;
//    @Autowired
//    private SurveyRepository surveyRepository;
//
//    @Autowired
//    private SendManageRepository sendManageRepository;
//
//    public boolean triggerJourney(
//            Long orgId,
//            Long userId,
//            Long sceneId,
//            Customer customer,
//            String externalUserId,
//            Long departmentId,
//            Long journeyRecordId,
//            Map<String, Object> params
//    ) {
//        if (customer == null) {
//            return false;
//        }
//        JourneyInteractionDto interactionDto = parseInteraction(sceneId);
//        // 判断勿扰模式
//        Boolean notDisturb = notDisturb(interactionDto.getDisturbMomentDto(), customer.getId(), externalUserId, sceneId);
//        log.info("客户：{} 勿扰模式: {}", customer.getId(), notDisturb);
//        if (notDisturb) {
//            return false;
//        }
//        // 开始构建发送任务
//        Duration delay = parseDelay(interactionDto.getMomentDto());
//        String timed = parseTimed(interactionDto.getMomentDto());
//        String clientId = UUID.randomUUID().toString();
//        // 如果客户存在，则查询客户旅程记录，并设置问卷id,clientId
//        CustomerJourneyRecord journeyRecord = customerJourneyRecordService.get(journeyRecordId);
//        Long taskProgressId = journeyRecord == null ? null : journeyRecord.getTaskProgressId();
//        if (journeyRecord != null) {
//            journeyRecord.setClientId(clientId);
//            journeyRecord.setSurveyId(interactionDto.getSurveyId());
//            customerJourneyRecordService.save(journeyRecord);
//        }
//        SurveyLinkDto surveyLink = customerMessageService.buildSurveyUrl(interactionDto.getSurveyId(), customer.getId(), departmentId, clientId, params, journeyRecordId, interactionDto.getExpireTime());
//        Map<String, Object> placeholderParams = customerMessageService.buildNativeParams(surveyLink, interactionDto.getSurveyName(), customer, params);
//
//        if (interactionDto.getApp() != null) {
//            Connector connector = connectorRepository.findByOrgIdAndType(orgId, ConnectorType.API).orElse(null);
//            if (connector != null) {
//                ctmTaskTrigger.customerSendApi(new CustomerSendApiDto(orgId, userId, taskProgressId, journeyRecordId, connector.getId(), surveyLink.getShortUrl(), sceneId, customer.getId(), externalUserId, departmentId, delay, timed));
//            }
//        }
//
//        if (interactionDto.getSms() != null && RegHelper.isMobile(customer.getMobile())) {
//            JourneyInteractionDto.InteractionTemplate smsTemplate = interactionDto.getSms();
//            TemplateInfoDto templateInfo = customerMessageService.getSmsTemplate(smsTemplate.getThirdpartyTemplateId(), (String) smsTemplate.getContent().get("content"));
//            String content = customerMessageService.buildSmsContent(templateInfo.getSmsContent(), placeholderParams);
//            CustomerSendSmsDto dto = CustomerSendSmsDto.fromJourney(orgId, userId, taskProgressId, journeyRecordId, null, content, customer.getMobile(), templateInfo.getSmsTemplateId(), templateInfo.getSmsTemplateName(), templateInfo.getSmsSignId(), templateInfo.getSmsRealSign(), delay, timed);
//            ctmTaskTrigger.customerSendSms(dto);
//        }
//
//        if (interactionDto.getWechat() != null) {
//            // 优先使用客户中心的微信用户信息发送，如果没有微信用户，并且有临时的openId，使用 临时的openId发送
//            Long wechatOpenConfigId = null;
//            String openId = null;
//            ThirdPartyCustomer thirdPartyCustomer = customerMessageService.getWechatCustomer(customer);
//            if (thirdPartyCustomer != null) {
//                wechatOpenConfigId = thirdPartyCustomer.getThirdpartyAuthId();
//                openId = thirdPartyCustomer.getOpenId();
//            }
//            if (wechatOpenConfigId == null && StringUtils.isNotEmpty(customer.getWechatParams().getOpenId())) {
//                WechatOpenConfig wechatOpenConfig = customerService.getWechatOpenConfig(customer.getWechatParams().getAppId());
//                if (wechatOpenConfig != null) {
//                    wechatOpenConfigId = wechatOpenConfig.getConfigId();
//                    openId = customer.getWechatParams().getOpenId();
//                }
//            }
//            if (wechatOpenConfigId != null && StringUtils.isNotEmpty(openId)) {
//                JourneyInteractionDto.InteractionTemplate wechatTemplate = interactionDto.getWechat();
//                Map<String, Object> templateContent = wechatTemplate.getContent();
//                TemplateInfoDto templateInfo = null;
//                if (wechatTemplate.getWechatTemplateId() != null) {
//                    templateInfo = customerMessageService.getWeChatTemplate(wechatTemplate.getWechatTemplateId(), wechatOpenConfigId, templateContent);
//                } else if (wechatTemplate.getThirdpartyTemplateId() != null) {
//                    templateInfo = customerMessageService.getWeChatTemplate(wechatTemplate.getThirdpartyTemplateId(), templateContent);
//                }
//                if (templateInfo != null) {
//                    String content = customerMessageService.buildWechatContent(templateInfo.getWeChatTemplateId(), openId, surveyLink.getOriginUrl(), templateInfo.getWeChatContent(), placeholderParams);
//                    ctmTaskTrigger.customerSendWechat(new CustomerSendWechatDto(orgId, userId, taskProgressId, journeyRecordId, templateInfo.getWechatConfigId(), templateInfo.getWeChatAppId(), templateInfo.getWeChatTemplateId(), openId, content, surveyLink.getOriginUrl(), delay, timed));
//                }
//            }
//        }
//        customerAnswersService.addByJourney(customer, interactionDto.getSurveyId(), interactionDto.getSurveyName(), clientId);
//        return true;
//    }
//
//
////    /**
////     * sendManage 兼容以前的场景推送
////     *
////     * @return
////     */
////
////    private ExperienceInteractionPublish mockInteractionByJourneyId(Long journeyId) {
////        List<SendManage> sendManageList = sendManageRepository.findByOrgIdAndTriggerTypeAndTriggerId(TenantContext.getCurrentTenant(), SendManageTriggerType.JOURNEY, journeyId);
////        if (CollectionUtils.isNotEmpty(sendManageList)) {
////            ExperienceInteractionPublish interaction = new ExperienceInteractionPublish();
////            SendManage sendManage = sendManageList.get(0);
////            interaction.setInteractionSids(sendManage.getSendSids());
////            interaction.setInteractionMoment(sendManage.getSendMoment());
////            interaction.setExpireMoment(sendManage.getExpireMoment());
////            interaction.setInteractionName(sendManage.getSendName());
////            interaction.setDisturbMoment(sendManage.getDisturbMoment());
////            interaction.setConfig(sendManage.getChannel().stream().map(x -> (PushChannelConfigDto) x).collect(Collectors.toList()));
////            return interaction;
////        }
////        return null;
////    }
//
//    private JourneyInteractionDto parseInteraction(Long journeyId) {
//        ExperienceInteractionPublish interaction = experienceInteractionPublishRepository.findByJourneyId(journeyId).orElse(null);
//        if (interaction == null || CollectionUtils.isEmpty(interaction.getInteractionSids())) {
////            interaction = mockInteractionByJourneyId(journeyId);
//            if (interaction == null) {
//                throw new BadRequestException("场景推送不存在");
//            }
//        }
//        List<Long> sidList = interaction.getInteractionSids();
//        int randomIndex = new Random().nextInt(sidList.size());
//        Long surveyId = sidList.get(randomIndex);
//        SimpleSurvey survey = surveyRepository.findSimpleById(surveyId);
//        if (survey == null) {
//            throw new BadRequestException("问卷不存在");
//        }
//        JourneyInteractionDto interactionDto = new JourneyInteractionDto();
//        interactionDto.setSurveyId(surveyId);
//        interactionDto.setSurveyName(survey.getTitle());
//        Optional.ofNullable(interaction.getInteractionMoment()).ifPresent(interactionDto::setMomentDto);
//        Optional.ofNullable(interaction.getExpireMoment()).ifPresent(m -> interactionDto.setExpireTime(m.calcExpireTime()));
//        interactionDto.setDisturbMomentDto(interaction.getDisturbMoment());
//        interactionDto.setInteractionName(interaction.getInteractionName());
//        if (interaction.getConfig() != null) {
//            interaction.getConfig().forEach(i -> {
//                if (i.getType() == InteractionCollectorType.WECHAT) {
//                    if (i.getWechatOpenTemplateId() != null) {
//                        interactionDto.setWechat(JourneyInteractionDto.InteractionTemplate.fromWechatTemplateId(i.getWechatOpenTemplateId(), i.getContent()));
//                    } else {
//                        interactionDto.setWechat(JourneyInteractionDto.InteractionTemplate.fromThirdpartyTemplateId(i.getThirdpartyTemplateId(), i.getContent()));
//                    }
//                } else if (i.getType() == InteractionCollectorType.APP) {
//                    interactionDto.setApp(new JourneyInteractionDto.InteractionTemplate());
//                } else if (i.getType() == InteractionCollectorType.SMS) {
//                    interactionDto.setSms(JourneyInteractionDto.InteractionTemplate.fromThirdpartyTemplateId(i.getThirdpartyTemplateId(), i.getContent()));
//                }
//            });
//        }
//        return interactionDto;
//    }
//
//
//    private Duration parseDelay(PushMomentDto momentDto) {
//        if (momentDto == null || momentDto.getMomentType() == MomentType.IMMEDIATELY || momentDto.getMomentType() == MomentType.TIMED) {
//            return null;
//        } else {
//            return TimeUtils.parseDuration(momentDto.getDuration());
//        }
//    }
//
//
//    private String parseTimed(PushMomentDto momentDto) {
//        if (momentDto == null || momentDto.getMomentType() == MomentType.IMMEDIATELY || momentDto.getMomentType() == MomentType.LATER) {
//            return null;
//        } else {
//            return String.format("%s/%s/%d/%d/0", momentDto.getTimedType().name(), String.join(",", momentDto.getWeeks()), momentDto.getHour(), momentDto.getMinute());
//        }
//    }
//
//    /**
//     * 需要通知的客户id
//     */
//    private Boolean notDisturb(DisturbMomentDto disturbMomentDto, Long customerId, String externalUserId, Long
//            sceneId) {
//        AtomicReference<Boolean> result = new AtomicReference<>(false);
//        try {
//            if (disturbMomentDto == null) {
//                result.set(false);
//            } else {
//                if (!disturbMomentDto.getEnable()) {
//                    result.set(false);
//                } else {
//
//                    List<CustomerJourneyRecord> records = customerRecordService.getCustomerJourneyRecords(customerId, sceneId);
//                    // 最新记录是本次写入的记录
//                    if (records.size() <= 1) {
//                        return false;
//                    }
//
//                    // 去除本次写入的记录
//                    records.sort(Comparator.comparing(CustomerJourneyRecord::getCreateTime).reversed());
//                    CustomerJourneyRecord latestRecord = records.remove(0);
//                    CustomerJourneyRecord lastRecord = records.get(0);
//
//                    switch (disturbMomentDto.getType()) {
//                        case COUNT:
//                            // 取余判断是否打扰
//                            if (records.size() % (disturbMomentDto.getValue() + 1) != 0) {
//                                log.info("不打扰客户id: {}", customerId);
//                                result.set(true);
//                                latestRecord.setDisturbStatus(DisturbStatus.NOTDISTURB);
//                                customerRecordService.saveCustomerJourneyRecord(latestRecord);
//                            }
//                            break;
//                        case TIME:
//                            // 计算时间戳与今天间隔天数
//                            long days = Duration.between(lastRecord.getCreateTime().toInstant(), Instant.now()).toDays();
//                            if (days <= disturbMomentDto.getValue()) {
//                                log.info("不打扰客户id: {}", customerId);
//                                result.set(true);
//                                latestRecord.setDisturbStatus(DisturbStatus.NOTDISTURB);
//                                customerRecordService.saveCustomerJourneyRecord(latestRecord);
//                            }
//                            break;
//                        default:
//                    }
//                }
//            }
//        } catch (Exception e) {
//            log.error("勿扰模式 sceneId:异常", e);
//        }
//        return result.get();
//    }
//
//
//    @Transactional
//    public boolean addCustomerJourney(CustomerAddJourneyDto dto) {
//        Long orgId = TenantContext.getCurrentTenant();
//        Long userId = TenantContext.getCurrentUserId();
//        Set<Long> customerIds = new HashSet<>();
//        customerService.consumerSelectCustomerIds(orgId, userId, dto, customerIds::addAll);
//        checkSmsCost(orgId, dto.getJourneyId(), customerIds.size());
//        TaskProgress taskProgress = userTaskService.createTask(orgId, userId, UserTaskType.batchAddJourney, customerIds.size(), dto);
//        ctmTaskTrigger.customerBatchAddJourney(new CustomerBatchAddJourneyDto(orgId, userId, taskProgress.getId(), JsonHelper.toJson(dto)));
//        return true;
//    }
//
//    @Transactional
//    public boolean addCustomerJourneyByOpenApi(Long orgId, Long userId, Long journeyId, Long customerId, Long departmentId, Map<String, Object> params) {
//        checkSmsCost(orgId, journeyId, 1);
//        CustomerAddJourneyDto dto = new CustomerAddJourneyDto();
//        dto.setDepartmentId(departmentId);
//        dto.setJourneyId(journeyId);
//        dto.setSelectType(0);
//        dto.setSelectCustomerIds(List.of(customerId));
//        dto.setUrlCustomParams(params);
//        TaskProgress taskProgress = userTaskService.createTask(orgId, userId, UserTaskType.openApiAddJourney, 1, dto);
//        ctmTaskTrigger.customerBatchAddJourney(new CustomerBatchAddJourneyDto(orgId, userId, taskProgress.getId(), JsonHelper.toJson(dto)));
//        return true;
//    }
//
//    @Transactional
//    public boolean addCustomerJourneyByTriggerYouzan(Long orgId, Long userId, Long journeyId, Long customerId, Long departmentId, Map<String, Object> params) {
//        checkSmsCost(orgId, journeyId, 1);
//        CustomerAddJourneyDto dto = new CustomerAddJourneyDto();
//        dto.setJourneyId(journeyId);
//        dto.setDepartmentId(departmentId);
//        dto.setSelectType(0);
//        dto.setSelectCustomerIds(List.of(customerId));
//        dto.setUrlCustomParams(params);
//        TaskProgress taskProgress = userTaskService.createTask(orgId, userId, UserTaskType.triggerYouzanAddJourney, 1, dto);
//        ctmTaskTrigger.customerBatchAddJourney(new CustomerBatchAddJourneyDto(orgId, userId, taskProgress.getId(), JsonHelper.toJson(dto)));
//        return true;
//    }
//
//    public void checkSmsCost(Long orgId, Long journeyId, int customerSize) {
//        TemplateInfoDto templateInfo = getSmsTemplateByJourney(journeyId);
//        if (templateInfo != null) {
//            customerMessageService.checkSmsCost(orgId, templateInfo, customerSize);
//        }
//    }
//
//    public TemplateInfoDto getSmsTemplateByJourney(Long journeyId) {
//        JourneyInteractionDto interactionDto = parseInteraction(journeyId);
//        if (interactionDto.getSms() != null) {
//            JourneyInteractionDto.InteractionTemplate smsTemplate = interactionDto.getSms();
//            if (smsTemplate.getThirdpartyTemplateId() != null) {
//                return customerMessageService.getSmsTemplate(smsTemplate.getThirdpartyTemplateId(), (String) smsTemplate.getContent().get("content"));
//            }
//        }
//        return null;
//    }
//
//}
