package cn.hanyi.ctm.service.journey.elements.indicator;

import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import cn.hanyi.ctm.dto.journey.JourneyWarningDto;
import cn.hanyi.ctm.entity.journey.ExperienceIndicator;
import cn.hanyi.ctm.entity.journey.ExperienceIndicatorPublish;
import cn.hanyi.ctm.entity.journey.ExperienceIndicatorPublishDto;
import cn.hanyi.ctm.repository.ExperienceIndicatorPublishRepository;
import cn.hanyi.ctm.service.JourneyWarningService;
import cn.hanyi.ctm.service.journey.IComponentElement;
import cn.hanyi.ctm.service.journey.IJourneyMapPublish;
import org.apache.commons.collections.CollectionUtils;
import org.befun.core.entity.BaseEntity;
import org.befun.core.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class JourneyIndicatorPublishService extends BaseService<ExperienceIndicatorPublish, ExperienceIndicatorPublishDto, ExperienceIndicatorPublishRepository>
        implements IComponentElement<ExperienceIndicatorPublish, ExperienceIndicatorPublishDto, ExperienceIndicatorPublishRepository>,
        IJourneyMapPublish<ExperienceIndicator, ExperienceIndicatorPublish, ExperienceIndicatorPublishRepository> {

    @Autowired
    private JourneyWarningService journeyWarningService;
    @Autowired
    private JourneyIndicatorService journeyIndicatorService;

    @Override
    public JourneyComponentType componentType() {
        return JourneyComponentType.experience_indicator;
    }

    @Override
    public boolean publishComponent() {
        return true;
    }

    @Override
    public void afterMapToDto(List<ExperienceIndicatorPublish> entity, List<ExperienceIndicatorPublishDto> dto) {
        super.afterMapToDto(entity, dto);
        // set warning
        Set<Long> ids = new HashSet<>();
        if (CollectionUtils.isNotEmpty(dto)) {
            dto.forEach(i -> Optional.ofNullable(i.getId()).ifPresent(ids::add));
        }
        Map<Long, JourneyWarningDto> warningMap = journeyWarningService.groupByRelationId(JourneyComponentType.experience_indicator, ids, true);
        if (CollectionUtils.isNotEmpty(dto)) {
            dto.forEach(i -> Optional.ofNullable(warningMap.get(i.getId())).ifPresent(i::setWarning));
        }
    }

    @Override
    public ExperienceIndicatorPublish newInstance() {
        return new ExperienceIndicatorPublish();
    }

    @Override
    public void afterCopyToPublish(ExperienceIndicatorPublish target) {
        journeyWarningService.publish(componentType(), target.getId());
    }

    @Override
    public void beforeDeletePublishList(List<ExperienceIndicatorPublish> deleteList) {
        Set<Long> ids = deleteList.stream().map(BaseEntity::getId).collect(Collectors.toSet());
        journeyWarningService.deletePublish(JourneyComponentType.experience_indicator, ids);
    }
}
