package cn.hanyi.ctm.service;

import cn.hanyi.ctm.constant.OrganizationOptionalLimitType;
import cn.hanyi.ctm.service.persona.CustomerPersonaService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.math3.util.Pair;
import org.befun.auth.constant.UserStatus;
import org.befun.auth.entity.Organization;
import org.befun.auth.service.OrganizationService;
import org.befun.auth.service.UserService;
import org.befun.core.rest.context.TenantContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import static org.befun.core.constant.EntityScopeStrategyType.ORGANIZATION;


/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OptionalLimitService {

    @Autowired
    private UserService userService;

    @Autowired
    private OrganizationService organizationService;

    @Autowired
    private JourneyMapService journeyMapService;

    @Autowired
    private CustomerPersonaService customerPersonaService;

    @Autowired
//    @Qualifier("surveyJdbcTemplate")
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;


    public Pair<Integer, Integer> optionalLimitCheck(OrganizationOptionalLimitType type) {
        switch (type) {
            case child_user_limit:
                return childUserLimitCheck();
            case customer_lifecycle_limit:
                return customerLifecycleLimitCheck();
            case surveys_limit:
                return surveysLimitCheck();
            case event_rules_limit:
                return eventRulesLimitCheck();
            case customer_person_limit:
                return customerPersonLimitCheck();
            case bi_dashboard_limit:
                return biDashboardLimitCheck();
            case responses_limit:
                return responsesLimitCheck();

        }
        return Pair.create(0, 0);
    }

    public Pair<Integer, Integer> customerPersonLimitCheck() {
        Organization org = organizationService.requireCurrent();
        Integer limit = organizationService.parseOrgOptionalLimit(org).getCustomerPersonLimit();
        int size = customerPersonaService.scopeQuery(ORGANIZATION, () -> (int) customerPersonaService.getRepository().count());
        size = Math.max(0, limit - size);
        return Pair.create(size, limit);
    }

    public Pair<Integer, Integer> biDashboardLimitCheck() {
        Organization org = organizationService.requireCurrent();
        Integer limit = organizationService.parseOrgOptionalLimit(org).getBiDashboardLimit();
        String sql = String.format("select count(1) from bi_dashboard where org_id = %d and is_folder = 0 and type = 0 ", org.getId());
        log.info("biDashboardLimitCheck：{}", sql);
        Integer exist = jdbcTemplate.queryForObject(sql, Integer.class);
        int num = Math.max(0, limit - (exist == null ? 0 : exist));
        return Pair.create(num, limit);
    }

    public Pair<Integer, Integer> eventRulesLimitCheck() {
        Organization org = organizationService.requireCurrent();
        Integer limit = organizationService.parseOrgOptionalLimit(org).getEventRulesLimit();
        String sql = String.format("select count(1) from event_monitor_rules where org_id = %d and deleted = %d ", org.getId(), 0);
        log.info("eventRulesLimitCheck：{}", sql);
        Integer exist = jdbcTemplate.queryForObject(sql, Integer.class);
        int num = Math.max(0, limit - (exist == null ? 0 : exist));
        return Pair.create(num, limit);
    }

    public Pair<Integer, Integer> childUserLimitCheck() {
        Organization org = organizationService.requireCurrent();
        Integer limit = organizationService.parseOrgOptionalLimit(org).getChildUserLimit();
        int size = userService.getRepository().countByOrgIdAndStatusIn(org.getId(), UserStatus.activeStatus());
        size = Math.max(0, limit - size);
        return Pair.create(size, limit);
    }

    public Pair<Integer, Integer> customerLifecycleLimitCheck() {
        Organization org = organizationService.requireCurrent();
        Integer limit = organizationService.parseOrgOptionalLimit(org).getCustomerLifecycleLimit();
        int size = journeyMapService.scopeQuery(ORGANIZATION, () -> (int) journeyMapService.getRepository().count());
        size = Math.max(0, limit - size);
        return Pair.create(size, limit);
    }

    public Pair<Integer, Integer> surveysLimitCheck() {
        Organization org = organizationService.requireCurrent();
        Integer limit = organizationService.parseOrgOptionalLimit(org).getSurveysLimit();
        String sql = String.format("select count(1) from survey where org_id = %d and deleted = %d ", org.getId(), 0);
        log.info("surveysLimitCheck：{}", sql);
        Integer exist = jdbcTemplate.queryForObject(sql, Integer.class);
        int num = Math.max(0, limit - (exist == null ? 0 : exist));
        cacheSurveyNumber(num);
        return Pair.create(num, limit);
    }

    private void cacheSurveyNumber(int number) {
        String key = String.format("limiter.user.%s.%s", TenantContext.getCurrentTenant(), TenantContext.getCurrentUserId());
        stringRedisTemplate.opsForValue().set(key, String.valueOf(number));
    }

    public Pair<Integer, Integer> responsesLimitCheck() {
        Organization org = organizationService.requireCurrent();
        Integer limit = organizationService.parseOrgOptionalLimit(org).getResponsesLimit();
        String sql = String.format("SELECT sum(response_finish_num) from survey where org_id=%s and YEAR(modify_time) = YEAR(CURDATE());", TenantContext.getCurrentTenant());
        log.info("responsesLimitCheck：{}", sql);
        Integer exist = jdbcTemplate.queryForObject(sql, Integer.class);
        int num = Math.max(0, limit - (exist == null ? 0 : exist));
        return Pair.create(num, limit);
    }
}
