package cn.hanyi.ctm.service.persona;

import cn.hanyi.ctm.constant.persona.PersonaComponentType;
import cn.hanyi.ctm.dto.persona.CustomerPersonaComponentAddDto;
import cn.hanyi.ctm.dto.persona.CustomerPersonaComponentUpdateDto;
import cn.hanyi.ctm.dto.persona.component.PersonaComponentProfileDto;
import cn.hanyi.ctm.entity.persona.CustomerPersona;
import cn.hanyi.ctm.entity.persona.CustomerPersonaComponent;
import cn.hanyi.ctm.entity.persona.CustomerPersonaComponentDto;
import cn.hanyi.ctm.properties.PersonaProperties;
import cn.hanyi.ctm.repository.CustomerPersonComponentRepository;
import cn.hanyi.ctm.service.persona.component.IPersonaComponent;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.EmbeddedFieldContext;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.CustomEmbeddedService;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Consumer;

import static cn.hanyi.ctm.constant.persona.PersonaComponentType.*;

@Service
public class CustomerPersonaComponentService extends CustomEmbeddedService<CustomerPersonaComponent, CustomerPersonaComponentDto, CustomerPersonComponentRepository>
        implements ApplicationRunner {

    @Autowired
    private CustomerPersonaService customerPersonaService;
    @Autowired
    private PersonaProperties personaProperties;
    @Lazy
    @Autowired
    private List<IPersonaComponent<?>> components;
    private final Map<PersonaComponentType, IPersonaComponent<?>> componentMap = new HashMap<>();

    @Override
    public void run(ApplicationArguments args) {
        components.forEach(i -> componentMap.put(i.type(), i));
    }

    private void applyComponent(PersonaComponentType type, Consumer<IPersonaComponent<?>> apply) {
        IPersonaComponent<?> component = componentMap.get(type);
        if (component != null) {
            apply.accept(component);
        } else {
            throw new BadRequestException("不支持的组件类型");
        }
    }

    public List<CustomerPersonaComponentDto> findAll(long personaId) {
        return findAllNoPage((root, query, build) -> build.equal(root.get("personaId"), personaId), Sort.unsorted());
    }

    @Override
    public void afterMapToDto(List<CustomerPersonaComponent> entity, List<CustomerPersonaComponentDto> dto) {
        if (CollectionUtils.isEmpty(dto)) {
            return;
        }
        dto.forEach(d -> {
            Optional.ofNullable(d.getEntity()).ifPresent(e -> {
                d.setLayoutData(JsonHelper.toMap2(e.getLayout(), String.class, Integer.class));
                applyComponent(e.getType(), c -> c.parseContent(e, d));
            });
        });
    }

    @Override
    @Transactional
    public <SD extends BaseEntityDTO<CustomerPersonaComponent>> CustomerPersonaComponentDto createEmbeddedMany2(Long rootId, EmbeddedFieldContext fieldContext, SD data) {
        CustomerPersonaComponentAddDto dto = (CustomerPersonaComponentAddDto) data;
        CustomerPersonaComponent entity = new CustomerPersonaComponent();
        entity.setPersonaId(rootId);
        entity.setName(dto.getName());
        entity.setType(dto.getType());
        entity.setLayout(JsonHelper.toJson(dto.getLayoutData()));
        applyComponent(entity.getType(), c -> c.formatContent(entity, dto));
        repository.save(entity);
        return afterCustomCreateOrUpdateOne(entity);
    }

    @Override
    @Transactional
    public <SD extends BaseEntityDTO<CustomerPersonaComponent>> CustomerPersonaComponentDto updateOneEmbeddedMany2(Long rootId, String rootFieldNameInEmbedded, Long embeddedId, SD change) {
        CustomerPersonaComponentUpdateDto dto = (CustomerPersonaComponentUpdateDto) change;
        CustomerPersonaComponent entity = require(embeddedId);
        entity.setName(dto.getName());
        entity.setLayout(JsonHelper.toJson(dto.getLayoutData()));
        applyComponent(entity.getType(), c -> c.formatContent(entity, dto));
        repository.save(entity);
        updatePersona(entity, dto);
        return afterCustomCreateOrUpdateOne(entity);
    }

    /**
     * 修改画像的修改时间和修改人
     */
    private void updatePersona(CustomerPersonaComponent entity, CustomerPersonaComponentUpdateDto dto) {
        PersonaComponentProfileDto profileDto = null;
        if (entity.getType() == profile) {
            profileDto = dto.getProfile();
        }
        customerPersonaService.updateByComponentChange(entity.getPersonaId(), TenantContext.requireCurrentUserId(), profileDto);
    }

    public void createDefaultComponents(CustomerPersona persona) {
        List<CustomerPersonaComponent> components = new ArrayList<>();
        Optional.ofNullable(personaProperties.getItems()).ifPresent(items -> items.forEach(item -> {
            components.add(buildDefault(item.getName(), item.getType(), item.getContent(), persona, item.getLayout()));
        }));
        if (!components.isEmpty()) {
            repository.saveAll(components);
        }
    }

    private CustomerPersonaComponent buildDefault(String name, PersonaComponentType type, String content, CustomerPersona persona, PersonaProperties.Layout layout) {
        CustomerPersonaComponent entity = new CustomerPersonaComponent();
        entity.setOrgId(persona.getOrgId());
        entity.setPersonaId(persona.getId());
        entity.setName(name);
        entity.setType(type);
        entity.setLayout(JsonHelper.toJson(layout));
        if (StringUtils.isNotEmpty(content)) {
            entity.setContent(content);
        } else {
            componentMap.get(type).setDefaultContent(persona, entity);
        }
        return entity;
    }


}
