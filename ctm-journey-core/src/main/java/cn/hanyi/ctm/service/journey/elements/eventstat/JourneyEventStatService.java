package cn.hanyi.ctm.service.journey.elements.eventstat;

import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import cn.hanyi.ctm.dto.journey.EventStatDataResponseDto;
import cn.hanyi.ctm.dto.journey.EventStatSaveDto;
import cn.hanyi.ctm.dto.journey.JourneyWarningDto;
import cn.hanyi.ctm.entity.journey.*;
import cn.hanyi.ctm.repository.ElementEventStatRepository;
import cn.hanyi.ctm.service.data.EventStatDataService;
import cn.hanyi.ctm.service.JourneyWarningService;
import cn.hanyi.ctm.service.journey.IComponentJourneyElement;
import cn.hanyi.ctm.service.journey.elements.scene.JourneySceneService;
import org.apache.commons.collections.CollectionUtils;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.DeepEmbeddedFieldContext;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.CustomDeepEmbeddedService;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class JourneyEventStatService extends CustomDeepEmbeddedService<ElementEventStat, ElementEventStatDto, ElementEventStatRepository>
        implements IComponentJourneyElement<ElementEventStat, ElementEventStatDto, ElementEventStatRepository> {

    @Autowired
    private JourneyWarningService journeyWarningService;
    @Autowired
    private JourneySceneService journeySceneService;
    @Autowired
    private EventStatDataService eventStatService;

    @Override
    public JourneyComponentType componentType() {
        return JourneyComponentType.event_stat;
    }

    @Override
    public void afterMapToDto(List<ElementEventStat> entity, List<ElementEventStatDto> dto) {
        super.afterMapToDto(entity, dto);
        Set<Long> ids = new HashSet<>();
        Set<Long> eventRuleIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(dto)) {
            dto.forEach(i -> {
                Optional.ofNullable(i.getId()).ifPresent(ids::add);
                Optional.ofNullable(i.getEventRuleId()).ifPresent(eventRuleIds::add);
            });
        }
        Map<Long, JourneyWarningDto> warningMap = journeyWarningService.groupByRelationId(JourneyComponentType.event_stat, ids, false);
        Map<Long, String> eventRuleMap = journeyWarningService.getEventRuleTitleByIds(eventRuleIds);
        if (CollectionUtils.isNotEmpty(dto)) {
            dto.forEach(i -> {
                Optional.ofNullable(warningMap.get(i.getId())).ifPresent(i::setWarning);
                Optional.ofNullable(eventRuleMap.get(i.getEventRuleId())).ifPresent(i::setEventRuleName);
            });
        }
    }

    @Override
    public <SD extends BaseEntityDTO<ElementEventStat>> ElementEventStatDto createDeepEmbeddedMany2(Long journeyMapId, Long componentId, DeepEmbeddedFieldContext deepEmbeddedFieldContext, SD data) {
        Long orgId = TenantContext.getCurrentTenant();
        EventStatSaveDto dto = (EventStatSaveDto) data;
        ElementEventStat exists = repository.findFirstByJourneyMapIdAndComponentIdAndJourneyId(journeyMapId, componentId, dto.getJourneyId());
        if (exists != null) {
            throw new BadRequestException("场景已创建事件统计");
        }
        journeySceneService.checkIsCurrentOrg(dto.getJourneyId());
        journeyWarningService.checkRuleOrg(dto.getEventRuleId(), TenantContext.getCurrentTenant());
        ElementEventStat entity = new ElementEventStat(orgId, journeyMapId, componentId, dto.getJourneyId(), dto.getEventRuleId(), dto.getStatType());
        repository.save(entity);
        ElementEventStatDto r = mapToDto(entity);
        if (r != null) {
            afterMapToDto(r.getEntity(), r);
            r.setCalEventStat(eventStatService.dataEdit(journeyMapId, componentId, entity));
        }
        return r;
    }

    @Override
    public <SD extends BaseEntityDTO<ElementEventStat>> ElementEventStatDto updateOneDeepEmbeddedMany2(Long journeyMapId, String embeddedMapperBy, Long componentId, String deepEmbeddedMapperBy, Long eventStatId, SD change) {
        Long orgId = TenantContext.getCurrentTenant();
        EventStatSaveDto dto = (EventStatSaveDto) change;
        ElementEventStat entity = require(eventStatId);
        journeyWarningService.checkRuleOrg(dto.getEventRuleId(), orgId);
        entity.setEventRuleId(dto.getEventRuleId());
        entity.setStatType(dto.getStatType());
        repository.save(entity);
        ElementEventStatDto r = mapToDto(entity);
        if (r != null) {
            afterMapToDto(r.getEntity(), r);
            r.setCalEventStat(eventStatService.dataEdit(journeyMapId, componentId, entity));
        }
        return r;
    }

    @Override
    public Boolean deleteOneDeepEmbeddedMany(Long entityId, String embeddedMapperBy, Long embeddedId, String deepEmbeddedMapperBy, Long deepId) {
        Boolean r = super.deleteOneDeepEmbeddedMany(entityId, embeddedMapperBy, embeddedId, deepEmbeddedMapperBy, deepId);
        journeyWarningService.delete(JourneyComponentType.event_stat, deepId);
        return r;
    }

    @Override
    public void fillDataByComponent(JourneyComponentDto componentDto) {
        if (componentDto == null || componentDto.getType() != JourneyComponentType.event_stat) {
            return;
        }
        List<Object> elements = componentDto.getElements();
        if (CollectionUtils.isNotEmpty(elements)) {
            List<EventStatDataResponseDto> dataList = eventStatService.dataList(componentDto.getJourneyMapId(), componentDto.getId(), false, null, null, null);
            Map<Long, EventStatDataResponseDto> dataMap = dataList.stream().collect(Collectors.toMap(EventStatDataResponseDto::getId, Function.identity(), (m1, m2) -> m1));
            elements.forEach(j -> {
                ElementEventStatDto dto = (ElementEventStatDto) j;
                dto.setCalEventStat(dataMap.get(dto.getId()));
            });
        }
    }
}
