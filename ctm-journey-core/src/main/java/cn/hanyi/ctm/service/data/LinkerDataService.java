package cn.hanyi.ctm.service.data;

import cn.hanyi.ctm.dto.journey.ElementLinkerConfigDto;
import cn.hanyi.ctm.dto.journey.LinkerDataResponseDto;
import cn.hanyi.ctm.entity.journey.ElementLinkerBase;
import cn.hanyi.ctm.service.JourneyMapService;
import cn.hanyi.ctm.service.journey.elements.linker.JourneyLinkerPublishService;
import cn.hanyi.ctm.service.journey.elements.linker.JourneyLinkerService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.befun.auth.constant.ThirdPartyAuthType;
import org.befun.auth.dto.linker.LinkerBaiduTongjiParamDto;
import org.befun.auth.dto.linker.LinkerParamDto;
import org.befun.auth.dto.linker.LinkerResponseDataDto;
import org.befun.auth.dto.linker.LinkerShenCeParamDto;
import org.befun.auth.service.linker.ILinkerData;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Slf4j
@Service
public class LinkerDataService {

    @Autowired
    private JourneyMapService journeyMapService;
    @Autowired
    private JourneyLinkerService journeyLinkerService;
    @Autowired
    private JourneyLinkerPublishService journeyLinkerPublishService;
    @Autowired
    private List<ILinkerData<?>> linkerDatas;

    public List<LinkerDataResponseDto> dataList(Long journeyMapId, Long componentId, boolean publish, String startDate, String endDate) {
        List<? extends ElementLinkerBase> list;
        if (publish) {
            list = journeyLinkerPublishService.findAllEntity(componentId);
        } else {
            list = journeyLinkerService.findAllEntity(componentId);
        }
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().parallel().map(i -> data(journeyMapId, componentId, i, startDate, endDate)).collect(Collectors.toList());
        }
        return List.of();
    }

    public LinkerDataResponseDto editData(Long journeyMapId, Long componentId, ElementLinkerBase entity) {
        try {
            return data(journeyMapId, componentId, entity, null, null);
        } catch (Exception e) {
            LinkerDataResponseDto result = new LinkerDataResponseDto();
            result.setId(entity.getId());
            result.setLinkerType(entity.getLinkerType());
            return result;
        }
    }

    public LinkerDataResponseDto data(Long journeyMapId, Long componentId, Long linkerId, boolean publish, String startDate, String endDate) {
        ElementLinkerBase entity = getEntity(linkerId, publish);
        return data(journeyMapId, componentId, entity, startDate, endDate);
    }

    public LinkerDataResponseDto data(Long journeyMapId, Long componentId, ElementLinkerBase entity, String startDate, String endDate) {
        LinkerDataResponseDto result = null;
        if (entity != null) {
            result = data(journeyMapId, componentId, startDate, endDate, entity.getLinkerType(), () -> JsonHelper.toObject(entity.getLinkerConfig(), ElementLinkerConfigDto.class));
            result.setId(entity.getId());
        }
        if (result == null) {
            result = new LinkerDataResponseDto();
        }
        return result;
    }

    public LinkerDataResponseDto dataBaiduTongji(Long journeyMapId, Long componentId, String method, String metrics, String gran, String visitor) {
        return data(journeyMapId, componentId, null, null,
                ThirdPartyAuthType.BAIDU_TONGJI.getCamelName(),
                () -> new ElementLinkerConfigDto(new ElementLinkerConfigDto.BaiduTongjiConfig(method, metrics, gran, visitor)));
    }

    public LinkerDataResponseDto dataShenCe(Long journeyMapId, Long componentId, String reportPath, String requestBody) {
        return data(journeyMapId, componentId, null, null,
                ThirdPartyAuthType.SHEN_CE.getCamelName(),
                () -> new ElementLinkerConfigDto(new ElementLinkerConfigDto.ShenCeConfig(reportPath, requestBody)));
    }


    private LinkerDataResponseDto data(Long journeyMapId, Long componentId, String startDate, String endDate, String linkerType, Supplier<ElementLinkerConfigDto> getLinkerConfig) {
        Pair<LocalDate, LocalDate> currentDate = journeyMapService.parseJourneyMapDateRange(journeyMapId, componentId, startDate, endDate);
        LocalDate s = currentDate.getLeft();
        LocalDate e = currentDate.getRight();
        LinkerDataResponseDto result = new LinkerDataResponseDto();
        if (StringUtils.isNotEmpty(linkerType)) {
            result.setLinkerType(linkerType);
            try {
                LinkerResponseDataDto data = Optional.ofNullable(getLinker(linkerType)).map(linker -> {
                    ElementLinkerConfigDto config = getLinkerConfig.get();
                    return linker.getData("cem", buildLinkerParam(s, e, linkerType, config));
                }).orElse(null);
                if (data != null) {
                    result.setCurrentValue(data.getCurrentValue());
                    result.setRecentLabel(data.getRecentLabel());
                    result.setRecentValue(data.getRecentValue());
                }
            } catch (Throwable error) {
                log.error("连接器数据查询失败：{}", linkerType, error);
            }
        }
        return result;
    }

    @SuppressWarnings("unchecked")
    private <P extends LinkerParamDto> ILinkerData<P> getLinker(String linkerType) {
        return (ILinkerData<P>) linkerDatas.stream().filter(i -> i.supportLinker(linkerType)).findFirst().orElse(null);
    }

    @SuppressWarnings("unchecked")
    private <P extends LinkerParamDto> P buildLinkerParam(LocalDate startDate, LocalDate endDate, String linkerType, ElementLinkerConfigDto linkerConfig) {
        if (linkerConfig == null) {
            return null;
        }
        if (ThirdPartyAuthType.BAIDU_TONGJI.getCamelName().equals(linkerType)) {
            ElementLinkerConfigDto.BaiduTongjiConfig config = linkerConfig.getBaiduTongji();
            if (config != null) {
                return (P) new LinkerBaiduTongjiParamDto(startDate, endDate, config.getMethod(), config.getMetrics(), config.getGran(), config.getVisitor());
            }
        } else if (ThirdPartyAuthType.SHEN_CE.getCamelName().equals(linkerType)) {
            ElementLinkerConfigDto.ShenCeConfig config = linkerConfig.getShenCe();
            if (config != null) {
                return (P) new LinkerShenCeParamDto(startDate, endDate, config.getReportPath(), config.getRequestBody());
            }
        }
        return null;
    }

    public ElementLinkerBase getEntity(Long linkerId, boolean publish) {
        if (publish) {
            return journeyLinkerPublishService.get(linkerId);
        } else {
            return journeyLinkerService.get(linkerId);
        }
    }
}
