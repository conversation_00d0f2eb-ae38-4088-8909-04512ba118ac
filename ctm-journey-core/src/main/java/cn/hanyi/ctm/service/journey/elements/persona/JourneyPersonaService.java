package cn.hanyi.ctm.service.journey.elements.persona;

import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import cn.hanyi.ctm.dto.journey.ElementPersonaSaveDto;
import cn.hanyi.ctm.entity.journey.ElementPersona;
import cn.hanyi.ctm.entity.journey.ElementPersonaDto;
import cn.hanyi.ctm.repository.ElementPersonaRepository;
import cn.hanyi.ctm.service.journey.IComponentJourneyElement;
import cn.hanyi.ctm.service.journey.elements.scene.JourneySceneService;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.DeepEmbeddedFieldContext;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.CustomDeepEmbeddedService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class JourneyPersonaService extends CustomDeepEmbeddedService<ElementPersona, ElementPersonaDto, ElementPersonaRepository>
        implements IComponentJourneyElement<ElementPersona, ElementPersonaDto, ElementPersonaRepository> {

    @Autowired
    private JourneySceneService journeySceneService;

    @Override
    public JourneyComponentType componentType() {
        return JourneyComponentType.persona_content;
    }

    @Override
    public <SD extends BaseEntityDTO<ElementPersona>> ElementPersonaDto createDeepEmbeddedMany2(Long journeyMapId, Long componentId, DeepEmbeddedFieldContext deepEmbeddedFieldContext, SD data) {
        Long orgId = TenantContext.getCurrentTenant();
        ElementPersonaSaveDto dto = (ElementPersonaSaveDto) data;
        ElementPersona exists = repository.findFirstByJourneyMapIdAndComponentIdAndJourneyId(journeyMapId, componentId, dto.getJourneyId());
        if (exists != null) {
            throw new BadRequestException("场景已创建画像元素");
        }
        journeySceneService.checkIsCurrentOrg(dto.getJourneyId());
        ElementPersona entity = new ElementPersona();
        entity.setOrgId(orgId);
        entity.setJourneyMapId(journeyMapId);
        entity.setComponentId(componentId);
        entity.setJourneyId(dto.getJourneyId());
        entity.setContent(dto.getContent());
        repository.save(entity);
        ElementPersonaDto r = mapToDto(entity);
        if (r != null) {
            afterMapToDto(r.getEntity(), r);
        }
        return r;
    }

    @Override
    public <SD extends BaseEntityDTO<ElementPersona>> ElementPersonaDto updateOneDeepEmbeddedMany2(Long journeyMapId, String rootFieldNameInEmbedded, Long componentId, String deepEmbeddedMapperBy, Long personaId, SD change) {
        ElementPersonaSaveDto dto = (ElementPersonaSaveDto) change;
        ElementPersona entity = require(personaId);
        entity.setContent(dto.getContent());
        repository.save(entity);
        ElementPersonaDto r = mapToDto(entity);
        if (r != null) {
            afterMapToDto(r.getEntity(), r);
        }
        return r;
    }
}
