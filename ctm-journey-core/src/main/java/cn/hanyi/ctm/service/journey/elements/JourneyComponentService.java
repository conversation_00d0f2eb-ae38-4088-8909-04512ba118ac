package cn.hanyi.ctm.service.journey.elements;

import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import cn.hanyi.ctm.dto.journey.JourneyComponentCreateDto;
import cn.hanyi.ctm.dto.journey.JourneyComponentCreatePersonaDto;
import cn.hanyi.ctm.dto.journey.JourneyComponentReplacePersonaDto;
import cn.hanyi.ctm.dto.journey.JourneyOrderDto;
import cn.hanyi.ctm.entity.journey.*;
import cn.hanyi.ctm.properties.InitJourneyMapProperties;
import cn.hanyi.ctm.repository.CtmSurveyRepository;
import cn.hanyi.ctm.repository.JourneyComponentRepository;
import cn.hanyi.ctm.service.JourneyMapService;
import cn.hanyi.ctm.service.journey.ComponentElementHelper;
import cn.hanyi.ctm.service.journey.IComponent;
import cn.hanyi.ctm.service.journey.IComponentElement;
import cn.hanyi.ctm.service.journey.elements.curve.JourneyCurveService;
import cn.hanyi.ctm.service.journey.elements.scene.JourneySceneService;
import cn.hanyi.ctm.service.journey.elements.textbox.JourneyTextBoxService;
import cn.hanyi.ctm.service.persona.CustomerPersonaService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.CustomEmbeddedService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.hanyi.ctm.constant.journeymap.JourneyComponentType.*;

@Service
public class JourneyComponentService extends CustomEmbeddedService<JourneyComponent, JourneyComponentDto, JourneyComponentRepository>
        implements IComponent<JourneyComponent, JourneyComponentDto, JourneyComponentRepository> {
    @Lazy
    @Autowired
    private JourneyMapService journeyMapService;
    @Autowired
    private JourneySceneService journeySceneService;
    @Autowired
    private JourneyTextBoxService journeyTextBoxService;
    @Autowired
    private JourneyCurveService journeyCurveService;
    @Autowired
    private CtmSurveyRepository ctmSurveyRepository;
    @Autowired
    private CustomerPersonaService customerPersonaService;
    @Autowired
    private InitJourneyMapProperties initJourneyMapProperties;

    @Override
    public void afterMapToDto(List<JourneyComponent> entity, List<JourneyComponentDto> dto) {
        IComponent.super.afterMapToDto(entity, dto);
    }

    @Override
    public JourneyComponentDto findOneEmbeddedMany(Long rootId, String rootFieldNameInEmbedded, Long embeddedId) {
        JourneyComponent component = require(embeddedId);
        if (component.getType() != null && component.getType().hasChildren) {
            List<JourneyComponent> entity = new ArrayList<>();
            entity.add(component);
            List<JourneyComponent> children = repository.findByJourneyMapIdAndParentId(rootId, embeddedId);
            if (CollectionUtils.isNotEmpty(children)) {
                entity.addAll(children);
            }
            List<JourneyComponentDto> dto = mapToDto(entity);
            afterMapToDto(entity, dto);
            if (CollectionUtils.isNotEmpty(dto)) {
                return dto.get(0);
            } else {
                throw new BadRequestException();
            }
        } else {
            JourneyComponentDto dto = mapToDto(component);
            afterMapToDto(List.of(component), List.of(dto));
            return dto;
        }
    }

    public void createInitJourneyComponent(Long orgId, Long journeyMapId) {
        if (initJourneyMapProperties.getComponents().isEmpty()
                || initJourneyMapProperties.getComponents().stream().noneMatch(i -> i.getType() == journey)
                || initJourneyMapProperties.getJourneys().isEmpty()) {
            return;
        }
        List<JourneyComponent> components = initJourneyMapProperties.getComponents().stream()
                .map(i -> new JourneyComponent(orgId, journeyMapId, i.getOrder(), i.getTitle(), i.getType()))
                .collect(Collectors.toList());
        repository.saveAll(components);
        JourneyComponent journeyComponent = components.stream().filter(i -> i.getType() == journey).findFirst().orElse(null);
        if (journeyComponent == null) {
            return;
        }
        Map<Journey, Journey> journeyFirstChildMap = new HashMap<>();
        List<Journey> allJourneys = new ArrayList<>();
        List<Journey> parentJourneys = initJourneyMapProperties.getJourneys().stream()
                .map(i -> {
                    Journey p = new Journey(orgId, journeyComponent.getId(), i.getTitle(), i.getColor(), i.getOrder());
                    allJourneys.add(p);
                    if (initJourneyMapProperties.isHasJourneyFirstChild()) {
                        Journey c = new Journey(orgId, journeyComponent.getId(), "", i.getColor(), 0);
                        allJourneys.add(c);
                        journeyFirstChildMap.put(p, c);
                    }
                    return p;
                })
                .collect(Collectors.toList());
        journeySceneService.saveAll(parentJourneys);
        if (!journeyFirstChildMap.isEmpty()) {
            journeyFirstChildMap.forEach((p, c) -> {
                c.setParentId(p.getId());
                journeySceneService.save(c);
            });
        }

        initJourneyMapProperties.getComponents().forEach(c -> {
            if (c.getType() == curve) {
                JourneyComponent curveComponent = components.stream().filter(i -> i.getType() == curve).findFirst().orElse(null);
                if (curveComponent != null) {
                    List<ElementCurve> elementCurves = allJourneys.stream()
                            .map(j -> new ElementCurve(curveComponent.getId(), orgId, j.getId()))
                            .collect(Collectors.toList());
                    journeyCurveService.saveAll(elementCurves);
                }
            } else if (c.getType() == textbox) {
                JourneyComponent textComponent = components.stream().filter(i -> i.getOrder() == c.getOrder()).findFirst().orElse(null);
                if (textComponent != null && c.getSubType() != null) {
                    String defaultText = StringUtils.isNotEmpty(c.getText()) ? c.getText() : "";
                    List<String> text = parentJourneys.stream().map(j -> defaultText).collect(Collectors.toList());
                    ElementTextBox textBox = new ElementTextBox(textComponent.getId(), orgId, c.getSubType(), text);
                    journeyTextBoxService.save(textBox);
                }
            }
        });
    }

    /**
     * 添加组件,(文本框、曲线)初始化元素
     */
    @SuppressWarnings("unchecked")
    @Transactional(rollbackFor = Exception.class)
    public JourneyComponentDto createJourneyComponent(Long journeyMapId, JourneyComponentCreateDto dto) {
        Long orgId = TenantContext.getCurrentTenant();
        journeyMapService.checkAdmin(journeyMapId);
        JourneyComponent component = new JourneyComponent(orgId, dto.getParentId(), journeyMapId, dto.getOrder(), dto.getTitle(), dto.getType(), dto.getRelationId());
        repository.save(component);
        JourneyComponentDto r = mapToDto(component);
        List<Object> elements = (List<Object>) Optional.ofNullable(getComponentElement(r.getType()))
                .map(i -> i.addByComponent(orgId, journeyMapId, component.getId(), dto))
                .orElse(new ArrayList<>());
        r.setElements(elements);
        return r;
    }

    /**
     * 批量添加画像子组件
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean createJourneyComponentPersona(Long journeyMapId, JourneyComponentCreatePersonaDto dto) {
        Long orgId = TenantContext.getCurrentTenant();
        journeyMapService.checkAdmin(journeyMapId);
        require(dto.getParentId());
        checkExistRelation(journeyMapId, dto.getParentId(), dto.getPersonaIds(), persona_content, existPersonaIds -> String.format("已成功添加画像%s，请勿重复操作", String.join("，", customerPersonaService.getTitleByIds(existPersonaIds))));
        int nextOrder = repository.findFirstByJourneyMapIdAndParentIdAndTypeOrderByOrderDesc(journeyMapId, dto.getParentId(), persona_content)
                .map(i -> i.getOrder() == null ? 1 : i.getOrder()).orElse(1);
        List<JourneyComponent> add = new ArrayList<>();
        for (Long personaId : dto.getPersonaIds()) {
            nextOrder++;
            JourneyComponent component = new JourneyComponent(orgId, dto.getParentId(), journeyMapId, nextOrder, dto.getTitle(), persona_content, personaId);
            add.add(component);
        }
        repository.saveAll(add);
        return true;
    }

    private void checkExistRelation(Long journeyMapId, Long parentId, List<Long> relationIds, JourneyComponentType type, Function<List<Long>, String> getTip) {
        List<JourneyComponent> exist = repository.findByJourneyMapIdAndParentIdAndTypeAndRelationIdIn(journeyMapId, parentId, type, relationIds);
        if (CollectionUtils.isNotEmpty(exist)) {
            List<Long> existRelationIds = exist.stream().map(JourneyComponentBase::getRelationId).collect(Collectors.toList());
            throw new BadRequestException(getTip.apply(existRelationIds));
        }
    }

    /**
     * 替换画像子组件
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean replaceJourneyComponentPersona(Long journeyMapId, Long componentId, JourneyComponentReplacePersonaDto dto) {
        journeyMapService.checkAdmin(journeyMapId);
        JourneyComponent component = require(componentId);
        checkExistRelation(journeyMapId, component.getParentId(), List.of(dto.getPersonaId()), persona_content, existPersonaIds -> String.format("已成功添加画像%s，请勿重复操作", String.join("，", customerPersonaService.getTitleByIds(existPersonaIds))));
        component.setTitle(dto.getTitle());
        component.setRelationId(dto.getPersonaId());
        repository.save(component);
        return true;
    }

    /**
     * 查询指定类型的组件列表
     */
    public List<JourneyComponent> getComponentsByType(Long journeyMapId, JourneyComponentType componentType) {
        return getComponentsByTypes(journeyMapId, List.of(componentType));
    }

    /**
     * 查询指定类型的组件列表
     */
    public List<JourneyComponent> getComponentsByTypes(Long journeyMapId, List<JourneyComponentType> componentTypes) {
        if (journeyMapId == null || journeyMapId <= 0 || componentTypes == null || componentTypes.isEmpty()) {
            return null;
        }
        return repository.findByJourneyMapIdAndTypeIn(journeyMapId, componentTypes);
    }

    /**
     * 查询指定类型的组件列表
     */
    public List<JourneyComponentDto> getComponentsDtoByTypes(Long journeyMapId, List<JourneyComponentType> componentTypes) {
        List<JourneyComponent> entity = getComponentsByTypes(journeyMapId, componentTypes);
        List<JourneyComponentDto> dto = mapToDto(entity);
        afterMapToDto(entity, dto);
        return dto;
    }

    /**
     * 更新组件顺序
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateComponentsOrder(Long journeyMapId, List<JourneyOrderDto> orderDtoList) {
        journeyMapService.checkAdmin(journeyMapId);
        Long orgId = TenantContext.getCurrentTenant();
        orderDtoList.forEach(o -> repository.updateOrderById(o.getOrder(), o.getId(), orgId));
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteOneEmbeddedMany(Long entityId, String embeddedMapperBy, Long embeddedId) {
        journeyMapService.require(entityId);
        journeyMapService.checkAdmin(entityId);
        JourneyComponent entity = require(embeddedId);
        List<JourneyComponent> children = repository.findByJourneyMapIdAndParentId(entity.getJourneyMapId(), entity.getId());
        List<JourneyComponent> deleteComponents = new ArrayList<>();
        deleteComponents.add(entity);
        if (CollectionUtils.isNotEmpty(children)) {
            deleteComponents.addAll(children);
        }
        repository.deleteAll(deleteComponents);
        deleteElementByComponents(deleteComponents);
        return true;
    }

    @Override
    public CtmSurveyRepository getCtmSurveyRepository() {
        return ctmSurveyRepository;
    }

    @Override
    public CustomerPersonaService getCustomerPersonaService() {
        return customerPersonaService;
    }

    @Override
    public IComponentElement<?, ?, ?> getComponentElement(JourneyComponentType type) {
        return ComponentElementHelper.editElementMap.get(type);
    }

}
