package cn.hanyi.ctm.service.journey.elements.persona;

import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import cn.hanyi.ctm.entity.journey.ElementPersona;
import cn.hanyi.ctm.entity.journey.ElementPersonaPublish;
import cn.hanyi.ctm.entity.journey.ElementPersonaPublishDto;
import cn.hanyi.ctm.repository.ElementPersonaPublishRepository;
import cn.hanyi.ctm.service.journey.IComponentElement;
import cn.hanyi.ctm.service.journey.IJourneyMapPublish;
import org.befun.core.service.BaseService;
import org.springframework.stereotype.Service;

@Service
public class JourneyPersonaPublishService extends BaseService<ElementPersonaPublish, ElementPersonaPublishDto, ElementPersonaPublishRepository>
        implements
        IComponentElement<ElementPersonaPublish, ElementPersonaPublishDto, ElementPersonaPublishRepository>,
        IJourneyMapPublish<ElementPersona, ElementPersonaPublish, ElementPersonaPublishRepository> {

    @Override
    public boolean publishComponent() {
        return true;
    }

    @Override
    public JourneyComponentType componentType() {
        return JourneyComponentType.persona_content;
    }

    @Override
    public ElementPersonaPublish newInstance() {
        return new ElementPersonaPublish();
    }
}
