package cn.hanyi.ctm.service.data;

import cn.hanyi.ctm.dto.journey.CalculatingFilterDto;
import cn.hanyi.ctm.dto.journey.EventStatDataResponseDto;
import cn.hanyi.ctm.dto.stat.CacheStatEventDataDto;
import cn.hanyi.ctm.dto.stat.CacheStatEventParamDto;
import cn.hanyi.ctm.entity.journey.ElementEventStatBase;
import cn.hanyi.ctm.service.JourneyMapService;
import cn.hanyi.ctm.service.journey.elements.JourneyComponentPublishService;
import cn.hanyi.ctm.service.journey.elements.JourneyComponentService;
import cn.hanyi.ctm.service.journey.elements.eventstat.JourneyEventStatPublishService;
import cn.hanyi.ctm.service.journey.elements.eventstat.JourneyEventStatService;
import cn.hanyi.ctm.service.stat.CacheStatEventService;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class EventStatDataService {

    @Autowired
    private JourneyMapService journeyMapService;
    @Lazy
    @Autowired
    private JourneyEventStatService journeyEventStatService;
    @Autowired
    private JourneyEventStatPublishService journeyEventStatPublishService;
    @Autowired
    private CacheStatEventService cacheStatEventService;
    @Autowired
    private JourneyComponentService journeyComponentService;
    @Autowired
    private JourneyComponentPublishService journeyComponentPublishService;

    public List<EventStatDataResponseDto> dataList(Long journeyMapId, Long componentId, boolean publish, String startDate, String endDate, Long departmentId) {
        List<? extends ElementEventStatBase> list;
        if (publish) {
            list = journeyEventStatPublishService.findAllEntity(componentId);
        } else {
            list = journeyEventStatService.findAllEntity(componentId);
        }
        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().parallel().map(i -> data(journeyMapId, componentId, i, startDate, endDate, departmentId)).collect(Collectors.toList());
        }
        return List.of();
    }

    public EventStatDataResponseDto dataEdit(Long journeyMapId, Long componentId, ElementEventStatBase entity) {
        return data(journeyMapId, componentId, entity, null, null, null);
    }

    public EventStatDataResponseDto data(Long journeyMapId, Long componentId, Long eventStatId, boolean publish, String startDate, String endDate, Long departmentId) {
        ElementEventStatBase entity = getEntity(eventStatId, publish); // nullable
        return data(journeyMapId, componentId, entity, startDate, endDate, departmentId);
    }

    public EventStatDataResponseDto data(Long journeyMapId, Long componentId, ElementEventStatBase entity /*nullable*/, String startDate, String endDate, Long departmentId) {
        EventStatDataResponseDto result = new EventStatDataResponseDto();

        Pair<LocalDate, LocalDate> currentDate = journeyMapService.parseJourneyMapDateRange(journeyMapId, componentId, startDate, endDate);
        LocalDate s = currentDate.getLeft();
        LocalDate e = currentDate.getRight();
        Pair<LocalDate, LocalDate> relativeDate = journeyMapService.relativeDate(s, e);
        LocalDate sp = relativeDate.getLeft();
        LocalDate ep = relativeDate.getRight();

        int currentSum = 0;
        int previousSum = 0;
        if (entity != null) {
            result.setId(entity.getId());
            CacheStatEventParamDto param = new CacheStatEventParamDto(entity.getOrgId(), entity.getEventRuleId(), entity.getStatType());
            List<CacheStatEventDataDto> current = cacheStatEventService.getData(param, s, e, true, new CalculatingFilterDto());
            if (current != null) {
                for (CacheStatEventDataDto i : current) {
                    currentSum += Optional.ofNullable(i.getCount()).orElse(0);
                    result.getRecentValue().add(Lists.newArrayList(i.getDay(), i.getCount()));
                }
                List<CacheStatEventDataDto> previous = cacheStatEventService.getData(param, sp, ep, false, new CalculatingFilterDto());
                if (previous != null) {
                    for (CacheStatEventDataDto i : previous) {
                        previousSum += Optional.ofNullable(i.getCount()).orElse(0);
                    }
                }
            }
        }
        result.setCurrentValue(currentSum);
        result.setRatioValue((currentSum == 0 || previousSum == 0) ? null : formatDouble(currentSum * 1.0 / previousSum));
        return result;
    }

    public Double formatDouble(Double value) {
        if (value == null) {
            return null;
        }
        return Long.valueOf(Math.round(value * 10000)).doubleValue() / 10000;
    }

    public ElementEventStatBase getEntity(Long eventStatId, boolean publish) {
        if (publish) {
            return journeyEventStatPublishService.get(eventStatId);
        } else {
            return journeyEventStatService.get(eventStatId);
        }
    }
}
