package cn.hanyi.ctm.service.journey.elements;

import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import cn.hanyi.ctm.entity.journey.JourneyComponent;
import cn.hanyi.ctm.entity.journey.JourneyComponentPublish;
import cn.hanyi.ctm.entity.journey.JourneyComponentPublishDto;
import cn.hanyi.ctm.repository.CtmSurveyRepository;
import cn.hanyi.ctm.repository.JourneyComponentPublishRepository;
import cn.hanyi.ctm.service.journey.ComponentElementHelper;
import cn.hanyi.ctm.service.journey.IComponent;
import cn.hanyi.ctm.service.journey.IComponentElement;
import cn.hanyi.ctm.service.journey.IJourneyMapPublish;
import cn.hanyi.ctm.service.persona.CustomerPersonaService;
import org.befun.core.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class JourneyComponentPublishService extends BaseService<JourneyComponentPublish, JourneyComponentPublishDto, JourneyComponentPublishRepository>
        implements IComponent<JourneyComponentPublish, JourneyComponentPublishDto, JourneyComponentPublishRepository>,
        IJourneyMapPublish<JourneyComponent, JourneyComponentPublish, JourneyComponentPublishRepository> {
    @Autowired
    private JourneyComponentService journeyComponentService;
    @Autowired
    private CtmSurveyRepository ctmSurveyRepository;
    @Autowired
    private CustomerPersonaService customerPersonaService;

    @Override
    public JourneyComponentType componentType() {
        return null;
    }

    @Override
    public void afterMapToDto(List<JourneyComponentPublish> entity, List<JourneyComponentPublishDto> dto) {
        IComponent.super.afterMapToDto(entity, dto);
    }

    @Override
    public JourneyComponentPublish newInstance() {
        return new JourneyComponentPublish();
    }

    @Override
    public List<JourneyComponent> getSourceList(Long parentId) {
        return journeyComponentService.findAllEntity(parentId);
    }

    @Override
    public List<JourneyComponentPublish> getTargetList(Long parentId) {
        return findAllEntity(parentId);
    }

    @Override
    public CtmSurveyRepository getCtmSurveyRepository() {
        return ctmSurveyRepository;
    }

    @Override
    public CustomerPersonaService getCustomerPersonaService() {
        return customerPersonaService;
    }

    @Override
    public IComponentElement<?, ?, ?> getComponentElement(JourneyComponentType type) {
        return ComponentElementHelper.publishElementMap.get(type);
    }

    @Override
    public void beforeDeletePublishList(List<JourneyComponentPublish> deleteList) {
        deleteElementByComponents(deleteList);
    }

    @Transactional
    public boolean updateJourneyComponentDateFilter(long journeyMapId, long componentId, String dateFilter) {
        JourneyComponentPublish entity = get(componentId);
        if (entity != null) {
            entity.setDateFilter(dateFilter);
            repository.save(entity);
        }
        return true;
    }
}
