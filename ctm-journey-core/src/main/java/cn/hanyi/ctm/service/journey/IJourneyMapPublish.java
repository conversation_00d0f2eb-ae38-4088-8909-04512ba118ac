package cn.hanyi.ctm.service.journey;

import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import org.apache.commons.collections.CollectionUtils;
import org.befun.core.entity.BaseEntity;
import org.befun.core.repository.ResourceRepository;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@SuppressWarnings("unchecked")
public interface IJourneyMapPublish<S extends BaseEntity, T extends BaseEntity, R extends ResourceRepository<T, Long>> {

    R getRepository();

    T get(Long id);

    T newInstance();

    JourneyComponentType componentType();

    default List<S> getSourceList(Long parentId) {
        return (List<S>) ComponentElementHelper.editElementMap.get(componentType()).findAllEntity(parentId);
    }

    default List<T> getTargetList(Long parentId) {
        return (List<T>) ComponentElementHelper.publishElementMap.get(componentType()).findAllEntity(parentId);
    }

    default List<T> publish(Long parentId) {
        List<S> sourceList = getSourceList(parentId);
        List<T> targetList = getTargetList(parentId);

        List<T> deleteList = minus(targetList, sourceList);
        List<S> addList = minus(sourceList, targetList);
        List<S> updateList = minus(sourceList, addList);

        R targetRepository = getRepository();

        if (CollectionUtils.isNotEmpty(deleteList)) {
            beforeDeletePublishList(deleteList);
            targetRepository.deleteAll(deleteList);
        }
        if (CollectionUtils.isNotEmpty(addList)) {
            addList.forEach(i -> targetRepository.save(copyToPublish(i)));
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            updateList.forEach(i -> targetRepository.save(copyToPublish(i)));
        }
        return getTargetList(parentId);
    }

    private <X1 extends BaseEntity, X2 extends BaseEntity> List<X1> minus(List<X1> list1, List<X2> list2) {
        if (CollectionUtils.isEmpty(list1)) {
            return new ArrayList<>();
        }
        if (CollectionUtils.isEmpty(list2)) {
            return list1;
        }
        List<Long> list2Ids = list2.stream().map(BaseEntity::getId).filter(Objects::nonNull).collect(Collectors.toList());
        return list1.stream().filter(i -> !list2Ids.contains(i.getId())).collect(Collectors.toList());
    }

    default T copyToPublish(S source) {
        T target = get(source.getId());
        if (target == null) {
            target = newInstance();
        }
        beforeCopyToPublish(source, target);
        BeanUtils.copyProperties(source, target);
        afterCopyToPublish(target);
        return target;
    }

    default void beforeDeletePublishList(List<T> deleteList) {

    }

    default void beforeCopyToPublish(S source, T target) {

    }

    default void afterCopyToPublish(T target) {

    }
}
