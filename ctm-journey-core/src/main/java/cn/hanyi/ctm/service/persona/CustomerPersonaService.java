package cn.hanyi.ctm.service.persona;

import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import cn.hanyi.ctm.dto.journey.JourneyMapCreateResponseDto;
import cn.hanyi.ctm.dto.persona.CustomerPersonaComponentUpdateDto;
import cn.hanyi.ctm.dto.persona.CustomerPersonaUpdateDto;
import cn.hanyi.ctm.dto.persona.component.PersonaComponentProfileDto;
import cn.hanyi.ctm.entity.journey.JourneyComponent;
import cn.hanyi.ctm.entity.journey.JourneyComponentPublish;
import cn.hanyi.ctm.entity.journey.JourneyMap;
import cn.hanyi.ctm.entity.journey.JourneyMapDto;
import cn.hanyi.ctm.entity.persona.*;
import cn.hanyi.ctm.repository.CustomerPersonRepository;
import cn.hanyi.ctm.service.JourneyMapService;
import cn.hanyi.ctm.service.journey.elements.JourneyComponentPublishService;
import cn.hanyi.ctm.service.journey.elements.JourneyComponentService;
import cn.hanyi.ctm.service.persona.component.PersonaComponentProfileService;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.util.EntityUtils;
import org.befun.auth.constant.ResourcePermissionRelationType;
import org.befun.auth.dto.OrganizationOptionalLimitDto;
import org.befun.auth.entity.Organization;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.OrganizationService;
import org.befun.auth.service.ResourcePermissionService;
import org.befun.auth.service.UserService;
import org.befun.core.constant.EntityScopeStrategyType;
import org.befun.core.constant.ResourceShareFlag;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.entity.BaseEntity;
import org.befun.core.exception.EntityNotFoundException;
import org.befun.core.exception.OverLimitException;
import org.befun.core.rest.context.EmbeddedFieldContext;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.BaseMixedResourceService;
import org.befun.core.utils.JsonHelper;
import org.befun.core.utils.RegHelper;
import org.befun.extension.service.FileService;
import org.befun.extension.service.NativeSqlHelper;
import org.modelmapper.ModelMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static org.befun.auth.constant.ResourcePermissionRelationType.NONE;
import static org.befun.auth.constant.ResourcePermissionRelationType.OWNER;
import static org.befun.auth.constant.ResourcePermissionType.PERSONA;
import static org.befun.auth.constant.ResourcePermissionType.PERSONA_GROUP;
import static org.befun.core.constant.EntityScopeStrategyType.ORGANIZATION;

@Service
@Slf4j
public class CustomerPersonaService extends BaseMixedResourceService<
        CustomerPersona, CustomerPersonaDto, CustomerPersonRepository,
        CustomerPersonaGroup, CustomerPersonaGroupDto, CustomerPersonaGroupService> {

    @Autowired
    private NativeSqlHelper nativeSqlHelper;
    @Autowired
    private CustomerPersonaGroupService customerPersonaGroupService;
    @Autowired
    private CustomerPersonaComponentService customerPersonaComponentService;
    @Autowired
    private PersonaComponentProfileService personaComponentProfileService;
    @Autowired
    private UserService userService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private ResourcePermissionService resourcePermissionService;
    @Autowired
    private JourneyMapService journeyMapService;
    @Autowired
    private JourneyComponentService journeyComponentService;
    @Autowired
    private JourneyComponentPublishService journeyComponentPublishService;

    @Autowired
    private FileService fileService;

    @Autowired
    private ObjectMapper objectMapper;

    @Override
    protected void addCustomRelations(Map<String, EmbeddedFieldContext> relationMaps) {
        relationMaps.put("components", new EmbeddedFieldContext(CustomerPersonaComponent.class, "personaId"));
    }

    @Override
    @Transactional
    public <S extends BaseEntityDTO<CustomerPersona>> CustomerPersonaDto create(S data) {
        if (!checkPersonSize()) {
            throw new OverLimitException();
        }
        CustomerPersonaDto dto = super.create(data);
        customerPersonaComponentService.createDefaultComponents(dto.getEntity());
        CustomerPersonaDto result = findOneContainComponents(dto.getId());
        result.setCurrentType(OWNER);
        return result;
    }

    @Transactional
    public CustomerPersonaDto clone(CustomerPersona source, Long groupId, String title) {
        CustomerPersona clone = (CustomerPersona)source.clone();
        clone.setName(source.getName()+"_复制");
        clone.setGroupId(groupId);
        if(StringUtils.isNotEmpty(title)){
            clone.setName(title);
        }
        CustomerPersona customerPersona = super.save(clone);
        List<CustomerPersonaComponentDto> componentDtos = customerPersonaComponentService.findAll(source.getId());
        copyComponents(componentDtos,customerPersona.getId());
        CustomerPersonaDto customerPersonaDto = mapToDto(clone);
        customerPersonaDto.setComponents(componentDtos);
        return customerPersonaDto;
    }

    public void copyComponents(List<CustomerPersonaComponentDto> componentDtos,Long clonePersonaId) {
        if(componentDtos == null || componentDtos.isEmpty() || Objects.isNull(clonePersonaId)){
            return;
        }
        List<CustomerPersonaComponent> components = new ArrayList<>();

        for (CustomerPersonaComponentDto componentDto : componentDtos) {
            CustomerPersonaComponent target = new CustomerPersonaComponent();
            BeanUtils.copyProperties(componentDto.getEntity(),target);
            target.setId(null);
            target.setCreateTime(null);
            target.setPersonaId(clonePersonaId);
            components.add(target);
        }
        customerPersonaComponentService.saveAll(components);
    }

    /**
     * 判断是否有创建画像的额度
     */
    public boolean checkPersonSize() {
        return remainingPersonSize() > 0;
    }

    /**
     * 可创建画像的数量
     */
    public int remainingPersonSize() {
        Organization org = organizationService.requireCurrent();
        OrganizationOptionalLimitDto optionalLimit = organizationService.parseOrgOptionalLimit(org);
        Integer limit = optionalLimit.getCustomerPersonLimit();
        if (limit == null || limit <= 0) {
            return 0;
        }
        int size = scopeQuery(ORGANIZATION, () -> (int) repository.count());
        return Math.max(0, limit - size);
    }

    @Override
    public <EE extends BaseEntity, ED extends BaseEntityDTO<EE>, SD extends BaseEntityDTO<EE>> ED updateOneEmbeddedMany(Long rootId, String fieldNameInRoot, Long embeddedId, Class<EE> eeClass, Class<ED> edClass, SD change) {
        try {
            CustomerPersonaComponentUpdateDto dto = (CustomerPersonaComponentUpdateDto) change;
            Optional.ofNullable(dto.getProfile())
                    .ifPresent(profile ->
                            deleteImage(embeddedId, profile.getAvatar(), "avatar")
                    );

            Optional.ofNullable(dto.getImage()).ifPresent(image -> {
                deleteImage(embeddedId, image.getUrl(), "url");
            });
        } catch (Exception e) {
            log.error("delete file error: {}", e.getMessage());
        }
        return super.updateOneEmbeddedMany(rootId, fieldNameInRoot, embeddedId, eeClass, edClass, change);
    }

    private void deleteImage(Long embeddedId, String url, String path) {
        Optional.ofNullable(customerPersonaComponentService.get(embeddedId))
                .flatMap(customerPersonaComponent -> Optional.ofNullable(JsonHelper.toMap(customerPersonaComponent.getContent())))
                .ifPresent(map -> {
                    String imageOld = Objects.toString(map.get(path));
                    if (imageOld != null && !imageOld.equals(url)) {
                        log.info("delete file: {}", imageOld);
                        fileService.delete(RegHelper.parseFileName(imageOld));
                    }
                });
    }

    @Override
    public <S extends BaseEntityDTO<CustomerPersona>> CustomerPersonaDto updateOne(long id, S change) {
        CustomerPersonaUpdateDto dto = (CustomerPersonaUpdateDto) change;
        PersonaComponentProfileDto profile = personaComponentProfileService.updateProfileName(TenantContext.requireCurrentTenant(), id, dto.getName());
        if (profile != null) {
            updateByComponentChange(id, TenantContext.requireCurrentUserId(), profile);
        }
        return findOne(id);
    }

    public void updateByComponentChange(Long personaId, Long modifyUserId, PersonaComponentProfileDto profile) {
        CustomerPersona entity = require(personaId);
        entity.setModifyUserId(modifyUserId);
        entity.setModifyTime(new Date());
        if (profile != null) {
            entity.setName(profile.getName());
            entity.setProfessional(profile.getProfessional());
            entity.setAvatar(profile.getAvatar());
        }
        repository.save(entity);
    }

    @Transactional
    public void updateByJourneyMapChange(Long orgId, Long journeyMapId) {
        // 1 找到指定旅程关联的所有画像id
        String sql = "select group_concat(jcp.relation_id) from journey_component_publish jcp" +
                " inner join journey_map jm on jcp.journey_map_id=jm.id and jm.is_delete=0" +
                " where jcp.org_id=" + orgId +
                " and jm.id=" + journeyMapId +
                " and jcp.type='persona_content'";
        String ids = nativeSqlHelper.queryString(sql);
        if (StringUtils.isNotEmpty(ids)) {
            // 2 找到画像id列表关联的所有
            sql = "select jcp.relation_id personaId, jm.id journeyMapId,jm.title journeyMapTitle from journey_component_publish jcp" +
                    " inner join journey_map jm on jcp.journey_map_id=jm.id and jm.is_delete=0" +
                    " where jcp.org_id=" + orgId +
                    " and jcp.relation_id in (" + ids + ")" +
                    " and jcp.type='persona_content'";
            List<TempUpdatePersonaJourneyMap> list = nativeSqlHelper.queryListObject(sql, TempUpdatePersonaJourneyMap.class);
            if (CollectionUtils.isNotEmpty(list)) {
                Map<Long, Map<Long, String>> map = new HashMap<>();
                list.forEach(i -> {
                    if (i.getPersonaId() != null && i.getPersonaId() > 0
                            && i.getJourneyMapId() != null && i.getJourneyMapId() > 0
                            && StringUtils.isNotEmpty(i.getJourneyMapTitle())) {
                        map.computeIfAbsent(i.getPersonaId(), h -> new HashMap<>()).put(i.getJourneyMapId(), i.getJourneyMapTitle());
                    }
                });
                map.forEach((k, v) -> repository.updateCustomerPersonaJourneyTitles(k, String.join(",", v.values())));
            }
        }
    }

    @Getter
    @Setter
    public static class TempUpdatePersonaJourneyMap {
        private Long personaId;
        private Long journeyMapId;
        private String journeyMapTitle;
    }

    public CustomerPersonaDto findOneContainComponents(long id, long journeyMapId, long componentId, boolean publish) {
        CustomerPersonaDto dto;
        if (journeyMapId == 0) {
            dto = findOne(id);
        } else {
            journeyMapService.checkMember(journeyMapId);
            long matchId = 0;
            if (publish) {
                JourneyComponentPublish component = journeyComponentPublishService.get(componentId);
                if (component != null && component.getRelationId() != null && component.getType() == JourneyComponentType.persona_content) {
                    matchId = component.getRelationId();
                }
            } else {
                JourneyComponent component = journeyComponentService.get(componentId);
                if (component != null && component.getRelationId() != null && component.getType() == JourneyComponentType.persona_content) {
                    matchId = component.getRelationId();
                }
            }
            if (matchId == id) {
                dto = scopeQuery(EntityScopeStrategyType.ORGANIZATION, () -> findOne(id));
            } else {
                throw new EntityNotFoundException(CustomerPersona.class);
            }
        }
        dto.setCurrentType(getCurrentType(id, dto.getGroupId()));
        dto.setComponents(customerPersonaComponentService.findAll(id));
        return dto;
    }

    private ResourcePermissionRelationType getCurrentType(long id, Long groupId) {
        ResourcePermissionRelationType type = resourcePermissionService.calculateCurrentRelations(id, PERSONA);
        if (type == NONE && groupId != null && groupId > 0) {
            return resourcePermissionService.calculateCurrentRelations(groupId, PERSONA_GROUP);
        }
        return type;
    }

    public CustomerPersonaDto findOneContainComponents(long id) {
        return findOneContainComponents(id, 0, 0, false);
    }

    @Override
    public void afterMapToDto(List<CustomerPersona> entity, List<CustomerPersonaDto> dto) {
        if (CollectionUtils.isEmpty(dto)) {
            return;
        }
        Set<Long> userIds = new HashSet<>();
        Set<Long> ids = new HashSet<>();
        Set<Long> categoryIds = new HashSet<>();
        dto.forEach(i -> {
            Optional.ofNullable(i.getId()).ifPresent(ids::add);
            Optional.ofNullable(i.getUserId()).ifPresent(userIds::add);
            Optional.ofNullable(i.getModifyUserId()).ifPresent(userIds::add);
            Optional.ofNullable(i.getGroupId()).ifPresent(categoryIds::add);
        });
        // 所有相关的用户
        Map<Long, SimpleUser> userMap = userService.getSimpleMapByIds(userIds);
        // 所有相关的目录
        Map<Long, CustomerPersonaGroupDto> categoryMap = customerPersonaGroupService.getGroupMapByIds(categoryIds, BaseEntity::getId);
        Map<Long, Integer> countMap = getJourneyCountMap(ids);
        dto.forEach(i -> {
            confirmPersonaHasOwner(i, userMap);// 确保画像有创建人
            i.setCreateUser(userMap.get(i.getUserId()));
            i.setModifyUser(userMap.get(i.getModifyUserId()));
            i.setGroup(categoryMap.get(i.getGroupId()));
            i.setCountJourneyMap(countMap.getOrDefault(i.getId(), 0));
        });
        // 先不加载当前用户对这些画像的数据权限关系，这里会很慢
//        Map<Long, ResourcePermissionRelationType> relationTypeMap = resourcePermissionService.calculateCurrentRelations(new ArrayList<>(ids), PERSONA);
//        dto.forEach(i -> i.setCurrentType(relationTypeMap.getOrDefault(i.getId(), NONE)));
        resourceCorporationService.fillResourcePermissionInfo(dto);

    }

    /**
     * 查询每个画像被多少个旅程关联了
     */
    private Map<Long, Integer> getJourneyCountMap(Set<Long> ids) {
        Map<Long, Integer> countMap = new HashMap<>();
        if (CollectionUtils.isEmpty(ids)) {
            return countMap;
        }
        Long orgId = TenantContext.requireCurrentTenant();
        String sql = String.format("select jcp.relation_id id,count(distinct jcp.journey_map_id) count from journey_component_publish jcp" +
                " inner join journey_map jm on jcp.journey_map_id=jm.id and jm.is_delete=0" +
                " where jcp.org_id=%d" +
                " and jcp.type='persona_content'" +
                " and jcp.relation_id in (%s)", orgId, ids.stream().map(Objects::toString).collect(Collectors.joining(","))) +
                " group by jcp.relation_id";
        List<GroupCount> groupCounts = nativeSqlHelper.queryListObject(sql, GroupCount.class);
        if (CollectionUtils.isNotEmpty(groupCounts)) {
            groupCounts.forEach(i -> {
                if (i.getId() != null && i.getCount() != null) {
                    countMap.put(i.id, i.count);
                }
            });
        }
        return countMap;
    }

    @Getter
    @Setter
    public static class GroupCount {
        private Long id;
        private Integer count;
    }

    /**
     * 如果画像没有创建人，这里会重新设置企业的创建人为画像的创建人
     */
    private void confirmPersonaHasOwner(CustomerPersonaDto dto, Map<Long, SimpleUser> userMap) {
        if (dto == null) {
            return;
        }
        if (!userMap.containsKey(dto.getUserId()) && dto.getEntity() != null) {
            CustomerPersona entity = dto.getEntity();
            Optional.ofNullable(getOrgOwnerUser(userMap)).ifPresent(orgOwnerUser -> {
                entity.setUserId(orgOwnerUser.getId());
                repository.save(entity);
                // 如果超管之前已经加入的旅程，这里把他移除
                resourceCorporationService.stopShareToUser(entity.getId(), PERSONA, orgOwnerUser.getId(), ResourceShareFlag.STOP_NOT_FOUND_IGNORE);
            });
        }
    }

    private SimpleUser getOrgOwnerUser(Map<Long, SimpleUser> userMap) {
        SimpleUser orgOwnerUser = userMap.get(-99L);
        if (orgOwnerUser == null) {
            // 企业创建人
            Long orgOwnerId = Optional.ofNullable(organizationService.requireCurrent()).map(Organization::getOwnerId).orElse(null);
            orgOwnerUser = userService.getSimple(orgOwnerId).orElse(null);
            if (orgOwnerUser != null) {
                userMap.put(-99L, orgOwnerUser);
            }
        }
        return orgOwnerUser;
    }

    public List<String> getTitleByIds(List<Long> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            return repository.findAllById(ids).stream().map(CustomerPersona::getName).collect(Collectors.toList());
        }
        return List.of();
    }
}
