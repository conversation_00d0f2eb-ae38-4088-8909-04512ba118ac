package cn.hanyi.ctm.service.persona;

import cn.hanyi.ctm.entity.persona.CustomerPersona;
import cn.hanyi.ctm.entity.persona.CustomerPersonaDto;
import cn.hanyi.ctm.entity.persona.CustomerPersonaGroup;
import cn.hanyi.ctm.entity.persona.CustomerPersonaGroupDto;
import cn.hanyi.ctm.repository.CustomerPersonGroupRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.UserService;
import org.befun.core.constant.EntityScopeStrategyType;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.BaseMixedGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class CustomerPersonaGroupService extends BaseMixedGroupService<
        CustomerPersonaGroup, CustomerPersonaGroupDto, CustomerPersonGroupRepository,
        CustomerPersona, CustomerPersonaDto, CustomerPersonaService> {

    @Autowired
    private UserService userService;
    @Autowired
    private CustomerPersonaService customerPersonaService;

    @Override
    public <S extends BaseEntityDTO<CustomerPersonaGroup>> CustomerPersonaGroupDto updateOne(long id, S change) {
        return scopeQuery(EntityScopeStrategyType.OWNER_CORPORATION, () -> super.updateOne(id, change));
    }

    @Override
    public void afterMapToDto(List<CustomerPersonaGroup> entity, List<CustomerPersonaGroupDto> dto) {
        if (CollectionUtils.isEmpty(dto)) {
            return;
        }
        Set<Long> userIds = new HashSet<>();
        Set<Long> ids = new HashSet<>();
        dto.forEach(i -> {
            Optional.ofNullable(i.getId()).ifPresent(ids::add);
            Optional.ofNullable(i.getModifyUserId()).ifPresent(userIds::add);
        });
        // 所有相关的用户
        Map<Long, SimpleUser> userMap = userService.getSimpleMapByIds(userIds);
        Map<Long, Integer> countMap = customerPersonaService.getGroupCountMap(ids);
        dto.forEach(i -> {
            i.setModifyUser(userMap.get(i.getModifyUserId()));
            i.setCountPersona(countMap.getOrDefault(i.getId(), 0));
        });
        resourceCorporationService.fillResourcePermissionInfo(dto);
    }


    @Override
    public List<CustomerPersonaGroupDto> findAll(ResourceEntityQueryDto<CustomerPersonaGroupDto> queryDto) {
        List<CustomerPersonaGroupDto> list = super.findAll(queryDto);
        if (list == null) {
            list = new ArrayList<>();
        }
        list.add(0, getEmptyGroup());
        return list;
    }

    public CustomerPersonaGroupDto getEmptyGroup() {
        Long orgId = TenantContext.getCurrentTenant();
        CustomerPersonaGroupDto defaultGroup = new CustomerPersonaGroupDto();
        defaultGroup.setOrgId(orgId);
        defaultGroup.setId(0L);
        defaultGroup.setName("项目列表");
        return defaultGroup;
    }

}
