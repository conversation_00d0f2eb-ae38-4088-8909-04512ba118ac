//package cn.hanyi.ctm.service;
//
//import cn.hanyi.ctm.constant.ExpireMomentDto;
//import cn.hanyi.ctm.constant.InteractionCollectorType;
//import cn.hanyi.ctm.constant.SendStatus;
//import cn.hanyi.ctm.dto.ApiPushRequestDto;
//import cn.hanyi.ctm.dto.AppMassPushRequestDto;
//import cn.hanyi.ctm.dto.customer.DisturbStatus;
//import cn.hanyi.ctm.dto.journey.*;
//import cn.hanyi.ctm.entity.Customer;
//import cn.hanyi.ctm.entity.CustomerJourneyRecord;
//import cn.hanyi.ctm.entity.journey.ExperienceInteractionPublish;
//import cn.hanyi.ctm.entity.journey.JourneyMap;
//import cn.hanyi.ctm.entity.journey.JourneyPublish;
//import cn.hanyi.ctm.repository.CustomerJourneyRecordsRepository;
//import cn.hanyi.ctm.repository.ExperienceInteractionPublishRepository;
//import cn.hanyi.ctm.repository.JourneyMapRepository;
//import cn.hanyi.ctm.service.journey.elements.scene.JourneyScenePublishService;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.befun.auth.entity.Department;
//import org.befun.auth.service.DepartmentService;
//import org.befun.auth.service.UserService;
//import org.befun.core.exception.BadRequestException;
//import org.befun.core.exception.EntityNotFoundException;
//import org.befun.core.exception.OverLimitException;
//import org.befun.core.rest.context.TenantContext;
//import org.befun.core.utils.JsonHelper;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Service;
//import org.springframework.util.Assert;
//
//import java.time.Duration;
//import java.time.Instant;
//import java.util.*;
//import java.util.concurrent.atomic.AtomicReference;
//
//@Slf4j
//@Service
//public class CustomerJourneyService {
//
//    @Autowired
//    private JourneyMapRepository journeyMapRepository;
//    @Autowired
//    ExperienceInteractionPublishRepository experienceInteractionPublishRepository;
//    @Autowired
//    PushService pushService;
//    @Autowired
//    private CustomerService customerService;
//    @Autowired
//    private IndicatorCustomerService indicatorCustomerService;
//    @Autowired
//    private CustomerJourneyRecordsRepository customerJourneyRecordsRepository;
//    @Autowired
//    private DepartmentService departmentService;
//    @Autowired
//    private UserService userService;
//    @Autowired
//    private JourneyScenePublishService journeySceneService;
//    @Value("${survey.survey-url-prefix.root:}")
//    private String liteInfoDomain = "";
//
//    @Autowired
//    private CustomerRecordService customerRecordService;
//
//    public List<JourneyMap> getCustomerJourneyMap(Long customerId) {
//        Set<Long> journeyMapIds = indicatorCustomerService.joinIndicator(TenantContext.getCurrentTenant(), customerId);
//        if (!journeyMapIds.isEmpty()) {
//            return journeyMapRepository.findAllById(journeyMapIds);
//        } else {
//            return List.of();
//        }
//    }
//
//    /**
//     * 触发互动并记录答卷和日志
//     */
//    public void triggerJourney(
//            Long orgId,
//            Customer customer,
//            Long sceneId,
//            String externalUserId,
//            Map<String, Object> params,
//            Long departmentId,
//            Long createUserId,
//            Long journeyRecordId
//    ) {
//
//        Optional<ExperienceInteractionPublish> interaction = experienceInteractionPublishRepository
//                .findByJourneyId(sceneId);
//        if (interaction.isEmpty()) {
//            throw new EntityNotFoundException();
//        }
//        InteractionDto interactionDto = parseInteraction(interaction.get());
//        if (interactionDto == null) {
//            log.warn("parse interaction failed, interaction: {}", interaction.get());
//            throw new BadRequestException();
//        }
//        Map<String, Object> surveyParams = new HashMap<>();
//        Map<String, Object> finalParams = new HashMap<>();
//        surveyParams.put("title", interactionDto.getSurveyName());
//        finalParams.put("survey", surveyParams);
//
//        Map<String, Object> urlParams = params != null ? params : new HashMap<>();
//        urlParams.put("journey_record_id", journeyRecordId);
//        String url = customerRecordService.buildUrl(interactionDto.getSurveyUrl(), departmentId, interactionDto.getExpireTime(), urlParams);
//
//        List<InteractionResponseDto> responseDtos = internalTriggerInteraction(interactionDto, sceneId, orgId, externalUserId, departmentId, customer, url, finalParams);
//
//        List<Customer> customers = new ArrayList<>();
//        if (customer != null) {
//            customers.add(customer);
//        }
//        customerRecordService.afterSendSurveytoCustomers(customers, responseDtos, interactionDto.getSid(),
//                interactionDto.getSurveyName(), createUserId, departmentId, journeyRecordId,
//                null);
//    }
//
//
//    /**
//     * 需要通知的客户id
//     *
//     * @return List<CustomerId>
//     */
//    private Boolean notDisturb(DisturbMomentDto disturbMomentDto, Long customerId, String externalUserId, Long sceneId) {
//        AtomicReference<Boolean> result = new AtomicReference<>(false);
//        try {
//            if (disturbMomentDto == null) {
//                result.set(false);
//            } else {
//                if (!disturbMomentDto.getEnable()) {
//                    result.set(false);
//                } else {
//
//                    List<CustomerJourneyRecord> records = customerRecordService.getCustomerJourneyRecords(customerId, sceneId);
//                    // 最新记录是本次写入的记录
//                    if (records.size() <= 1) {
//                        return false;
//                    }
//
//                    // 去除本次写入的记录
//                    records.sort(Comparator.comparing(CustomerJourneyRecord::getCreateTime).reversed());
//                    CustomerJourneyRecord latestRecord = records.remove(0);
//                    CustomerJourneyRecord lastRecord = records.get(0);
//
//                    switch (disturbMomentDto.getType()) {
//                        case COUNT:
//                            // 取余判断是否打扰
//                            if (records.size() % (disturbMomentDto.getValue() + 1) != 0) {
//                                log.info("不打扰客户id: {}", customerId);
//                                result.set(true);
//                                latestRecord.setDisturbStatus(DisturbStatus.NOTDISTURB);
//                                customerRecordService.saveCustomerJourneyRecord(latestRecord);
//                            }
//                            break;
//                        case TIME:
//                            // 计算时间戳与今天间隔天数
//                            long days = Duration.between(lastRecord.getCreateTime().toInstant(), Instant.now()).toDays();
//                            if (days <= disturbMomentDto.getValue()) {
//                                log.info("不打扰客户id: {}", customerId);
//                                result.set(true);
//                                latestRecord.setDisturbStatus(DisturbStatus.NOTDISTURB);
//                                customerRecordService.saveCustomerJourneyRecord(latestRecord);
//                            }
//                            break;
//                        default:
//                    }
//                }
//            }
//        } catch (Exception e) {
//            log.error("勿扰模式 sceneId:异常", e);
//        }
//        return result.get();
//    }
//
//    /**
//     * 触发体验互动
//     */
//    public List<InteractionResponseDto> internalTriggerInteraction(
//            InteractionDto interactionDto,
//            Long sceneId,
//            Long orgId,
//            String externalUserId,
//            Long departmentId,
//            Customer customer,
//            String url,
//            Map<String, Object> params) {
//
//        List<InteractionResponseDto> result = new ArrayList<>();
//        Long customerId = customer != null ? customer.getId() : null;
//        List<Long> customerIds = new ArrayList<>() {{
//            add(customerId);
//        }};
//
//        AppMassPushRequestDto appMassPushRequestDto = null;
//        ApiPushRequestDto apiPushRequestDto = null;
//
//        for (int i = 0; i < interactionDto.getCollectorList().size(); i++) {
//            Long templateId = interactionDto.getCtmTemplateIdList().get(i);
//            InteractionCollectorType type = interactionDto.getCollectorList().get(i);
//            Assert.notNull(type, "invalid interaction collector type");
//            Boolean skipLimiter = type != InteractionCollectorType.SMS;
//            InteractionResponseDto responseDto = new InteractionResponseDto();
//            responseDto.setInteractionName(interactionDto.getInteractionName());
//            responseDto.setCollector(type);
//
//            Boolean notDisturb = notDisturb(interactionDto.getDisturbMomentDto(), customerId, externalUserId, sceneId);
//            log.info("客户：{} 勿扰模式: {}", customerId, notDisturb);
//            if (!notDisturb) {
//
//                try {
//                    if (interactionDto.getCollectorList().get(i) == InteractionCollectorType.SMS || interactionDto.getCollectorList().get(i) == InteractionCollectorType.WECHAT) {
//                        appMassPushRequestDto =
//                                new AppMassPushRequestDto(orgId, url, true, customerIds, departmentId, params, interactionDto.getMomentDto(), "");
//                        appMassPushRequestDto.setTemplateId(templateId);
//                        appMassPushRequestDto.setSceneId(sceneId);
//                        appMassPushRequestDto.setSkipLimiter(skipLimiter);
//                        responseDto.setInteractionResponse(
//                                JsonHelper.toMap(pushService.sendMessageByApp(appMassPushRequestDto)));
//                    } else if (interactionDto.getCollectorList().get(i) == InteractionCollectorType.APP) {
//                        apiPushRequestDto = new ApiPushRequestDto(orgId, sceneId, customerId, externalUserId, departmentId, url, true, interactionDto.getMomentDto(), params);
//                        responseDto.setInteractionResponse(
//                                JsonHelper.toMap(pushService.sendMessageByExternalApp(apiPushRequestDto)));
//                    }
//                    responseDto.setSendStatus(SendStatus.SUCCESS);
//
//                } catch (OverLimitException ex) {
//                    log.warn(
//                            "push message over limit, {}, {}, {}", apiPushRequestDto, appMassPushRequestDto, ex.getStackTrace());
//                    responseDto.setSendStatus(SendStatus.OVERLIMIT);
//                } catch (Exception ex) {
//                    log.warn("push message error, {}, {}, {}", apiPushRequestDto, appMassPushRequestDto, ex.getStackTrace());
//                    responseDto.setSendStatus(SendStatus.FAILED);
//                }
//            } else {
//                responseDto.setSendStatus(SendStatus.NOTDISTURB);
//            }
//
//
//            result.add(responseDto);
//        }
//        return result;
//    }
//
//    private InteractionDto parseInteraction(ExperienceInteractionPublish interaction) {
//        List<String> surveyUrlList = interaction.getSurveyUrls();
//        List<String> surveyTitleList = interaction.getSurveyTitles();
//        List<Long> sidList = interaction.getInteractionSids();
//        List<Long> interactionCollectorsList = interaction.getInteractionsCollectors();
//        List<Long> ctmTemplateIdList = interaction.getCtmTemplateId();
//        PushMomentDto pushMoment = interaction.getInteractionMoment();
//        ExpireMomentDto expireMoment = interaction.getExpireMoment();
//
//        List<InteractionCollectorType> collectorTypeList = new ArrayList<>();
//        if (surveyUrlList != null && surveyUrlList.size() > 0
//                && surveyUrlList.size() == surveyTitleList.size()
//                && ctmTemplateIdList.size() == interactionCollectorsList.size()) {
//            InteractionDto interactionDto = new InteractionDto();
//            Random random = new Random();
//            int randomIndex = random.nextInt(surveyUrlList.size());
//
//            for (Long collector : interactionCollectorsList) {
//                InteractionCollectorType type =
//                        InteractionCollectorType.getEnumByValue(collector);
//                if (type != null) {
//                    collectorTypeList.add(type);
//                } else log.warn("error format interaction collector :{}", interaction);
//            }
//
//            Optional.ofNullable(pushMoment).ifPresent(m -> interactionDto.setMomentDto(pushMoment));
//            Optional.ofNullable(expireMoment).ifPresent(m -> interactionDto.setExpireTime(expireMoment.calcExpireTime()));
//            String surveyUrl = String.format("%s%d", liteInfoDomain, sidList.get(randomIndex));
//
//            interactionDto.setDisturbMomentDto(interaction.getDisturbMoment());
//            interactionDto.setInteractionName(interaction.getInteractionName());
//            interactionDto.setCollectorList(collectorTypeList);
//            interactionDto.setCtmTemplateIdList(ctmTemplateIdList);
//            interactionDto.setSid(String.valueOf(sidList.get(randomIndex)));
//            interactionDto.setSurveyName(surveyTitleList.get(randomIndex));
//            interactionDto.setSurveyUrl(surveyUrl);
//            return interactionDto;
//        } else return null;
//    }
//
//
//    /**
//     * 开放接口新建
//     */
//    public CustomerJourneyRecord create(Customer customer, Long journeyId, JourneyTriggerRequestDto journeyTriggerRequestDto) {
//        JourneyPublish journey = journeySceneService.get(journeyId);
//        if (journey != null) {
//            CustomerJourneyRecord customerJourneyRecord = new CustomerJourneyRecord();
//            customerJourneyRecord.setDepartmentId(customer.getDepartmentId());
//
//            Department department = null;
//            if (journeyTriggerRequestDto.getDepartmentId() != null) {
//                department = departmentService.get(journeyTriggerRequestDto.getDepartmentId());
//            }
//            if (department == null && StringUtils.isNotEmpty(journeyTriggerRequestDto.getDepartmentCode())) {
//                department = departmentService.getByCodeOrRoot(customer.getOrgId(), journeyTriggerRequestDto.getDepartmentCode());
//            }
//            if (department == null) {
//                department = departmentService.getRoot(customer.getOrgId());
//            }
//
//            Optional.ofNullable(department).ifPresent(dep -> {
//                customerJourneyRecord.setDepartmentTitle(dep.getTitle());
//                customerJourneyRecord.setDepartmentId(dep.getId());
//            });
//            customerJourneyRecord.setJourneyId(journeyId);
//            customerJourneyRecord.setJourneyTitle(journey.getJourneyName());
//            customerJourneyRecord.setCreatedByUid(customer.getCreatedByUid());
//            Optional.ofNullable(userService.get(customer.getCreatedByUid())).ifPresent(user -> {
//                customerJourneyRecord.setCreatedByName(user.getTruename());
//            });
//            customerJourneyRecord.setCustomerId(customer.getId());
//            customerJourneyRecord.setDetails("");
//            customerJourneyRecord.setDisturbStatus(DisturbStatus.INIT_OPEN);
//            return customerJourneyRecordsRepository.save(customerJourneyRecord);
//        }
//        return null;
//    }
//}
