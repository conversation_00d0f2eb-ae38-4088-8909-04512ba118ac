package cn.hanyi.ctm.service.journey.elements.interaction;

import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import cn.hanyi.ctm.dto.journey.InteractionRelationDto;
import cn.hanyi.ctm.entity.ConnectorDto;
import cn.hanyi.ctm.entity.ThirdPartyTemplateDto;
import cn.hanyi.ctm.entity.journey.ExperienceInteraction;
import cn.hanyi.ctm.entity.journey.ExperienceInteractionDto;
import cn.hanyi.ctm.entity.journey.Journey;
import cn.hanyi.ctm.repository.ExperienceInteractionRepository;
import cn.hanyi.ctm.service.ConnectorService;
import cn.hanyi.ctm.service.JourneyWarningService;
import cn.hanyi.ctm.service.ThirdPartyTemplateService;
import cn.hanyi.ctm.service.journey.IComponentJourneyElement;
import cn.hanyi.ctm.service.journey.elements.scene.JourneySceneService;
import org.befun.auth.service.OrganizationService;
import org.befun.auth.service.OrganizationWalletService;
import org.befun.core.dto.ResourceBatchUpdateRequestDto;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.rest.context.DeepEmbeddedFieldContext;
import org.befun.core.service.CustomDeepEmbeddedService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class JourneyInteractionService extends CustomDeepEmbeddedService<ExperienceInteraction, ExperienceInteractionDto, ExperienceInteractionRepository>
        implements IComponentJourneyElement<ExperienceInteraction, ExperienceInteractionDto, ExperienceInteractionRepository> {
    @Autowired
    private JourneySceneService journeySceneService;
    @Autowired
    private JourneyWarningService journeyWarningService;

    @Autowired
    private OrganizationWalletService organizationWalletService;

    @Autowired
    private OrganizationService organizationService;

    @Autowired
    private ConnectorService connectorService;

    @Autowired
    private ThirdPartyTemplateService thirdPartyTemplateService;

    @Override
    public JourneyComponentType componentType() {
        return JourneyComponentType.experience_interaction;
    }

    @Override
    public ExperienceInteractionDto createDeepEmbeddedMany(Long entityId, Long embeddedId, DeepEmbeddedFieldContext deepEmbeddedFieldContext, ExperienceInteractionDto data) {
        ExperienceInteractionDto experienceInteractionDto = super.createDeepEmbeddedMany(entityId, embeddedId, deepEmbeddedFieldContext, data);
        updateJourneyExperienceInteractionId(experienceInteractionDto.getJourneyId(), experienceInteractionDto.getEntity().getId());
        return experienceInteractionDto;
    }

    @Override
    public Boolean deleteOneDeepEmbeddedMany(Long entityId, String embeddedMapperBy, Long embeddedId, String deepEmbeddedMapperBy, Long deepId) {
        Boolean result = super.deleteOneDeepEmbeddedMany(entityId, embeddedMapperBy, embeddedId, deepEmbeddedMapperBy, deepId);
        journeySceneService.deleteInteractionId(deepId);
        journeyWarningService.delete(JourneyComponentType.experience_indicator, deepId);
        return result;
    }

    @Override
    public ExperienceInteractionDto updateOneDeepEmbeddedMany(Long entityId, String embeddedMapperBy, Long embeddedId, String deepEmbeddedMapperBy, Long deepId, ExperienceInteractionDto change) {
        ExperienceInteractionDto experienceInteractionDto = super.updateOneDeepEmbeddedMany(entityId, embeddedMapperBy, embeddedId, deepEmbeddedMapperBy, deepId, change);
        updateJourneyExperienceInteractionId(experienceInteractionDto.getJourneyId(), experienceInteractionDto.getEntity().getId());
        return experienceInteractionDto;
    }

    @Override
    public List<ExperienceInteractionDto> batchUpdateDeepEmbeddedMany(Long entityId, String embeddedMapperBy, Long embeddedId, String deepEmbeddedMapperBy, ResourceBatchUpdateRequestDto<ExperienceInteractionDto> batchChangeDto) {
        List<ExperienceInteractionDto> experienceInteractionDtoList = super.batchUpdateDeepEmbeddedMany(entityId, embeddedMapperBy, embeddedId, deepEmbeddedMapperBy, batchChangeDto);
        experienceInteractionDtoList.forEach(experienceInteractionDto -> updateJourneyExperienceInteractionId(experienceInteractionDto.getJourneyId(), experienceInteractionDto.getEntity().getId()));
        return experienceInteractionDtoList;
    }

    private void updateJourneyExperienceInteractionId(Long journeyId, Long experienceInteractionId) {
        Journey journey = journeySceneService.require(journeyId);
        journey.setExperienceInteractionId(experienceInteractionId);
        journeySceneService.save(journey);
    }

    public InteractionRelationDto getInteractionRelation() {
        var org = organizationService.requireCurrent();
        var wallet = organizationWalletService.getByOrg(org.getId());
        // 这里的是企业配置数据 讲道理不会有许多 所有就把所有的拉取出来
        var connectorQueryDto = new ResourceEntityQueryDto<ConnectorDto>();
        connectorQueryDto.setLimit(500);
        var connectors = connectorService.findAll(connectorQueryDto);

        var thirdPartyQueryDto = new ResourceEntityQueryDto<ThirdPartyTemplateDto>();
        thirdPartyQueryDto.setLimit(500);
        var thirdPartyTemplates = thirdPartyTemplateService.findAll(thirdPartyQueryDto);
        InteractionRelationDto interactionRelationDto = new InteractionRelationDto(wallet.getSms(), connectors.getContent(), thirdPartyTemplates.getContent());
        return interactionRelationDto;
    }

}

