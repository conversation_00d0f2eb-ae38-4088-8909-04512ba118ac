package cn.hanyi.ctm.service.journey;

import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import cn.hanyi.ctm.entity.journey.JourneyComponentPublish;
import cn.hanyi.ctm.service.journey.elements.JourneyComponentPublishService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ComponentElementHelper implements ApplicationRunner {

    @Autowired
    private List<IComponentElement<?, ?, ?>> componentElements;

    public static final Map<JourneyComponentType, IComponentElement<?, ?, ?>> editElementMap = new HashMap<>();
    public static final Map<JourneyComponentType, IComponentElement<?, ?, ?>> publishElementMap = new HashMap<>();
    public static final Map<JourneyComponentType, IComponentJourneyElement<?, ?, ?>> editJourneyElementMap = new HashMap<>();
    public static final Map<JourneyComponentType, IComponentJourneyElement<?, ?, ?>> publishJourneyElementMap = new HashMap<>();

    @Autowired
    private List<IJourneyMapPublish<?, ?, ?>> journeyMapPublishes;
    public static final Map<JourneyComponentType, IJourneyMapPublish<?, ?, ?>> journeyMapPublishMap = new HashMap<>();

    @Override
    public void run(ApplicationArguments args) throws Exception {
        init();
    }

    public void init() {
        if (componentElements != null) {
            componentElements.forEach(i -> {
                if (i.publishComponent()) {
                    publishElementMap.put(i.componentType(), i);
                    if (i.componentType().sameAs != null) {
                        publishElementMap.put(i.componentType().sameAs, i);
                    }
                    if (i instanceof IComponentJourneyElement) {
                        publishJourneyElementMap.put(i.componentType(), (IComponentJourneyElement<?, ?, ?>) i);
                        if (i.componentType().sameAs != null) {
                            publishJourneyElementMap.put(i.componentType().sameAs, (IComponentJourneyElement<?, ?, ?>) i);
                        }
                    }
                } else {
                    editElementMap.put(i.componentType(), i);
                    if (i.componentType().sameAs != null) {
                        editElementMap.put(i.componentType().sameAs, i);
                    }
                    if (i instanceof IComponentJourneyElement) {
                        editJourneyElementMap.put(i.componentType(), (IComponentJourneyElement<?, ?, ?>) i);
                        if (i.componentType().sameAs != null) {
                            editJourneyElementMap.put(i.componentType().sameAs, (IComponentJourneyElement<?, ?, ?>) i);
                        }
                    }
                }
            });
        }
        if (journeyMapPublishes != null) {
            journeyMapPublishes.forEach(i -> {
                if (i.componentType() != null) {
                    journeyMapPublishMap.put(i.componentType(), i);
                    if (i.componentType().sameAs != null) {
                        journeyMapPublishMap.put(i.componentType().sameAs, i);
                    }
                }
            });
        }
    }
}
