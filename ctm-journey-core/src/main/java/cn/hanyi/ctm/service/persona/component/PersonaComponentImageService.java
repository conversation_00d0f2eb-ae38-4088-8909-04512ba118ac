package cn.hanyi.ctm.service.persona.component;

import cn.hanyi.ctm.constant.persona.PersonaComponentType;
import cn.hanyi.ctm.dto.persona.component.PersonaComponentImageDto;
import cn.hanyi.ctm.dto.persona.ext.CustomerPersonaComponentExtDto;
import cn.hanyi.ctm.entity.persona.CustomerPersona;
import cn.hanyi.ctm.entity.persona.CustomerPersonaComponent;
import org.befun.core.utils.JsonHelper;
import org.springframework.stereotype.Service;

import java.util.function.BiConsumer;
import java.util.function.Function;

@Service
public class PersonaComponentImageService implements IPersonaComponent<PersonaComponentImageDto> {

    @Override
    public void setDefaultContent(CustomerPersona persona, CustomerPersonaComponent entity) {
        entity.setContent(JsonHelper.toJson(new PersonaComponentImageDto()));
    }

    @Override
    public PersonaComponentType type() {
        return PersonaComponentType.image;
    }

    @Override
    public Class<PersonaComponentImageDto> componentClass() {
        return PersonaComponentImageDto.class;
    }

    @Override
    public <D extends CustomerPersonaComponentExtDto> Function<D, PersonaComponentImageDto> getComponent() {
        return CustomerPersonaComponentExtDto::getImage;
    }

    @Override
    public <D extends CustomerPersonaComponentExtDto> BiConsumer<D, PersonaComponentImageDto> setComponent() {
        return CustomerPersonaComponentExtDto::setImage;
    }
}
