package cn.hanyi.ctm.service.journey.elements.linker;

import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import cn.hanyi.ctm.dto.journey.ElementLinkerConfigDto;
import cn.hanyi.ctm.dto.journey.ElementLinkerSaveDto;
import cn.hanyi.ctm.entity.journey.ElementLinker;
import cn.hanyi.ctm.entity.journey.ElementLinkerDto;
import cn.hanyi.ctm.repository.ElementLinkerRepository;
import cn.hanyi.ctm.service.data.LinkerDataService;
import cn.hanyi.ctm.service.journey.IComponentJourneyElement;
import cn.hanyi.ctm.service.journey.elements.scene.JourneySceneService;
import org.apache.commons.collections.CollectionUtils;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.DeepEmbeddedFieldContext;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.CustomDeepEmbeddedService;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class JourneyLinkerService extends CustomDeepEmbeddedService<ElementLinker, ElementLinkerDto, ElementLinkerRepository>
        implements IComponentJourneyElement<ElementLinker, ElementLinkerDto, ElementLinkerRepository> {

    @Autowired
    private JourneySceneService journeySceneService;
    @Autowired
    private LinkerDataService linkerDataService;

    @Override
    public JourneyComponentType componentType() {
        return JourneyComponentType.linker;
    }

    @Override
    public void afterMapToDto(List<ElementLinker> entity, List<ElementLinkerDto> dto) {
        if (CollectionUtils.isNotEmpty(dto)) {
            dto.forEach(i -> {
                i.setLinkerConfig(JsonHelper.toObject(i.getEntity().getLinkerConfig(), ElementLinkerConfigDto.class));
            });
        }
    }

    @Override
    public <SD extends BaseEntityDTO<ElementLinker>> ElementLinkerDto createDeepEmbeddedMany2(Long journeyMapId, Long componentId, DeepEmbeddedFieldContext deepEmbeddedFieldContext, SD data) {
        Long orgId = TenantContext.getCurrentTenant();
        ElementLinkerSaveDto dto = (ElementLinkerSaveDto) data;
        ElementLinker exists = repository.findFirstByJourneyMapIdAndComponentIdAndJourneyId(journeyMapId, componentId, dto.getJourneyId());
        if (exists != null) {
            throw new BadRequestException("场景已创建连接器");
        }
        dto.checkParam();
        journeySceneService.checkIsCurrentOrg(dto.getJourneyId());
        ElementLinker entity = new ElementLinker(orgId, journeyMapId, componentId, dto.getJourneyId(), dto.getName(), dto.getLinkerType(), JsonHelper.toJson(dto.getLinkerConfig()));
        repository.save(entity);
        ElementLinkerDto r = mapToDto(entity);
        if (r != null) {
            afterMapToDto(r.getEntity(), r);
            r.setCalLinker(linkerDataService.editData(journeyMapId, componentId, entity));
        }
        return r;
    }

    @Override
    public <SD extends BaseEntityDTO<ElementLinker>> ElementLinkerDto updateOneDeepEmbeddedMany2(Long journeyMapId, String embeddedMapperBy, Long componentId, String deepEmbeddedMapperBy, Long linkerId, SD change) {
        ElementLinkerSaveDto dto = (ElementLinkerSaveDto) change;
        ElementLinker entity = require(linkerId);
        entity.setName(dto.getName());
        entity.setLinkerType(dto.getLinkerType());
        entity.setLinkerConfig(JsonHelper.toJson(dto.getLinkerConfig()));
        repository.save(entity);
        ElementLinkerDto r = mapToDto(entity);
        if (r != null) {
            afterMapToDto(r.getEntity(), r);
            r.setCalLinker(linkerDataService.editData(journeyMapId, componentId, entity));
        }
        return r;
    }
}
