package cn.hanyi.ctm.service.journey.elements.interaction;

import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import cn.hanyi.ctm.entity.journey.ExperienceInteraction;
import cn.hanyi.ctm.entity.journey.ExperienceInteractionPublish;
import cn.hanyi.ctm.entity.journey.ExperienceInteractionPublishDto;
import cn.hanyi.ctm.repository.ExperienceInteractionPublishRepository;
import cn.hanyi.ctm.service.journey.IComponentElement;
import cn.hanyi.ctm.service.journey.IJourneyMapPublish;
import org.befun.core.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class JourneyInteractionPublishService extends BaseService<ExperienceInteractionPublish, ExperienceInteractionPublishDto, ExperienceInteractionPublishRepository>
        implements
        IComponentElement<ExperienceInteractionPublish, ExperienceInteractionPublishDto, ExperienceInteractionPublishRepository>,
        IJourneyMapPublish<ExperienceInteraction, ExperienceInteractionPublish, ExperienceInteractionPublishRepository> {

    @Override
    public JourneyComponentType componentType() {
        return JourneyComponentType.experience_interaction;
    }

    @Override
    public boolean publishComponent() {
        return true;
    }

    @Override
    public ExperienceInteractionPublish newInstance() {
        return new ExperienceInteractionPublish();
    }

}
