package cn.hanyi.ctm.service;

import cn.hanyi.ctm.dto.journey.*;
import cn.hanyi.ctm.dto.user.CreateUserDto;
import cn.hanyi.ctm.entity.journey.*;
import cn.hanyi.ctm.repository.JourneyMapRepository;
import cn.hanyi.ctm.service.journey.ComponentElementHelper;
import cn.hanyi.ctm.service.journey.IJourneyMapPublish;
import cn.hanyi.ctm.service.journey.elements.JourneyComponentPublishService;
import cn.hanyi.ctm.service.journey.elements.JourneyComponentService;
import cn.hanyi.ctm.workertrigger.ICtmEventTrigger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.befun.auth.constant.ResourcePermissionRelationType;
import org.befun.auth.constant.ResourcePermissionType;
import org.befun.auth.dto.OrganizationOptionalLimitDto;
import org.befun.auth.entity.Organization;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.IResourcePermissionRelationService;
import org.befun.auth.service.OrganizationService;
import org.befun.auth.service.ResourcePermissionService;
import org.befun.auth.service.UserService;
import org.befun.core.constant.ResourceShareFlag;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.DeepEmbeddedFieldContext;
import org.befun.core.rest.context.EmbeddedFieldContext;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.BaseMixedResourceService;
import org.befun.core.service.ResourceCorporationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.Period;
import java.util.*;
import java.util.function.Function;

import static org.befun.auth.constant.ResourcePermissionRelationType.*;
import static org.befun.auth.constant.ResourcePermissionType.JOURNEY;
import static org.befun.core.constant.EntityScopeStrategyType.ORGANIZATION;

@Service
@Slf4j
public class JourneyMapService extends BaseMixedResourceService<
        JourneyMap, JourneyMapDto, JourneyMapRepository,
        JourneyMapGroup, JourneyMapGroupDto, JourneyMapGroupService
        > implements IResourcePermissionRelationService {

    @Autowired
    private UserService userService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private JourneyMapCopyService journeyMapCopyService;
    @Autowired
    private JourneyComponentService journeyComponentService;
    @Autowired
    private JourneyComponentPublishService journeyComponentPublishService;
    @Autowired
    private ResourcePermissionService resourcePermissionService;
    @Autowired
    private ResourceCorporationService resourceCorporationService;
    @Autowired
    private ICtmEventTrigger ctmEventTrigger;

    private final LocalDate limitDate = LocalDate.of(2010, 1, 1);

    @Override
    public ResourcePermissionService getResourcePermissionService() {
        return resourcePermissionService;
    }

    @Override
    public ResourcePermissionType permissionType() {
        return JOURNEY;
    }

    public void checkAdmin(JourneyMap entity) {
        if (entity != null && entity.getUserId() != null && entity.getUserId().equals(TenantContext.getCurrentUserId())) {
            return;
        }
        if (entity != null) {
            checkAdmin(entity.getId());
        } else {
            throw new BadRequestException("无权限");
        }
    }

    @Override
    protected void addCustomRelations(Map<String, EmbeddedFieldContext> relationMaps) {
        // 把 JourneyComponent 模拟成 JourneyMap 的下一级
        EmbeddedFieldContext components = new EmbeddedFieldContext(JourneyComponentBase.class, "journeyMapId");
        relationMaps.put("components", components);
        Function<Class<?>, DeepEmbeddedFieldContext> deep = clazz -> new DeepEmbeddedFieldContext(clazz, "componentId", components);
        // 把 Journey 模拟成 JourneyComponent 的下一级
        components.deepRelationMaps.put("journeys", deep.apply(ElementTextBoxBase.class));
        // 把 ElementTextBox 模拟成 JourneyComponent 的下一级
        components.deepRelationMaps.put("elementTexts", deep.apply(ElementTextBoxBase.class));
        // 把 ElementCurve 模拟成 JourneyComponent 的下一级
        components.deepRelationMaps.put("elementCurves", deep.apply(ElementCurveBase.class));
        // 把 ExperienceInteraction 模拟成 JourneyComponent 的下一级
        components.deepRelationMaps.put("experienceInteractions", deep.apply(ExperienceInteractionBase.class));
        // 把 ExperienceIndicator 模拟成 JourneyComponent 的下一级
        components.deepRelationMaps.put("experienceIndicators", deep.apply(ExperienceIndicatorBase.class));
        // 把 ElementEventStat 模拟成 JourneyComponent 的下一级
        components.deepRelationMaps.put("eventStats", deep.apply(ElementEventStatBase.class));
        // 把 ElementLiner 模拟成 JourneyComponent 的下一级
        components.deepRelationMaps.put("linkers", deep.apply(ElementLinkerBase.class));
        // 把 ElementPersona 模拟成 JourneyComponent 的下一级
        components.deepRelationMaps.put("personas", deep.apply(ElementPersonaBase.class));
    }

    @Override
    public void afterMapToDto(List<JourneyMap> entity, List<JourneyMapDto> dto) {
        if (CollectionUtils.isEmpty(dto)) {
            return;
        }
        Set<Long> userIds = new HashSet<>();
        Set<Long> ids = new HashSet<>();
        dto.forEach(i -> {
            Optional.ofNullable(i.getId()).ifPresent(ids::add);
            Optional.ofNullable(i.getUserId()).ifPresent(userIds::add);
            Optional.ofNullable(i.getModifyUserId()).ifPresent(userIds::add);
        });
        // 当前登录用户
        Optional.ofNullable(TenantContext.getCurrentUserId()).ifPresent(userIds::add);
        // 企业创建人
        Long orgOwnerId = Optional.ofNullable(organizationService.requireCurrent()).map(Organization::getOwnerId).orElse(null);
        Optional.ofNullable(orgOwnerId).ifPresent(userIds::add);
        // 所有相关的用户信息
        Map<Long, SimpleUser> userMap = userService.getSimpleMapByIds(userIds);
        dto.forEach(i -> {
            confirmJourneyMapHasOwner(i, orgOwnerId, userMap);// 确保旅程有创建人
            i.setOwnerUser(userMap.get(i.getUserId()));
            i.setModifyUser(userMap.get(i.getModifyUserId()));
        });
        // 旅程和当前用户的关系
        Map<Long, ResourcePermissionRelationType> relationTypeMap = resourcePermissionService.calculateCurrentRelations(new ArrayList<>(ids), JOURNEY);
        dto.forEach(i -> {
            i.setCurrentType(relationTypeMap.getOrDefault(i.getId(), NONE));
        });
    }

    /**
     * 如果旅程没有拥有者，这里会重新设置企业的创建人为旅程的拥有者
     */
    private void confirmJourneyMapHasOwner(JourneyMapDto dto, Long orgOwnerId, Map<Long, SimpleUser> userMap) {
        if (orgOwnerId == null || dto == null) {
            return;
        }
        SimpleUser orgOwner = userMap.get(orgOwnerId);
        if (orgOwner == null) {
            return;
        }
        if (!userMap.containsKey(dto.getUserId()) && dto.getEntity() != null) {
            dto.setUserId(orgOwnerId);
            JourneyMap entity = dto.getEntity();
            entity.setUserId(orgOwnerId);
            repository.save(entity);
            // 如果超管之前已经加入的旅程，这里把他移除
            resourceCorporationService.stopShareToUser(entity.getId(), JOURNEY, orgOwnerId, ResourceShareFlag.STOP_NOT_FOUND_IGNORE);
        }
    }

    /**
     * 更新旅程
     * 旅程成员权限 {@link JourneyMapService#findAll(ResourceEntityQueryDto)}
     */
    @Override
    public <S extends BaseEntityDTO<JourneyMap>> JourneyMapDto updateOne(long id, S change) {
        JourneyUpdateRequestDto dto = (JourneyUpdateRequestDto) change;
        checkAdmin(id);
        JourneyMap entity = require(id);
        if (StringUtils.isNotEmpty(dto.getTitle())) {
            entity.setTitle(dto.getTitle());
        }
        if (StringUtils.isNotEmpty(dto.getCover())) {
            entity.setCover(dto.getCover());
        }
        if (StringUtils.isNotEmpty(dto.getDefaultDateFilter())) {
            entity.setDefaultDateFilter(dto.getDefaultDateFilter());
        }
        repository.save(entity);
        return mapToDto(entity);
    }

    /**
     * 创建旅程
     * 旅程成员权限 {@link JourneyMapService#findAll(ResourceEntityQueryDto)}
     */
    @Transactional(rollbackFor = Exception.class)
    public JourneyMapCreateResponseDto create(JourneyCreateRequestDto dto) {
        if (!checkJourneyMapSize()) {
            return JourneyMapCreateResponseDto.overLimit();
        }
        SimpleUser current = userService.requireCurrentSimple();
        JourneyMap entity = new JourneyMap();
        entity.setUserId(current.getId());
        entity.setModifyUserId(current.getId());
        entity.setTitle(dto.getTitle());
        entity.setGroupId(dto.getGroupId());
        if (StringUtils.isNotEmpty(dto.getCover())) {
            entity.setCover(dto.getCover());
        }
        if (StringUtils.isNotEmpty(dto.getDefaultDateFilter())) {
            entity.setDefaultDateFilter(dto.getDefaultDateFilter());
        }
        JourneyMapDto r = createJourneyMap(TenantContext.getCurrentTenant(), entity, true);
        r.setCurrentType(OWNER); // 这里重新设置一下是拥有者，因为查询是否为拥有者是用的jdbcTemplate，不在同一个事务里面，当前事务还未提交，查询不到数据
        return JourneyMapCreateResponseDto.success(r);
    }


    /**
     * 可创建旅程的数量
     */
    public int remainingJourneyMapSize() {
        Organization org = organizationService.requireCurrent();
        OrganizationOptionalLimitDto optionalLimit = organizationService.parseOrgOptionalLimit(org);
        Integer limit = optionalLimit.getCustomerLifecycleLimit();
        log.info("journeyMap limit size:{}", limit);
        if (limit == null || limit <= 0) {
            return 0;
        }
        TenantContext.addCustomEntityScopeStrategy(JourneyMap.class, ORGANIZATION);
        int size = (int) repository.count();
        TenantContext.clearCustomEntityScopeStrategy(JourneyMap.class);
        return Math.max(0, limit - size);
    }

    /**
     * 判断是否有创建旅程的额度
     */
    public boolean checkJourneyMapSize() {
        return remainingJourneyMapSize() > 0;
    }


    /**
     * 发布旅程
     * 旅程成员权限 {@link JourneyMapService#findAll(ResourceEntityQueryDto)}
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean publish(JourneyMap entity) {
        checkAdmin(entity);
        publish(entity.getOrgId(), TenantContext.requireCurrentUserId(), entity.getId());
        entity.setModifyTime(new Date());
        SimpleUser currentUser = userService.requireCurrentSimple();
        entity.setModifyUserId(currentUser.getId());
        repository.save(entity);//更新修改时间
        return true;
    }

    private void publish(Long orgId, Long userId, Long journeyMapId) {
        List<JourneyComponentPublish> targetList = journeyComponentPublishService.publish(journeyMapId);
        if (CollectionUtils.isNotEmpty(targetList)) {
            targetList.forEach(target -> {
                IJourneyMapPublish<?, ?, ?> journeyMapPublish = ComponentElementHelper.journeyMapPublishMap.get(target.getType());
                if (journeyMapPublish != null) {
                    journeyMapPublish.publish(target.getId());
                }
            });
        }
        ctmEventTrigger.journeyMapPublish(orgId, userId, journeyMapId);
    }

    /**
     * 将自己添加到管理员列表，必须有超级管理员的角色
     * 旅程成员权限 {@link JourneyMapService#findAll(ResourceEntityQueryDto)}
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean addMyself(JourneyMap entity) {
        if (!userService.currentHasSuperAdminRole()) {
            throw new BadRequestException("无权限");
        }
        Long orgId = TenantContext.getCurrentTenant();
        Long userId = TenantContext.getCurrentUserId();
        resourceCorporationService.shareToUser(entity.getId(), JOURNEY, ADMIN, orgId, userId, ResourceShareFlag.SHARE_FOUND_UPDATE_RELATION);
        return true;
    }

    /**
     * 修改旅程的拥有者，当前用户降级为 ADMIN
     * 旅程成员权限 {@link JourneyMapService#findAll(ResourceEntityQueryDto)}
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean changeOwner(JourneyMap entity, JourneyOwnerRequestDto dto) {
        checkOwner(entity.getId());
        SimpleUser targetUser = userService.requireSimple(dto.getUserId());
        entity.setUserId(targetUser.getId());
        repository.save(entity);

        Long orgId = TenantContext.getCurrentTenant();
        Long userId = TenantContext.getCurrentUserId();

        // 移除目标用户
        resourceCorporationService.stopShareToUser(entity.getId(), JOURNEY, targetUser.getId(), ResourceShareFlag.STOP_NOT_FOUND_IGNORE);

        // 把自己加入 ADMIN
        resourceCorporationService.shareToUser(entity.getId(), JOURNEY, ADMIN, orgId, userId, ResourceShareFlag.SHARE_FOUND_UPDATE_RELATION);
        return true;
    }

    /**
     * 删除旅程
     * 旅程成员权限 {@link JourneyMapService#findAll(ResourceEntityQueryDto)}
     */
    @Transactional
    public Boolean deleteOne(long id) {
        checkOwner(id);
        Boolean r = super.deleteOne(id);
        ctmEventTrigger.journeyMapDelete(TenantContext.requireCurrentTenant(), TenantContext.requireCurrentUserId(), id);
        return r;
    }

    private JourneyMapDto createJourneyMap(long orgId, JourneyMap journeyMap, boolean publish) {
        journeyMap.setOrgId(orgId);
        repository.saveAndFlush(journeyMap);
        journeyComponentService.createInitJourneyComponent(orgId, journeyMap.getId());
        if (publish) {
            publish(journeyMap);
        }
        JourneyMapDto dto = mapToDto(journeyMap);
        dto.setEntity(journeyMap);
        afterMapToDto(journeyMap, dto);
        return dto;
    }

    /**
     * 初始化旅程地图
     */
    @Transactional
    public void initJourneyMap(CreateUserDto userDto) {
        createJourneyMap(userDto.getOrgId(), new JourneyMap(), true);
    }

    @Transactional
    public JourneyMapCreateResponseDto clone(JourneyMap source, Long groupId, String title) {
        checkAdmin(source.getId());
        if (!checkJourneyMapSize()) {
            return JourneyMapCreateResponseDto.overLimit();
        }
        JourneyMap target = journeyMapCopyService.copy(TenantContext.getCurrentTenant(), TenantContext.getCurrentUserId(), groupId, title, source);
        publish(target);
        JourneyMapDto dto = mapToDto(target);
        afterMapToDto(target, dto);
        dto.setCurrentType(OWNER); // 这里重新设置一下是拥有者，因为查询是否为拥有者是用的jdbcTemplate，不在同一个事务里面，当前事务还未提交，查询不到数据
        return JourneyMapCreateResponseDto.success(dto);
    }

    @Transactional
    public boolean clone(Long sourceId, Long targetOrgId, Long targetUserId) {
        JourneyMap source = require(sourceId);
        Organization targetOrg = organizationService.require(targetOrgId);
        if (targetUserId == null || targetUserId <= 0) {
            targetUserId = targetOrg.getOwnerId();
        }
        JourneyMap target = journeyMapCopyService.copy(targetOrgId, targetUserId, null, source.getTitle(), source);
        publish(targetOrgId, targetUserId, target.getId());
        return true;
    }

    /**
     * 解析旅程的时间段
     * 时间条件不再通过参数传入，每个组件都有自己的时间条件，直接用发布状态的组件上的时间条件
     *
     * @since 1.8.9
     */
    public Pair<LocalDate, LocalDate> parseJourneyMapDateRange(Long journeyMapId, Long componentId, String start, String end) {
        JourneyComponentPublish entity = journeyComponentPublishService.get(componentId);
        LocalDate s, e = LocalDate.now();
        String interval = entity != null ? entity.getDateFilter() : "last_one_month";
        if (StringUtils.isEmpty(interval)) {
            s = e.minusMonths(1);
        } else if ("last_seven_days".equalsIgnoreCase(interval)) {
            s = e.minusDays(7);
        } else if ("last_fiften_days".equalsIgnoreCase(interval)) {
            s = e.minusDays(15);
        } else if ("last_one_month".equalsIgnoreCase(interval)) {
            s = e.minusMonths(1);
        } else if ("last_three_months".equalsIgnoreCase(interval)) {
            s = e.minusMonths(3);
        } else if ("last_one_year".equalsIgnoreCase(interval)) {
            s = e.minusYears(1);
        } else {
            s = e.minusMonths(1);
        }
        return Pair.of(s, e);
    }

    public Pair<LocalDate, LocalDate> relativeDate(LocalDate s, LocalDate e) {
        if (s.isBefore(limitDate) || s.isEqual(limitDate)) {
            return Pair.of(null, null);
        }
        Period period = Period.between(s, e);
        return Pair.of(s.minus(period), e.minus(period));
    }

    public JourneyMapResponseDto components(Long journeyMapId) {
        checkMember(journeyMapId);
        return new JourneyMapResponseDto(findOne(journeyMapId), journeyComponentService.findAll(journeyMapId));
    }

    public JourneyMapPublishResponseDto publishComponents(Long journeyMapId) {
        checkMember(journeyMapId);
        return new JourneyMapPublishResponseDto(findOne(journeyMapId), journeyComponentPublishService.findAll(journeyMapId));
    }
}
