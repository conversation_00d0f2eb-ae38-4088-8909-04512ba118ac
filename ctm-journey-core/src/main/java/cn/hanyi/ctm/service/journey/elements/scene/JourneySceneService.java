package cn.hanyi.ctm.service.journey.elements.scene;

import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import cn.hanyi.ctm.dto.journey.JourneyCreateDto;
import cn.hanyi.ctm.dto.journey.JourneyCreateResponseDto;
import cn.hanyi.ctm.dto.journey.JourneyOrderDto;
import cn.hanyi.ctm.entity.journey.*;
import cn.hanyi.ctm.repository.ExperienceIndicatorRepository;
import cn.hanyi.ctm.repository.ExperienceInteractionRepository;
import cn.hanyi.ctm.repository.JourneyRepository;
import cn.hanyi.ctm.service.JourneyMapService;
import cn.hanyi.ctm.service.journey.ComponentElementHelper;
import cn.hanyi.ctm.service.journey.IComponentElement;
import cn.hanyi.ctm.service.journey.elements.JourneyComponentService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.befun.auth.service.TreeConvertService;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.CustomDeepEmbeddedService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;

import static cn.hanyi.ctm.constant.journeymap.JourneyComponentType.*;

@Service
@Slf4j
public class JourneySceneService extends CustomDeepEmbeddedService<Journey, JourneyDto, JourneyRepository>
        implements IComponentElement<Journey, JourneyDto, JourneyRepository> {

    @Autowired
    private ExperienceIndicatorRepository experienceIndicatorRepository;
    @Autowired
    private ExperienceInteractionRepository experienceInteractionRepository;
    @Autowired
    private JourneyMapService journeyMapService;
    @Autowired
    private TreeConvertService treeConvertService;
    @Lazy
    @Autowired
    private JourneyComponentService journeyComponentService;

    @Override
    protected Object requireParent(long l) {
        return l;
    }

    @Override
    public JourneyComponentType componentType() {
        return JourneyComponentType.journey;
    }

    @Override
    public void afterMapToDto(List<Journey> entity, List<JourneyDto> dto) {
    }

    public List<Journey> findAllEntity(Long componentId) {
        return repository.findAll(((root, query, builder) -> builder.equal(root.get("componentId"), componentId)), Sort.by(Sort.Direction.ASC, "order"));
    }

    /**
     * 找到旅程的所有的有效场景，返回的是根节点列表（树结构）
     */
    public List<JourneyDto> findTreeByJourneyMapId(Long journeyMapId) {
        List<JourneyComponent> journeyComponents = journeyComponentService.getComponentsByType(journeyMapId, JourneyComponentType.journey);
        if (CollectionUtils.isEmpty(journeyComponents)) {
            return new ArrayList<>();
        }
        JourneyComponent journeyComponent = journeyComponents.get(0);
        return findAll(journeyComponent.getId());
    }

    /**
     * 找到旅程的所有的有效场景，返回的是全部场景的列表（并且填充了父子关系）
     */
    public List<JourneyDto> findAllByJourneyMapId(Long journeyMapId) {
        return treeConvertService.treeToList(findTreeByJourneyMapId(journeyMapId), Function.identity());
    }

    /**
     * 这个参数是场景的组件id
     * 找到旅程的所有的有效场景，返回的是根节点列表（树结构）
     */
    public List<JourneyDto> findAll(Long componentId) {
        return Optional.ofNullable(treeConvertService.listToTree(findAllEntity(componentId), this::mapToDto)).orElse(new ArrayList<>());
    }

    @Transactional(rollbackFor = Exception.class)
    public JourneyCreateResponseDto deleteJourney(Long journeyMapId, Long componentId, Long journeyId) {
        JourneyMap journeyMap = journeyMapService.checkIsCurrentOrg(journeyMapId);
        journeyMapService.checkAdmin(journeyMap);
        journeyComponentService.checkIsCurrentOrg(componentId);
        Journey journey = checkIsCurrentOrg(journeyId);
        List<Journey> selfAndChildren = new ArrayList<>(); // 当前场景和他的所有子场景，如果这个列表只有一个元素，则说明当前场景是叶子场景
        parseChildren(selfAndChildren, List.of(journey), i -> repository.findAllByParentId(i.getId()));
        if (!selfAndChildren.isEmpty()) {
            deleteElementByJourneys(journeyMapId, journey, selfAndChildren);
            repository.deleteAll(selfAndChildren);
        }
        updateParentNode(journey, 1);
        // 返回删除后的journey和所有的curve组件,event_stat组件
        List<JourneyComponentDto> componentDtoList = journeyComponentService.getComponentsDtoByTypes(journeyMapId, List.of(curve, event_stat, linker, persona, persona_content));
        fillDataByComponents(componentDtoList);
        return new JourneyCreateResponseDto(mapToDto(journey), componentDtoList);
    }

    private void fillDataByComponents(List<JourneyComponentDto> componentDtoList) {
        if (CollectionUtils.isNotEmpty(componentDtoList)) {
            componentDtoList.forEach(i -> {
                Optional.ofNullable(ComponentElementHelper.editJourneyElementMap.get(i.getType())).ifPresent(j -> {
                    j.fillDataByComponent(i);
                });
            });
        }
    }

    /**
     * 删除场景(和子场景)同时删除元素
     */
    public void deleteElementByJourneys(Long journeyMapId, Journey journey, List<Journey> selfAndChildren) {
        if (journey != null && org.apache.commons.collections4.CollectionUtils.isNotEmpty(selfAndChildren)) {
            Set<Long> ids = new HashSet<>();
            selfAndChildren.forEach(i -> ids.add(i.getId()));
            boolean isLeaf = selfAndChildren.size() == 1 && Objects.equals(journey.getId(), selfAndChildren.get(0).getId());
            boolean isLastChild = isLastChild(journey);
            ComponentElementHelper.editJourneyElementMap.values().forEach(element -> {
                if (element.componentType().supportTransfer) {
                    // 先场景转移，
                    element.transferByDelete(journeyMapId, journey.getId(), journey.getParentId(), isLeaf, isLastChild);
                }
                if (element.componentType().hasRelationJourney) {
                    // 在删除其他的子场景的数据
                    element.deleteByJourneyIds(journey.getOrgId(), ids);
                }
            });
        }
    }

    public boolean isLastChild(Journey journey) {
        if (journey.getParentId() == null || journey.getParentId() == 0) {
            return false;
        }
        List<Journey> list = repository.findAllByParentId(journey.getParentId());
        if (CollectionUtils.isEmpty(list)) {
            return false;
        }
        Journey j;
        return list.size() == 1 && (j = list.get(0)) != null && j.getId().equals(journey.getId());
    }

    private void parseChildren(List<Journey> selfAndChildren, List<Journey> list, Function<Journey, List<Journey>> getChildren) {
        if (CollectionUtils.isNotEmpty(list)) {
            selfAndChildren.addAll(list);
            list.forEach(i -> parseChildren(selfAndChildren, getChildren.apply(i), getChildren));
        }
    }

    private void updateParentNode(Journey journey, int isLeaf) {
        Optional.ofNullable(get(journey.getParentId())).ifPresent(parent -> {
            parent.setIsLeaf(isLeaf);
            repository.save(parent);
        });
    }

    public void deleteInteractionId(Long interactionId) {
        repository.findByExperienceInteractionId(interactionId).ifPresent(journey -> {
            journey.setExperienceInteractionId(0L);
            repository.save(journey);
        });
    }

    public void deleteIndicatorId(Long indicatorId) {
        repository.findByExperienceIndicatorId(indicatorId).ifPresent(journey -> {
            journey.setExperienceIndicatorId(0L);
            repository.save(journey);
        });
    }

    /**
     * 添加场景时修改叶子节点，初始化表情曲线元素
     */
    @Transactional(rollbackFor = Exception.class)
    public JourneyCreateResponseDto createJourney(Long journeyMapId, Long componentId, JourneyCreateDto param) {
        Long orgId = TenantContext.getCurrentTenant();
        Journey journey = new Journey();
        journey.setOrgId(orgId);
        journey.setComponentId(componentId);
        journey.setColor(param.getColor());
        journey.setParentId(param.getParentId());
        journey.setJourneyName(param.getJourneyName());
        repository.save(journey);
        updateParentNode(journey, 0);
        ComponentElementHelper.editJourneyElementMap.forEach((k, v) -> {
            // 添加场景之后，需要同时添加情绪曲线, 等等
            v.addByJourney(orgId, journeyMapId, journey.getId());
            if (v.componentType().supportTransfer) {
                // 场景转移，事件统计，等等
                v.transferByAdd(journeyMapId, journey.getId(), journey.getParentId());
            }
        });
        // 返回新创建的journey和所有的curve组件,event_stat组件
        List<JourneyComponentDto> componentDtoList = journeyComponentService.getComponentsDtoByTypes(journeyMapId, List.of(curve, event_stat, linker, persona, persona_content));
        fillDataByComponents(componentDtoList);
        return new JourneyCreateResponseDto(mapToDto(journey), componentDtoList);
    }

    /**
     * 更新场景顺序
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateJourneyOrder(List<JourneyOrderDto> orderDtoList) {
        Long orgId = TenantContext.getCurrentTenant();
        orderDtoList.forEach(o -> repository.updateOrderById(o.getOrder(), o.getId(), orgId));
        return true;
    }

    public Set<Long> getFullIds(Long componentId, Long journeyId) {
        Set<Long> ids = new HashSet<>();
        Map<Long, JourneyDto> map = new HashMap<>();
        treeConvertService.listToTree(findAllEntity(componentId), i -> {
            JourneyDto dto = mapToDto(i);
            map.put(i.getId(), dto);
            return dto;
        });
        JourneyDto current = map.get(journeyId);
        if (current != null) {
            List<Long> childrenIds = treeConvertService.treeToList(List.of(current), JourneyDto::getId);
            if (CollectionUtils.isNotEmpty(childrenIds)) {
                ids.addAll(childrenIds);
            }
            Long pid = current.getPid();
            while (pid != null) {
                JourneyDto parent = map.get(pid);
                if (parent != null) {
                    ids.add(pid);
                    pid = parent.getPid();
                } else {
                    pid = null;
                }
            }
            ids.remove(current.getId());
        }
        return ids;
    }

}
