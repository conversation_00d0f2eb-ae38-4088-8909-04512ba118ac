package cn.hanyi.ctm.service.persona.component;

import cn.hanyi.ctm.constant.persona.PersonaComponentType;
import cn.hanyi.ctm.dto.persona.component.PersonaComponentTextboxDto;
import cn.hanyi.ctm.dto.persona.ext.CustomerPersonaComponentExtDto;
import cn.hanyi.ctm.entity.persona.CustomerPersona;
import cn.hanyi.ctm.entity.persona.CustomerPersonaComponent;
import org.befun.core.utils.JsonHelper;
import org.springframework.stereotype.Service;

import java.util.function.BiConsumer;
import java.util.function.Function;

@Service
public class PersonaComponentTextboxService implements IPersonaComponent<PersonaComponentTextboxDto> {

    @Override
    public void setDefaultContent(CustomerPersona persona, CustomerPersonaComponent entity) {
        entity.setContent(JsonHelper.toJson(new PersonaComponentTextboxDto()));
    }

    @Override
    public PersonaComponentType type() {
        return PersonaComponentType.textbox;
    }

    @Override
    public Class<PersonaComponentTextboxDto> componentClass() {
        return PersonaComponentTextboxDto.class;
    }

    @Override
    public <D extends CustomerPersonaComponentExtDto> Function<D, PersonaComponentTextboxDto> getComponent() {
        return CustomerPersonaComponentExtDto::getTextbox;
    }

    @Override
    public <D extends CustomerPersonaComponentExtDto> BiConsumer<D, PersonaComponentTextboxDto> setComponent() {
        return CustomerPersonaComponentExtDto::setTextbox;
    }
}
