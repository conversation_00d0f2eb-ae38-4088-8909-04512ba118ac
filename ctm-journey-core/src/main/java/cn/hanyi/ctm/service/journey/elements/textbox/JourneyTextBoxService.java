package cn.hanyi.ctm.service.journey.elements.textbox;

import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import cn.hanyi.ctm.dto.journey.JourneyComponentCreateDto;
import cn.hanyi.ctm.entity.journey.ElementTextBox;
import cn.hanyi.ctm.entity.journey.ElementTextBoxDto;
import cn.hanyi.ctm.entity.journey.JourneyDto;
import cn.hanyi.ctm.repository.ElementTextBoxRepository;
import cn.hanyi.ctm.service.journey.IComponentElement;
import cn.hanyi.ctm.service.journey.elements.scene.JourneySceneService;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.CustomDeepEmbeddedService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class JourneyTextBoxService extends CustomDeepEmbeddedService<ElementTextBox, ElementTextBoxDto, ElementTextBoxRepository>
        implements IComponentElement<ElementTextBox, ElementTextBoxDto, ElementTextBoxRepository> {

    @Autowired
    private JourneySceneService journeySceneService;

    @Override
    protected Object requireParent(long l) {
        return l;
    }

    @Override
    public JourneyComponentType componentType() {
        return JourneyComponentType.textbox;
    }

    @Override
    public void afterMapToDto(List<ElementTextBox> entity, List<ElementTextBoxDto> dto) {
    }

    /**
     * 添加组件后，给所有的叶子场景创建一个空文本
     */
    @Override
    public List<ElementTextBoxDto> addByComponent(Long orgId, Long journeyMapId, Long componentId, JourneyComponentCreateDto dto) {
        List<String> content = new ArrayList<>();
        List<JourneyDto> list = journeySceneService.findAllByJourneyMapId(journeyMapId);
        if (CollectionUtils.isNotEmpty(list)) {
            String defaultText = StringUtils.isNotEmpty(dto.getText()) ? dto.getText() : "";
            // 给所有的子节点 添加一个空字符串
            list.forEach(i -> {
                // 如果 children 是空的，则是子节点
                if (CollectionUtils.isEmpty(i.getChildren())) {
                    content.add(defaultText);
                }
            });
        }
        ElementTextBox elementTextBox = new ElementTextBox(componentId, TenantContext.getCurrentTenant(), dto.getElementType(), content);
        repository.save(elementTextBox);
        return findAll(componentId);
    }

}
