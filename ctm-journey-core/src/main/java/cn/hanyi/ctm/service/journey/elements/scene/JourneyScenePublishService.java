package cn.hanyi.ctm.service.journey.elements.scene;

import cn.hanyi.ctm.constant.SendManageTriggerType;
import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import cn.hanyi.ctm.dto.SendChannelConfigDto;
import cn.hanyi.ctm.dto.journey.JourneyTreeDto;
import cn.hanyi.ctm.entity.SendManage;
import cn.hanyi.ctm.entity.journey.*;
import cn.hanyi.ctm.repository.JourneyPublishRepository;
import cn.hanyi.ctm.repository.SendManageRepository;
import cn.hanyi.ctm.service.JourneyMapService;
import cn.hanyi.ctm.service.journey.IComponentElement;
import cn.hanyi.ctm.service.journey.IJourneyMapPublish;
import cn.hanyi.ctm.service.journey.elements.JourneyComponentPublishService;
import cn.hanyi.ctm.service.journey.elements.interaction.JourneyInteractionPublishService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.befun.auth.service.TreeConvertService;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.CustomDeepEmbeddedService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class JourneyScenePublishService extends CustomDeepEmbeddedService<JourneyPublish, JourneyPublishDto, JourneyPublishRepository>
        implements IComponentElement<JourneyPublish, JourneyPublishDto, JourneyPublishRepository>,
        IJourneyMapPublish<Journey, JourneyPublish, JourneyPublishRepository> {

    @Autowired
    private TreeConvertService treeConvertService;
    @Autowired
    private JourneyMapService journeyMapService;
    @Autowired
    private JourneyComponentPublishService journeyComponentPublishService;
    @Autowired
    private JourneyInteractionPublishService journeyInteractionPublishService;
    @Autowired
    private JourneySceneService journeySceneService;

    @Autowired
    private SendManageRepository sendManageRepository;

    @Override
    protected Object requireParent(long l) {
        return l;
    }

    @Override
    public JourneyComponentType componentType() {
        return JourneyComponentType.journey;
    }

    @Override
    public boolean publishComponent() {
        return true;
    }

    public List<JourneyPublish> findAllEntity(Long componentId) {
        return repository.findAll(((root, query, builder) -> builder.equal(root.get("componentId"), componentId)), Sort.by(Sort.Direction.ASC, "order"));
    }

    public List<JourneyPublishDto> findAll(Long componentId) {
        return Optional.ofNullable(treeConvertService.listToTree(findAllEntity(componentId), this::mapToDto)).orElse(new ArrayList<>());
    }

    /**
     * 构造格式一致的场景树，第一层为journeyMap
     */
    public List<JourneyTreeDto> journeyTree() {
        List<JourneyPublish> journeys = repository.findAll((root, query, builder) -> builder.equal(root.get("orgId"), TenantContext.getCurrentTenant()), Sort.by(Sort.Direction.ASC, "order"));
        if (CollectionUtils.isNotEmpty(journeys)) {
            Map<Long, JourneyTreeDto> rootMap = new HashMap<>();
            Set<Long> componentIds = new HashSet<>();
            Set<Long> journeyIds = new HashSet<>();
            Map<Long, JourneyTreeDto> childrenMap = journeys.stream().peek(i -> {
                componentIds.add(i.getComponentId());
                journeyIds.add(i.getId());
            }).collect(Collectors.toMap(JourneyPublish::getId, JourneyTreeDto::fromJourney));
            Map<Long, SendManage> sendManageMap = getSendManageMap(journeyIds);
            Map<Long, JourneyComponentPublish> componentPublishMap = getComponentPublishMap(componentIds);
            Map<Long, JourneyMap> journeyMapMap = getJourneyMapMap(componentPublishMap);
            journeys.forEach(i -> {
                JourneyComponentPublish component = componentPublishMap.get(i.getComponentId());
                if (component != null) {
                    JourneyMap journeyMap = journeyMapMap.get(component.getJourneyMapId());
                    if (journeyMap != null) {
                        JourneyTreeDto root = rootMap.get(journeyMap.getId());
                        if (root == null) {
                            root = JourneyTreeDto.fromJourneyMap(journeyMap);
                            rootMap.put(journeyMap.getId(), root);
                        }
                        JourneyTreeDto current = childrenMap.get(i.getId());
                        if (current != null) {
                            SendManage sendManage = sendManageMap.get(current.getId());
                            if (sendManage != null) {
                                current.setEnabled(sendManage.getEnable() != null && sendManage.getEnable());
                                current.setSendManageId(sendManage.getId());
                                if (CollectionUtils.isNotEmpty(sendManage.getChannel())) {
                                    current.setSendManageChannelTypes(sendManage.getChannel().stream().map(SendChannelConfigDto::getType).collect(Collectors.toList()));
                                }
                            }
                            if (i.getParentId() == null || i.getParentId() == 0L) {
                                root.getChildren().add(current);
                            } else {
                                JourneyTreeDto parent = childrenMap.get(i.getParentId());
                                if (parent != null) {
                                    parent.getChildren().add(current);
                                }
                            }
                        }
                    }
                }
            });
            return rootMap.values().stream().sorted(Comparator.comparing(JourneyTreeDto::getModifyTime).reversed()).collect(Collectors.toList());
        }
        return null;
    }

    private Map<Long, SendManage> getSendManageMap(Set<Long> journeyId) {
        Map<Long, SendManage> sendManageMap = new HashMap<>();
        List<Long> ids = journeyId.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (!ids.isEmpty()) {
            List<SendManage> sendManageList = sendManageRepository.findByOrgIdAndTriggerTypeAndTriggerIdIn(TenantContext.getCurrentTenant(), SendManageTriggerType.JOURNEY, journeyId);
            if (CollectionUtils.isNotEmpty(sendManageList)) {
                sendManageList.forEach(i -> sendManageMap.put(i.getTriggerId(), i));
            }
        }
        return sendManageMap;
    }

    private Map<Long, JourneyComponentPublish> getComponentPublishMap(Set<Long> componentIds) {
        Map<Long, JourneyComponentPublish> componentPublishMap = null;
        List<Long> ids = componentIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (!ids.isEmpty()) {
            componentPublishMap = journeyComponentPublishService.getGroupMapByIds(ids, JourneyComponentPublish::getId, Function.identity());
        }
        if (componentPublishMap == null) {
            componentPublishMap = new HashMap<>();
        }
        return componentPublishMap;
    }

    private Map<Long, JourneyMap> getJourneyMapMap(Map<Long, JourneyComponentPublish> componentPublishMap) {
        Map<Long, JourneyMap> journeyMapMap = null;
        Set<Long> journeyMapIds = new HashSet<>();
        componentPublishMap.values().forEach(i -> journeyMapIds.add(i.getJourneyMapId()));
        List<Long> ids = journeyMapIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (!ids.isEmpty()) {
            journeyMapMap = journeyMapService.getGroupMapByIds(ids, JourneyMap::getId, Function.identity());
        }
        if (journeyMapMap == null) {
            journeyMapMap = new HashMap<>();
        }
        return journeyMapMap;
    }

    @Override
    public JourneyPublish newInstance() {
        return new JourneyPublish();
    }

    @Override
    public List<Journey> getSourceList(Long parentId) {
        return journeySceneService.findAllEntity(parentId);
    }

    @Override
    public List<JourneyPublish> getTargetList(Long parentId) {
        return findAllEntity(parentId);
    }
}
