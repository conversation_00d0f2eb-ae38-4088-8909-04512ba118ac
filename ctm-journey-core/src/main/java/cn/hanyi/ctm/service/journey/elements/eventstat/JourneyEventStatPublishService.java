package cn.hanyi.ctm.service.journey.elements.eventstat;

import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import cn.hanyi.ctm.dto.journey.JourneyWarningDto;
import cn.hanyi.ctm.entity.journey.ElementEventStat;
import cn.hanyi.ctm.entity.journey.ElementEventStatPublish;
import cn.hanyi.ctm.entity.journey.ElementEventStatPublishDto;
import cn.hanyi.ctm.repository.ElementEventStatPublishRepository;
import cn.hanyi.ctm.service.JourneyWarningService;
import cn.hanyi.ctm.service.journey.IComponentElement;
import cn.hanyi.ctm.service.journey.IJourneyMapPublish;
import org.apache.commons.collections.CollectionUtils;
import org.befun.core.entity.BaseEntity;
import org.befun.core.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class JourneyEventStatPublishService extends BaseService<ElementEventStatPublish, ElementEventStatPublishDto, ElementEventStatPublishRepository>
        implements IComponentElement<ElementEventStatPublish, ElementEventStatPublishDto, ElementEventStatPublishRepository>,
        IJourneyMapPublish<ElementEventStat, ElementEventStatPublish, ElementEventStatPublishRepository> {

    @Autowired
    private JourneyWarningService journeyWarningService;

    @Override
    public boolean publishComponent() {
        return true;
    }

    @Override
    public JourneyComponentType componentType() {
        return JourneyComponentType.event_stat;
    }

    @Override
    public void afterMapToDto(List<ElementEventStatPublish> entity, List<ElementEventStatPublishDto> dto) {
        super.afterMapToDto(entity, dto);
        Set<Long> ids = new HashSet<>();
        Set<Long> eventRuleIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(dto)) {
            dto.forEach(i -> {
                Optional.ofNullable(i.getId()).ifPresent(ids::add);
                Optional.ofNullable(i.getEventRuleId()).ifPresent(eventRuleIds::add);
            });
        }
        Map<Long, JourneyWarningDto> warningMap = journeyWarningService.groupByRelationId(JourneyComponentType.event_stat, ids, true);
        Map<Long, String> eventRuleMap = journeyWarningService.getEventRuleTitleByIds(eventRuleIds);
        if (CollectionUtils.isNotEmpty(dto)) {
            dto.forEach(i -> {
                Optional.ofNullable(warningMap.get(i.getId())).ifPresent(i::setWarning);
                Optional.ofNullable(eventRuleMap.get(i.getEventRuleId())).ifPresent(i::setEventRuleName);
            });
        }
    }

    @Override
    public ElementEventStatPublish newInstance() {
        return new ElementEventStatPublish();
    }

    @Override
    public void afterCopyToPublish(ElementEventStatPublish target) {
        journeyWarningService.publish(componentType(), target.getId());
    }

    @Override
    public void beforeDeletePublishList(List<ElementEventStatPublish> deleteList) {
        Set<Long> ids = deleteList.stream().map(BaseEntity::getId).collect(Collectors.toSet());
        journeyWarningService.deletePublish(JourneyComponentType.event_stat, ids);
    }
}
