package cn.hanyi.ctm.service.journey.elements.curve;

import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import cn.hanyi.ctm.entity.journey.ElementCurve;
import cn.hanyi.ctm.entity.journey.ElementCurvePublish;
import cn.hanyi.ctm.entity.journey.ElementCurvePublishDto;
import cn.hanyi.ctm.repository.ElementCurvePublishRepository;
import cn.hanyi.ctm.repository.ExperienceIndicatorPublishRepository;
import cn.hanyi.ctm.service.journey.IComponentElement;
import cn.hanyi.ctm.service.journey.IJourneyMapPublish;
import org.befun.core.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class JourneyCurvePublishService extends BaseService<ElementCurvePublish, ElementCurvePublishDto, ElementCurvePublishRepository>
        implements
        IComponentElement<ElementCurvePublish, ElementCurvePublishDto, ElementCurvePublishRepository>,
        IJourneyMapPublish<ElementCurve, ElementCurvePublish, ElementCurvePublishRepository> {


    @Override
    public boolean publishComponent() {
        return true;
    }

    @Override
    public JourneyComponentType componentType() {
        return JourneyComponentType.curve;
    }

    @Override
    public ElementCurvePublish newInstance() {
        return new ElementCurvePublish();
    }

}
