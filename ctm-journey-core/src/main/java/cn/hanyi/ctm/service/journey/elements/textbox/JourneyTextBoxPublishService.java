package cn.hanyi.ctm.service.journey.elements.textbox;

import cn.hanyi.ctm.constant.journeymap.ElementTextBoxType;
import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import cn.hanyi.ctm.entity.journey.ElementTextBox;
import cn.hanyi.ctm.entity.journey.ElementTextBoxPublish;
import cn.hanyi.ctm.entity.journey.ElementTextBoxPublishDto;
import cn.hanyi.ctm.repository.ElementTextBoxPublishRepository;
import cn.hanyi.ctm.service.journey.IComponentElement;
import cn.hanyi.ctm.service.journey.IJourneyMapPublish;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.service.BaseService;
import org.befun.core.utils.JsonHelper;
import org.befun.core.utils.RegHelper;
import org.befun.extension.service.FileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
@Slf4j
public class JourneyTextBoxPublishService extends BaseService<ElementTextBoxPublish, ElementTextBoxPublishDto, ElementTextBoxPublishRepository>
        implements IComponentElement<ElementTextBoxPublish, ElementTextBoxPublishDto, ElementTextBoxPublishRepository>,
        IJourneyMapPublish<ElementTextBox, ElementTextBoxPublish, ElementTextBoxPublishRepository> {

    @Autowired
    private JourneyTextBoxService journeyTextBoxService;

    @Autowired
    private FileService fileService;

    @Override
    public JourneyComponentType componentType() {
        return JourneyComponentType.textbox;
    }

    @Override
    public boolean publishComponent() {
        return true;
    }

    @Override
    public void afterMapToDto(List<ElementTextBoxPublish> entity, List<ElementTextBoxPublishDto> dto) {
    }

    @Override
    public ElementTextBoxPublish newInstance() {
        return new ElementTextBoxPublish();
    }

    @Override
    public List<ElementTextBox> getSourceList(Long parentId) {
        return journeyTextBoxService.findAllEntity(parentId);
    }

    @Override
    public List<ElementTextBoxPublish> getTargetList(Long parentId) {
        return findAllEntity(parentId);
    }

    @Override
    public void beforeCopyToPublish(ElementTextBox source, ElementTextBoxPublish target) {
        List<String> sourceContent = source.getContent();
        List<String> targetContent = target.getContent();

        try {
            if (ElementTextBoxType.storyboard == source.getType() && sourceContent.size() == targetContent.size() && !sourceContent.isEmpty()) {
                for (int i = 0; i < sourceContent.size(); i++) {
                    String content = sourceContent.get(i);
                    if (content != null && !content.equals(targetContent.get(i))) {
                        String fileUrl = targetContent.get(i);
                        Map<String, Object> targetUrlMap = JsonHelper.toMap(fileUrl);
                        Optional.ofNullable(targetUrlMap.get("url")).flatMap(url -> Optional.ofNullable(RegHelper.parseFileName(url.toString()))).ifPresent(fileName -> {
                            log.info("delete file: {}", fileName);
                            fileService.delete(fileName);
                        });
                    }
                }
            }
        } catch (Exception e) {
            log.error("delete file error: {}", e.getMessage());
        }

    }
}
