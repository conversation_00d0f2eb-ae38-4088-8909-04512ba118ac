package cn.hanyi.ctm.service.journey.elements.curve;

import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import cn.hanyi.ctm.dto.journey.JourneyComponentCreateDto;
import cn.hanyi.ctm.entity.journey.ElementCurve;
import cn.hanyi.ctm.entity.journey.ElementCurveDto;
import cn.hanyi.ctm.entity.journey.JourneyComponent;
import cn.hanyi.ctm.entity.journey.JourneyDto;
import cn.hanyi.ctm.repository.ElementCurveRepository;
import cn.hanyi.ctm.service.journey.elements.scene.JourneySceneService;
import cn.hanyi.ctm.service.journey.IComponentJourneyElement;
import cn.hanyi.ctm.service.journey.elements.JourneyComponentService;
import org.apache.commons.collections.CollectionUtils;
import org.befun.core.service.CustomDeepEmbeddedService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class JourneyCurveService extends CustomDeepEmbeddedService<ElementCurve, ElementCurveDto, ElementCurveRepository>
        implements IComponentJourneyElement<ElementCurve, ElementCurveDto, ElementCurveRepository> {

    @Autowired
    private JourneyComponentService journeyComponentService;
    @Autowired
    private JourneySceneService journeySceneService;

    @Override
    public JourneyComponentType componentType() {
        return JourneyComponentType.curve;
    }

    /**
     * 添加组件后，给每个场景都添加一个情绪曲线
     */
    @Override
    public List<ElementCurveDto> addByComponent(Long orgId, Long journeyMapId, Long componentId, JourneyComponentCreateDto dto) {
        List<JourneyDto> journeys = journeySceneService.findAllByJourneyMapId(journeyMapId);
        if (CollectionUtils.isEmpty(journeys)) {
            return null;
        }
        List<ElementCurve> add = journeys.stream().map(i -> new ElementCurve(componentId, orgId, i.getId())).collect(Collectors.toList());
        repository.saveAll(add);
        return findAll(componentId);
    }

    /**
     * 添加场景后，每个情绪曲线组件都需要添加一项
     */
    @Override
    public void addByJourney(Long orgId, Long journeyMapId, Long journeyId) {
        List<JourneyComponent> journeyComponents = journeyComponentService.getComponentsByType(journeyMapId, JourneyComponentType.curve);
        if (CollectionUtils.isEmpty(journeyComponents)) {
            return;
        }
        List<ElementCurve> add = journeyComponents.stream().map(i -> new ElementCurve(i.getId(), orgId, journeyId)).collect(Collectors.toList());
        repository.saveAll(add);
    }
}
