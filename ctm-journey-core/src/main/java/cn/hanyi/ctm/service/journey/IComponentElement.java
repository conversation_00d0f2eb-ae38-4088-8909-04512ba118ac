package cn.hanyi.ctm.service.journey;

import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import cn.hanyi.ctm.dto.journey.JourneyComponentCreateDto;
import org.apache.commons.collections.CollectionUtils;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.entity.BaseEntity;
import org.befun.core.repository.ResourceRepository;

import java.util.ArrayList;
import java.util.List;

/**
 * 组件元素的公共功能
 * 1 通过组件id,查询所元素
 * 2 删除组件时，通过组件id,删除所有的元素
 */
public interface IComponentElement<E extends BaseEntity, D extends BaseEntityDTO<E>, R extends ResourceRepository<E, Long>> {

    R getRepository();

    JourneyComponentType componentType();

    List<D> mapToDto(List<E> entity);

    void afterMapToDto(List<E> entity, List<D> dto);

    default boolean publishComponent() {
        return false;
    }

    /**
     * 通过组件id，查询所有的组件元素，并转换为dto
     */
    default List<D> findAll(Long componentId) {
        List<E> entity = findAllEntity(componentId);
        List<D> dto = mapToDto(entity);
        afterMapToDto(entity, dto);
        return dto;
    }

    /**
     * 通过组件id，查询所有的组件元素
     */
    default List<E> findAllEntity(Long componentId) {
        return getRepository().findAll(((root, query, builder) -> builder.equal(root.get("componentId"), componentId)));
    }

    /**
     * 删除组件后，同时删除所有组件元素
     */
    default void deleteByComponent(Long componentId) {
        List<E> entity = findAllEntity(componentId);
        if (CollectionUtils.isNotEmpty(entity)) {
            getRepository().deleteAll(entity);
        }
    }

    /**
     * 新增组件后，部分组件元素（textbox,curve）也要立即创建
     */
    default List<D> addByComponent(Long orgId, Long journeyMapId, Long componentId, JourneyComponentCreateDto dto) {
        return new ArrayList<>();
    }
}
