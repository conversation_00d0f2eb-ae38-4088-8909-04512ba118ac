package cn.hanyi.ctm.service.persona.component;

import cn.hanyi.ctm.constant.persona.PersonaComponentType;
import cn.hanyi.ctm.dto.persona.component.PersonaComponentSliderBarDto;
import cn.hanyi.ctm.dto.persona.ext.CustomerPersonaComponentExtDto;
import cn.hanyi.ctm.entity.persona.CustomerPersona;
import cn.hanyi.ctm.entity.persona.CustomerPersonaComponent;
import org.befun.core.utils.JsonHelper;
import org.springframework.stereotype.Service;

import java.util.function.BiConsumer;
import java.util.function.Function;

@Service
public class PersonaComponentSliderBarService implements IPersonaComponent<PersonaComponentSliderBarDto> {

    @Override
    public void setDefaultContent(CustomerPersona persona, CustomerPersonaComponent entity) {
        entity.setContent(JsonHelper.toJson(PersonaComponentSliderBarDto.defaultSliderBar()));
    }

    @Override
    public PersonaComponentType type() {
        return PersonaComponentType.sliderBar;
    }

    @Override
    public Class<PersonaComponentSliderBarDto> componentClass() {
        return PersonaComponentSliderBarDto.class;
    }

    @Override
    public <D extends CustomerPersonaComponentExtDto> Function<D, PersonaComponentSliderBarDto> getComponent() {
        return CustomerPersonaComponentExtDto::getSliderBar;
    }

    @Override
    public <D extends CustomerPersonaComponentExtDto> BiConsumer<D, PersonaComponentSliderBarDto> setComponent() {
        return CustomerPersonaComponentExtDto::setSliderBar;
    }
}
