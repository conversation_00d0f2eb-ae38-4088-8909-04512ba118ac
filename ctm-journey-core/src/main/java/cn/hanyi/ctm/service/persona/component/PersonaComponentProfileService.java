package cn.hanyi.ctm.service.persona.component;

import cn.hanyi.ctm.constant.persona.PersonaComponentType;
import cn.hanyi.ctm.dto.persona.component.PersonaComponentImageDto;
import cn.hanyi.ctm.dto.persona.component.PersonaComponentProfileDto;
import cn.hanyi.ctm.dto.persona.ext.CustomerPersonaComponentExtDto;
import cn.hanyi.ctm.entity.persona.CustomerPersona;
import cn.hanyi.ctm.entity.persona.CustomerPersonaComponent;
import cn.hanyi.ctm.properties.PersonaProperties;
import cn.hanyi.ctm.repository.CustomerPersonComponentRepository;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.Function;

@Service
public class PersonaComponentProfileService implements IPersonaComponent<PersonaComponentProfileDto> {

    @Autowired
    private CustomerPersonComponentRepository customerPersonComponentRepository;

    @Override
    public void setDefaultContent(CustomerPersona persona, CustomerPersonaComponent entity) {
        entity.setContent(JsonHelper.toJson(new PersonaComponentProfileDto(persona.getName())));
    }

    @Override
    public PersonaComponentType type() {
        return PersonaComponentType.profile;
    }

    @Override
    public Class<PersonaComponentProfileDto> componentClass() {
        return PersonaComponentProfileDto.class;
    }

    @Override
    public <D extends CustomerPersonaComponentExtDto> Function<D, PersonaComponentProfileDto> getComponent() {
        return CustomerPersonaComponentExtDto::getProfile;
    }

    @Override
    public <D extends CustomerPersonaComponentExtDto> BiConsumer<D, PersonaComponentProfileDto> setComponent() {
        return CustomerPersonaComponentExtDto::setProfile;
    }

    public PersonaComponentProfileDto updateProfileName(Long orgId, Long personaId, String name) {
        return customerPersonComponentRepository.findFirstByOrgIdAndPersonaIdAndType(orgId, personaId, PersonaComponentType.profile).map(entity -> {
            PersonaComponentProfileDto profile = parse(entity.getContent());
            if (profile != null) {
                profile.setName(name);
                entity.setContent(format(profile));
                customerPersonComponentRepository.save(entity);
                return profile;
            }
            return null;
        }).orElse(null);
    }
}
