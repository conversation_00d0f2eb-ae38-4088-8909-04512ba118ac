package cn.hanyi.ctm.entity.journey;

import cn.hanyi.ctm.constant.journeymap.ElementTextBoxType;
import com.fasterxml.jackson.annotation.JsonBackReference;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;

@Table(name = "element_textbox")
@Entity
@Getter
@Setter
@NoArgsConstructor
@DtoClass(includeAllFields = true)
public class ElementTextBox extends ElementTextBoxBase {


    public ElementTextBox(Long componentId, Long orgId, ElementTextBoxType type, List<String> content) {
        this.setComponentId(componentId);
        this.setOrgId(orgId);
        this.setType(type);
        this.setContent(content);
    }

}