package cn.hanyi.ctm.entity.persona;

import cn.hanyi.ctm.dto.persona.ext.CustomerPersonaExtDto;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.constant.EntityScopeStrategyType;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.EnterpriseModifyAware;
import org.befun.core.entity.EnterpriseOwnerAware;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.entity.annotation.SoftDelete;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Getter
@Setter
@Table(name = "customer_persona")
@Where(clause = "deleted=0")
@SoftDelete(propertyType = Integer.class, deleteValue = "1")
@EntityScopeStrategy(value = EntityScopeStrategyType.OWNER_GROUP_CORPORATION, resource = "PERSONA", groupResource = "PERSONA_GROUP")
@DtoClass(superClass = CustomerPersonaExtDto.class)
public class CustomerPersona extends EnterpriseEntity implements EnterpriseOwnerAware, EnterpriseModifyAware {

    @DtoProperty
    @Column(name = "user_id")
    private Long userId;

    @DtoProperty
    @Column(name = "group_id")
    private Long groupId;

    @DtoProperty(jsonView = ResourceViews.Basic.class)
    private String avatar;

    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    @Column(name = "name")
    private String name;

    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @Column(name = "professional")
    private String professional;

    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    @Column(name = "journey_map_titles")
    private String journeyMapTitles;

    @DtoProperty
    @Column(name = "modify_user_id")
    private Long modifyUserId;

    @Column(name = "deleted")
    private Integer deleted = 0;

}