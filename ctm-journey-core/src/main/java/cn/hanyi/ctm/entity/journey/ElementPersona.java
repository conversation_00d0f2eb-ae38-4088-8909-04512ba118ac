package cn.hanyi.ctm.entity.journey;

import cn.hanyi.ctm.dto.journey.ext.ElementPersonaExtDto;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;

import javax.persistence.Entity;
import javax.persistence.Table;

@Table(name = "element_persona")
@Entity
@Getter
@Setter
@DtoClass(superClass = ElementPersonaExtDto.class)
public class ElementPersona extends ElementPersonaBase {

}