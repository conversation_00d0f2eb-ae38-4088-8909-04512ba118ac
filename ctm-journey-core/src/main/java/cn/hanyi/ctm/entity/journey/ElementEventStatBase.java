package cn.hanyi.ctm.entity.journey;

import cn.hanyi.ctm.dto.journey.ext.IElementJourneyId;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;

@Getter
@Setter
@MappedSuperclass
public class ElementEventStatBase extends EnterpriseEntity implements IElementJourneyId {

    @Schema(hidden = true, description = "旅程id")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "journey_map_id")
    private Long journeyMapId;

    @Schema(description = "组件id")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "component_id")
    private Long componentId;

    @Schema(description = "场景id")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "journey_id")
    private Long journeyId;

    @Schema(description = "预警规则id")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "event_rule_id")
    private Long eventRuleId;

    @Schema(description = "统计类型：1 全部 2 待处理")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "stat_type")
    private Integer statType;


}