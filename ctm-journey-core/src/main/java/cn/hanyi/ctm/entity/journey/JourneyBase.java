package cn.hanyi.ctm.entity.journey;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;


@Getter
@Setter
@MappedSuperclass
public class JourneyBase extends EnterpriseEntity {

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "component_id")
    private Long componentId;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "journey_name")
    private String journeyName;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "color")
    private String color;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "parent_id")
    private Long parentId = 0L;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "experience_indicator_id")
    private Long experienceIndicatorId = 0L;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "experience_interaction_id")
    private Long experienceInteractionId = 0L;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "is_leaf")
    private Integer isLeaf = 1;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "`order`")
    private Integer order = 0;
}