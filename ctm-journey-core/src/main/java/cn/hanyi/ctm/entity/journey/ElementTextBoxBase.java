package cn.hanyi.ctm.entity.journey;

import cn.hanyi.ctm.constant.journeymap.ElementTextBoxType;
import com.fasterxml.jackson.annotation.JsonView;
import java.util.List;
import javax.persistence.Convert;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.converter.ListConverter;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;

@Getter
@Setter
@MappedSuperclass
public class ElementTextBoxBase extends EnterpriseEntity {

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "component_id")
    private Long componentId;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    private ElementTextBoxType type;

    @JsonView(ResourceViews.Basic.class)
    @Convert(converter = ListConverter.class)
    @Column(name = "content")
    private List<String> content = List.of();
}