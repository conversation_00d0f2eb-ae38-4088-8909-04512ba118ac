package cn.hanyi.ctm.entity.journey;

import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import cn.hanyi.ctm.dto.journey.ext.JourneyComponentExtDto;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;

@Table(name = "journey_component")
@Entity
@Getter
@Setter
@NoArgsConstructor
@DtoClass(includeAllFields = true, superClass = JourneyComponentExtDto.class, superClassParameterizedType = true)
public class JourneyComponent extends JourneyComponentBase {

    public JourneyComponent(Long orgId, Long journeyMapId, Integer order, String title, JourneyComponentType type) {
        this.setOrgId(orgId);
        this.setOrder(order);
        this.setJourneyMapId(journeyMapId);
        this.setTitle(title);
        this.setType(type);
    }

    public JourneyComponent(Long orgId, Long parentId, Long journeyMapId, Integer order, String title, JourneyComponentType type, Long relationId) {
        this.setOrgId(orgId);
        this.setParentId(parentId);
        this.setOrder(order);
        this.setJourneyMapId(journeyMapId);
        this.setTitle(title);
        this.setType(type);
        this.setRelationId(relationId);
    }
}