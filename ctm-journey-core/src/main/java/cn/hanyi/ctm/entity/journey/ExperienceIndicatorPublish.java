package cn.hanyi.ctm.entity.journey;

import cn.hanyi.ctm.dto.journey.ext.ExperienceIndicatorPublishExtDto;
import com.fasterxml.jackson.annotation.JsonBackReference;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Table(name = "experience_indicator_publish")
@Entity
@Getter
@Setter
@DtoClass(includeAllFields = true, superClass = ExperienceIndicatorPublishExtDto.class)
public class ExperienceIndicatorPublish extends ExperienceIndicatorBase {
}