package cn.hanyi.ctm.entity.journey;

import lombok.*;
import org.befun.core.dto.annotation.DtoClass;

import javax.persistence.Entity;
import javax.persistence.Table;

@Table(name = "element_curve")
@Entity
@Getter
@Setter
@NoArgsConstructor
@DtoClass(includeAllFields = true)
public class ElementCurve extends ElementCurveBase {

    public ElementCurve(Long componentId, Long orgId, Long journeyId) {
        this.setComponentId(componentId);
        this.setOrgId(orgId);
        this.setJourneyId(journeyId);
        this.initElementCurveDynamicEmotion();
    }

}