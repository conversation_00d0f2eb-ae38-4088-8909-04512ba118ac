package cn.hanyi.ctm.entity.journey;

import cn.hanyi.ctm.dto.journey.ext.JourneyPublishExtDto;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.annotation.EntityScopeStrategy;

import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Getter
@Setter
@Table(name = "journey_publish")
@DtoClass(includeAllFields = true, superClass = JourneyPublishExtDto.class, superClassParameterizedType = true)
public class JourneyPublish extends JourneyBase {
}