package cn.hanyi.ctm.entity.journey;

import cn.hanyi.ctm.dto.journey.ext.JourneyMapExtDto;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.constant.EntityScopeStrategyType;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Getter
@Setter
@Table(name = "journey_map")
@EntityScopeStrategy(value = EntityScopeStrategyType.OWNER_GROUP_CORPORATION, resource = "JOURNEY", groupResource = "JOURNEY_GROUP")
@DtoClass(includeAllFields = true, superClass = JourneyMapExtDto.class)
public class JourneyMap extends EnterpriseEntity {

    @DtoProperty
    @Column(name = "user_id")
    private Long userId;

    @DtoProperty
    @Column(name = "group_id")
    private Long groupId;

    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    @Column(name = "title")
    private String title = "";

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "cover")
    private String cover = "";

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "default_date_filter")
    private String defaultDateFilter = "last_one_month";

    @DtoProperty
    @Column(name = "modify_user_id")
    private Long modifyUserId;

    @JsonIgnore
    @Column(name = "is_delete")
    private Integer isDelete = 0;

}