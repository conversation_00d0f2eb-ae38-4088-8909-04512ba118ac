package cn.hanyi.ctm.entity.journey;

import cn.hanyi.ctm.dto.journey.ext.ElementLinkerExtDto;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;

import javax.persistence.Entity;
import javax.persistence.Table;

@Table(name = "element_linker")
@Entity
@Getter
@Setter
@DtoClass(superClass = ElementLinkerExtDto.class)
public class ElementLinker extends ElementLinkerBase {

    public ElementLinker() {
    }

    public ElementLinker(Long orgId, Long journeyMapId, Long componentId, Long journeyId, String name, String linkerType, String linkerConfig) {
        setOrgId(orgId);
        setJourneyMapId(journeyMapId);
        setComponentId(componentId);
        setJourneyId(journeyId);
        setName(name);
        setLinkerType(linkerType);
        setLinkerConfig(linkerConfig);
    }
}