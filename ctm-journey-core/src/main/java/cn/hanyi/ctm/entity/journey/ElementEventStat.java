package cn.hanyi.ctm.entity.journey;

import cn.hanyi.ctm.dto.journey.ext.ElementEventStatExtDto;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;

import javax.persistence.Entity;
import javax.persistence.Table;

@Table(name = "element_event_stat")
@Entity
@Getter
@Setter
@DtoClass(includeAllFields = true, superClass = ElementEventStatExtDto.class)
public class ElementEventStat extends ElementEventStatBase {

    public ElementEventStat() {
    }

    public ElementEventStat(Long orgId, Long journeyMapId, Long componentId, Long journeyId, Long eventRuleId, Integer statType) {
        setOrgId(orgId);
        setJourneyMapId(journeyMapId);
        setComponentId(componentId);
        setJourneyId(journeyId);
        setEventRuleId(eventRuleId);
        setStatType(statType);
    }
}