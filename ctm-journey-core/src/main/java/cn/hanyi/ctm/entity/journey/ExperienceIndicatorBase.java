package cn.hanyi.ctm.entity.journey;

import cn.hanyi.ctm.constant.ExperienceInteractionStatus;
import cn.hanyi.ctm.dto.journey.CalculatingFilterDto;
import cn.hanyi.ctm.dto.journey.ext.IElementJourneyId;
import cn.hanyi.ctm.dto.journey.ext.IExperienceMatrix;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.converter.ListConverter;
import org.befun.core.converter.LongRawListConverter;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.hibernate.types.JsonColumn;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.util.List;

@Getter
@Setter
@MappedSuperclass
public class ExperienceIndicatorBase extends EnterpriseEntity implements IElementJourneyId, IExperienceMatrix {

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "component_id")
    private Long componentId;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "journey_id")
    private Long journeyId;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "indicator_name")
    private String indicatorName;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "target_value")
    private Double targetValue;

    /**
     * average NSS NPS WeightAvg percent(1.8.0新增)
     */
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "calculating_method")
    @Schema(description = "计算方式：加权平均 WeightAvg，平均 average，净推荐度 NPS，净满意度 NSS，选项占比 percent")
    private String calculatingMethod;


    /**
     * 计算过滤条件 (1.8.4新增)
     */
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "calculating_filter")
    @Type(type = JsonColumn.TYPE)
    @Schema(description = "过滤方式： {'filter': [TypeFilterMethodDto,TypeFilterMethodDto], 'logicType':'AND'} ")
    private CalculatingFilterDto calculatingFilter;

    @JsonView(ResourceViews.Basic.class)
    @Convert(converter = LongRawListConverter.class)
    @Column(name = "sids")
    @Schema(description = "问卷的id")
    private List<Long> sids = List.of();

    @JsonView(ResourceViews.Basic.class)
    @Convert(converter = ListConverter.class)
    @Column(name = "survey_titles")
    @Schema(description = "问卷的标题，数组长度和sids一致")
    private List<String> surveyTitles = List.of();

    @JsonView(ResourceViews.Basic.class)
    @Convert(converter = LongRawListConverter.class)
    @Column(name = "qids")
    @Schema(description = "问题的id，数组长度和sids一致")
    private List<Long> qids = List.of();

    //*** 1.7.7 增加矩阵量表的选项
    @JsonView(ResourceViews.Basic.class)
    @Type(type = JsonColumn.TYPE)
    @Column(name = "item_names")
    @Schema(description = "矩阵量表题的选项，数组长度和sids一致")
    private List<String> itemNames = List.of();

    @JsonView(ResourceViews.Basic.class)
    @Convert(converter = ListConverter.class)
    @Column(name = "questions")
    @Schema(description = "问题的标题，数组长度和sids一致")
    private List<String> questions = List.of();

    @JsonView(ResourceViews.Basic.class)
    @Convert(converter = LongRawListConverter.class)
    @Column(name = "weights")
    @Schema(description = "加权平均计算方式的权重，数组长度和sids一致")
    private List<Long> weights = List.of();

    // 1.8.0 增加选项占比
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "percent_item")
    @Schema(description = "选项占比计算方式的选项")
    private String percentItem;

    // 1.8.0 增加图表样式，(line|pie)
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "chart_style")
    @Schema(description = "图表样式：line 直线图, 饼图 pie")
    private String chartStyle;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "is_default_display")
    private Integer isDefaultDisplay;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "survey_source")
    private Integer surveySource;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "is_valid")
    @Enumerated(EnumType.ORDINAL)
    private ExperienceInteractionStatus isValid = ExperienceInteractionStatus.VALID;
}