package cn.hanyi.ctm.entity.journey;

import cn.hanyi.ctm.dto.journey.ext.ElementEventStatPublishExtDto;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;

import javax.persistence.Entity;
import javax.persistence.Table;

@Table(name = "element_event_stat_publish")
@Entity
@Getter
@Setter
@DtoClass(includeAllFields = true, superClass = ElementEventStatPublishExtDto.class)
public class ElementEventStatPublish extends ElementEventStatBase {
}