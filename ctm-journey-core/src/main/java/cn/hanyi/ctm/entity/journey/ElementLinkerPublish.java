package cn.hanyi.ctm.entity.journey;

import cn.hanyi.ctm.dto.journey.ext.ElementLinkerPublishExtDto;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;

import javax.persistence.Entity;
import javax.persistence.Table;

@Table(name = "element_linker_publish")
@Entity
@Getter
@Setter
@DtoClass(superClass = ElementLinkerPublishExtDto.class)
public class ElementLinkerPublish extends ElementLinkerBase {
}