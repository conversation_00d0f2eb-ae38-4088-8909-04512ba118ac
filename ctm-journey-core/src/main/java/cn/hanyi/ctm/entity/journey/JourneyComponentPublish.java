package cn.hanyi.ctm.entity.journey;

import cn.hanyi.ctm.dto.journey.ext.JourneyComponentPublishExtDto;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Table(name = "journey_component_publish")
@Entity
@Getter
@Setter
@DtoClass(includeAllFields = true, superClass = JourneyComponentPublishExtDto.class, superClassParameterizedType = true)
public class JourneyComponentPublish extends JourneyComponentBase {

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "date_filter")
    private String dateFilter;

    public String getDateFilter() {
        return dateFilter != null ? dateFilter : "last_one_month";
    }
}