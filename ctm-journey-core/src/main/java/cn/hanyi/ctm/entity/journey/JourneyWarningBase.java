package cn.hanyi.ctm.entity.journey;

import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import cn.hanyi.ctm.constant.warning.IndicatorCompare;
import cn.hanyi.ctm.constant.warning.IndicatorWarningFrequency;
import cn.hanyi.ctm.dto.journey.WarningReceiverDto;
import cn.hanyi.ctm.dto.journey.WarningReceiverDtoConverter;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;

@Getter
@Setter
@MappedSuperclass
public class JourneyWarningBase extends EnterpriseEntity {

    @Schema(description = "旅程id")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "journey_map_id")
    private Long journeyMapId;


    @Schema(description = "组件id")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "component_id")
    private Long componentId;

    @Schema(description = "场景id")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "journey_id")
    private Long journeyId;

    @Schema(description = "关联元素id: 预警规则是和体验指标或者事件统计关联的，这里的id则是指标id或者事件统计id")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "relation_id")
    private Long relationId;

    @Schema(description = "关联组件类型：事件统计 event_stat; 体验指标 experience_indicator")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "relation_type")
    @Enumerated(EnumType.STRING)
    private JourneyComponentType relationType;

    @Schema(description = "预警规则是否开启：否 是")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "enable_warning")
    private Boolean enableWarning;

    @Schema(description = "预警频率：天 月")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "warning_frequency")
    private IndicatorWarningFrequency warningFrequency;

    @Schema(description = "预警范围：0(全部)，1，3，6，7，30")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "warning_range")
    private Integer warningRange;

    @Schema(description = "预警规则：LE GT EQ LT GE")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "warning_compare")
    private IndicatorCompare warningCompare;

    @Schema(description = "设定值：整数或者小数，如果是百分比，先转换为小数在保存")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "warning_value")
    private Double warningValue;

    @Schema(description = "通知是否开启：0 否 1 是")
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "enable_notify")
    private Boolean enableNotify = true;

    @Convert(converter = WarningReceiverDtoConverter.class)
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "接受预警用户列表")
    private WarningReceiverDto receiver = new WarningReceiverDto();
}
