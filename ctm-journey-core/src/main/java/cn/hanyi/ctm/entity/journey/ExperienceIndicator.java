package cn.hanyi.ctm.entity.journey;

import cn.hanyi.ctm.dto.journey.ext.ExperienceIndicatorExtDto;
import com.fasterxml.jackson.annotation.JsonBackReference;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.Entity;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Table(name = "experience_indicator")
@Entity
@Getter
@Setter
@DtoClass(includeAllFields = true, superClass = ExperienceIndicatorExtDto.class)
public class ExperienceIndicator extends ExperienceIndicatorBase {
}