package cn.hanyi.ctm.entity.journey;

import cn.hanyi.ctm.dto.journey.ext.IElementJourneyId;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.EnterpriseEntity;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;

@Getter
@Setter
@MappedSuperclass
public class ElementLinkerBase extends EnterpriseEntity implements IElementJourneyId {

    @Column(name = "journey_map_id")
    private Long journeyMapId;

    @DtoProperty(description = "组件id")
    @Column(name = "component_id")
    private Long componentId;

    @DtoProperty(description = "场景id")
    @Column(name = "journey_id")
    private Long journeyId;

    @DtoProperty(description = "名称")
    @Column(name = "name")
    private String name;

    @DtoProperty(description = "连接器类型：baiduTongji,shenCe")
    @Column(name = "linker_type")
    private String linkerType;

    @Column(name = "linker_config")
    private String linkerConfig;

}