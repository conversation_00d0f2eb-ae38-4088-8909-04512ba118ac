package cn.hanyi.ctm.entity.journey;

import cn.hanyi.ctm.dto.journey.ext.JourneyMapGroupExtDto;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.constant.EntityScopeStrategyType;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.EnterpriseModifyAware;
import org.befun.core.entity.EnterpriseOwnerAware;
import org.befun.core.entity.EnterpriseOwnerEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Getter
@Setter
@Table(name = "journey_map_group")
@EntityScopeStrategy(value = EntityScopeStrategyType.OWNER, resource = "JOURNEY_GROUP", enableAdmin = false)
@DtoClass(includeAllFields = true, superClass = JourneyMapGroupExtDto.class)
public class JourneyMapGroup extends EnterpriseOwnerEntity implements EnterpriseOwnerAware, EnterpriseModifyAware {

    @DtoProperty(jsonView = ResourceViews.Basic.class)
    private String avatar;

    @DtoProperty(jsonView = ResourceViews.Basic.class)
    private String name;

    @DtoProperty
    @Column(name = "modify_user_id")
    private Long modifyUserId;

}