package cn.hanyi.ctm.entity.journey;

import cn.hanyi.ctm.constant.journeymap.JourneyComponentType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;


@Getter
@Setter
@MappedSuperclass
public class JourneyComponentBase extends EnterpriseEntity {

    @JsonView(ResourceViews.Basic.class)
    @JoinColumn(name = "parent_id")
    private Long parentId;

    @JsonView(ResourceViews.Basic.class)
    @JoinColumn(name = "journey_map_id")
    private Long journeyMapId;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    private JourneyComponentType type;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "relation_id")
    private Long relationId;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "title")
    private String title;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "`order`")
    private Integer order;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "is_display")
    private Integer isDisplay = 1;

    @JsonIgnore
    @Column(name = "is_delete")
    private Integer isDelete = 0;


}