package cn.hanyi.ctm.entity.persona;

import cn.hanyi.ctm.constant.persona.PersonaComponentType;
import cn.hanyi.ctm.dto.persona.ext.CustomerPersonaComponentExtDto;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;

@Entity
@Getter
@Setter
@Table(name = "customer_persona_component")
@EntityScopeStrategy
@DtoClass(superClass = CustomerPersonaComponentExtDto.class)
public class CustomerPersonaComponent extends EnterpriseEntity {

    @DtoProperty(ignore = true)
    @Column(name = "persona_id")
    private Long personaId;

    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @Column(name = "name")
    private String name;

    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @Enumerated(EnumType.STRING)
    private PersonaComponentType type;

    @DtoProperty(ignore = true)
    @Column(name = "content")
    private String content;

    @DtoProperty(ignore = true)
    @Column(name = "parameters")
    private String parameters;

    @DtoProperty(ignore = true)
    @Column(name = "layout")
    private String layout;

}