package cn.hanyi.ctm.entity.journey;

import cn.hanyi.ctm.dto.journey.ext.JourneyExtDto;
import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

@Entity
@Getter
@Setter
@Table(name = "journey")
@NoArgsConstructor
@DtoClass(includeAllFields = true, superClass = JourneyExtDto.class, superClassParameterizedType = true)
public class Journey extends JourneyBase {

    public Journey(Long orgId, Long componentId, String title, String color, int order) {
        this.setOrgId(orgId);
        this.setComponentId(componentId);
        this.setJourneyName(title);
        this.setColor(color);
        this.setOrder(order);
    }

    @Transient
    public List<Journey> children;

}