package cn.hanyi.ctm.entity.journey;

import cn.hanyi.ctm.dto.journey.ext.ElementPersonaPublishExtDto;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;

import javax.persistence.Entity;
import javax.persistence.Table;

@Table(name = "element_persona_publish")
@Entity
@Getter
@Setter
@DtoClass(superClass = ElementPersonaPublishExtDto.class)
public class ElementPersonaPublish extends ElementPersonaBase {
}