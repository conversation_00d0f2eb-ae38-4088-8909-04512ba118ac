package cn.hanyi.ctm.entity.journey;

import cn.hanyi.ctm.dto.journey.ext.IElementJourneyId;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.converter.ListConverter;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.EnterpriseEntity;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.MappedSuperclass;
import java.util.List;

@Getter
@Setter
@MappedSuperclass
public class ElementPersonaBase extends EnterpriseEntity implements IElementJourneyId {

    @Column(name = "journey_map_id")
    private Long journeyMapId;

    @DtoProperty(description = "组件id")
    @Column(name = "component_id")
    private Long componentId;

    @DtoProperty(description = "场景id")
    @Column(name = "journey_id")
    private Long journeyId;

    @DtoProperty(description = "内容")
    @Column(name = "content")
    private String content;

}