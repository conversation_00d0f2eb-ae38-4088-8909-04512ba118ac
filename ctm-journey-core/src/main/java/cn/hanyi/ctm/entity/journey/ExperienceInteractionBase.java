package cn.hanyi.ctm.entity.journey;

import cn.hanyi.ctm.constant.ExperienceInteractionStatus;
import cn.hanyi.ctm.constant.ExpireMomentDto;
import cn.hanyi.ctm.dto.journey.*;
import cn.hanyi.ctm.dto.journey.ext.IElementJourneyId;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.converter.ListConverter;
import org.befun.core.converter.LongRawListConverter;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.hibernate.types.JsonColumn;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.Type;

import javax.persistence.*;
import java.util.List;

@Getter
@Setter
@MappedSuperclass
public class ExperienceInteractionBase extends EnterpriseEntity implements IElementJourneyId {

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "component_id")
    private Long componentId;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "journey_id")
    private Long journeyId;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "interaction_name")
    private String interactionName;

    @JsonView(ResourceViews.Basic.class)
    @Convert(converter = LongRawListConverter.class)
    @Column(name = "interaction_sids")
    private List<Long> interactionSids = List.of();

    @JsonView(ResourceViews.Basic.class)
    @Convert(converter = ListConverter.class)
    @Column(name = "survey_titles")
    private List<String> surveyTitles = List.of();

    @JsonView(ResourceViews.Basic.class)
    @Convert(converter = ListConverter.class)
    @Column(name = "survey_urls")
    private List<String> surveyUrls = List.of();

    @JsonView(ResourceViews.Basic.class)
    @Convert(converter = PushMomentDtoConverter.class)
    @Column(name = "interaction_moment")
    private PushMomentDto interactionMoment;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "disturb_moment")
    @Schema(description = "勿扰模式")
    @Convert(converter = DisturbMomentDtoConverter.class)
    private DisturbMomentDto disturbMoment = new DisturbMomentDto();

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "expire_moment")
    @Schema(description = "链接有效期")
    @Type(type = JsonColumn.TYPE)
    private ExpireMomentDto expireMoment = new ExpireMomentDto();

    @JsonView(ResourceViews.Basic.class)
    private String content = "";

    @JsonView(ResourceViews.Basic.class)
    @Convert(converter = LongRawListConverter.class)
    @Column(name = "interactions_collectors")
    private List<Long> interactionsCollectors = List.of();

    @JsonView(ResourceViews.Basic.class)
    @Convert(converter = LongRawListConverter.class)
    @Column(name = "ctm_template_ids")
    private List<Long> ctmTemplateId = List.of();

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "config")
    @Type(type = JsonColumn.TYPE)
    private List<PushChannelConfigDto> config;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "is_valid")
    @Enumerated(EnumType.ORDINAL)
    private ExperienceInteractionStatus isValid = ExperienceInteractionStatus.VALID;

}
