package cn.hanyi.ctm.entity.journey;

import cn.hanyi.ctm.dto.journey.ElementCurveDynamicEmotionDto;
import cn.hanyi.ctm.dto.journey.ElementCurveDynamicEmotionDtoConverter;
import cn.hanyi.ctm.dto.journey.ext.IElementJourneyId;
import com.fasterxml.jackson.annotation.JsonView;
import java.util.ArrayList;
import java.util.List;
import javax.persistence.Convert;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.MappedSuperclass;

@Getter
@Setter
@MappedSuperclass
public class ElementCurveBase extends EnterpriseEntity implements IElementJourneyId {

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "component_id")
    private Long componentId;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "journey_id")
    private Long journeyId;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "description")
    private String description = "";

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "layout")
    private Integer layout = 3;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "color")
    private String color = "#c6c8cc";

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "emotion_type")
    private int emotionType = 2;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "enable_dynamic_emotion")
    private Boolean enableDynamicEmotion = false;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "dynamic_emotions", columnDefinition = "VARCHAR(1024)")
    @Convert(converter = ElementCurveDynamicEmotionDtoConverter.class)
    private List<ElementCurveDynamicEmotionDto> dynamicEmotions = new ArrayList<>();

    public void initElementCurveDynamicEmotion() {
        List<ElementCurveDynamicEmotionDto> emotionDtos = new ArrayList<>();
        emotionDtos.add(new ElementCurveDynamicEmotionDto("#FFC500", 0, null, null));
        emotionDtos.add(new ElementCurveDynamicEmotionDto("#FFC500", 1, null, null));
        emotionDtos.add(new ElementCurveDynamicEmotionDto("#FFC500", 2, null, null));
        emotionDtos.add(new ElementCurveDynamicEmotionDto("#E13B32", 3, null, null));
        emotionDtos.add(new ElementCurveDynamicEmotionDto("#E13B32", 4, null, null));
        this.dynamicEmotions = emotionDtos;
    }
}