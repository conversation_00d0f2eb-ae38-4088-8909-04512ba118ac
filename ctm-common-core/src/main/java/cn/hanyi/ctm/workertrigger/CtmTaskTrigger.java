package cn.hanyi.ctm.workertrigger;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CtmTaskTrigger implements ICtmTaskTrigger {

    @Autowired(required = false)
    private List<ICtmTaskConsumer> taskConsumers;

    @Override
    public List<ICtmTaskConsumer> getConsumers() {
        return taskConsumers;
    }
}
