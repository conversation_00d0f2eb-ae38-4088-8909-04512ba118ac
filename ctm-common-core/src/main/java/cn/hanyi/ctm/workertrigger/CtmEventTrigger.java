package cn.hanyi.ctm.workertrigger;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CtmEventTrigger implements ICtmEventTrigger {

    @Autowired(required = false)
    private List<ICtmEventConsumer> ctmEventConsumers;

    @Override
    public List<ICtmEventConsumer> getConsumers() {
        return ctmEventConsumers;
    }
}
