package cn.hanyi.ctm.dto;

import org.springframework.util.StringUtils;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Converter
public class StringListNewLineConverter implements AttributeConverter<List<String>, String> {
    public StringListNewLineConverter() {
    }

    public String getSplitChar() {
        return ",";
    }

    @Override
    public String convertToDatabaseColumn(List<String> stringList) {
        return stringList != null ? String.join(this.getSplitChar(), stringList) : null;
    }

    @Override
    public List<String> convertToEntityAttribute(String string) {
        return StringUtils.isEmpty(string) ? Collections.emptyList() : Arrays.asList(string.split(this.getSplitChar()));
    }
}
