package cn.hanyi.ctm.dto;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.view.ResourceViews;
import org.befun.core.utils.DateHelper;
import org.befun.task.constant.TaskExecutionTimedType;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.List;

@Getter
@Setter
public class BaseTimerDto {
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "定时类型")
    private TaskExecutionTimedType timedType;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "周列表")
    private List<DayOfWeek> weeks;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "月列表：1号～31号，-1 倒数1天")
    private List<Integer> days;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "时")
    private Integer hour;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "分")
    private Integer minute;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "开始时间：yyyy-MM-dd HH:mm:ss")
    private String startDate;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "结束时间：yyyy-MM-dd HH:mm:ss")
    private String endDate;

    public void check() {
        if (timedType == null) {
            throw new BadRequestException("定时类型不能为空");
        }
        if (timedType == TaskExecutionTimedType.WEEKLY) {
            if (CollectionUtils.isEmpty(weeks)) {
                throw new BadRequestException("定时类型为每周时，周列表不能为空");
            }
        } else if (timedType == TaskExecutionTimedType.MONTHLY) {
            if (CollectionUtils.isEmpty(days)) {
                throw new BadRequestException("定时类型为每月时，月列表不能为空");
            }
        }
        if (hour == null || hour < 0 || hour > 23) {
            throw new BadRequestException("小时不能为空，且必须在0～23之间");
        }
        if (minute == null || minute < 0 || minute > 59) {
            throw new BadRequestException("分钟不能为空，且必须在0～59之间");
        }
        LocalDateTime limitStart = DateHelper.parseDateTime(startDate);
        LocalDateTime limitEnd = DateHelper.parseDateTime(endDate);
        if (limitStart == null || limitEnd == null) {
            throw new BadRequestException("开始时间和结束时间不能为空，并且包含时分秒");
        } else if (limitStart.isAfter(limitEnd)) {
            throw new BadRequestException("开始时间必须小于结束时间");
        }
    }

    /**
     * [start,end)
     */
    public LocalDateTime findNextTime(LocalDateTime start, LocalDateTime end) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime limitStart = DateHelper.parseDateTime(startDate);
        LocalDateTime limitEnd = DateHelper.parseDateTime(endDate);
        if (limitStart == null || limitEnd == null || now.isBefore(limitStart) || now.isAfter(limitEnd)) {
            return null;
        }
        LocalDate today = now.toLocalDate();
        if (todayIsMatched(today)) {
            LocalDateTime time = today.atTime(hour, minute);
            if (!time.isBefore(start) && time.isBefore(end)) {
                return time;
            }
        }
        return null;
    }

    private boolean todayIsMatched(LocalDate today) {
        boolean compareTime = false;
        if (timedType == TaskExecutionTimedType.DAILY) {
            compareTime = true;
        } else if (timedType == TaskExecutionTimedType.WEEKLY) {
            compareTime = weeks.contains(today.getDayOfWeek());
        } else if (timedType == TaskExecutionTimedType.MONTHLY) {
            if (days.contains(today.getDayOfMonth())) {
                compareTime = true;
            } else if (days.contains(-1) && today.isEqual(today.with(TemporalAdjusters.lastDayOfMonth()))) {
                compareTime = true;
            }
        }
        return compareTime;
    }
}
