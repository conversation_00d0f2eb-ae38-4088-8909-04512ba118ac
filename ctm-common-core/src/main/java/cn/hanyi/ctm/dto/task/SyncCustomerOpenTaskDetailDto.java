//package cn.hanyi.ctm.dto.task;
//
//import lombok.Getter;
//import lombok.Setter;
//import org.befun.task.dto.PageableTaskDetailDto;
//
//@Getter
//@Setter
//public class SyncCustomerOpenTaskDetailDto extends PageableTaskDetailDto {
//    private Long orgId;
//    private int opt;    // 1 update 2 delete
//    private String customer;
//    private String deleteExternalUserId;
//
//    @Override
//    public int countComplete() {
//        return 1;
//    }
//}
