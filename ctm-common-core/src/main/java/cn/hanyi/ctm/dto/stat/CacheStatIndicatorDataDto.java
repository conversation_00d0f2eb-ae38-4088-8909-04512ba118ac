package cn.hanyi.ctm.dto.stat;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Getter
@Setter
@NoArgsConstructor
public class CacheStatIndicatorDataDto {
    public String day;             // 日期 2022-06-29
    public Boolean hasData = true;
    public Integer count;          // 每天的次数
    public Integer avgSum;         // 每天的总分
    public Integer npsGt8;         // 大于8的次数
    public Integer npsLt7;         // 小于7的次数

    public Map<String, ItemCount> itemCountMap = new HashMap<>(); // key: item name; value: item count

    public CacheStatIndicatorDataDto(String day) {
        this.day = day;
        this.hasData = false;
    }

    public void aggregate() {
        avgSum = 0;
        npsGt8 = 0;
        npsLt7 = 0;
        for (Map.Entry<String, ItemCount> entry : itemCountMap.entrySet()) {
            int score = Optional.ofNullable(entry.getValue()).map(ItemCount::getItemScore).orElse(0);
            int count = Optional.ofNullable(entry.getValue()).map(ItemCount::getItemCount).orElse(0);
            avgSum += Optional.ofNullable(entry.getValue()).map(ItemCount::getTotalScore).orElse(0);
            if (score < 7) {
                npsLt7 += count;
            }
            if (score > 8) {
                npsGt8 += count;
            }
        }

    }

    /**
     * 累加每个选项中的总数量
     */
    public int realCount() {
        int count = 0;
        for (Map.Entry<String, ItemCount> entry : itemCountMap.entrySet()) {
            count += Optional.ofNullable(entry.getValue()).map(ItemCount::getItemCount).orElse(0);
        }
        return count;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    public static class ItemCount {
        private String itemName;
        private int itemCount;
        private int itemScore;
        private int totalScore;

        public ItemCount(String itemName, int itemCount, int itemScore, int totalScore) {
            this.itemName = itemName;
            this.itemCount = itemCount;
            this.itemScore = itemScore;
            this.totalScore = totalScore;
            if (itemScore > 0) {
                this.totalScore = itemScore * itemCount;
            }
        }
    }


}
