package cn.hanyi.ctm.dto.task;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;

import java.util.List;


/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DataWarningDto extends BaseDTO {
    private Long eventId;
    private List<String> warningNotices;
    private List<DataWarningRuleDto> warningRules;
    private String actionUsername;
    private String actionTime;
    private String status;
    private List<DataWarningRemarkDto> remarks;

    // 兼容以前warningNotices 只是字符串的情况
    public void setWarningRules2(List<String> warningRules) {
        this.warningRules = warningRules.stream().map(d -> new DataWarningRuleDto(d, null)).collect(java.util.stream.Collectors.toList());
    }


}