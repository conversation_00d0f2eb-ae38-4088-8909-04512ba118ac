package cn.hanyi.ctm.dto.journey;

import org.apache.commons.lang3.StringUtils;
import org.befun.core.utils.JsonHelper;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter
public class DisturbMomentDtoConverter implements AttributeConverter<DisturbMomentDto, String> {

    @Override
    public String convertToDatabaseColumn(DisturbMomentDto dto) {
        if (dto == null) {
            return null;
        } else {
            return JsonHelper.toJson(dto);
        }
    }

    @Override
    public DisturbMomentDto convertToEntityAttribute(String dbData) {
        if (StringUtils.isEmpty(dbData)) {
            return new DisturbMomentDto();
        } else {
            return JsonHelper.toObject(dbData, DisturbMomentDto.class);
        }
    }
}