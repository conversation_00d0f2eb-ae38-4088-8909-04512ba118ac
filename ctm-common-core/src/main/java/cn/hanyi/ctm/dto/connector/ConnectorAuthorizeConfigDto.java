package cn.hanyi.ctm.dto.connector;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class ConnectorAuthorizeConfigDto {

    @JsonView(ResourceViews.Basic.class)
    private String none;
    @JsonView(ResourceViews.Basic.class)
    private String token;
    @JsonView(ResourceViews.Basic.class)
    private Key sign;

    @Setter
    @Getter
    public static class Key {
        @JsonView(ResourceViews.Basic.class)
        private String appKey;
        @JsonView(ResourceViews.Basic.class)
        private String appSecret;
    }
}
