package cn.hanyi.ctm.dto.journey;

import cn.hanyi.ctm.constant.SubTypeFilter;
import cn.hanyi.ctm.constant.TypeFilter;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

/***
 * ctm 体验指标过滤渠道
 *
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class TypeFilterSubFilterDto {
    @JsonView(ResourceViews.Basic.class)
    private Long id;
    @JsonView(ResourceViews.Basic.class)
    private TypeFilter type;
    @JsonView(ResourceViews.Basic.class)
    private String name;
    @JsonView(ResourceViews.Basic.class)
    private SubTypeFilter subType;
}
