package cn.hanyi.ctm.dto.customer;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.dto.userconfig.UserConfigCustomerQueryItemDto;
import org.befun.core.rest.view.ResourceViews;

import java.util.List;

@Getter
@Setter
public class CustomerDynamicSearchDto {

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "逻辑关系：and | or")
    private String logic = "and";

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "条件")
    private List<UserConfigCustomerQueryItemDto> items;

}
