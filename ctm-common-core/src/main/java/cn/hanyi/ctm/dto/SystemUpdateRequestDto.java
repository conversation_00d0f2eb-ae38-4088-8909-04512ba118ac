package cn.hanyi.ctm.dto;

import cn.hanyi.ctm.constant.SystemUpdateType;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Getter
@Setter
public class SystemUpdateRequestDto {

    @NotEmpty
    private String version;
    @NotEmpty
    private String secret;
    @NotNull
    private SystemUpdateType type;

    private String allData;

    @Valid
    private List<SystemUpdatePartRequestDto> parts;

}
