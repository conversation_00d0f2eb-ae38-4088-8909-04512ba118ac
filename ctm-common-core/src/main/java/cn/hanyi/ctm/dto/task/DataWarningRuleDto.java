package cn.hanyi.ctm.dto.task;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;

import java.util.List;


/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DataWarningRuleDto extends BaseDTO {
    private String title;
    private String level;
    private List<String> warningNotices;

    public DataWarningRuleDto(String title, String level) {
        this.title = title;
        this.level = level;
    }
}