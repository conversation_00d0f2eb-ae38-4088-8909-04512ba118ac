package cn.hanyi.ctm.dto.customer;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.view.ResourceViews;

import java.util.List;

@Getter
@Setter
public class CustomerSelectedDto {

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "选择客户的模式：0 客户id列表（默认）；1 客户搜索条件; 2 上传文件", required = true)
    private int selectType = 0;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "选择客户id列表")
    private List<Long> selectCustomerIds;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "选择客户的搜索条件")
    private CustomerCombineSearchDto selectConditions;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "上传客户的文件路径")
    private String uploadUrl;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "上传客户的文件名")
    private String uploadFileName;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "上传客户的文件客户数量")
    private Integer uploadCount;

    public void check() {
        if (selectType == 0) {
            if (CollectionUtils.isEmpty(selectCustomerIds)) {
                throw new BadRequestException("选择客户id列表不能为空");
            }
        } else if (selectType == 2) {
            if (StringUtils.isEmpty(uploadUrl)) {
                throw new BadRequestException("上传文件不能为空");
            }
        } else if (selectType != 1) {
            throw new BadRequestException("客户目标类型错误");
        }
    }

    public <T extends CustomerSelectedDto> T copyTo(T dto) {
        dto.setSelectType(getSelectType());
        dto.setSelectCustomerIds(getSelectCustomerIds());
        dto.setSelectConditions(getSelectConditions());
        dto.setUploadUrl(getUploadUrl());
        return dto;
    }
}
