package cn.hanyi.ctm.dto.journey;

import cn.hanyi.ctm.constant.DisturbType;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;
import org.befun.core.rest.view.ResourceViews;


/**
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class DisturbMomentDto extends BaseDTO {
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "勿扰模式开关")
    private Boolean enable= false;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "勿扰类型")
    private DisturbType type;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "勿扰值")
    private Integer value;

}
