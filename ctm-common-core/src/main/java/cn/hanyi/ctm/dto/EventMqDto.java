package cn.hanyi.ctm.dto;

import cn.hanyi.ctm.constant.EventWarningType;
import cn.hanyi.ctm.constant.event.EventMonitorCondition;
import cn.hanyi.ctm.constant.event.EventNotifyDelayUnit;
import cn.hanyi.ctm.constant.event.EventNotifyMoment;
import cn.hanyi.ctm.constant.event.EventType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.auth.constant.NotificationType;
import org.befun.task.BaseTaskDetailDto;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EventMqDto extends BaseTaskDetailDto implements Comparable<EventMqDto> {
    private Long eventId;  // 事件ID(预警事件、体验指标)
    private Long orgId;
    private Long roleId;
    
    private String roleIds; // task经过redis list无法转换 "1,2,3"
    private EventNotifyMoment notifyMoment;       // 立即 延迟
    private Integer delayInterval;                // 延迟时间间隔
    private EventNotifyDelayUnit delayUnit;       // 延迟时间单位 分钟 小时 天 月
    private EventMonitorCondition delayCondition; // 延迟通知条件 未处理 未关闭
    private List<NotificationType> notifyChannel; // 通知方式 wx email sms
    private String notifyChannelString;           // 暂时解决延时任务redis无法正确解析list
    private EventWarningType level;               // hide 当事件触发的规则有重复的roleId时，用来排序

    private EventType eventType = EventType.EVENT; // 事件类型
    private Double value = 0.0;                         // 存放体验指标值或者预警的次数


    public void convertNotifyChannelString() {
        notifyChannelString = notifyChannel.stream().map(x -> x.name()).collect(Collectors.joining(","));
        notifyChannel = null;
    }

    public void convertNotifyChannelList() {
        notifyChannel = Arrays.stream(notifyChannelString.split(",")).map(x -> NotificationType.valueOf(x)).collect(Collectors.toList());
        notifyChannelString = null;
    }


    public Duration getDelayDuration() {
        if (delayUnit != null && delayInterval != null && delayInterval > 0) {
            if (delayUnit == EventNotifyDelayUnit.MINUTE) {
                return Duration.ofMinutes(delayInterval);
            } else if (delayUnit == EventNotifyDelayUnit.HOUR) {
                return Duration.ofHours(delayInterval);
            } else if (delayUnit == EventNotifyDelayUnit.DAY) {
                return Duration.ofDays(delayInterval);
            } else if (delayUnit == EventNotifyDelayUnit.MONTH) {
                LocalDateTime now = LocalDateTime.now();
                LocalDateTime end = now.plusMonths(delayInterval);
                return Duration.between(now, end);
            }
        }
        return null;
    }

    public String summary() {
        return String.format("eventId=%d, orgId=%d, roleId=%d, notifyChannel=%s",
                eventId, orgId, roleId, Optional.ofNullable(notifyChannel).map(i -> i.stream().map(Enum::name).collect(Collectors.joining(",", "[", "]"))).orElse("[]"));
    }

    public int weight() {
        if (notifyMoment == null || notifyMoment == EventNotifyMoment.IMMEDIATE) {
            return 0;
        }
        Duration duration = getDelayDuration();
        return duration == null ? 1 : (int) duration.getSeconds();
    }


    @Override
    public int compareTo(EventMqDto o) {
        int i1 = Optional.ofNullable(level).map(i -> 3 - i.getOrder()).orElse(3);
        int i2 = Optional.ofNullable(o.level).map(i -> 3 - i.getOrder()).orElse(3);
        if (i1 == i2) {
            i1 = weight();
            i2 = o.weight();
        }
        return Integer.compare(i1, i2);
    }
}