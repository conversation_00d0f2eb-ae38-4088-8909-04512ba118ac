package cn.hanyi.ctm.dto.journey;

import cn.hanyi.ctm.constant.InteractionCollectorType;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

import java.util.Map;

@Getter
@Setter
public class PushChannelConfigDto {

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "推送渠道")
    private InteractionCollectorType type;
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "推送渠道短信时的模版id")
    private Long thirdpartyTemplateId;
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "推送渠道微信时的模版id")
    private Long wechatOpenTemplateId;
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "推送自定义内容")
    private Map<String, Object> content;

}
