package cn.hanyi.ctm.dto.stat;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigInteger;

@Getter
@Setter
@NoArgsConstructor
public class CacheStatIndicatorParamDto {
    private Long surveyId;
    private Long questionId;
    private String itemName;
    private String percentItem;
    private double weight;

    private Long departmentId;

    private String key;

    private BigInteger filterHash;


    public CacheStatIndicatorParamDto(Long surveyId, Long questionId, String itemName, String percentItem, double weight, Long departmentId, BigInteger filterHash) {
        this.surveyId = surveyId;
        this.questionId = questionId;
        this.itemName = itemName;
        this.percentItem = percentItem;
        this.weight = weight;
        this.departmentId = departmentId;
        this.filterHash = filterHash;
    }
}
