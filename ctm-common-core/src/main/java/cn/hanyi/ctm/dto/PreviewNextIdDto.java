package cn.hanyi.ctm.dto;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;
import org.befun.core.rest.view.ResourceViews;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class PreviewNextIdDto extends BaseDTO {

    @JsonView(ResourceViews.Basic.class)
    private Long previewId;

    @JsonView(ResourceViews.Basic.class)
    private Long nextId;

}