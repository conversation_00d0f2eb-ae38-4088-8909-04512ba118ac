package cn.hanyi.ctm.dto.survey;

import cn.hanyi.ctm.constant.survey.SurveyChangeStatus;
import cn.hanyi.ctm.constant.survey.SurveyChangeType;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;
import org.befun.core.rest.context.TenantContext;


/**
 *
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
public class SurveyChangeDto extends BaseDTO {

    private Long userId = TenantContext.getCurrentUserId();
    private Long orgId = TenantContext.getCurrentTenant();
    private Long surveyId;
    private Long entityId;
    private String entityName;
    private SurveyChangeType entityType;
    private SurveyChangeStatus status;
}
