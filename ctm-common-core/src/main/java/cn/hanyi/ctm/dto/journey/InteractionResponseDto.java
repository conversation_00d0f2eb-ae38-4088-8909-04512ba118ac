package cn.hanyi.ctm.dto.journey;

import cn.hanyi.ctm.constant.InteractionCollectorType;
import cn.hanyi.ctm.constant.SendStatus;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;

import java.util.Map;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class InteractionResponseDto extends BaseDTO {
    private String interactionName;
    private InteractionCollectorType collector;
    private SendStatus sendStatus;
    private Map<String, Object> interactionResponse;
    private Long errorCode;
}
