package cn.hanyi.ctm.dto.customer;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.rest.view.ResourceViews;
import org.befun.core.validation.ValidSearchText;

@Getter
@Setter
public class CustomerCombineSearchDto extends CustomerDynamicSearchDto {

    @JsonView(ResourceViews.Basic.class)
    @ValidSearchText
    @JsonProperty("_q")
    @Schema(name = "_q", description = "搜索关键字")
    private String q;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "选择的部门id，-1 全部； -2 未分组； >0 指定的部门id")
    private Long selectDepartmentId;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "选择的组id")
    private Long selectGroupId;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "联系人类型：ALL,WECHAT,MOBILE,EMAIL；默认是ALL")
    private String contactType;

    @Schema(hidden = true, description = "有手机号的")
    private boolean hasMobile;

    @Schema(hidden = true, description = "有绑定微信的")
    private boolean hasWechat;

    @Schema(hidden = true, description = "有邮箱的")
    private boolean hasEmail;

    public boolean isHasMobile() {
        return StringUtils.isNotEmpty(contactType) && contactType.equals("MOBILE");
    }

    public boolean isHasWechat() {
        return StringUtils.isNotEmpty(contactType) && contactType.equals("WECHAT");
    }

    public boolean isHasEmail() {
        return StringUtils.isNotEmpty(contactType) && contactType.equals("EMAIL");
    }
}
