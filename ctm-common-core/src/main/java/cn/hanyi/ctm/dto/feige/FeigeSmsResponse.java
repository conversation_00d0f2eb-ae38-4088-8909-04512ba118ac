package cn.hanyi.ctm.dto.feige;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Setter
@Getter
public class FeigeSmsResponse {
    @JsonProperty("SendId")
    private String sendId;

    @JsonProperty("InvalidCount")
    private int invalidCount;

    @JsonProperty("SuccessCount")
    private int successCount;

    @JsonProperty("BlackCount")
    private int blackCount;

    @JsonProperty("Code")
    private int code;

    @JsonProperty("Message")
    private String message;
}
