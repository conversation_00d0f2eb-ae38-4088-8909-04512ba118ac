package cn.hanyi.ctm.dto.user;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.auth.projection.SimpleUser;
import org.befun.core.rest.view.ResourceViews;

/**
 * <AUTHOR>
 * 旧版本的auth 叠加了0.5的版本了 等下次ctm切换到0.5版本的时候再改
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class SimpleUserDto {
    @JsonView(ResourceViews.Basic.class)
    private Long id;
    @JsonView(ResourceViews.Basic.class)
    private String truename;
    @JsonView(ResourceViews.Basic.class)
    private String avatar;
    @JsonView(ResourceViews.Basic.class)
    private Boolean isAdmin;
    @JsonView(ResourceViews.Basic.class)
    private String mobile;
    @JsonView(ResourceViews.Basic.class)
    private String email;

    public SimpleUserDto(SimpleUser user) {
       if (user != null) {
           this.id = user.getId();
           this.truename = user.getTruename();
           this.avatar = user.getAvatar();
           this.isAdmin = user.getIsAdmin();
           this.mobile = user.getMobile();
           this.email = user.getEmail();
       }
        }
}
