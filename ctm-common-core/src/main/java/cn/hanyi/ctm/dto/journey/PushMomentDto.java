package cn.hanyi.ctm.dto.journey;

import cn.hanyi.ctm.constant.MomentType;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;
import org.befun.core.rest.view.ResourceViews;
import org.befun.task.constant.TaskExecutionTimedType;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class PushMomentDto extends BaseDTO {
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "互动时刻类型")
    private MomentType momentType = MomentType.IMMEDIATELY;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "定时类型")
    private TaskExecutionTimedType timedType;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "星期列表")
    private String[] weeks = new String[0];

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "时")
    private Integer hour;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "分")
    private Integer minute;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "延迟推送时长")
    private String duration;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "是否启用基准时间参数")
    private Boolean enableBaseTimeParam;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "基准时间参数名称")
    private String baseTimeParam;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "是否启用截止时长")
    private Boolean enableDeadDuration;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "推送截止时长")
    private String deadDuration;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "是否启用短信勿扰时间段")
    private Boolean enableSilentTime;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "短信勿扰时间段")
    private String silentTime;

}
