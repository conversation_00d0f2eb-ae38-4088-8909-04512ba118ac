//package cn.hanyi.ctm.dto.task;
//
//import lombok.Getter;
//import lombok.Setter;
//import org.apache.commons.collections4.CollectionUtils;
//import org.befun.task.dto.PageableTaskDetailDto;
//
//import java.util.*;
//import java.util.stream.Collectors;
//
//@Getter
//@Setter
//public class CustomerPushPageableTaskDetailDto extends PageableTaskDetailDto {
//
//    private String pushIds;
//    private int realSize; // 本次处理的真实数量
//
//    @Override
//    public int countComplete() {
//        return realSize;
//    }
//
//    public void formatIds(List<Long> logIds) {
//        if (CollectionUtils.isNotEmpty(logIds)) {
//            realSize = logIds.size();
//            this.pushIds = logIds.stream().map(Objects::toString).collect(Collectors.joining(","));
//        }
//    }
//
//    public List<Long> parseIds() {
//        return Optional.ofNullable(pushIds).map(i -> Arrays.stream(i.split(",")).map(Long::valueOf).collect(Collectors.toList())).orElse(new ArrayList<>());
//    }
//}
