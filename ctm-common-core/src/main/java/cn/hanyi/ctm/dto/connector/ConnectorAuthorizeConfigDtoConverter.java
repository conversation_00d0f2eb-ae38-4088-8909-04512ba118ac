package cn.hanyi.ctm.dto.connector;

import org.apache.commons.lang3.StringUtils;
import org.befun.core.utils.JsonHelper;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter
public class ConnectorAuthorizeConfigDtoConverter implements AttributeConverter<ConnectorAuthorizeConfigDto, String> {

    @Override
    public String convertToDatabaseColumn(ConnectorAuthorizeConfigDto dto) {
        if (dto == null) {
            return null;
        } else {
            return JsonHelper.toJson(dto);
        }
    }

    @Override
    public ConnectorAuthorizeConfigDto convertToEntityAttribute(String dbData) {
        if (StringUtils.isEmpty(dbData)) {
            return null;
        } else {
            return JsonHelper.toObject(dbData, ConnectorAuthorizeConfigDto.class);
        }
    }
}