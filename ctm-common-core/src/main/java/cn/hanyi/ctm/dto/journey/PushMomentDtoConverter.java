package cn.hanyi.ctm.dto.journey;

import org.apache.commons.lang3.StringUtils;
import org.befun.core.utils.JsonHelper;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter
public class PushMomentDtoConverter implements AttributeConverter<PushMomentDto, String> {

    @Override
    public String convertToDatabaseColumn(PushMomentDto dto) {
        if (dto == null) {
            return null;
        } else {
            return JsonHelper.toJson(dto);
        }
    }

    @Override
    public PushMomentDto convertToEntityAttribute(String dbData) {
        if (StringUtils.isEmpty(dbData)) {
            return null;
        } else {
            return JsonHelper.toObject(dbData, PushMomentDto.class);
        }
    }
}