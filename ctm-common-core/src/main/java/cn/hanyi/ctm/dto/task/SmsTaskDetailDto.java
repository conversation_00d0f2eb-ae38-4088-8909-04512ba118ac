package cn.hanyi.ctm.dto.task;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.*;
import org.befun.task.BaseTaskDetailDto;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class SmsTaskDetailDto extends BaseTaskDetailDto {
//    @JsonPropertyDescription("connectorId")
//    private String Account;
//
//    @JsonPropertyDescription("connectorSecret")
//    private String Pwd;
    private Long orgId;

    private Long connectorId;

    private String Content;

    private String Mobile;

    private String TemplateId;

    private String SignId;
}
