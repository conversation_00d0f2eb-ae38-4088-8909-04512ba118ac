package cn.hanyi.ctm.dto.journey;

import cn.hanyi.ctm.constant.TypeFilterLogic;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

import java.util.ArrayList;
import java.util.List;

@Setter
@Getter
public class CalculatingFilterDto {

    @JsonView(ResourceViews.Basic.class)
    private TypeFilterLogic logicType;
    @JsonView(ResourceViews.Basic.class)
    private List<TypeFilterDto> filter = new ArrayList<>();
}
