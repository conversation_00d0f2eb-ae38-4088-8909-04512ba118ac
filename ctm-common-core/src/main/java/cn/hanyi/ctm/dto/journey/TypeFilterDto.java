package cn.hanyi.ctm.dto.journey;

import cn.hanyi.ctm.constant.TypeFilter;
import cn.hanyi.ctm.constant.TypeFilterMethod;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

import java.util.List;

/***
 * ctm 体验指标过滤渠道
 *
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class TypeFilterDto {
    @JsonView(ResourceViews.Basic.class)
    private TypeFilter type;
    @JsonView(ResourceViews.Basic.class)
    private TypeFilterMethod method;
    @JsonView(ResourceViews.Basic.class)
    private List<TypeFilterSubFilterDto> subFilter;
}
