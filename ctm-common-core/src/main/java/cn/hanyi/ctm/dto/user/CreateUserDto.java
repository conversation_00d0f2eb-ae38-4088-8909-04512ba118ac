package cn.hanyi.ctm.dto.user;

import com.fasterxml.jackson.annotation.JsonAlias;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class CreateUserDto extends BaseDTO {

    @JsonAlias("org_id")
    private Long orgId;

    @JsonAlias("role_id")
    private Object roleId;

    private String username;

    public List getRoleId() {
        if (roleId instanceof List) {
            return (List) roleId;
        }
        return List.of((Long) roleId);
    }
}
