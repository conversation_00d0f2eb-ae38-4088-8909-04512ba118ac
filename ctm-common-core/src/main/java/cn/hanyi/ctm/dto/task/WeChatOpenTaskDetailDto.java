package cn.hanyi.ctm.dto.task;

import cn.hanyi.ctm.constant.MessageType;
import lombok.*;
import org.befun.task.BaseTaskDetailDto;

import java.util.List;
import java.util.Map;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class WeChatOpenTaskDetailDto extends BaseTaskDetailDto {
    private Long orgId;

    private Long connectorId;

    private String appId;

    private MessageType messageType;

    private String  customerOpenId;

    private String message;

    private String url;

}
