//package cn.hanyi.ctm.dto.task;
//
//import lombok.Getter;
//import lombok.Setter;
//import org.apache.commons.collections4.CollectionUtils;
//import org.befun.task.dto.PageableTaskDetailDto;
//
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.List;
//import java.util.Optional;
//import java.util.stream.Collectors;
//
//@Getter
//@Setter
//public class SyncCustomerWechatInfoTaskDetailDto extends PageableTaskDetailDto {
//    private Long orgId;
//    private Long connectorId;
//    private String appId;
//    private int realSize; // 本次处理的真实数量
//    private String openIds;
//
//    @Override
//    public int countComplete() {
//        return realSize;
//    }
//
//    public void formatOpenIds(List<String> openIds) {
//        if (CollectionUtils.isNotEmpty(openIds)) {
//            realSize = openIds.size();
//            this.openIds = String.join(",", openIds);
//        }
//    }
//
//    public List<String> parseOpenIds() {
//        return Optional.ofNullable(openIds).map(i -> Arrays.stream(i.split(",")).collect(Collectors.toList())).orElse(new ArrayList<>());
//    }
//}
