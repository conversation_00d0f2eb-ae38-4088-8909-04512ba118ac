package cn.hanyi.ctm.dto.customer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import java.util.Map;

@Getter
@Setter
public class CustomerAddJourneyDto extends CustomerSelectedDto {

    @NotNull
    @Schema(description = "部门id", required = true)
    private Long departmentId;

    @NotNull
    @Schema(description = "场景id", required = true)
    private Long journeyId;

    @Schema(description = "发送id", hidden = true)
    private Long sendManageId;

    @Schema(description = "问卷自定以参数", hidden = true)
    private Map<String, Object> urlCustomParams;

}
