package cn.hanyi.ctm.utils;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashSet;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * <AUTHOR>
 */
@Slf4j
public class RegularExpressionUtils {

    static Pattern pattern = Pattern.compile("<.+?>", Pattern.DOTALL);
    static final Pattern PATTERN_QUESTION_NAME = Pattern.compile("([\\S]*?)\\.");
    static final Pattern PATTERN_THESAURUS_ID = Pattern.compile("([\\S]*)\\.(comment)?((?i)c)ontainsThesaurus\\(([1-9]\\d*)\\)");

    static final Pattern PATTERN_SENTIMENT_LABEL = Pattern.compile("([\\S]*)\\.(sentiment|commentSentiment)\\(\"(NEGATIVE|POSITIVE)\"\\)");
    static final Pattern PATTERN_TOPIC_ID = Pattern.compile("([\\S]*).(topicIn|commentTopicIn)\\(\"([\\s\\S]*)\",([1-9]\\d*)\\)");

    public static String replaceHtml(String html) {
        String result = (html == null) ? null : pattern.matcher(html).replaceAll("");
        return result;
    }

    public static HashMap<Long, String> getThesaurusIds(String expression) {
        Matcher matcher = PATTERN_THESAURUS_ID.matcher(expression);
        HashMap<Long, String> thesaurusIdMap = new HashMap<>();

        int matcherStart = 0;
        while (matcher.find(matcherStart)) {
            thesaurusIdMap.put(Long.valueOf(matcher.group(4)), matcher.group(1));
            matcherStart = matcher.end();
        }
        return thesaurusIdMap;
    }

    public static ArrayList<String[]> getSentimentIdLabel(String expression) {
        Matcher matcher = PATTERN_SENTIMENT_LABEL.matcher(expression);
        ArrayList<String[]> sentimentIdLabelList = new ArrayList<>();

        int matcherStart = 0;
        while (matcher.find(matcherStart)) {

            String[] sentimentIdLabel = new String[3];
            var qName = matcher.group(1);
            var label = matcher.group(3);
            var type = matcher.group(2);
            sentimentIdLabel[0] = qName;
            sentimentIdLabel[1] = label;
            sentimentIdLabel[2] = type;

            sentimentIdLabelList.add(sentimentIdLabel);
            matcherStart = matcher.end();
        }

        return sentimentIdLabelList;
    }

    public static ArrayList<String[]> getTopicId(String expression) {
        Matcher matcher = PATTERN_TOPIC_ID.matcher(expression);
        ArrayList<String[]> topicIdList = new ArrayList<>();

        int matcherStart = 0;
        while (matcher.find(matcherStart)) {

            String[] topicId = new String[4];
            var qName = matcher.group(1);
            var label = matcher.group(3);
            var type = matcher.group(2);
            var id = matcher.group(4);
            topicId[0] = qName;
            topicId[1] = label;
            topicId[2] = type;
            topicId[3] = id;

            topicIdList.add(topicId);
            matcherStart = matcher.end();
        }

        return topicIdList;
    }

    public static Set<String> getQuestionNames(String expression) {
        Matcher matcher = PATTERN_QUESTION_NAME.matcher(expression);
        LinkedHashSet<String> nameList = new LinkedHashSet<>();

        int matcherStart = 0;
        while (matcher.find(matcherStart)) {
            nameList.add(matcher.group(1));
            matcherStart = matcher.end();
        }
        return nameList;
    }

    public static void main(String[] args) {
        String exp = "1NrRnl.sentiment(\"NEGATIVE\") and 2NrRnl.commentSentiment(\"NEGATIVE\")";
        ArrayList<String[]> s = getSentimentIdLabel(exp);
        System.out.println(s);

        String topic = "1NrRnl.sentiment(\"NEGATIVE\") and 2NrRnl.commentSentiment(\"NEGATIVE\") and 1NrRnl.topicIn(\"产品/价格/成本\",1) and 1NrRnl.commentTopicIn(\"产品/价格/成本\",1)";

        ArrayList<String[]> t = getTopicId(topic);
        System.out.println(t);


    }


}
