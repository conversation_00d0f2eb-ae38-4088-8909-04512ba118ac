package cn.hanyi.ctm.utils;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.*;
import java.util.function.BiFunction;
import java.util.function.BiPredicate;
import java.util.stream.Collectors;

public final class ListHelper {

    public static boolean validId(Long... id) {
        return id != null && Arrays.stream(id).allMatch(i -> i != null && i > 0);
    }

    /**
     * 从 列表1 中减去 列表2 中匹配的元素
     */
    @NonNull
    public static <T1, T2> List<T1> minusNotNull(List<T1> list1, List<T2> list2, BiPredicate<T1, T2> isEqual) {
        return Optional.ofNullable(minus(list1, list2, isEqual)).orElse(new ArrayList<>());
    }

    /**
     * 从 列表1 中减去 列表2 中匹配的元素
     */
    @Nullable
    public static <T1, T2> List<T1> minus(List<T1> list1, List<T2> list2, BiPredicate<T1, T2> isEqual) {
        if (CollectionUtils.isEmpty(list1)) {
            return null;
        }
        if (CollectionUtils.isEmpty(list2)) {
            return list1;
        }
        List<T1> list = new ArrayList<>();
        list1.forEach(i1 -> {
            // list2 中不存在的
            if (list2.stream().noneMatch(i2 -> isEqual.test(i1, i2))) {
                list.add(i1);
            }
        });
        return list;
    }

    /**
     * 列表1 中存在，列表2 中存在
     */
    public static <T1, T2> List<T1> contain(List<T1> list1, List<T2> list2, BiPredicate<T1, T2> isEqual, BiFunction<T1, T2, T1> merge) {
        if (CollectionUtils.isEmpty(list1)) {
            return null;
        }
        if (CollectionUtils.isEmpty(list2)) {
            return null;
        }
        List<T1> list = new ArrayList<>();
        for (T1 i : list1) {
            if (i == null) {
                continue;
            }
            for (T2 j : list2) {
                if (j == null) {
                    continue;
                }
                if (isEqual.test(i, j)) {
                    list.add(merge.apply(i, j));
                    break;
                }
            }
        }
        return list;
    }

    public static Set<Long> confirmNotEmpty(List<Long> list, Long emptyValue) {
        Set<Long> r = new HashSet<>();
        if (CollectionUtils.isNotEmpty(list)) {
            list.stream().filter(Objects::nonNull).forEach(r::add);
        }
        if (r.isEmpty()) {
            r.add(emptyValue);
        }
        return r;
    }
}
