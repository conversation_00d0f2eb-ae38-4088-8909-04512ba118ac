package cn.hanyi.ctm.constant.error;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum CtmErrorCode {
    EXCEL_TYPE_ERROR(40000, "上传文件格式有误，请按模版内容格式规范修改后重新上传"),
    EXCEL_SIZE_ERROR(40001, "请选择5M以内的文件上传"),
    EXCEL_HEAD_ERROR(40002, "无效的表头字段"),
    EXCEL_ERROR(40003, "上传失败"),
    EXCEL_NOT_SUPPORT_ERROR(40004, "不支持Strict Open XML 格式。保存时请选择Excel WorkBook 格式"),


    ;

    private int value;
    private String message;

    CtmErrorCode(int value, String message) {
        this.value = value;
        this.message = message;
    }
}
