package cn.hanyi.ctm.constant.connector;

import lombok.Getter;

@Getter
public enum ConnectorType {
    SMS(0, ConnectorProviderType.FEIGE, "短信"),
    PLATFORM(1, ConnectorProviderType.WECHATOPEN, "开放平台"),
    API(2, ConnectorProviderType.FEIGE, "API"),
    WEBHOOK(3, ConnectorProviderType.WEBHOOK, "Webhook"),
    EMAIL(4, ConnectorProviderType.EMAIL, "邮件"),
    WECHAT_WORK(5, ConnectorProviderType.BOT, "企微机器人"),
    DING_DING(6, ConnectorProviderType.BOT, "钉钉机器人"),
    FEI_SHU(7, ConnectorProviderType.BOT, "飞书机器人"),
    ;

    private final int value;
    private final ConnectorProviderType defaultProviderType;
    private final String name;
    
    ConnectorType(int value, ConnectorProviderType defaultProviderType, String name) {
        this.value = value;
        this.name = name;
        this.defaultProviderType = defaultProviderType;
    }
}
