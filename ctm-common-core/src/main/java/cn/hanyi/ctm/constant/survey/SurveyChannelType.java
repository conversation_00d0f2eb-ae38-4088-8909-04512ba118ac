package cn.hanyi.ctm.constant.survey;


import cn.hanyi.ctm.constant.InteractionCollectorType;
import lombok.Getter;

import java.util.Arrays;

@Getter
public enum SurveyChannelType {
    COMMON("通用链接"),
    SHORT_LINK("短链接"),
    PHONE_MSG("手机短信"),
    WECHAT_SERVICE("微信服务号"),
    INJECT_WEB("页面嵌入"),
    SURVEY_PLUS("调研家社区"),
    SCENE_INTERACTION("场景互动");

    private final String text;

    SurveyChannelType(String text) {
        this.text = text;
    }


    /**
     * 问卷渠道和客户互动的渠道不太一样，这里写一个对应的关系
     *
     * @see InteractionCollectorType
     */
    public static InteractionCollectorType mapToInteractionType(Integer type) {
        if (type == null) {
            return InteractionCollectorType.COMMON;
        }
        SurveyChannelType channelType = Arrays.stream(values()).filter(i -> i.ordinal() == type).findFirst().orElse(COMMON);
        return Arrays.stream(InteractionCollectorType.values()).filter(i -> i.name().equals(channelType.name())).findFirst().orElse(InteractionCollectorType.COMMON);
    }
}