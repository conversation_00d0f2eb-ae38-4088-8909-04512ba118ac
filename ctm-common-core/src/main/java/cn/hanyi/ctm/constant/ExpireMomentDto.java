package cn.hanyi.ctm.constant;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;
import org.befun.core.rest.view.ResourceViews;

import java.time.LocalDateTime;

@Getter
@Setter
public class ExpireMomentDto extends BaseDTO {

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "开关")
    private Boolean enable = false;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "类型")
    private ExpireType type;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "数值")
    private Integer value;

    public LocalDateTime calcExpireTime() {
        if (enable != null && enable && type != null && value != null && value > 0) {
            return type.expireTime(value);
        }
        return null;
    }

    public LocalDateTime calcExpireTime(LocalDateTime baseTime) {
        if (enable != null && enable && type != null && value != null && value > 0) {
            return type.expireTime(baseTime, value);
        }
        return null;
    }
}
