package cn.hanyi.ctm.constant;

import lombok.Getter;

@Getter
public enum InteractionCollectorType {
    WECHAT(1,"场景互动-微信"),
    APP(2,"场景互动-企业网关"),
    SMS(3,"场景互动-短信"),
    // 以上是客户旅程定义的渠道
    // 以下是发送问卷中的渠道

    COMMON(4,"通用链接"),
    SHORT_LINK(5,"短链接"),
    PHONE_MSG(6,"手机短信"),
    WECHAT_SERVICE(7,"微信服务号"),
    INJECT_WEB(8,"页面嵌入"),
    SURVEY_PLUS(9,"调研家社区"),
    SCENE_INTERACTION(10, "场景互动"), //分为最上面的3类
    EMAIL(11, "场景互动"), //分为最上面的3类
    ;


    private final int value;
    private final String label;

    InteractionCollectorType(int value, String label) {
        this.value = value;
        this.label = label;
    }

    private static InteractionCollectorType[] myEnumValues = null;

    public static InteractionCollectorType getEnumByValue(Long value){
        if (InteractionCollectorType.myEnumValues == null) {
            InteractionCollectorType.myEnumValues = InteractionCollectorType.values();
        }
        for(InteractionCollectorType e : InteractionCollectorType.myEnumValues){
            if(e.getValue() == value) return e;
        }
        return null;
    }
}
