package cn.hanyi.ctm.constant;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

@Getter
public enum EventWarningType {
    NONE("", 0),
    LOW("低预警事件", 1),
    MIDDLE("中预警事件", 2),
    HIGH("高预警事件", 3),
    ;
    private final String text;
    private final Integer order;

    EventWarningType(String text, int order) {
        this.text = text;
        this.order = order;
    }

    public String getSimpleText() {
        if (StringUtils.isEmpty(text)) {
            return "";
        }
        return text.substring(0, 1);
    }
}
