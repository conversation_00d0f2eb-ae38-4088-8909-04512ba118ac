package cn.hanyi.ctm.constant;

import java.time.LocalDateTime;

public enum ExpireType {
    YEAR {
        @Override
        public LocalDateTime expireTime(LocalDateTime baseTime, int value) {
            return confirmBaseTime(baseTime).plusYears(value);
        }
    },
    MONTH {
        @Override
        public LocalDateTime expireTime(LocalDateTime baseTime, int value) {
            return confirmBaseTime(baseTime).plusMonths(value);
        }
    },
    WEEK {
        @Override
        public LocalDateTime expireTime(LocalDateTime baseTime, int value) {
            return confirmBaseTime(baseTime).plusWeeks(value);
        }
    },
    DAY {
        @Override
        public LocalDateTime expireTime(LocalDateTime baseTime, int value) {
            return confirmBaseTime(baseTime).plusDays(value);
        }
    },
    HOUR {
        @Override
        public LocalDateTime expireTime(LocalDateTime baseTime, int value) {
            return confirmBaseTime(baseTime).plusHours(value);
        }
    },
    ;

    public LocalDateTime expireTime(int value) {
        return expireTime(LocalDateTime.now(), value);
    }

    public LocalDateTime expireTime(LocalDateTime baseTime, int value) {
        return null;
    }

    private static LocalDateTime confirmBaseTime(LocalDateTime baseTime) {
        return baseTime == null ? LocalDateTime.now() : baseTime;
    }
}
