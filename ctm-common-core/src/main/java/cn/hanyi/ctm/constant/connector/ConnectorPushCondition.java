package cn.hanyi.ctm.constant.connector;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum ConnectorPushCondition {
    CREATED("创建"),
    UPDATED("更新"),
    DELETED("删除"),
    ENABLE("启用"),
    DISABLE("停用"),
    NEXT("下一步"),
    SUBMIT("提交"),
    WARNING("预警"), // WARNING_WAIT
    WARNING_APPLYING("预警"), // WARNING_APPLYING
    WARNING_SUCCESS("预警"), // WARNING_SUCCESS
    ;

    private final String value;

    ConnectorPushCondition(String value) {
        this.value = value;
    }

}
