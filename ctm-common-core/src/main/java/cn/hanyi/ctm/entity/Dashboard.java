package cn.hanyi.ctm.entity;

import com.fasterxml.jackson.annotation.JsonView;
import java.util.List;
import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.converter.ListConverter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.rest.view.ResourceViews;

@Entity
@Table(name = "dashboard")
@JsonView(ResourceViews.Basic.class)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@DtoClass(includeAllFields = true)
public class Dashboard extends EnterpriseEntity {
    @Column(name = "is_folder")
    private int isFolder = 0;

    @Column(name = "is_preview")
    private int isPreview = 1;

    @Column(name = "preview_id")
    private Long previewId;

    private String title = "";

    private Long pid;

    @Column(name = "`left`")
    private int left;

    @Column(name = "`right`")
    private int right;

    @Column(name = "`level`")
    private int level;

    private String icon = "";

    @Column(name = "filter")
    @Convert(converter = ListConverter.class)
    private List<String> filter = List.of();

}
