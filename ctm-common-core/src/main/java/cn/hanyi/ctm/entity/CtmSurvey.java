package cn.hanyi.ctm.entity;

import lombok.Getter;
import lombok.Setter;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

import static org.befun.core.constant.EntityScopeStrategyType.OWNER_CORPORATION;

/**
 * 事件列表规则调整，需要先查出所有的有权限的问卷id
 * 如果是
 * 有问卷权限的，不用部门过滤，看到全部预警
 * 没有问卷权限，有被预警通知的，可以根据部门看到预警
 */
@Entity
@Getter
@Setter
@Table(name = "survey")
@Where(clause = "deleted=0")
@EntityScopeStrategy(value = OWNER_CORPORATION,resource = "SURVEY")
public class CtmSurvey extends EnterpriseEntity {

    @Column(name = "title")
    private String title;
    @Column(name = "user_id")
    private Long userId;
}
