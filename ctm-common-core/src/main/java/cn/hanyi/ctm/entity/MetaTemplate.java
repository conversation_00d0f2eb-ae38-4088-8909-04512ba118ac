package cn.hanyi.ctm.entity;

import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.EnterpriseEntity;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Getter
@Setter
@Table(name = "meta_template")
@DtoClass(includeAllFields = true)
public class MetaTemplate extends EnterpriseEntity {
    private String name;
    private String description;

    @Override
    public String toString() {
        return String.format(
                "User[id=%d, name='%s']",
                id, name);
    }
}
