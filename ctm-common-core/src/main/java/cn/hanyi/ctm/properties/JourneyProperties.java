package cn.hanyi.ctm.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Map;

@Getter
@Setter
@Configuration()
@ConfigurationProperties(prefix = "ctm.journey")
public class JourneyProperties {
    private String emailTemplateAdd;
    private String emailTemplateRemove;
    private String journeyUrl;
    private Map<String, Integer> journeySize;
    private String emailTemplateIndicator;
}
