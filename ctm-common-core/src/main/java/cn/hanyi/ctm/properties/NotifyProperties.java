package cn.hanyi.ctm.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration()
@ConfigurationProperties(prefix = "ctm.notify")
public class NotifyProperties {

    private String app;
    private String warning = "warning";
    private String cooperation = "cooperation";
    private String close = "close";
    private String journey = "journey";
    private String customer = "customer";

}
