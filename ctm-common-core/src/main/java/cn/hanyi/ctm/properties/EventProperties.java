package cn.hanyi.ctm.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration()
@ConfigurationProperties(prefix = "ctm.event")
public class EventProperties {

    private String targetUrl ;

    private String notifyTopic = "ctm_notify_event";
    private String notifyGroup = "ctm_notify_event";

    private String surveyResponseTopic = "survey_response";
    private String surveyResponseGroup = "ctm_survey_response";

    private String surveyChangeTopic = "survey_change";
    private String surveyChangeGroup = "ctm_survey_change";

    private String defaultSmsTemplate = "default_sms_event";

    private String ctmMessageTopic = "ctm_message";

    private Boolean enableNotify = true;
    private String shareUrl;
    private String shareMessage;
}
