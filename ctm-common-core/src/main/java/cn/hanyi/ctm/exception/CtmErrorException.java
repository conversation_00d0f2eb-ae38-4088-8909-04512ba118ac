package cn.hanyi.ctm.exception;

import cn.hanyi.ctm.constant.error.CtmErrorCode;
import lombok.Getter;
import org.springframework.http.HttpStatus;

/**
 * The class description
 *
 * <AUTHOR>
 */

@Getter
public class CtmErrorException extends RuntimeException {
    private HttpStatus status;
    private int code;
    private int internalCode;
    private String message;
    private String detail;

    public CtmErrorException(CtmErrorCode ctmErrorCode) {
        this.status = HttpStatus.OK;
        this.code = ctmErrorCode.getValue();
        this.message = ctmErrorCode.getMessage();
    }

    public CtmErrorException(CtmErrorCode ctmErrorCode, String message) {
        this.status = HttpStatus.OK;
        this.code = ctmErrorCode.getValue();
        this.message = message;
    }
}
