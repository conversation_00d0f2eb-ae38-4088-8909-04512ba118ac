package cn.hanyi.ctm.exception;

import org.befun.core.dto.BaseResponseDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;

@ControllerAdvice
public class CtmExceptionHandler {

    private static final Logger log = LoggerFactory.getLogger(org.befun.core.rest.ResponseExceptionHandler.class);

    @ExceptionHandler({CtmErrorException.class})
    public ResponseEntity<BaseResponseDto<?>> handleOperatorException(CtmErrorException ex) {
        log.warn("handleOperatorException", ex);
        BaseResponseDto<?> responseDto = new BaseResponseDto();
        responseDto.setMessage(ex.getMessage());
        responseDto.setCode(ex.getCode());
        return ResponseEntity.ok().body(responseDto);
    }
}
