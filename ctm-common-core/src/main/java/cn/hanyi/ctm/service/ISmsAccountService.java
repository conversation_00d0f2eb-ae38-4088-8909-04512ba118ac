//package cn.hanyi.ctm.service;
//
//public interface ISmsAccountService {
//
//    /**
//     * 充值
//     */
//    default int recharge(Long orgId, int amount) {
//        return Integer.MAX_VALUE;
//    }
//
//    /**
//     * 通过字数计算短信条数
//     */
//    default int calcNumberByText(String text) {
//        return 0;
//    }
//
//    /**
//     * 查询真实余额
//     */
//    default int balance(Long orgId) {
//        return Integer.MAX_VALUE;
//    }
//
//    /**
//     * 查询可用余额: 真实余额-冻结余额
//     */
//    default int availableBalance(Long orgId) {
//        return Integer.MAX_VALUE;
//    }
//
//    /**
//     * 增加冻结记录
//     */
//    default void frozen(Long orgId, Long taskProgressId, Integer cost) {
//    }
//
//    /**
//     * 是否有额度
//     */
//    default boolean hasBalance(Long orgId, Integer cost) {
//        return true;
//    }
//
//    /**
//     * 使用额度，可能会导致余额小于0，
//     */
//    default void consumer(Long orgId, Integer cost) {
//
//    }
//
//    /**
//     * 使用额度，可能会导致余额小于0，
//     */
//    default void consumer(Long orgId, Long taskProgressId, Integer cost) {
//
//    }
//
//    /**
//     * 短信发送完毕， 删除冻结记录， 重新把缓存余额写入数据库
//     */
//    default void releaseFrozen(Long orgId, Long taskProgressId) {
//
//    }
//
//    default void syncToDb(Long orgId) {
//
//    }
//
//}
