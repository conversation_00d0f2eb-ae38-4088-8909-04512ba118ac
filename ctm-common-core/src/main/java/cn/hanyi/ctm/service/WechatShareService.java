package cn.hanyi.ctm.service;


import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.WxJsapiSignature;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import org.befun.auth.provider.wechat.mp.WechatMpAuthProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
@Slf4j
public class WechatShareService {

    @Autowired
    WechatMpAuthProvider wechatMpAuthProvider;

    public WxJsapiSignature getWxSignature(String app, String url) {
        WxMpService wxMpService = wechatMpAuthProvider.getWxMpService(app);
        WxJsapiSignature jsapiSignature = null;
        try {
            jsapiSignature = wxMpService.createJsapiSignature(url);
        } catch (WxErrorException e) {
            e.printStackTrace();
        }
        return jsapiSignature;
    }
}
