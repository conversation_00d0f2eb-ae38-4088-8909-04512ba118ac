package cn.hanyi.ctm.service.stat;

import cn.hanyi.ctm.dto.journey.CalculatingFilterDto;
import cn.hanyi.ctm.dto.stat.CacheStatEventDataDto;
import cn.hanyi.ctm.dto.stat.CacheStatEventParamDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.core.utils.DateHelper;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
public class CacheStatEventService extends CacheStatService<CacheStatEventParamDto, CacheStatEventDataDto> {

    @Override
    public Class<CacheStatEventDataDto> cacheClass() {
        return CacheStatEventDataDto.class;
    }

    /**
     * 修改事件状态为已处理后，需要清除事件创建日期的缓存
     */
    public void clearByEventStatus(List<Long> ruleIds, LocalDate eventDate) {
        if (CollectionUtils.isNotEmpty(ruleIds)) {
            long second = toSecond(eventDate);
            // 需要清除待处理的统计数量
            ruleIds.forEach(k -> zSetOpt().removeRangeByScore(key(k, 2), second, second));
        }
    }

    private String key(Long eventRuleId, Integer statType) {
        return String.format("stat:event-rule:%d:%d", eventRuleId, statType);
    }

    @Override
    public String key(CacheStatEventParamDto dto) {
        if (dto.getKey() == null) {
            String key = key(dto.getEventRuleId(), dto.getStatType());
            dto.setKey(key);
            addCacheKey(key);
        }
        return dto.getKey();
    }

    @Override
    public LocalDate startLimit(CacheStatEventParamDto dto) {
        if (dto.getEventRuleId() != null && dto.getEventRuleId() > 0) {
            String sql = "SELECT create_time FROM event_monitor_rules where id = " + dto.getEventRuleId();
            List<LocalDate> createTime = jdbcTemplate.queryForList(sql, LocalDate.class);
            if (CollectionUtils.isNotEmpty(createTime)) {
                return createTime.get(0);
            }
        }
        return null;
    }

    @Override
    public CacheStatEventDataDto emptyDay(LocalDate day) {
        return new CacheStatEventDataDto(day.toString());
    }

    @Override
    protected boolean isNotEmpty(CacheStatEventDataDto cache) {
        return cache != null && cache.hasData;
    }

    @Override
    protected Map<Long, CacheStatEventDataDto> getDbDataByDateRange(CacheStatEventParamDto dto, LocalDate start, LocalDate end, CalculatingFilterDto calculatingFilter) {
        Map<Long, CacheStatEventDataDto> map = new HashMap<>();
        Long orgId = dto.getOrgId();
        Long eventRuleId = dto.getEventRuleId();
        Integer statType = dto.getStatType();
        if (orgId != null && statType != null && eventRuleId != null) {
            List<CacheStatEventDataDto> list = getRawValue(start, end, orgId, eventRuleId, statType);
            if (CollectionUtils.isNotEmpty(list)) {
                for (CacheStatEventDataDto i : list) {
                    Optional.ofNullable(DateHelper.parseAdjust(i.day)).ifPresent(datetime -> {
                        map.put(toSecond(datetime.toLocalDate()), i);
                    });
                }
            }
        }
        return map;
    }

    private List<CacheStatEventDataDto> getRawValue(LocalDate s, LocalDate e, Long orgId, Long eventRuleId, Integer statType) {
        String status;
        if (statType == 1) {
            status = "(1,2,3)"; // 全部
        } else if (statType == 2) {
            status = "(1,2)";   // 待处理
        } else {
            return null;
        }
        String sql = "select " +
                " DATE_FORMAT(er.create_time,'%Y-%m-%d') `day`," +
                " count(er.id) `count`" +
                " from event_result er" +
                " inner join event_result_rule_relation errr on errr.event_id=er.id and errr.rule_id=" + eventRuleId +
                " where er.org_id=" + orgId +
                " and er.create_time >= '" + s + "'" +
                " and er.create_time < '" + e.plusDays(1) + "'" +
                " and er.status in " + status +
                " GROUP BY DATE_FORMAT(er.create_time,'%Y-%m-%d')";
        log.info("eventStatSql：{}", sql);
        return jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(CacheStatEventDataDto.class));
    }
}
