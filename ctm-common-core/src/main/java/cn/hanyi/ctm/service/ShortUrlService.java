package cn.hanyi.ctm.service;

import cn.hanyi.ctm.dto.ShortUrlDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.extension.dto.CreateLinkDto;
import org.befun.extension.dto.CreateLinkResponseDto;
import org.befun.extension.property.LinkProperty;
import org.befun.extension.service.LinkService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class ShortUrlService {

    @Autowired
    private LinkProperty shortUrlRootProperty;
    @Autowired
    private LinkService linkService;

    public int getEvaluateLength() {
        // TBD
        if (StringUtils.isNotEmpty(shortUrlRootProperty.getRoot())) {
            return shortUrlRootProperty.getRoot().length() + 4;
        } else {
            return 4;
        }
    }

    public ShortUrlDto generate(String url) {
        if (StringUtils.isEmpty(url)) {
            return null;
        }
        log.debug("shorting url for {}", url);
        try {
            //url转换成短链
            CreateLinkResponseDto dto = linkService.generateShortUrl(new CreateLinkDto(url));
            if (dto != null
                    && StringUtils.isNotEmpty(dto.getUrl())
                    && StringUtils.isNotEmpty(dto.getId())) {
                return new ShortUrlDto(dto.getUrl(), dto.getId());
            }
        } catch (Exception e) {
            log.error("short url generator error url={}", url, e);
        }
        return null;

    }
}
