package cn.hanyi.ctm.service.stat;

import cn.hanyi.ctm.dto.journey.CalculatingFilterDto;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.DefaultTypedTuple;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

public abstract class CacheStatService<PARAM, CACHE> {


    @Autowired
    protected JdbcTemplate jdbcTemplate;
    @Autowired
    protected StringRedisTemplate stringRedisTemplate;

    protected abstract Class<CACHE> cacheClass();

    protected abstract String key(PARAM param);

    public abstract LocalDate startLimit(PARAM param);

    protected abstract CACHE emptyDay(LocalDate day);

    protected abstract boolean isNotEmpty(CACHE cache);

    @NotNull
    protected abstract Map<Long, CACHE> getDbDataByDateRange(PARAM param, LocalDate start, LocalDate end, CalculatingFilterDto calculatingFilter);

    public List<CACHE> getData(PARAM param, LocalDate start, LocalDate end, CalculatingFilterDto calculatingFilter) {
        return getData(param, start, end, true, calculatingFilter);
    }

    public List<CACHE> getData(PARAM param, LocalDate start, LocalDate end, boolean fillEmpty, CalculatingFilterDto calculatingFilter) {
        String key = key(param);
        LocalDate startLimit = startLimit(param);
        if (startLimit == null) {
            // 没有查询到原始数据的创建时间，则数据可能被删除了
            return null;
        }
        return getByRange(key, startLimit, start, end, fillEmpty, lossDate -> getDbData(param, lossDate, calculatingFilter));
    }

    /**
     * 查询指定日期区间的统计数据，
     *
     * @param fillEmpty true 如果某一天无数据，则为 {@link CacheStatService#emptyDay(LocalDate)}空数据
     */
    private List<CACHE> getByRange(String key, LocalDate startLimit, LocalDate start, LocalDate end, boolean fillEmpty, Function<List<LocalDate>, Map<Long, CACHE>> getDataByDb) {
        if (start == null) {
            start = startLimit;
        }
        if (end == null) {
            end = LocalDate.now();
        }
        LocalDate min = startLimit.isAfter(start) ? startLimit : start;
        Map<Long, String> cacheDataMap = getCacheData(key, min, end);
        Map<Long, CACHE> allData = new TreeMap<>();
        List<LocalDate> lossDate = new ArrayList<>();
        LocalDate step = start;
        while (step.isBefore(end) || step.isEqual(end)) {
            long second = toSecond(step);
            if (step.isBefore(startLimit)) {
                // 如果统计日期在原始数据创建日期之前，则直接填充empty
                if (fillEmpty) {
                    allData.put(second, emptyDay(step));
                }
            } else {
                CACHE cache = JsonHelper.toObject(cacheDataMap.get(second), cacheClass());
                if (cache == null) {
                    lossDate.add(step);
                } else if (isNotEmpty(cache) || fillEmpty) {
                    allData.put(second, cache);
                }
            }
            step = step.plusDays(1);
        }
        if (!lossDate.isEmpty()) {
            // 中间有缺失的数据，需要补齐
            Optional.ofNullable(getDataByDb.apply(lossDate)).ifPresent(dbData -> {
                //cache loss date
                cacheData(key, dbData);
                dbData.forEach((k, v) -> {
                    if (isNotEmpty(v) || fillEmpty) {
                        allData.put(k, v);
                    }
                });
            });
        }
        return new ArrayList<>(allData.values());
    }

    /**
     * 从缓存中获取指定日期区间的统计数据
     *
     * @return key 为日期的 second，value 为统计数据的 json
     */
    protected Map<Long, String> getCacheData(String key, LocalDate start, LocalDate end) {
        Map<Long, String> dataMap = new HashMap<>();
        if (start.isAfter(end)) {
            return dataMap;
        }
        Set<ZSetOperations.TypedTuple<String>> data = zSetOpt().rangeByScoreWithScores(key, toSecond(start), toSecond(end));
        if (CollectionUtils.isNotEmpty(data)) {
            data.forEach(i -> {
                Optional.ofNullable(i.getScore()).map(Double::longValue).ifPresent(k -> {
                    Optional.ofNullable(i.getValue()).ifPresent(l -> {
                        dataMap.put(k, l);
                    });
                });
            });
        }
        return dataMap;
    }

    /**
     * 从数据库中获取指定日期的统计数据，
     * 如果要查询的日期数量比较多，则转换为日期区间查询
     *
     * @return key 为日期的 second，value 为统计数据的对象
     */
    private Map<Long, CACHE> getDbData(PARAM param, List<LocalDate> lossDate, CalculatingFilterDto calculatingFilter) {
        // 如果 lossDate 数量 <= 5 则 按照这些日期查询，
        // 如果 lossDate 数量 > 5 则按照最小日期和最大日期的区间查询
        Map<Long, CACHE> dbDataMap = new HashMap<>();
        if (lossDate.size() <= 5) {
            IntStream.range(0, lossDate.size()).parallel().forEach(i -> {
                LocalDate day = lossDate.get(i);
                dbDataMap.put(toSecond(day), getDbDataByDay(param, day, calculatingFilter));
            });
        } else {
            int size = lossDate.size();
            LocalDate today = LocalDate.now();
            LocalDate yesterday = today.minusDays(1);
            Map<Long, CACHE> map;
            if (today.isEqual(lossDate.get(size - 1)) && yesterday.isAfter(lossDate.get(size - 2))) {
                map = getDbDataByDateRange(param, lossDate.get(0), lossDate.get(size - 2), calculatingFilter);
                map.put(toSecond(today), getDbDataByDay(param, today, calculatingFilter));
            } else {
                map = getDbDataByDateRange(param, lossDate.get(0), lossDate.get(size - 1), calculatingFilter);
            }
            for (LocalDate i : lossDate) {
                long second = toSecond(i);
                dbDataMap.put(second, map.getOrDefault(second, emptyDay(i)));
            }
        }
        return dbDataMap;
    }

    /**
     * 从数据库中获取某一天的统计数据，
     */
    private CACHE getDbDataByDay(PARAM param, LocalDate day, CalculatingFilterDto calculatingFilter) {
        Map<Long, CACHE> map = getDbDataByDateRange(param, day, day, calculatingFilter);
        CACHE cache = null;
        if (MapUtils.isNotEmpty(map)) {
            cache = map.get(toSecond(day));
        }
        if (cache == null) {
            return emptyDay(day);
        }
        return cache;
    }

    /**
     * 缓存每天的统计数据
     */
    protected void cacheData(String key, Map<Long, CACHE> dataMap) {
        // 不缓存大于等于今天的数据
        long now = toSecond(LocalDate.now());
        Set<ZSetOperations.TypedTuple<String>> appendCacheData = dataMap.entrySet().stream().filter(i -> i.getKey() < now) // 过滤掉今天和之后的数据，今天的数据需要实时查询
                .map(j -> new DefaultTypedTuple<>(JsonHelper.toJson(j.getValue()), j.getKey().doubleValue())).collect(Collectors.toSet());
        if (!appendCacheData.isEmpty()) {
            zSetOpt().add(key, appendCacheData);
        }
    }

    /**
     * 日期转换为秒数
     */
    protected long toSecond(LocalDate localDate) {
        return localDate.atStartOfDay(ZoneId.systemDefault()).toEpochSecond();
    }

    protected ZSetOperations<String, String> zSetOpt() {
        return stringRedisTemplate.opsForZSet();
    }

    protected SetOperations<String, String> setOpt() {
        return stringRedisTemplate.opsForSet();
    }

    public void addCacheKey(String... key) {
        setOpt().add("stat:all-keys", key);
    }

    public void removeAllStatCache() {
        Set<String> keys = setOpt().members("stat:all-keys");
        if (CollectionUtils.isNotEmpty(keys)) {
            stringRedisTemplate.delete(keys);
        }
        stringRedisTemplate.delete(List.of(
                "stat:all-keys",
                "journey-warning:value",
                "journey-warning:notify:event_stat",
                "journey-warning:notify:experience_indicator"));
    }
}
