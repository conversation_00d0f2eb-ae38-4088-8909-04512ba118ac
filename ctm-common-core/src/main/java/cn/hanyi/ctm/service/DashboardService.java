package cn.hanyi.ctm.service;

import cn.hanyi.ctm.dto.user.CreateUserDto;
import cn.hanyi.ctm.entity.Dashboard;
import cn.hanyi.ctm.repository.DashboardRepository;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.service.RoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class DashboardService {
    @Autowired
    DashboardRepository dashboardRepository;
    @Autowired
    RoleService roleService;

    /**
     * 初始化dashboard及权限
     *
     */
    @Transactional
    public void initDashboard(CreateUserDto dto) {
        Dashboard dashboard =  new Dashboard(0, 1, 0L, "根节点", 0L, 1, 2, 1, "", new ArrayList<>());
        dashboard.setOrgId(dto.getOrgId());
        dashboardRepository.save(dashboard);
        Dashboard dashboardPreview = new Dashboard(0, 0, dashboard.getId(), "根节点", 0L, 1, 2, 1, "", new ArrayList<>());
        dashboardPreview.setOrgId(dto.getOrgId());
        dashboardRepository.save(dashboardPreview);

        List<Long> dashboardIds = new ArrayList<>();
        dashboardIds.add(dashboard.getId());
        dashboardIds.add(dashboardPreview.getId());

        Optional.ofNullable(dto.getRoleId()).ifPresent(r -> r.forEach(rId -> roleService.saveDashboardPermission((Long) rId, dashboardIds)));

    }
}
