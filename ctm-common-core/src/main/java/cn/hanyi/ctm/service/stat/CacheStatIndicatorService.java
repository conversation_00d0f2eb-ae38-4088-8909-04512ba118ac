package cn.hanyi.ctm.service.stat;

import cn.hanyi.ctm.constant.SentimentType;
import cn.hanyi.ctm.constant.SubTypeFilter;
import cn.hanyi.ctm.constant.TypeFilterMethod;
import cn.hanyi.ctm.constant.survey.QuestionType;
import cn.hanyi.ctm.dto.journey.CalculatingFilterDto;
import cn.hanyi.ctm.dto.journey.TypeFilterDto;
import cn.hanyi.ctm.dto.stat.CacheStatIndicatorDataDto;
import cn.hanyi.ctm.dto.stat.CacheStatIndicatorParamDto;
import cn.hanyi.ctm.dto.survey.SurveyQuestionItemInfoDto;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.befun.auth.service.DepartmentService;
import org.befun.core.utils.DateHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.math.BigInteger;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
@Service
public class CacheStatIndicatorService extends CacheStatService<CacheStatIndicatorParamDto, CacheStatIndicatorDataDto> {

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private DepartmentService departmentService;

    @Override
    public Class<CacheStatIndicatorDataDto> cacheClass() {
        return CacheStatIndicatorDataDto.class;
    }

    @Override
    public List<CacheStatIndicatorDataDto> getData(CacheStatIndicatorParamDto param, LocalDate start, LocalDate end, CalculatingFilterDto calculatingFilter) {
        return super.getData(param, start, end, calculatingFilter);
    }

    @Override
    public List<CacheStatIndicatorDataDto> getData(CacheStatIndicatorParamDto param, LocalDate start, LocalDate end, boolean fillEmpty, CalculatingFilterDto calculatingFilter) {
        return super.getData(param, start, end, fillEmpty, calculatingFilter);
    }

    public void clearBySurvey(Long surveyId) {
        String key = relationKey(surveyId);
        Set<String> keys = setOpt().members(key);
        if (CollectionUtils.isNotEmpty(keys)) {
            stringRedisTemplate.delete(keys);
        }
    }

    /**
     * 删除答卷之后，需要清空这一天的缓存数据
     * 1 找到所有的与这个问卷关联的 key
     * 2 删除答卷日期的数据
     */
    public void clearByResponse(Long surveyId, Date responseDate) {
        clearByResponse(surveyId, DateHelper.toLocalDate(responseDate));
    }

    /**
     * 删除答卷之后，需要清空这一天的缓存数据
     * 1 找到所有的与这个问卷关联的 key
     * 2 删除答卷日期的数据
     */
    public void clearByResponse(Long surveyId, LocalDate responseDate) {
        if (responseDate == null) {
            return;
        }
        String key = relationKey(surveyId);
        Set<String> keys = setOpt().members(key);
        if (CollectionUtils.isNotEmpty(keys)) {
            long second = toSecond(responseDate);
            keys.forEach(k -> {
                zSetOpt().removeRangeByScore(k, second, second);
            });
        }
    }

    private String relationKey(Long surveyId) {
        return String.format("stat:indicator-keys:%d", surveyId);
    }

    private String key(Long surveyId, Long questionId, String itemName, BigInteger filterHash) {
        String filter = filterHash == null ? "" : ":" + String.valueOf(filterHash);
        if (StringUtils.isNotEmpty(itemName)) {
            return String.format("stat:indicator:%d:%d%s-%s", surveyId, questionId, filter, itemName);
        } else {
            return String.format("stat:indicator:%d:%d%s", surveyId, questionId, filter);
        }
    }

    @Override
    public String key(CacheStatIndicatorParamDto dto) {
        if (dto.getKey() == null) {
            String key = key(dto.getSurveyId(), dto.getQuestionId(), dto.getItemName(), dto.getFilterHash());
            dto.setKey(key);
            // 缓存与问卷相关的key
            String key2 = relationKey(dto.getSurveyId());
            setOpt().add(key2, dto.getKey());
            addCacheKey(key, key2);
        }
        return dto.getKey();
    }

    @Getter
    @Setter
    public static class SurveyResponseTime {
        LocalDateTime finishTime;
        LocalDateTime createTime;
    }

    public LocalDate findMinResponseDate(Long surveyId) {
        String sql = "SELECT min(finish_time) finishTime, min(create_time) createTime FROM survey_response where s_id=? and status = 1 and deleted = 0";
        List<SurveyResponseTime> list = jdbcTemplate.query(sql, (rs, rowNum) -> {
            Timestamp finishTime = rs.getTimestamp("finishTime");
            Timestamp createTime = rs.getTimestamp("createTime");
            if(finishTime == null || createTime == null){
                return null;
            }
            SurveyResponseTime item = new SurveyResponseTime();
            item.setFinishTime(finishTime.toLocalDateTime());
            item.setCreateTime(createTime.toLocalDateTime());
            return item;
        }, surveyId);
        list.removeIf(Objects::isNull);
        if (CollectionUtils.isNotEmpty(list)) {
            SurveyResponseTime datetime = list.get(0);
            LocalDateTime minTime = datetime.getFinishTime() != null ? datetime.getFinishTime() : datetime.getCreateTime();
            if (minTime != null) {
                return minTime.toLocalDate();
            }
        }
        return null;
    }

    @Override
    public LocalDate startLimit(CacheStatIndicatorParamDto dto) {
        // 问卷的开始时间，所有的统计数据都不会比问卷创建时间早? 问卷导入会导致 答卷时间比问卷创建时间早
        // 修改为找到最早的答卷时间
        return findMinResponseDate(dto.getSurveyId());
    }

    @Override
    public CacheStatIndicatorDataDto emptyDay(LocalDate day) {
        return new CacheStatIndicatorDataDto(day.toString());
    }

    @Override
    protected boolean isNotEmpty(CacheStatIndicatorDataDto cache) {
        return cache != null && cache.hasData;
    }

    @Override
    protected Map<Long, CacheStatIndicatorDataDto> getDbDataByDateRange(CacheStatIndicatorParamDto dto, LocalDate start, LocalDate end, CalculatingFilterDto calculatingFilter) {
        Map<Long, CacheStatIndicatorDataDto> map = new HashMap<>();
        List<CacheStatIndicatorDataDto> list = getRawValue(start, end, dto.getDepartmentId(), dto.getSurveyId(), dto.getQuestionId(), dto.getItemName(), calculatingFilter);
        if (CollectionUtils.isNotEmpty(list)) {
            for (CacheStatIndicatorDataDto i : list) {
                Optional.ofNullable(DateHelper.parseAdjust(i.day)).ifPresent(datetime -> {
                    map.put(toSecond(datetime.toLocalDate()), i);
                });
            }
        }
        return map;
    }

    private List<CacheStatIndicatorDataDto> getRawValue(LocalDate s, LocalDate e, Long departmentId, Long sId, Long qId, String itemName, CalculatingFilterDto calculatingFilter) {
        List<CacheStatIndicatorDataDto> result = new ArrayList<>();
        // 查询出问题类型和所有的选项
        Pair<QuestionType, List<SurveyQuestionItemInfoDto>> questionInfo = questionInfo(qId);
        if (questionInfo == null) {
            return result;
        }
        String itemColumns;
        // 量表
        if (questionInfo.getLeft() == QuestionType.SCORE) {
            itemColumns = itemColumn(questionInfo.getRight(), i ->
                    String.format("COUNT(if(src.i_val=%1$s,1,null)) `item%1$s`, 0 `score%1$s`", i.getValue())
            );
        } else if (questionInfo.getLeft() == QuestionType.NPS) {
            itemColumns = itemColumn(questionInfo.getRight(), i ->
                    String.format("COUNT(if(src.i_val=%1$s,1,null)) `item%1$s`, 0 `score%1$s`", i.getValue())
            );
        } else if (questionInfo.getLeft() == QuestionType.MATRIX_SCORE) {
            if (StringUtils.isEmpty(itemName)) {// 矩阵量表 未选择选项，无法计算，直接返回空
                return result;
            }
            itemColumns = itemColumn(questionInfo.getRight(), i ->
                    String.format("COUNT(if(JSON_EXTRACT(j_val,'$.%1$s')=%2$s,1,null)) `item%2$s`, 0 `score%2$s`", itemName, i.getValue())
            );
        } else if (questionInfo.getLeft() == QuestionType.EVALUATION) {
            itemColumns = itemColumn(questionInfo.getRight(), i ->
                    String.format("COUNT(if(src.s_val='%1$s',1,null)) `%1$s`, 0 `score%1$s`", i.getValue())
            );
        } else if (questionInfo.getLeft() == QuestionType.SCORE_EVALUATION) {
            itemColumns = itemColumn(questionInfo.getRight(), i ->
                    String.format("COUNT(if(src.s_val='%1$s',1,null)) `%1$s`, SUM(if(src.s_val='%1$s',cell_score,0)) `score%1$s`", i.getValue())
            );
        } else if (questionInfo.getLeft() == QuestionType.SINGLE_CHOICE) {
            itemColumns = itemColumn(questionInfo.getRight(), i ->
                    String.format("COUNT(if(src.s_val='%1$s',1,null)) `%1$s`, SUM(if(src.s_val='%1$s',cell_score,0)) `score%1$s`", i.getValue())
            );
        } else if (questionInfo.getLeft() == QuestionType.TEXT) {
            itemColumns = itemColumn(questionInfo.getRight(), i ->
                    String.format("COUNT(if(JSON_VALID(text_label)&&JSON_EXTRACT(text_label, '$.sentiment')=%1$s,text_label,null)) `item%1$s`", i.getValue())
            );
        } else {
            return result;
        }


        String sql = sqlBase(
                itemColumns,
                sqlWhereByIndicator(s, e, sId, qId, departmentId, indicatorFilterSql(calculatingFilter))
        );
        log.info("indicatorSql：{}", sql);
        List<Map<String, Object>> list = jdbcTemplate.queryForList(sql);
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        boolean mockItem = Set.of(QuestionType.SCORE, QuestionType.NPS, QuestionType.MATRIX_SCORE, QuestionType.TEXT).contains(questionInfo.getLeft());
        list.forEach(i -> {
            CacheStatIndicatorDataDto dto = new CacheStatIndicatorDataDto();
            String day = getColumnValue(i, "day", null, StringUtils::isNotEmpty, Function.identity());
            if (day != null) {
                dto.setDay(day);
                dto.setCount(getCountColumnValue(i, "count"));
                // 汇总每个选项的数量
                // item0 score0 item1 score1 item2 score2
                // 0     0      2     0      2     0
                questionInfo.getRight().forEach(j -> {
                    String itemColumn = (mockItem ? "item" : "") + j.getValue();
                    String scoreColumn = "score" + j.getValue();
                    int itemCount = getCountColumnValue(i, itemColumn);
                    int itemScore = j.getScore();
                    int totalScore = getCountColumnValue(i, scoreColumn);
                    dto.getItemCountMap().put(j.getValue(), new CacheStatIndicatorDataDto.ItemCount(j.getValue(), itemCount, itemScore, totalScore));
                });
                dto.aggregate();
                result.add(dto);
            }
        });
        return result;
    }

    private int getCountColumnValue(Map<String, Object> columnValues, String column) {
        return getColumnValue(columnValues, column, 0, NumberUtils::isDigits, Integer::parseInt);
    }

    private <T> T getColumnValue(Map<String, Object> columnValues, String column, T defaultValue, Predicate<String> test, Function<String, T> cast) {
        Object value = columnValues.get(column);
        if (value == null || !test.test(value.toString())) {
            return defaultValue;
        }
        return cast.apply(value.toString());
    }

    public Pair<QuestionType, List<SurveyQuestionItemInfoDto>> questionInfo(Long qId) {
        String sql = "select sq.type type,sqi.value,sqi.text from survey_question sq " +
                " left join survey_question_item sqi on sq.id=sqi.q_id" +
                " where sq.id=" + qId +
                " order by sqi.sequence";
        List<SurveyQuestionItemInfoDto> list = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(SurveyQuestionItemInfoDto.class));
        if (CollectionUtils.isNotEmpty(list)) {
            QuestionType questionType = list.get(0).getType();
            if (questionType == QuestionType.EVALUATION
                    || questionType == QuestionType.SCORE_EVALUATION
                    || questionType == QuestionType.SINGLE_CHOICE
            ) {
                List<SurveyQuestionItemInfoDto> item = list.stream().filter(Objects::nonNull).collect(Collectors.toList());
                if (!item.isEmpty()) {
                    return Pair.of(questionType, item);
                }
            } else if (questionType == QuestionType.SCORE
                    || questionType == QuestionType.MATRIX_SCORE
                    || questionType == QuestionType.NPS
            ) {
                return Pair.of(questionType, scoreItems(Pair.of(0, 10)));
            } else if (questionType == QuestionType.TEXT) {
                // 文本题情感  0-POSITIVE, 1-NEGATIVE
                return Pair.of(questionType, scoreItems(Pair.of(SentimentType.NEGATIVE.getFlag(), SentimentType.POSITIVE.getFlag())));
            }

        }
        return null;
    }

    private List<SurveyQuestionItemInfoDto> scoreItems(Pair<Integer, Integer> scores) {
        return IntStream.rangeClosed(scores.getLeft(), scores.getRight()).mapToObj(i -> new SurveyQuestionItemInfoDto(i + "", i)).collect(Collectors.toList());
    }

    private String itemColumn(List<SurveyQuestionItemInfoDto> itemNames, Function<SurveyQuestionItemInfoDto, String> mapToColumn) {
        return itemNames.stream()
                .map(mapToColumn)
                .collect(Collectors.joining(","));
    }

    private String sqlBase(String itemColumn, String sqlWhere) {
        return "SELECT " +
                " DATE_FORMAT(sr.finish_time,'%Y-%m-%d') `day`," +
                " COUNT(src.id) `count`," +
                itemColumn +
                " from survey_response_cell src " +
                " inner join survey_response sr " +
                " on src.r_id = sr.id " + sqlWhere +
                " GROUP BY DATE_FORMAT(sr.finish_time,'%Y-%m-%d')";
    }

    private String sqlWhereByIndicator(LocalDate s, LocalDate e, Long sId, Long qId, Long departmentId, String filterSql) {
        StringBuilder sb = new StringBuilder(" where ((");
        sb.append("sr.finish_time >= '").append(s.toString()).append("'");
        sb.append(" and sr.finish_time < '").append(e.plusDays(1)).append("'");
        sb.append(" and src.s_id = ").append(sId);
        sb.append(" and src.q_id = ").append(qId);
        // #2655 【SurveyLite】客户旅程、回收统计页、下载数据统计口径不一致
        //https://ones.ai/project/#/team/SGajWa97/task/31Ww5P5PRE2khLcH
        // status 0 未正常完成答题
        //        1 正常完成
        //        2 提前完成
        // 统计不包含中断和提前完成
        sb.append(" and sr.status = 1 and sr.deleted = 0");
        if (departmentId != null && departmentId > 0) {
            var department = departmentService.get(departmentId);
            if (department != null) {
                // 查询子部门的数据
                var subDepartmentIdList = departmentService.getSubDepartmentIdList(department.getOrgId(), departmentId, false);
                sb.append(String.format(" and sr.department_id in (%s)", subDepartmentIdList.stream().map(String::valueOf).collect(Collectors.joining(","))));               // 如果是顶层不加条件
            }
        }

        sb.append(String.format(")%s)", filterSql));
        return sb.toString();
    }

    /**
     * 构建体验指标过滤sql
     * <p>
     * connector_method in/not in （）
     * channel_id in/not in ()
     * department_id in/not in ()
     *
     * @param calculatingFilterDto
     * @return
     */
    private String indicatorFilterSql(CalculatingFilterDto calculatingFilterDto) {
        var sb = new StringBuilder();
        var logic = new StringBuilder();

        Optional.ofNullable(calculatingFilterDto).ifPresent(c -> {
            var logicMethod = new StringBuilder();
            var flag = c.getLogicType().getSql();
            for (TypeFilterMethod method : TypeFilterMethod.values()) {
                logicMethod.append(filterMethod(method, c.getFilter(), flag));
            }
            logic.append(logicMethod.substring(flag.length() + 1));
        });
        if (logic.length() > 0) {
            sb.append(String.format(" and (%s)", logic));
        }
        return sb.toString();
    }

    private String filterMethod(TypeFilterMethod method, List<TypeFilterDto> filterMethodDto, String flag) {
        StringBuilder sb = new StringBuilder();

        Set<Long> departmentId = new HashSet<>();
        Set<Long> collectionMethod = new HashSet<>();
        Set<Long> channelId = new HashSet<>();

        for (TypeFilterDto filter : filterMethodDto) {
            if (method.equals(filter.getMethod())) {
                Optional.ofNullable(filter.getSubFilter()).ifPresent(subFilter -> {
                    subFilter.forEach(f -> {
                        switch (f.getType()) {
                            case CHANNEL:
                                // 一店一码使用department_id 过滤
                                if (SubTypeFilter.DEPARTMENT.equals(f.getSubType())) {
                                    departmentId.add(f.getId());
                                } else {
                                    collectionMethod.add(f.getId());
                                }
                                break;
                            case CHANNEL_NAME:
                                channelId.add(f.getId());
                                break;
                        }
                    });

                });

            }
        }

        if (!departmentId.isEmpty()) {
            sb.append(String.format(" %s sr.department_id %s (%s) ", flag, method.getSql(), StringUtils.join(departmentId, ",")));
        }
        if (!collectionMethod.isEmpty()) {
            sb.append(String.format(" %s sr.connector_method %s (%s) ", flag, method.getSql(), StringUtils.join(collectionMethod, ",")));
        }
        if (!channelId.isEmpty()) {
            sb.append(String.format(" %s sr.channel_id %s (%s) ", flag, method.getSql(), StringUtils.join(channelId, ",")));
        }

        return sb.toString();
    }
}
