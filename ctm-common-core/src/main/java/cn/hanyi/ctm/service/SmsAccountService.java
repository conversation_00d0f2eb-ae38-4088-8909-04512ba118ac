//package cn.hanyi.ctm.service;
//
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.collections.MapUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.commons.lang3.math.NumberUtils;
//import org.befun.auth.service.UserTaskService;
//import org.befun.task.constant.TaskStatus;
//import org.befun.task.dto.TaskProgressDto;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.data.redis.core.HashOperations;
//import org.springframework.data.redis.core.StringRedisTemplate;
//import org.springframework.data.redis.core.ValueOperations;
//import org.springframework.jdbc.core.JdbcTemplate;
//import org.springframework.stereotype.Service;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//import java.util.Optional;
//
//@Service
//@ConditionalOnProperty(name = "ctm.enable-limit-sms", havingValue = "true", matchIfMissing = true)
//public class SmsAccountService implements ISmsAccountService {
//
//    @Autowired
//    private JdbcTemplate jdbcTemplate;
//    @Autowired
//    private StringRedisTemplate stringRedisTemplate;
//    @Autowired
//    private UserTaskService userTaskService;
//
//    @Override
//    public int recharge(Long orgId, int amount) {
//        Long balance = getValueOpt().increment(balanceKey(orgId), amount);
//        if (balance != null) {
//            syncToDb(orgId);
//            return balance.intValue();
//        }
//        return 0;
//    }
//
//    @Override
//    public int calcNumberByText(String content) {
//        if (StringUtils.isEmpty(content)) {
//            return 0;
//        }
//        if (content.length() <= 70) {
//            return 1;
//        }
//        int length = content.length();
//        return length % 67 == 0 ? length / 67 : (length / 67 + 1);
//    }
//
//    /**
//     * 查询真实余额
//     */
//    public int balance(Long orgId) {
//        String cache = getValueOpt().get(balanceKey(orgId));
//        if (NumberUtils.isDigits(cache)) {
//            return Integer.parseInt(cache);
//        }
//
//        String sql = String.format("select sms from organization_wallet where org_id = %d limit 1", orgId);
//        List<Integer> balanceList = jdbcTemplate.queryForList(sql, Integer.class);
//        Integer balance = null;
//        if (CollectionUtils.isNotEmpty(balanceList)) {
//            balance = balanceList.get(0);
//        }
//        balance = balance == null ? 0 : balance;
//        getValueOpt().set(balanceKey(orgId), balance.toString());
//        return balance;
//    }
//
//    /**
//     * 查询可用余额: 真实余额-冻结余额
//     */
//    public int availableBalance(Long orgId) {
//        int balance = balance(orgId);
//        if (balance <= 0) {
//            return 0;
//        }
//        int availableBalance = balance - countFrozen(orgId);
//        return Math.max(0, availableBalance);
//    }
//
//    /**
//     * 查询冻结余额
//     */
//    private int countFrozen(Long orgId) {
//        String frozenKey = frozenKey(orgId);
//        Map<String, String> frozen = getHashOpt().entries(frozenKey);
//        if (MapUtils.isNotEmpty(frozen)) {
//            List<String> release = new ArrayList<>();
//            List<Integer> count = new ArrayList<>();
//            frozen.forEach((k, v) -> {
//                if (keepFrozen(k)) {
//                    if (NumberUtils.isDigits(v)) {
//                        count.add(Integer.parseInt(v));
//                    }
//                } else {
//                    release.add(k);
//                }
//            });
//            if (!release.isEmpty()) {
//                stringRedisTemplate.opsForHash().delete(frozenKey, release.toArray());
//            }
//            return count.stream().mapToInt(i -> i).sum();
//        }
//        return 0;
//    }
//
//    /**
//     * 是否需要继续保持冻结数量
//     * 如果 taskProgress 不存在，或者状态不是 INIT RUNNING ，则清除掉冻结数量
//     */
//    private boolean keepFrozen(String taskProgressId) {
//        long id;
//        if (!NumberUtils.isDigits(taskProgressId) || (id = Long.parseLong(taskProgressId)) <= 0) {
//            return false;
//        }
//        TaskStatus status = Optional.ofNullable(userTaskService.progress(id)).map(TaskProgressDto::getStatus).orElse(TaskStatus.NONE);
//        return status == TaskStatus.INIT || status == TaskStatus.RUNNING;
//    }
//
//    /**
//     * 增加冻结记录
//     */
//    public void frozen(Long orgId, Long taskProgressId, Integer cost) {
//        getHashOpt().put(frozenKey(orgId), taskProgressId.toString(), cost.toString());
//    }
//
//    /**
//     * 是否有额度
//     */
//    public boolean hasBalance(Long orgId, Integer cost) {
//        int balance = balance(orgId);
//        return balance - cost >= 0;
//    }
//
//    /**
//     * 使用额度，可能会导致余额小于0，
//     */
//    public void consumer(Long orgId, Integer cost) {
//        getValueOpt().decrement(balanceKey(orgId), cost);
//    }
//
//    @Override
//    public void consumer(Long orgId, Long taskProgressId, Integer cost) {
//        getValueOpt().decrement(balanceKey(orgId), cost);
//        getHashOpt().increment(frozenKey(orgId), taskProgressId.toString(), -cost);
//    }
//
//    /**
//     * 短信发送完毕， 删除冻结记录， 重新把缓存余额写入数据库
//     */
//    public void releaseFrozen(Long orgId, Long taskProgressId) {
//        String frozenKey = frozenKey(orgId);
//        String hkKey = taskProgressId.toString();
//        if (getHashOpt().hasKey(frozenKey, hkKey)) {
//            getHashOpt().delete(frozenKey(orgId), hkKey);
//            syncToDb(orgId);
//        }
//    }
//
//    public void syncToDb(Long orgId) {
//        int balance = balance(orgId);
//        String sql = String.format("update organization_wallet set sms=%d where org_id=%d", balance, orgId);
//        jdbcTemplate.update(sql);
//    }
//
//    private HashOperations<String, String, String> getHashOpt() {
//        return stringRedisTemplate.opsForHash();
//    }
//
//    private ValueOperations<String, String> getValueOpt() {
//        return stringRedisTemplate.opsForValue();
//    }
//
//    private String frozenKey(Long orgId) {
//        return String.format("sms.frozen.%d", orgId);
//    }
//
//    private String balanceKey(Long orgId) {
//        return String.format("limiter.smsbalance.%d", orgId);
//    }
//
//}
