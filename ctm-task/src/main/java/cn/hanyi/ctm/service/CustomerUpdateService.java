package cn.hanyi.ctm.service;

import cn.hanyi.ctm.constant.AnswerStatus;
import cn.hanyi.ctm.constant.RecordType;
import cn.hanyi.ctm.constant.SendStatus;
import cn.hanyi.ctm.constant.UtmMedium;
import cn.hanyi.ctm.entity.Customer;
import cn.hanyi.ctm.entity.CustomerAnswers;
import cn.hanyi.ctm.entity.CustomerHistoryRecord;
import cn.hanyi.ctm.repository.CustomerAnswersRepository;
import cn.hanyi.ctm.repository.CustomerHistoryRecordsRepository;
import cn.hanyi.survey.core.dto.message.SurveyResponseMessageDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
public class CustomerUpdateService {

    @Autowired
    private CustomerService customerService;

    @Autowired
    private CustomerAnswersRepository customerAnswersRepository;

    @Autowired
    private CustomerHistoryRecordsRepository customerHistoryRecordsRepository;

    @Autowired
    private CustomerRecordService customerRecordService;

    /**
     * 更新客户答卷状态
     * 1 如果存在 customerId 则直接更新
     * 2
     *
     * @param message
     */
    public void updateCutomerAnswers(SurveyResponseMessageDto message, Customer customer) {

        if (customer == null) {
            return;
        }
        if (StringUtils.isNotEmpty(message.getTrackId())) {
            List<CustomerAnswers> customerAnswersList = new ArrayList<>();
            String[] track = message.getTrackId().split(":");

            if (ArrayUtils.contains(track, "record")) {
                Long journeyRecordId = Long.parseLong(track[track.length - 1]);
                customerAnswersList =
                        customerAnswersRepository.findAllByCustomerIdAndSidAndJourneyRecordId(
                                customer.getId(), String.valueOf(message.getSurveyId()), journeyRecordId);

            } else if (ArrayUtils.contains(track, "batch")) {
                String utmCampaign = track[track.length - 1];
                customerAnswersList =
                        customerAnswersRepository.findAllByCustomerIdAndSidAndUtmCampaign(
                                customer.getId(), String.valueOf(message.getSurveyId()), utmCampaign);
            }
            customerAnswersList.forEach(
                    customerAnswers -> {
                        customerAnswers.setDurationSeconds(message.getDurationInSeconds());
                        customerAnswers.setAnswerId(message.getResponseId());
                        customerAnswers.setAnswerTime(new Date(message.getFinishTime()));
                        customerAnswers.setAnswerStatus(AnswerStatus.SUBMITTED);
                    });
            customerAnswersRepository.saveAll(customerAnswersList);
        }
        // 1 通过 customerId 和 answerId 查询是否已经更新过填答数据
        if (customerAnswersRepository.countByCustomerIdAndAnswerId(customer.getId(), message.getResponseId()) == 0) {
            // 2 如果没有填答，通过 customerId 和 clientId 查询 填答记录
            Optional<CustomerAnswers> optional = customerAnswersRepository.findFirstByCustomerIdAndSidAndUtmMediumAndUtmCampaign(customer.getId(), message.getSurveyId() + "", UtmMedium.SURVEY_CHANNEL.name(), message.getClientId());
            CustomerAnswers answers;
            if (optional.isPresent()) {
                answers = optional.get();
            } else {
                answers = new CustomerAnswers();
                answers.setCustomer(customer);
                answers.setDepartmentIds(message.getDepartmentId() == null ? "" : message.getDepartmentId() + "");
                answers.setChannel(customerRecordService.getInteractionCollectorType(message.getChannelId()));
                answers.setSurveyName(message.getSurveyName());
                answers.setSid(message.getSurveyId() + "");
                answers.setUtmMedium(UtmMedium.SURVEY_CHANNEL.name());
                answers.setUtmCampaign(message.getClientId());
            }
            answers.setSendStatus(SendStatus.SUCCESS);
            answers.setAnswerId(message.getResponseId());
            answers.setAnswerTime(new Date());
            answers.setAnswerStatus(AnswerStatus.SUBMITTED);
            answers.setDurationSeconds(message.getDurationInSeconds());
            customerAnswersRepository.save(answers);
        }

        CustomerHistoryRecord customerHistoryRecord = new CustomerHistoryRecord();
        customerHistoryRecord.setCustomerId(customer.getId());
        customerHistoryRecord.setRecordType(RecordType.FILLEDSURVEY);
        customerHistoryRecord.setRecordContent(customerRecordService.buildRecordContent(RecordType.FILLEDSURVEY,
                "", customer.getUsername(), "", message.getSurveyName(), null, null));
        customerHistoryRecordsRepository.save(customerHistoryRecord);
    }

}
