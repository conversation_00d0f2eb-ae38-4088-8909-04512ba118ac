package cn.hanyi.ctm.repository;

import cn.hanyi.ctm.entity.CustomerAnswers;
import org.befun.core.repository.ResourceRepository;
import org.springframework.stereotype.Repository;
import java.util.List;
import java.util.Optional;

@Repository
public interface CustomerAnswersRepository extends ResourceRepository<CustomerAnswers, Long> {
    List<CustomerAnswers> findAllByCustomerIdAndSidAndJourneyRecordId(Long customerId, String Sid, Long journeyRecordId);
    List<CustomerAnswers> findAllByCustomerIdAndSidAndUtmCampaign(Long customerId, String Sid, String utmCampaign);
    long countByCustomerIdAndAnswerId(Long customerId,Long answerId);
    Optional<CustomerAnswers> findFirstByCustomerIdAndSidAndUtmMediumAndUtmCampaign(Long customerId, String sid, String utmMedium, String utmCampaign);
    Optional<CustomerAnswers> findTopOneByJourneyRecordIdOrderByCreateTimeDesc(Long journeyRecordId);
}
