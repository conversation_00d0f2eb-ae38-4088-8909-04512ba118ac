package cn.hanyi.ctm.repository;

import cn.hanyi.ctm.entity.CustomerExperienceIndicator;
import org.befun.core.repository.ResourceRepository;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Repository
public interface CustomerExperienceIndicatorRepository extends ResourceRepository<CustomerExperienceIndicator, Long> {

    @Transactional
    void deleteByOrgIdAndIdNotIn(Long orgId, List<Long> ids);

    Optional<CustomerExperienceIndicator> findTopOneByCustomerIdAndSurveyIdAndQuestionIdInOrderByCreateTimeDesc(Long customerId, Long surveyId, List<Long> questionIds);


}
