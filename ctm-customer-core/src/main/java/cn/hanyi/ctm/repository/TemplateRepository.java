package cn.hanyi.ctm.repository;

import org.befun.core.repository.ResourceRepository;
import cn.hanyi.ctm.constant.connector.ConnectorType;
import cn.hanyi.ctm.constant.SourceType;
import cn.hanyi.ctm.entity.Connector;
import cn.hanyi.ctm.entity.Template;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface TemplateRepository extends ResourceRepository<Template, Long> {
    Optional<Template> findTemplateByConnector(Connector connector);
    Optional<Template> findByOrgIdAndAndId(Long orgId, Long id);
    Optional<Template> findByName(String TemplateName);
    Iterable<Template> findAllTemplateByConnector(Connector connector);
    Optional<Template> findTemplateBySourceTypeAndType(SourceType soruceType, ConnectorType type);
    List<Template> findAllByConnector(Connector connector);
    int deleteAllByConnector(Connector connector);
}
