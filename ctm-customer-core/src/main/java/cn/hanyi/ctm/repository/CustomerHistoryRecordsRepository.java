package cn.hanyi.ctm.repository;

import cn.hanyi.ctm.constant.RecordType;
import cn.hanyi.ctm.entity.Customer;
import cn.hanyi.ctm.entity.CustomerHistoryRecord;
import org.befun.core.repository.ResourceRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CustomerHistoryRecordsRepository extends ResourceRepository<CustomerHistoryRecord, Long> {
    List<CustomerHistoryRecord> findAllByCustomerId(Long customerId);
    List<CustomerHistoryRecord> findAllByCustomerIdAndRecordType(Long customerId, RecordType recordType);
}
