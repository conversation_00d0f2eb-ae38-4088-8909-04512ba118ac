package cn.hanyi.ctm.repository;

import cn.hanyi.ctm.entity.Connector;
import cn.hanyi.ctm.entity.ConnectorConsumer;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Repository
public interface ConnectorConsumerRepository extends ResourceRepository<ConnectorConsumer, Long> {
    Optional<ConnectorConsumer> findByConnector(Connector connector);

    Optional<ConnectorConsumer> findByConnectorAndRelationId(Connector connector, Long relationId);

    List<ConnectorConsumer> findByRelationIdAndEnable(Long relationId, boolean enable);

    List<ConnectorConsumer> findByRelationIdInAndEnable(Set<Long> relationIds, boolean enable);

    ConnectorConsumer findOneByRelationId(Long relationId);

    List<ConnectorConsumer> findByRelationId(Long relationId);

    void deleteByConnector(Connector connector);

    @Modifying
    @Transactional
    void deleteByRelationId(Long relationId);
}
