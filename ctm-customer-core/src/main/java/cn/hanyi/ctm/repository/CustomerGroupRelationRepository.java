package cn.hanyi.ctm.repository;

import cn.hanyi.ctm.entity.CustomerGroupRelation;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Modifying;

import java.util.Collection;
import java.util.List;

public interface CustomerGroupRelationRepository extends ResourceRepository<CustomerGroupRelation, Long> {

    @Modifying
    void deleteByGroupId(Long groupId);

    @Modifying
    void deleteByCustomerId(Long customerId);

    List<CustomerGroupRelation> findByCustomerId(Long customerId);

    List<CustomerGroupRelation> findByCustomerIdIn(Collection<Long> customerIds);

    List<CustomerGroupRelation> findByGroupIdAndCustomerId(Long groupId, Long customerId);

    List<CustomerGroupRelation> findByGroupIdAndCustomerIdIn(Long groupId, List<Long> customerIds);
}