package cn.hanyi.ctm.repository;

import cn.hanyi.ctm.entity.CustomerJourneyRecord;
import org.befun.core.repository.ResourceRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CustomerJourneyRecordsRepository extends ResourceRepository<CustomerJourneyRecord, Long> {
    List<CustomerJourneyRecord> findByCustomerIdAndJourneyId(Long customerId, Long journeyId);
}
