package cn.hanyi.ctm.repository;

import org.befun.core.repository.ResourceRepository;
import cn.hanyi.ctm.entity.Connector;
import cn.hanyi.ctm.entity.ThirdPartyCustomer;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Repository
public interface ThirdPartyCustomerRepository extends ResourceRepository<ThirdPartyCustomer, Long> {
    Optional<ThirdPartyCustomer> findThirdPartyCustomerByConnectorAndOpenId(Connector connector, String openId);
    List<ThirdPartyCustomer> findAllByOpenId(String openId);
    List<ThirdPartyCustomer> findALLByConnector(Connector connector);
    void deleteAllByConnector(Connector connector);
    Optional<ThirdPartyCustomer> findFirstByThirdpartyAuthIdAndOpenId(Long thirdpartyAuthId, String openId);

    @Transactional
    @Modifying
    @Query("update ThirdPartyCustomer t set t.thirdpartyAuthId = ?1 where t.connector = ?2")
    int updateThirdpartyAuthIdByConnector(Long thirdpartyAuthId, Connector connector);
    ThirdPartyCustomer findFirstByOrgIdAndOpenId(Long orgId, String openId);
    List<ThirdPartyCustomer> findByOrgIdAndOpenId(Long orgId, String openId);
    List<ThirdPartyCustomer> findByOrgIdAndThirdpartyAuthId(Long orgId, Long thirdpartyAuthId, Pageable pageable);
    ThirdPartyCustomer findFirstByOrgIdAndThirdpartyAuthIdAndOpenId(Long orgId, Long thirdpartyAuthId, String openId);

}
