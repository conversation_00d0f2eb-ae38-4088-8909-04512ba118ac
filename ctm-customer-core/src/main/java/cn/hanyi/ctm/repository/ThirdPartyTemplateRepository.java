package cn.hanyi.ctm.repository;

import cn.hanyi.ctm.constant.TemplateCreateType;
import cn.hanyi.ctm.constant.connector.ConnectorType;
import cn.hanyi.ctm.entity.Connector;
import cn.hanyi.ctm.entity.ThirdPartyTemplate;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Repository
public interface ThirdPartyTemplateRepository extends ResourceRepository<ThirdPartyTemplate, Long> {
    List<ThirdPartyTemplate> findAllByConnector(Connector connector);
    Optional<ThirdPartyTemplate> findThirdPartyTemplateByOpenId(String openId);
    Optional<ThirdPartyTemplate> findByConnectorAndOpenId(Connector connector, String openId);
    void deleteAllByConnector(Connector connector);
    List<ThirdPartyTemplate> findByCreateTypeNot(TemplateCreateType createType);
    List<ThirdPartyTemplate> findByThirdpartyAuthId(Long thirdpartyAuthId);
    ThirdPartyTemplate findFirstByThirdpartyAuthIdAndIdIn(Long thirdpartyAuthId, Collection<Long> ids);
    List<ThirdPartyTemplate> findByConnectorType(ConnectorType connectorType);

    @Transactional
    @Modifying
    @Query("update ThirdPartyTemplate t set t.thirdpartyAuthId = ?1 where t.connector = ?2")
    int updateThirdpartyAuthIdByConnector(Long thirdpartyAuthId, Connector connector);

}
