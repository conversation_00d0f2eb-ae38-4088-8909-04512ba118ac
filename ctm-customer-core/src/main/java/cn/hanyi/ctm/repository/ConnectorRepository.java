package cn.hanyi.ctm.repository;

import cn.hanyi.ctm.constant.connector.ConnectorProviderType;
import cn.hanyi.ctm.constant.connector.ConnectorPushType;
import cn.hanyi.ctm.constant.connector.ConnectorType;
import cn.hanyi.ctm.constant.SourceType;
import cn.hanyi.ctm.entity.Connector;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface ConnectorRepository extends ResourceRepository<Connector, Long> {
    Optional<Connector> findConnectorByAppId(String appId);
    Optional<Connector> findByOrgIdAndAndId(Long orgId, Long id);
    Optional<Connector> findByOrOrgIdAndProviderType(Long orgId, ConnectorProviderType providerType);
    Optional<Connector> findByOrOrgIdAndType(Long orgId, ConnectorType type);
    Optional<Connector> findByOrgIdAndAndAppId(Long orgId, String appId);
    Optional<Connector> findConnectorByTypeAndSource(ConnectorType type, SourceType source);
    Optional<Connector> findByOrgIdAndType(Long OrgId, ConnectorType type);
    List<Connector> findAllByOrgId(Long orgId);
    List<Connector> findAllByIsDefault(Boolean isDefault);
    List<Connector> findAllByAppId(String appId);
    void deleteByOrgIdAndId(Long orgId, Long id);
    Optional<Connector> findFirstByOrgIdAndTypeAndProviderType(Long orgId, ConnectorType type,ConnectorProviderType providerType);
    @Query(
            nativeQuery = true,
            value = "select * from connector " +
                    "where is_delete = 0 and type = ?2 and provider_type =?3 " +
                    "and authorize_status = ?4 and related_id like %?5% " +
                    "and push_type = ?6 and conditions like %?7% and status = ?8 ")
    List<Connector> findWaringConnector(
            int type,
            int providerType,
            int authorizeStatus,
            Long relatedId,
            int pushType,
            String condition,
            int status
    );

    @Query(
            nativeQuery = true,
            value = "select * from connector " +
                    "where is_delete = 0 and id in (?1) and push_type = ?2 and conditions like %?3%")
    List<Connector> findByIdInAndPushTypeAndConditionContains(Set<Long> ids, int pushType, String condition);

    List<Connector> findByIdInAndPushType(Collection<Long> ids, ConnectorPushType pushType);
}
