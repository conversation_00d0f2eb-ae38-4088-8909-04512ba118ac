package cn.hanyi.ctm.repository;

import cn.hanyi.ctm.entity.Customer;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Repository
public interface CustomerRepository extends ResourceRepository<Customer, Long> {
    List<Customer> findByIdInOrderByCreateTimeDesc(Collection<Long> ids);

    List<Customer> findAllByOrgIdAndIdIn(Long orgId, List<Long> ids);

    List<Customer> findAllByOrgId(Long orgId);

    Optional<Customer> findCustomerByOrgIdAndId(Long orgId, Long id);

    Optional<Customer> findCustomerByThirdPartyCustomerId(Long tpCustomer);

    List<Customer> findAllByThirdPartyCustomerIdIn(List<Long> thirdPartyCustomers);

    Optional<Customer> findFirstByOrgIdAndExternalUserId(Long orgId, String externalUserId);

    Optional<Customer> findFirstByOrgIdAndMobileOrderByIdDesc(Long orgId, String mobile);

    @Query(nativeQuery = true,
            value = "select * from customer c where c.org_id= ?1 and (c.id=?2 or c.external_user_id=?3)")
    Optional<Customer> findFirstByOrgIdAndIdOrExternalUserId(Long orgId, Long id, String externalUserId);


    long deleteByIdIn(Collection<Long> ids);

    List<Customer> findByOrgIdAndThirdPartyCustomerId(Long orgId, Long tpCustomer);

    @Query(nativeQuery = true,
            value = "select * from customer c where c.org_id= ?1 and c.department_id is not null and c.department_id not in ?2")
    List<Customer> findAllByOrgIdAndNotDepartments(@Param("orgId") Long orgId, @Param("departmentIds") List<Long> departmentIds);

    @Modifying
    @Query(nativeQuery = true, value = "update customer c set c.authorize_status=0 where org_id=?1 " +
            " and exists (select 1 from thirdparty_customer tc where tc.connector_id=?2 and tc.id=c.thirdparty_customer_id)")
    int updateThirdPartCustomerAuthorizeStatus(Long orgId, Long connectorId);

    @Modifying
    @Query(nativeQuery = true, value = "delete from customer c where org_id=?1 " +
            " and exists (select 1 from thirdparty_customer tc where tc.connector_id=?2 and tc.id=c.thirdparty_customer_id)")
    int deleteThirdPartCustomer(Long orgId, Long connectorId);

    @Query(nativeQuery = true,
            value = "select external_user_id from customer c where c.org_id= ?1 group by external_user_id")
    List<String> findAllexternalUserId(@Param("orgId") Long orgId);

    @Modifying
    @Query(nativeQuery = true, value = "update customer set department_id=0, department_names='' " +
            "where org_id=?1 and department_id not in (select d.id from department d where d.org_id=?1)")
    int resetCustomerDepartment(Long orgId);

    @Modifying
    @Query(nativeQuery = true, value = "update customer set department_id=0, department_names='' " +
            "where org_id=?1 and department_id is null")
    int resetCustomerDepartmentNull(Long orgId);

    @Modifying
    @Query(nativeQuery = true, value = "update customer set department_id=?1 where org_id=?2 and id in ?3")
    int updateCustomerDepartment(@Param("departmentId") Long departmentId, @Param("orgId") Long orgId, @Param("ids") List<Long> ids);

    @Modifying
    @Query(nativeQuery = true, value = "update customer set belong_to_uids=?1 where org_id=?2 and id in ?3")
    int updateCustomerBelongUsers(@Param("userIds") String userIds, @Param("orgId") Long orgId, @Param("ids") List<Long> ids);

    long deleteByOrgIdAndThirdPartyCustomerIdIn(Long orgId, Collection<Long> thirdPartyCustomerIds);

}
