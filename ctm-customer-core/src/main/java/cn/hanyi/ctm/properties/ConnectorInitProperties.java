package cn.hanyi.ctm.properties;

import cn.hanyi.ctm.constant.TemplateCreateType;
import cn.hanyi.ctm.constant.connector.ConnectorProviderType;
import cn.hanyi.ctm.constant.connector.ConnectorType;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@Configuration()
@ConfigurationProperties(prefix = "ctm.connector")
@Validated
public class ConnectorInitProperties {

    private Map<ConnectorType, String> connectorName = new HashMap<>();
    private Map<ConnectorType, Boolean> enableCustom = new HashMap<>();
    private List<InitTemplate> templates = new ArrayList<>();

    @Getter
    @Setter
    public static class InitTemplate {

        private TemplateCreateType createType;
        private String name;
        private ConnectorType connectorType;

    }
}
