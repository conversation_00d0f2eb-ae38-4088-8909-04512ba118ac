package cn.hanyi.ctm.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@Configuration()
@ConfigurationProperties(prefix = "ctm.placeholder")
public class PlaceHolderProperties {


    private List<PlaceHolderConfig> config = new ArrayList<>();
    private List<PlaceHolderValue> placeholders = new ArrayList<>();

    @Getter
    @Setter
    public static class PlaceHolderConfig {

        private String label;
        private String value;
        private List<PlaceHolderConfig> children;

    }

    @Getter
    @Setter
    public static class PlaceHolderValue {
        private String name;
        private String type;
        private String title;
        private String key;

        public String getKey() {
            if (key == null) {
                key = "${" + name + "}";
            }
            return key;
        }

        public Map<String, Object> toMap() {
            return Map.of("name", name, "type", type == null ? "common" : type, "title", title);
        }
    }

}
