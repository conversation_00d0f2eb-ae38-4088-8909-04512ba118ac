package cn.hanyi.ctm.dto.customer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SyncCustomerResponseDto {

    @Schema(description = "客户id")
    private Long id;
    @Schema(description = "客户编号")
    private String externalUserId;
}
