package cn.hanyi.ctm.dto.ext;

import cn.hanyi.ctm.entity.CustomerGroup;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.BaseEntityDTO;

@Getter
@Setter
public class CustomerGroupExtDto extends BaseEntityDTO<CustomerGroup> {

    public CustomerGroupExtDto(CustomerGroup entity) {
        super(entity);
    }

    public CustomerGroupExtDto() {
    }

    @Schema(description = "组内客户数量")
    private int countCustomers;
}
