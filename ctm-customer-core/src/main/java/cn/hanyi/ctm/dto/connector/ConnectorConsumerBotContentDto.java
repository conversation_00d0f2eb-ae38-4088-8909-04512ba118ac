package cn.hanyi.ctm.dto.connector;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.*;
import org.befun.core.dto.BaseDTO;
import org.befun.core.rest.view.ResourceViews;

import java.util.Arrays;
import java.util.List;


/**
 * <AUTHOR>
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ConnectorConsumerBotContentDto extends BaseDTO {

    @JsonView(ResourceViews.Basic.class)
    private String title;
    @JsonView(ResourceViews.Basic.class)
    private String content;

    public List<String> splitContent() {
        return Arrays.asList(content.split("\\r?\\n|\\r"));
    }
}
