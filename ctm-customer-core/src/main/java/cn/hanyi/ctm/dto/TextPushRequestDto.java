package cn.hanyi.ctm.dto;

import cn.hanyi.ctm.dto.journey.PushMomentDto;
import org.befun.core.dto.BaseDTO;
import cn.hanyi.ctm.constant.connector.ConnectorProviderType;
import cn.hanyi.ctm.constant.connector.ConnectorType;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class TextPushRequestDto extends BaseDTO {

    private Map<String, Object> parameters;
    private Map<String, Object> content;
    @JsonProperty(required = true)
    private Long orgId;

    private List<Long> customerIds;

    @JsonProperty(required = true)
    private ConnectorType connectorType;

    @JsonProperty(required = true)
    private ConnectorProviderType providerType;

    private Boolean isDebug = false;
    private Boolean isDetail = false;

    private PushMomentDto momentDto;
}
