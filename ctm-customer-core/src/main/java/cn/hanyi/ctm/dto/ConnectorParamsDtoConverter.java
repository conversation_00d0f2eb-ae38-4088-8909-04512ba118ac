package cn.hanyi.ctm.dto;

import cn.hanyi.ctm.dto.connector.ConnectorParamsDto;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.utils.JsonHelper;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.List;

@Converter
public class ConnectorParamsDtoConverter implements AttributeConverter<List<ConnectorParamsDto>, String> {

    @Override
    public String convertToDatabaseColumn(List<ConnectorParamsDto> list) {
        if (list == null) {
            return null;
        } else {
            return JsonHelper.toJson(list);
        }
    }

    @Override
    public List<ConnectorParamsDto> convertToEntityAttribute(String dbData) {
        if (StringUtils.isEmpty(dbData)) {
            return null;
        } else {
            return JsonHelper.toList(dbData, ConnectorParamsDto.class);
        }
    }
}