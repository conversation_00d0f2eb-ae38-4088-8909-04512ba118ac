package cn.hanyi.ctm.dto.customer;

import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import org.befun.core.dto.resource.BaseResourceBatchRequestDto;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class BatchJourneyTriggerRequestDto extends BaseResourceBatchRequestDto {

    private String details = "";

    private Long createdByUid;

    private Long departmentId;

    private String departmentTitle;

    private Long journeyId;

    private String journeyTitle;

    private String createdByName;
}
