package cn.hanyi.ctm.dto.connector;

import cn.hanyi.ctm.constant.ConnectorParamsType;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.*;
import org.befun.core.dto.BaseDTO;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;


/**
 * <AUTHOR>
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ConnectorParamsDto extends BaseDTO {

    @JsonView(ResourceViews.Basic.class)
    private String name;
    @JsonView(ResourceViews.Basic.class)
    private Object value;
    @JsonView(ResourceViews.Basic.class)
    @Enumerated(EnumType.STRING)
    private ConnectorParamsType type;
}
