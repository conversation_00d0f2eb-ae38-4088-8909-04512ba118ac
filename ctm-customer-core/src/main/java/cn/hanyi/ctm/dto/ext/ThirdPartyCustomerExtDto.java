package cn.hanyi.ctm.dto.ext;

import cn.hanyi.ctm.entity.ThirdPartyCustomer;
import cn.hanyi.ctm.entity.ThirdPartyTemplate;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.auth.config.WechatOpenConfig;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;

@Getter
@Setter
public class ThirdPartyCustomerExtDto extends BaseEntityDTO<ThirdPartyCustomer> {

    public ThirdPartyCustomerExtDto(ThirdPartyCustomer entity) {
        super(entity);
    }

    public ThirdPartyCustomerExtDto() {
    }

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "微信公众号配置")
    private WechatOpenConfig wechatOpenConfig;
}
