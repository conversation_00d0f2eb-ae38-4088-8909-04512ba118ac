package cn.hanyi.ctm.dto.connector;

import cn.hanyi.ctm.entity.Connector;
import cn.hanyi.ctm.entity.ThirdPartyCustomer;
import org.apache.logging.log4j.message.ReusableMessage;
import org.befun.core.dto.BaseDTO;
import cn.hanyi.ctm.constant.GenderType;
import lombok.*;

/**
 * ThirdpartyCustomerDto: 第三方通用客户
 *
 * <AUTHOR>
 */
@Setter
@Getter
@NoArgsConstructor
@Builder
@AllArgsConstructor
public class ThirdPartyCustomerDto extends BaseDTO {
    private String openId;
    private String unionId;
    private String name;
    private String nickname;
    private String avatar;
    private GenderType sex;
    private String remark;
    private String city;
    private String province;
    private String country;
    private boolean isSubscribed;
    private Long subscribedAt;

    public ThirdPartyCustomer mapToEntity(long orgId, long thirdpartyAuthId) {
        ThirdPartyCustomer thirdPartyCustomer = new ThirdPartyCustomer();
        thirdPartyCustomer.setOrgId(orgId);
        thirdPartyCustomer.setThirdpartyAuthId(thirdpartyAuthId);
        thirdPartyCustomer.setUnionId(unionId);
        thirdPartyCustomer.setOpenId(openId);
        thirdPartyCustomer.setCity(city);
        thirdPartyCustomer.setProvince(province);
        thirdPartyCustomer.setCountry(country);
        thirdPartyCustomer.setAvatar(avatar);
        thirdPartyCustomer.setNickname(nickname);
        thirdPartyCustomer.setRemark(remark);
        thirdPartyCustomer.setSubscribed(isSubscribed);
        thirdPartyCustomer.setSubscribedAt(subscribedAt);
        thirdPartyCustomer.setSex(sex);
        return thirdPartyCustomer;
    }

    public ThirdPartyCustomer mapToEntity(Connector connector) {
        ThirdPartyCustomer thirdPartyCustomer = new ThirdPartyCustomer();
        thirdPartyCustomer.setConnector(connector);
        thirdPartyCustomer.setOrgId(connector.getOrgId());
        thirdPartyCustomer.setUnionId(unionId);
        thirdPartyCustomer.setOpenId(openId);
        thirdPartyCustomer.setCity(city);
        thirdPartyCustomer.setProvince(province);
        thirdPartyCustomer.setCountry(country);
        thirdPartyCustomer.setAvatar(avatar);
        thirdPartyCustomer.setNickname(nickname);
        thirdPartyCustomer.setRemark(remark);
        thirdPartyCustomer.setSubscribed(isSubscribed);
        thirdPartyCustomer.setSubscribedAt(subscribedAt);
        thirdPartyCustomer.setSex(sex);
        return thirdPartyCustomer;
    }

    public ThirdPartyCustomer mapToEntity(ThirdPartyCustomer thirdPartyCustomer) {
        if (thirdPartyCustomer != null) {
            thirdPartyCustomer.setUnionId(unionId);
            thirdPartyCustomer.setOpenId(openId);
            thirdPartyCustomer.setCity(city);
            thirdPartyCustomer.setProvince(province);
            thirdPartyCustomer.setCountry(country);
            thirdPartyCustomer.setAvatar(avatar);
            thirdPartyCustomer.setNickname(nickname);
            thirdPartyCustomer.setRemark(remark);
            thirdPartyCustomer.setSubscribed(isSubscribed);
            thirdPartyCustomer.setSubscribedAt(subscribedAt);
            thirdPartyCustomer.setSex(sex);
        }
        return thirdPartyCustomer;
    }
}
