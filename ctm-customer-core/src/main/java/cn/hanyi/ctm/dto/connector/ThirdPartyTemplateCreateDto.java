package cn.hanyi.ctm.dto.connector;

import cn.hanyi.ctm.constant.connector.ConnectorType;
import cn.hanyi.ctm.entity.ThirdPartyTemplate;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.CustomParamDto;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Getter
@Setter
public class ThirdPartyTemplateCreateDto extends CustomParamDto<ThirdPartyTemplate> {

    @NotEmpty
    @Schema(description = "名称")
    private String name;

    @NotEmpty
    @Schema(description = "主题")
    private String subject;

    @NotEmpty
    @Schema(description = "内容")
    private String content;

    @NotNull
    @Schema(description = "模板类型")
    private ConnectorType connectorType;

}
