package cn.hanyi.ctm.dto;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;
import org.befun.core.utils.NumberHelper;

import java.util.Date;

@Getter
@Setter
public class CustomerStatDto {

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "客户历程数")
    private Integer countJourneyRecord;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "最后历程时间")
    private Date lastJourneyRecordTime;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "问卷发送数")
    private Integer countSendSurvey;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "最后问卷发送时间")
    private Date lastSendSurveyTime;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "问卷完成数")
    private Integer countCompleteSurvey;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "最后问卷完成时间")
    private Date lastCompleteSurveyTime;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "问卷参与数")
    private Integer countJoinSurvey;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "预警触发数")
    private Integer countEvent;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "最后预警触发时间")
    private Date lastEventTime;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "预警统计：触发次数最多的预警规则，次数（预警规则名称）")
    private String mostEventRule;
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "预警统计：触发次数最多的预警规则，次数")
    private Integer mostEventRuleTimes;
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "预警统计：触发次数最多的预警规则，名称")
    private String mostEventRuleName;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "体验指标：最近一次评价的指标和得分，分值（体验指标名称）")
    private String journeyIndicator;
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "体验指标：最近一次评价的指标和得分，分值")
    private Double journeyIndicatorScore;
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "体验指标：最近一次评价的指标和得分，名称")
    private String journeyIndicatorName;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "手动体验指标：最近一次评价的指标和得分，分值")
    private String experienceIndicatorScore;
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "手动体验指标：最近一次评价的指标和得分，名称")
    private String experienceIndicatorName;

    public Integer getCountJourneyRecord() {
        return NumberHelper.unbox(countJourneyRecord);
    }

    public Integer getCountSendSurvey() {
        return NumberHelper.unbox(countSendSurvey);
    }

    public Integer getCountCompleteSurvey() {
        return NumberHelper.unbox(countCompleteSurvey);
    }

    public Integer getCountJoinSurvey() {
        return NumberHelper.unbox(countJoinSurvey);
    }

    public Integer getCountEvent() {
        return NumberHelper.unbox(countEvent);
    }
}
