package cn.hanyi.ctm.dto.customer.group;

import cn.hanyi.ctm.entity.CustomerGroup;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.CustomParamDto;

import javax.persistence.Column;
import javax.validation.constraints.NotEmpty;

@Getter
@Setter
public class CustomerGroupSaveDto extends CustomParamDto<CustomerGroup> {

    @NotEmpty
    @Column(name="name")
    private String name;

    @Column(name="code")
    private String code;

}
