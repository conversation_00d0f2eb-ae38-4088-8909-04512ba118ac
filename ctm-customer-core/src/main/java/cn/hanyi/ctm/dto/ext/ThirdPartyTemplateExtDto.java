package cn.hanyi.ctm.dto.ext;

import cn.hanyi.ctm.entity.ThirdPartyTemplate;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.projection.SimpleUser;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;

@Getter
@Setter
public class ThirdPartyTemplateExtDto extends BaseEntityDTO<ThirdPartyTemplate> {

    public ThirdPartyTemplateExtDto(ThirdPartyTemplate entity) {
        super(entity);
    }

    public ThirdPartyTemplateExtDto() {
    }

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "是否可以审核")
    private boolean canAudit;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "是否可以编辑")
    private boolean canUpdate;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "是否可以删除")
    private boolean canDelete;

    @Schema(description = "创建人用户信息")
    @JsonView(ResourceViews.Basic.class)
    private SimpleUser createUser;

    @Schema(description = "修改人用户信息")
    @JsonView(ResourceViews.Basic.class)
    private SimpleUser modifyUser;

}
