package cn.hanyi.ctm.dto;

import org.befun.core.dto.BaseDTO;
import cn.hanyi.ctm.constant.PushStrategy;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class MassPushRequestDto extends BasePushRequestDto {

    @JsonPropertyDescription("推送策略")
    private PushStrategy strategy = PushStrategy.BATCH;

    private Long departmentId;

    private List<Long> customerIds;
}
