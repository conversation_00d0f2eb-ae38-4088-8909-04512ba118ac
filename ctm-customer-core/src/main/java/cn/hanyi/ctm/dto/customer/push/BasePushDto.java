package cn.hanyi.ctm.dto.customer.push;

import lombok.Getter;
import lombok.Setter;
import org.befun.task.dto.TimedTaskDto;
import org.befun.task.utils.TimeUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Map;

@Getter
@Setter
public class BasePushDto {
    private Long orgId;
    private Long userId;
    private Long surveyId;                          // 问卷id
    private Map<String, Object> urlParams;          // 问卷地址参数
    private Map<String, Object> contentParams;      // 发送内容占位符数据参数

    private String taskId;
    private String relationType;
    private Long relationId;

    private TimedTaskDto timed;                     // 重复发送
    private Duration delay;                         // 延迟发送


    public void setDelay(String delay) {
        this.delay = TimeUtils.parseDuration(delay);
    }

    public void setDelay(Duration delay) {
        this.delay = delay;
    }

    public void setDelay(LocalDateTime delay) {
        this.delay = Duration.between(LocalDateTime.now(), delay);
    }
}
