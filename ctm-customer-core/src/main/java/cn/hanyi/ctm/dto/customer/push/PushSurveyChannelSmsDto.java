package cn.hanyi.ctm.dto.customer.push;

import cn.hanyi.ctm.constant.customer.CustomerPushRelationType;
import lombok.Builder;
import lombok.Getter;

import java.time.Duration;
import java.util.Map;


@Getter
@Builder
public class PushSurveyChannelSmsDto {
    private Long orgId;
    private Long userId;                        // 当前操作用户id
    private Long surveyId;
    private String taskId;
    private boolean isAppend;                   // true 追加发送 false 首次发送
    private boolean cost;
    private String templateName;
    private String templateContent;
    private Map<String, Object> contentParams;  // 发送内容占位符数据参数，所有客户共享的
    private Map<String, Object> urlParams;      // 问卷地址参数，所有客户共享的
    private Duration delay;

    public PushSmsDto mapToPushDto() {
        PushSmsDto sms = new PushSmsDto();
        sms.setOrgId(getOrgId());
        sms.setUserId(getUserId());
        sms.setSurveyId(getSurveyId());
        sms.setTaskId(getTaskId());
        sms.setCost(isCost());
        sms.setTemplateName(getTemplateName());
        sms.setTemplateContent(getTemplateContent());
        sms.setRelationType(CustomerPushRelationType.JOURNEY.name());
        sms.setDelay(getDelay());
        sms.setContentParams(getContentParams());
        sms.setUrlParams(getUrlParams());
        return sms;
    }
}
