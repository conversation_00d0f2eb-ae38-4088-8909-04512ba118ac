package cn.hanyi.ctm.dto.customer;

import cn.hanyi.ctm.entity.Customer;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.CustomParamDto;
import org.befun.core.rest.view.ResourceViews;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Getter
@Setter
public class CustomerTagSaveDto extends CustomParamDto<Customer> {
    @Schema(description = "标签")
    private List<String> tags;
}
