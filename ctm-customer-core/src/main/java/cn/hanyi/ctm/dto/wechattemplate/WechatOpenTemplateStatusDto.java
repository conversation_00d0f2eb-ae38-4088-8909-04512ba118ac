package cn.hanyi.ctm.dto.wechattemplate;

import cn.hanyi.ctm.entity.ThirdPartyTemplateDto;
import cn.hanyi.ctm.entity.WechatTemplateDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.auth.service.auth.config.WechatOpenConfig;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class WechatOpenTemplateStatusDto {

    @Schema(description = "微信模版信息")
    private WechatTemplateDto wechatTemplate;
    @Schema(description = "来源公众号信息")
    private WechatOpenConfig sourceConfig;
    @Schema(description = "来源公众号模版")
    private ThirdPartyTemplateDto sourceTemplate;
    @Schema(description = "已关联")
    private List<WechatOpenTemplateDto> bind = new ArrayList<>();
    @Schema(description = "未关联")
    private List<WechatOpenTemplateDto> unbind = new ArrayList<>();

}
