package cn.hanyi.ctm.dto.customer;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

import java.util.List;

/**
 * <AUTHOR>
 */
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class UploadCustomerResponseDto {
    @JsonView(ResourceViews.Basic.class)
    private Integer total;
    @JsonView(ResourceViews.Basic.class)
    private List<UploadCustomerDto> data;

}
