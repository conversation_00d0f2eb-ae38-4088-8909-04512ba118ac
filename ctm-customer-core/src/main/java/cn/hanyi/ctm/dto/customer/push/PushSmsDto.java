package cn.hanyi.ctm.dto.customer.push;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PushSmsDto extends BasePushDto {

    private boolean cost = true;

    /**
     * 模板名称，可以通过名称找到当前短信发送方式(飞鸽，阿里云)的配置信息（签名，id），
     * 有的短信发送方式，必须要此项
     */
    private String templateName;

    /**
     * 短信完整的内容
     * 可能已经替换部分的占位符的模板内容，（旅程互动和问卷渠道发送短信都可以修改占位符的内容）
     */
    private String templateContent;
}
