package cn.hanyi.ctm.dto.connector;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

@Getter
@Setter
public class ThirdPartyTemplateSmsUpdateDto extends ThirdPartyTemplateSmsCreateDto {

    @NotNull
    @Min(1)
    @Schema(description = "模板id")
    private Long id;

}
