package cn.hanyi.ctm.dto.wechattemplate;

import cn.hanyi.ctm.entity.ThirdPartyTemplateDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.auth.service.auth.config.WechatOpenConfig;

import java.util.ArrayList;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
public class WechatOpenTemplateDto {

    @Schema(description = "公众号信息")
    private WechatOpenConfig config;
    @Schema(description = "关联的公众号")
    private ThirdPartyTemplateDto bindTemplate;
    @Schema(description = "公众号模版")
    private List<ThirdPartyTemplateDto> templates;

    public WechatOpenTemplateDto(WechatOpenConfig config, List<ThirdPartyTemplateDto> templates) {
        this.config = config;
        this.templates = templates == null ? new ArrayList<>() : templates;
    }

    public WechatOpenTemplateDto(WechatOpenConfig config, ThirdPartyTemplateDto bindTemplate, List<ThirdPartyTemplateDto> templates) {
        this.config = config;
        this.bindTemplate = bindTemplate;
        this.templates = templates;
    }
}
