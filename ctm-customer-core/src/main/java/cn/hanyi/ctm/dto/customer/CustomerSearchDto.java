package cn.hanyi.ctm.dto.customer;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.Valid;

@Getter
@Setter
public class CustomerSearchDto {

    @JsonProperty("_limit")
    @Schema(name = "_limit", description = "每页数量")
    private int limit = 20;

    @JsonProperty("_page")
    @Schema(name = "_page", description = "页码")
    private int page = 1;

    @Valid
    @Schema(description = "选择客户的搜索条件")
    private CustomerCombineSearchDto selectConditions;

}
