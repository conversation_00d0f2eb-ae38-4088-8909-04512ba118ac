package cn.hanyi.ctm.dto;

import cn.hanyi.ctm.constant.MomentType;
import cn.hanyi.ctm.constant.PushStrategy;
import cn.hanyi.ctm.dto.journey.PushMomentDto;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class AppMassPushRequestDto extends MassPushRequestDto {

    @JsonProperty(required = true)
    private Long orgId;

    private String templateName;

    private Long sceneId;

    private Boolean skipLimiter = Boolean.FALSE;

    public AppMassPushRequestDto(Long orgId, String url, Boolean isDetail, List<Long> customerIds,
                                 Long departmentId, Map<String, Object> parameters, PushMomentDto momentDto, String templateName) {
        this.orgId = orgId;
        this.setParameters(parameters);
        this.setIsDetail(isDetail);
        this.setUrl(url);
        this.setCustomerIds(customerIds);
        this.setDepartmentId(departmentId);
        this.setTemplateName(templateName);
        this.setMomentDto(momentDto);
    }
}
