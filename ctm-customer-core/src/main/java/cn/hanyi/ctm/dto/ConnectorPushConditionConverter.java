package cn.hanyi.ctm.dto;

import cn.hanyi.ctm.constant.connector.ConnectorPushCondition;
import org.apache.commons.lang3.StringUtils;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Converter
public class ConnectorPushConditionConverter implements AttributeConverter<List<ConnectorPushCondition>, String> {

    @Override
    public String convertToDatabaseColumn(List<ConnectorPushCondition> list) {
        if (list == null) {
            return null;
        } else {
            return list.stream().map(Enum::name).collect(Collectors.joining(","));
        }
    }

    @Override
    public List<ConnectorPushCondition> convertToEntityAttribute(String dbData) {
        if (StringUtils.isEmpty(dbData)) {
            return null;
        } else {
            return Arrays.stream(dbData.split(",")).map(x->ConnectorPushCondition.valueOf(x)).collect(Collectors.toList());
        }
    }
}