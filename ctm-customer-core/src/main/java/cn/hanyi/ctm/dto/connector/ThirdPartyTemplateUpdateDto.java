package cn.hanyi.ctm.dto.connector;

import cn.hanyi.ctm.entity.ThirdPartyTemplate;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.CustomParamDto;

import javax.validation.constraints.NotEmpty;

@Getter
@Setter
public class ThirdPartyTemplateUpdateDto extends CustomParamDto<ThirdPartyTemplate> {

    @NotEmpty
    @Schema(description = "名称")
    private String name;

    @NotEmpty
    @Schema(description = "主题")
    private String subject;

    @NotEmpty
    @Schema(description = "内容")
    private String content;

}
