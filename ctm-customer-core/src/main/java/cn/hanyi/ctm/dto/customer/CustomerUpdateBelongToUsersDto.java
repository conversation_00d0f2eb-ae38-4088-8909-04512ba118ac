package cn.hanyi.ctm.dto.customer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Getter
@Setter
public class CustomerUpdateBelongToUsersDto extends CustomerSelectedDto {

    @Schema(description = "目标成员id列表")
    private List<@NotNull Long> targetUserIds;

}
