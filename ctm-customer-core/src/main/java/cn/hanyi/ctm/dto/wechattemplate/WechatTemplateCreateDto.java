package cn.hanyi.ctm.dto.wechattemplate;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

import javax.validation.constraints.NotNull;

@Getter
@Setter
public class WechatTemplateCreateDto extends WechatTemplateUpdateDto {

    @NotNull
    @Schema(description = "模版来源")
    @JsonView(ResourceViews.Basic.class)
    private Long sourceThirdpartyTemplateId;

}
