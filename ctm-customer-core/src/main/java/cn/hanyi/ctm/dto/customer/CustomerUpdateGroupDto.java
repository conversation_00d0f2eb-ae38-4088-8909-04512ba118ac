package cn.hanyi.ctm.dto.customer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Getter
@Setter
public class CustomerUpdateGroupDto extends CustomerSelectedDto {

    @NotEmpty
    @Schema(description = "目标组id列表", required = true)
    private List<@NotNull Long> targetGroupIds;

}
