package cn.hanyi.ctm.dto.customer.push;

import cn.hanyi.ctm.constant.customer.CustomerPushRelationType;
import lombok.Builder;
import lombok.Getter;

import java.time.Duration;
import java.util.Map;


@Getter
@Builder
public class PushSurveyChannelWechatDto {
    private Long orgId;
    private Long userId;                        // 当前操作用户id
    private Long surveyId;
    private String taskId;
    private boolean isAppend;                   // true 追加发送 false 首次发送
    private String appId;
    private String templateId;
    private Map<String,Object> templateContent;
    private Map<String, Object> contentParams;  // 发送内容占位符数据参数，所有客户共享的
    private Map<String, Object> urlParams;      // 问卷地址参数，所有客户共享的
    private Duration delay;

    public PushWechatDto mapToPushDto() {
        PushWechatDto wechat = new PushWechatDto();
        wechat.setOrgId(getOrgId());
        wechat.setUserId(getUserId());
        wechat.setSurveyId(getSurveyId());
        wechat.setTaskId(getTaskId());
        wechat.setAppId(getAppId());
        wechat.setTemplateId(getTemplateId());
        wechat.setTemplateContent(getTemplateContent());
        wechat.setRelationType(CustomerPushRelationType.JOURNEY.name());
        wechat.setDelay(getDelay());
        wechat.setContentParams(getContentParams());
        wechat.setUrlParams(getUrlParams());
        return wechat;
    }
}
