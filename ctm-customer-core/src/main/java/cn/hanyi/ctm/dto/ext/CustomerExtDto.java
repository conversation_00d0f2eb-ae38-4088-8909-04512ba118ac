package cn.hanyi.ctm.dto.ext;

import cn.hanyi.ctm.dto.CustomerStatDto;
import cn.hanyi.ctm.entity.Customer;
import cn.hanyi.ctm.entity.CustomerGroupDto;
import cn.hanyi.ctm.entity.ThirdPartyCustomerDto;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Getter
@Setter
public abstract class CustomerExtDto extends BaseEntityDTO<Customer> {

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "微信用户")
    private ThirdPartyCustomerDto thirdPartyCustomer;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "微信openid")
    private String openId;

    @Schema(description = "组列表")
    @JsonView(ResourceViews.Basic.class)
    private List<CustomerGroupDto> groups;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "所在地区：province/city/district 这三个字段组合起来的")
    private String area;                // province/city/district

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "所属部门, 最后一个层级的名称")
    private String departmentName;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "创建人")
    private String createUserName;      // createdByUid

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "修改人")
    private String modifyUserName;      // modifiedByUid

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "客户统计数据，可能为null")
    private CustomerStatDto customerStat;

    @JsonIgnore
    @Schema(hidden = true, description = "临时缓存一下entity中字符串转换的结果")
    private Set<Long> belongToUserIds;

    public abstract String getCity();

    public abstract String getDistrict();

    public abstract String getProvince();

//    public abstract String getAddress();

    public String getArea() {
        List<String> areas = new ArrayList<>();
        Optional.ofNullable(getProvince()).filter(StringUtils::isNotEmpty).ifPresent(areas::add);
        Optional.ofNullable(getCity()).filter(StringUtils::isNotEmpty).ifPresent(areas::add);
        Optional.ofNullable(getDistrict()).filter(StringUtils::isNotEmpty).ifPresent(areas::add);
//        Optional.ofNullable(getAddress()).ifPresent(areas::add);
        if (CollectionUtils.isNotEmpty(areas)) {
            return String.join("/", areas);
        } else {
            return "";
        }
    }

    public CustomerExtDto(Customer entity) {
        super(entity);
    }

    public CustomerExtDto() {
        super();
    }
}
