package cn.hanyi.ctm.dto.customer.push;

import cn.hanyi.ctm.constant.customer.CustomerPushRelationType;
import lombok.Getter;
import lombok.Setter;

import java.time.Duration;
import java.util.Map;


@Getter
@Setter
public class PushJourneyWechatDto extends PushJourneyBaseDto {
    private String appId;
    private String templateId;
    private Map<String, Object> templateContent;

    public PushJourneyWechatDto(Long orgId, Long surveyId, Long userId, Map<String, Object> contentParams, Map<String, Object> urlParams, Duration delay, String appId, String templateId, Map<String, Object> templateContent) {
        super(orgId, surveyId, userId, contentParams, urlParams, delay);
        this.appId = appId;
        this.templateId = templateId;
        this.templateContent = templateContent;
    }

    public PushWechatDto mapToPushDto() {
        PushWechatDto wechat = new PushWechatDto();
        wechat.setAppId(getAppId());
        wechat.setTemplateId(getTemplateId());
        wechat.setTemplateContent(getTemplateContent());
        wechat.setOrgId(getOrgId());
        wechat.setUserId(getUserId());
        wechat.setSurveyId(getSurveyId());
        wechat.setRelationType(CustomerPushRelationType.JOURNEY.name());
        wechat.setDelay(getDelay());
        wechat.setContentParams(getContentParams());
        wechat.setUrlParams(getUrlParams());
        return wechat;
    }
}
