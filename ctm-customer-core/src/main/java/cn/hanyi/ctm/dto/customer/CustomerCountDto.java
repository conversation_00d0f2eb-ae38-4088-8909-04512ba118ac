package cn.hanyi.ctm.dto.customer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;

import java.util.ArrayList;
import java.util.List;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class CustomerCountDto extends BaseDTO {
    private String departmentName;

    @Schema(description = "全部客户：-1；未分组客户：-2")
    private Long departmentId;

    private List<CustomerCountDto> subCustomerCount = new ArrayList<>();

    private int count;

    public CustomerCountDto() {
    }

    public CustomerCountDto(Long departmentId, String departmentName, int count) {
        this.departmentName = departmentName;
        this.departmentId = departmentId;
        this.count = count;
        this.subCustomerCount = new ArrayList<>();
    }
}
