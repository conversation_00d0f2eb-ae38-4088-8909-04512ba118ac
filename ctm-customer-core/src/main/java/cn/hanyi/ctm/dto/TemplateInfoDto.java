package cn.hanyi.ctm.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.template.TemplateEngine;

import java.util.Map;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class TemplateInfoDto {

    private Long thirdpartyTemplateId;

    private String smsTemplateId;
    private String smsTemplateName;
    private String smsSignId;
    private String smsRealSign;
    private String smsContent;
    private int smsLength;

    private Long wechatConfigId;
    private boolean weChatAuthorized;
    private String weChatAppId;
    private String weChatTemplateId;
    private Map<String, Object> weChatContent;

    private String emailSender;
    private String emailTitle;
    private String emailContent;

    public TemplateInfoDto(Long thirdpartyTemplateId, String smsTemplateId, String smsTemplateName, String smsSignId, String smsRealSign, String smsContent) {
        this.thirdpartyTemplateId = thirdpartyTemplateId;
        this.smsTemplateId = smsTemplateId;
        this.smsTemplateName = smsTemplateName;
        this.smsSignId = smsSignId;
        this.smsRealSign = smsRealSign;
        this.smsContent = smsContent;
    }

    public TemplateInfoDto(Long thirdpartyTemplateId, Long wechatConfigId, boolean weChatAuthorized, String weChatAppId, String weChatTemplateId, Map<String, Object> weChatContent) {
        this.thirdpartyTemplateId = thirdpartyTemplateId;
        this.wechatConfigId = wechatConfigId;
        this.weChatAuthorized = weChatAuthorized;
        this.weChatAppId = weChatAppId;
        this.weChatTemplateId = weChatTemplateId;
        this.weChatContent = weChatContent;
    }

    public static TemplateInfoDto createSms(Long thirdpartyTemplateId, String smsTemplateId, String smsTemplateName, String smsSignId, String smsRealSign, String smsContent) {
        TemplateInfoDto templateInfo = new TemplateInfoDto(thirdpartyTemplateId, smsTemplateId, smsTemplateName, smsSignId, smsRealSign, smsContent);
        templateInfo.setSmsLength(templateInfo.calcSmsLength());
        return templateInfo;
    }

    public static TemplateInfoDto createWeChat(Long thirdpartyTemplateId, Long wechatConfigId, boolean weChatAuthorized, String weChatAppId, String weChatTemplateId, Map<String, Object> weChatContent) {
        return new TemplateInfoDto(thirdpartyTemplateId, wechatConfigId, weChatAuthorized, weChatAppId, weChatTemplateId, weChatContent);
    }

    public static TemplateInfoDto createEmail(Long thirdpartyTemplateId, String emailSender, String emailTitle, String emailContent) {
        TemplateInfoDto templateInfoDto = new TemplateInfoDto();
        templateInfoDto.setThirdpartyTemplateId(thirdpartyTemplateId);
        templateInfoDto.setEmailSender(emailSender);
        templateInfoDto.setEmailTitle(emailTitle);
        templateInfoDto.setEmailContent(emailContent);
        return templateInfoDto;
    }

    public String fullSmsTemplate() {
        String content = smsContent;
        if (StringUtils.isNotEmpty(smsContent)) {
            content = StringUtils.isNotEmpty(smsRealSign) ? (smsRealSign + smsContent) : smsContent;
        }
        return content;
    }

    public int calcSmsLength() {
        String content = fullSmsTemplate();
        content = TemplateEngine.renderTextTemplate(content, Map.of("customer", Map.of("username", "xxxx"), "url", Map.of("code", "xxxxxx")));
        if (StringUtils.isNotEmpty(content)) {
            return content.length();
        }
        return 0;
    }
}
