package cn.hanyi.ctm.dto.customer;

import cn.hanyi.ctm.entity.Customer;
import cn.hanyi.ctm.service.StringCommonListConverter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Setter
@Getter
public class UploadCustomerDto {
    @ExcelProperty(value = "姓名")
    @JsonView(ResourceViews.Basic.class)
    @JsonPropertyDescription("姓名")
    private String username;

    @ExcelProperty(value = "手机号码")
    @JsonView(ResourceViews.Basic.class)
    @JsonPropertyDescription("手机号码")
    private String mobile;

    @ExcelProperty(value = "邮箱")
    @JsonView(ResourceViews.Basic.class)
    @JsonPropertyDescription("邮箱")
    private String email;

    @ExcelProperty(value = "客户编号")
    @JsonView(ResourceViews.Basic.class)
    @JsonPropertyDescription("外部客户编号")
    private String externalUserId;

    @ExcelProperty(value = "性别")
    @JsonView(ResourceViews.Basic.class)
    @JsonPropertyDescription("性别")
    private String gender;

    @ExcelProperty(value = "出生日期")
    @JsonView(ResourceViews.Basic.class)
    @JsonPropertyDescription("出生日期")
    private String birthday;

    @ExcelProperty(value = "省份")
    @JsonView(ResourceViews.Basic.class)
    @JsonPropertyDescription("省份")
    private String province;

    @ExcelProperty(value = "城市")
    @JsonView(ResourceViews.Basic.class)
    @JsonPropertyDescription("城市")
    private String city;

    @ExcelProperty(value = "区县")
    @JsonView(ResourceViews.Basic.class)
    @JsonPropertyDescription("区县")
    private String district;

    @ExcelProperty(value = "详细地址")
    @JsonView(ResourceViews.Basic.class)
    @JsonPropertyDescription("详细地址")
    private String address;

    @ExcelProperty(value = "所属层级")
    @JsonView(ResourceViews.Basic.class)
    @JsonPropertyDescription("所属层级")
    private String departmentName;

    @ExcelProperty(value = "所属员工")
    @JsonView(ResourceViews.Basic.class)
    @JsonPropertyDescription("所属员工")
    private String belongUserTruenames;

    @ExcelProperty(value = "所属分组")
    @JsonView(ResourceViews.Basic.class)
    @JsonPropertyDescription("所属分组")
    private String groupCode;

    @ExcelProperty(converter = StringCommonListConverter.class, value = "标签")
    @JsonView(ResourceViews.Basic.class)
    @JsonPropertyDescription("标签")
    private List<String> tags = List.of();

    public Customer convertToCustomer() {
        Customer customer = new Customer();
        customer.setDepartmentId(0L);
        customer.setUsername(username);
        customer.setMobile(mobile);
        customer.setEmail(email);
        customer.setExternalUserId(externalUserId);
        customer.setGender(gender);
        customer.setBirthday(birthday);
        customer.setProvince(province);
        customer.setCity(city);
        customer.setDistrict(district);
        if (tags == null) {
            tags = new ArrayList<>();
        } else {
            if (tags.size() > 19 || tags.stream().anyMatch(i -> i != null && i.length() > 10)) {
                tags = new ArrayList<>();
            }
        }
        customer.setTags(tags);
        return customer;

    }

}
