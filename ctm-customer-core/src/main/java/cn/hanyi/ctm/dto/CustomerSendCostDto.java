package cn.hanyi.ctm.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class CustomerSendCostDto {
    @Schema(description = "客户数量")
    private int countCustomer;
    @Schema(description = "单条短信字数")
    private int smsLength;
    @Schema(description = "每个客户需要消费的短信条数")
    private int oneCustomerCost;
    @Schema(description = "总共需要的短信条数")
    private int allCost;
    @Schema(description = "余额")
    private int balance;

    public CustomerSendCostDto(int customerSize, int smsLength, int oneCustomerCost, int balance) {
        this.countCustomer = customerSize;
        this.smsLength = smsLength;
        this.balance = balance;
        this.oneCustomerCost = oneCustomerCost;
        this.allCost = this.countCustomer * this.oneCustomerCost;
    }
}
