package cn.hanyi.ctm.dto;

import cn.hanyi.ctm.dto.journey.PushMomentDto;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.BaseDTO;

import java.util.Map;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ApiPushRequestDto extends BasePushRequestDto {

    @JsonPropertyDescription("链接")
    private String surveyUrl = "";

    private Long customerId;

    private Object externalUserId;

    private Long departmentId;

    private Long triggerTime;

    private String  gatewayName = "";

    private Long orgId;

    private Map<String, Object> params;

    private Long sceneId;

    public ApiPushRequestDto(Long orgId, Long sceneId, Long customerId, String externalUserId, Long departmentId, String url, Boolean isDetail,
            PushMomentDto momentDto, Map<String, Object> parameters) {
        this.orgId = orgId;
        this.setSceneId(sceneId);
        this.setCustomerId(customerId);
        this.setExternalUserId(externalUserId);
        this.setDepartmentId(departmentId);
        this.setUrl(url);
        this.setIsDetail(isDetail);
        this.setMomentDto(momentDto);
        this.setParameters(parameters);
    }
}
