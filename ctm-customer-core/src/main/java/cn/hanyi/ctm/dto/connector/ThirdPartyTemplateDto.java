package cn.hanyi.ctm.dto.connector;

import org.befun.core.dto.BaseDTO;
import org.befun.core.dto.TemplateParameter;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * ThirdPartyTemplateDto 第三方通用模版
 *
 * <AUTHOR>
 */
@Setter
@Getter
@AllArgsConstructor
@Builder
public class ThirdPartyTemplateDto extends BaseDTO {
    private String openId;
    private String title;
    private String content;
    private String example;
    private List<Map<String, Object>> params;
}
