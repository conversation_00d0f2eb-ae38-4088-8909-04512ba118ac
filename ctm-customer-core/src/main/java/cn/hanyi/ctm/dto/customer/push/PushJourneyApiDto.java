package cn.hanyi.ctm.dto.customer.push;

import cn.hanyi.ctm.constant.customer.CustomerPushRelationType;
import lombok.Getter;
import lombok.Setter;
import org.apache.http.entity.ContentType;
import org.springframework.http.HttpMethod;

import java.time.Duration;
import java.util.Map;


@Getter
@Setter
public class PushJourneyApiDto extends PushJourneyBaseDto {

    // api 信息
    private String url;
    private HttpMethod method;
    private Map<String, Object> headers;
    private ContentType contentType;
    private Map<String, Object> templateContent;

    public PushJourneyApiDto(Long orgId, Long surveyId, Long userId, Map<String, Object> contentParams, Map<String, Object> urlParams, Duration delay, String url, Map<String, Object> templateContent) {
        this(orgId, surveyId, userId, contentParams, urlParams, delay, url, HttpMethod.POST, null, ContentType.APPLICATION_JSON, templateContent);
    }

    public PushJourneyApiDto(Long orgId, Long surveyId, Long userId, Map<String, Object> contentParams, Map<String, Object> urlParams, Duration delay, String url, HttpMethod method, Map<String, Object> headers, ContentType contentType, Map<String, Object> templateContent) {
        super(orgId, surveyId, userId, contentParams, urlParams, delay);
        this.url = url;
        this.method = method;
        this.headers = headers;
        this.contentType = contentType;
        this.templateContent = templateContent;
    }

    public PushApiDto mapToPushDto() {
        PushApiDto api = new PushApiDto();
        api.setUrl(getUrl());
        api.setMethod(getMethod());
        api.setHeaders(getHeaders());
        api.setContentType(getContentType());
        api.setTemplateContent(getTemplateContent());
        api.setOrgId(getOrgId());
        api.setUserId(getUserId());
        api.setSurveyId(getSurveyId());
        api.setRelationType(CustomerPushRelationType.JOURNEY.name());
        api.setDelay(getDelay());
        api.setContentParams(getContentParams());
        api.setUrlParams(getUrlParams());
        return api;
    }
}
