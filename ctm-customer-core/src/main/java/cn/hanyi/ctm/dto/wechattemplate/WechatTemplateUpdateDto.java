package cn.hanyi.ctm.dto.wechattemplate;

import cn.hanyi.ctm.entity.WechatTemplate;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.CustomParamDto;
import org.befun.core.rest.view.ResourceViews;

import javax.validation.constraints.NotEmpty;
import java.util.HashMap;
import java.util.Map;

@Getter
@Setter
public class WechatTemplateUpdateDto extends CustomParamDto<WechatTemplate> {

    @NotEmpty
    @Schema(description = "模版内容样例，在模版列表展示用的")
    @JsonView(ResourceViews.Basic.class)
    private String example = "";

    @Schema(description = "模版参数内容")
    @JsonView(ResourceViews.Basic.class)
    private Map<String, Object> content = new HashMap<>();

}
