package cn.hanyi.ctm.dto.customer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Getter
@Setter
public class CustomerUpdateDepartmentDto extends CustomerSelectedDto {

    @NotNull
    @Schema(description = "目标部门id", required = true)
    private Long targetDepartmentId;

}
