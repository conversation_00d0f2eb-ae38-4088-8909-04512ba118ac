package cn.hanyi.ctm.dto.customer.push;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;

import java.time.Duration;
import java.util.Map;

@Getter
@Setter
@AllArgsConstructor
public class PushJourneyBaseDto {
    private Long orgId;
    private Long surveyId;
    private Long userId;                        // 当前操作用户id
    private Map<String, Object> contentParams;  // 发送内容占位符数据参数，所有客户共享的
    private Map<String, Object> urlParams;      // 问卷地址参数，所有客户共享的
    private Duration delay;
}
