package cn.hanyi.ctm.dto.customer.push;

import cn.hanyi.ctm.constant.customer.CustomerPushRelationType;
import lombok.Getter;
import lombok.Setter;

import java.time.Duration;
import java.util.Map;


@Getter
@Setter
public class PushJourneySmsDto extends PushJourneyBaseDto {
    private boolean cost;
    private String templateName;
    private String templateContent;

    public PushJourneySmsDto(Long orgId, Long surveyId, Long userId, Map<String, Object> contentParams, Map<String, Object> urlParams, Duration delay, boolean cost, String templateName, String templateContent) {
        super(orgId, surveyId, userId, contentParams, urlParams, delay);
        this.cost = cost;
        this.templateName = templateName;
        this.templateContent = templateContent;
    }

    public PushSmsDto mapToPushDto() {
        PushSmsDto sms = new PushSmsDto();
        sms.setCost(isCost());
        sms.setTemplateName(getTemplateName());
        sms.setTemplateContent(getTemplateContent());
        sms.setOrgId(getOrgId());
        sms.setUserId(getUserId());
        sms.setSurveyId(getSurveyId());
        sms.setRelationType(CustomerPushRelationType.JOURNEY.name());
        sms.setDelay(getDelay());
        sms.setContentParams(getContentParams());
        sms.setUrlParams(getUrlParams());
        return sms;
    }
}
