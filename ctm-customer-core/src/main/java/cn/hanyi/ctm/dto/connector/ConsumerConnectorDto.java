package cn.hanyi.ctm.dto.connector;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.befun.core.dto.BaseDTO;
import org.befun.core.rest.view.ResourceViews;

import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ConsumerConnectorDto extends BaseDTO {
    @Schema(description = "推送设置Id")
    @JsonView(ResourceViews.Basic.class)
    private Long connectorId;
    @Schema(description = "开启、关闭推送")
    @JsonView(ResourceViews.Basic.class)
    private Boolean enable = true;

    @Schema(description = "机器人推送")
    @JsonView(ResourceViews.Basic.class)
    private List<ConsumerConnectorBotDto> bots = new ArrayList();

    public ConsumerConnectorDto(Long connectorId, Boolean enable) {
        this.connectorId = connectorId;
        this.enable = enable;

    }

}
