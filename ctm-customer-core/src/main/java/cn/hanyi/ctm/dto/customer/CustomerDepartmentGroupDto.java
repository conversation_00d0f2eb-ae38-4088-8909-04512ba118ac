package cn.hanyi.ctm.dto.customer;

import cn.hanyi.ctm.entity.CustomerGroupDto;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.auth.dto.DepartmentTreeDto;
import org.befun.auth.dto.TreeDto;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
public class CustomerDepartmentGroupDto implements TreeDto<CustomerDepartmentGroupDto> {

    @JsonIgnore
    @Schema(hidden = true)
    private Long pid;
    private Long id;
    private String name;
    private String code;
    private Integer count;
    @Schema(description = "此项数据的类型：1 部门  2 组")
    private int type;
    @Schema(description = "此项数据是否为根节点")
    private boolean root;
    private List<CustomerDepartmentGroupDto> children = new ArrayList<>();

    public CustomerDepartmentGroupDto(Long id, String name, Integer count, int type, boolean root) {
        this(id, name, null, count, type, root);
    }

    public CustomerDepartmentGroupDto(Long id, String name, String code, Integer count, int type, boolean root) {
        this.id = id;
        this.name = name;
        this.code = code;
        this.count = count;
        this.type = type;
        this.root = root;
    }

    /**
     * countMap.put(-1L,all);
     * countMap.put(-2L,noDepartment);
     */
    public static List<CustomerDepartmentGroupDto> create(Map<Long, Integer> idCountMap, DepartmentTreeDto tree, List<CustomerGroupDto> groups) {
        CustomerDepartmentGroupDto all = new CustomerDepartmentGroupDto(-1L, "全部客户", idCountMap.get(-1L), 1, true);
        CustomerDepartmentGroupDto noDepartment = new CustomerDepartmentGroupDto(-2L, "未分组客户", idCountMap.get(-2L), 1, true);
        CustomerDepartmentGroupDto departmentTree = new CustomerDepartmentGroupDto(-2L, "未分组客户", idCountMap.get(-2L), 1, true);
        buildDepartmentTree(idCountMap, tree, departmentTree);
        CustomerDepartmentGroupDto groupList = new CustomerDepartmentGroupDto(null, "自定义分组", null, 2, true);
        buildGroupList(groups, groupList);
        return List.of(all, noDepartment, departmentTree, groupList);
    }

    private static void buildDepartmentTree(Map<Long, Integer> idCountMap, DepartmentTreeDto tree, CustomerDepartmentGroupDto departmentTree) {

    }

    private static void buildGroupList(List<CustomerGroupDto> groups, CustomerDepartmentGroupDto groupList) {
        if (CollectionUtils.isNotEmpty(groups)) {
            groups.forEach(i -> {
                groupList.getChildren().add(new CustomerDepartmentGroupDto(i.getId(), i.getName(), i.getCode(), i.getCountCustomers(), 2, false));
            });
        }
    }

    @Override
    public Long getPid() {
        return pid;
    }

    @Override
    public List<CustomerDepartmentGroupDto> children() {
        return children;
    }

    @Override
    public void addChild(CustomerDepartmentGroupDto child) {
        children.add(child);
    }
}
