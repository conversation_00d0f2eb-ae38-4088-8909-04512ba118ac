package cn.hanyi.ctm.dto.customer;

import cn.hanyi.ctm.entity.Customer;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.dto.CustomParamDto;
import org.befun.core.rest.view.ResourceViews;

import javax.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class CustomerSaveDto extends CustomParamDto<Customer> {

    @Schema(description = "客户编号")
    @JsonView(ResourceViews.Detail.class)
    private String externalUserId;

    @JsonView(ResourceViews.Detail.class)
    @Schema(description = "昵称")
    private String nickname = "";

    @NotEmpty
    @JsonView(ResourceViews.Detail.class)
    @Schema(description = "姓名")
    private String username = "";

    @Schema(description = "手机号码")
    @JsonView(ResourceViews.Detail.class)
    private String mobile = "";

    @Schema(description = "邮箱")
    @JsonView(ResourceViews.Detail.class)
    private String email = "";

    @JsonView(ResourceViews.Detail.class)
    @Schema(description = "性别")
    private String gender = "";

    @JsonView(ResourceViews.Detail.class)
    @Schema(description = "出生日期")
    private String birthday = "";

    @JsonView(ResourceViews.Detail.class)
    @Schema(description = "头像")
    private String avatar = "";

    @JsonView(ResourceViews.Detail.class)
    @Schema(description = "省份")
    private String province = "";

    @JsonView(ResourceViews.Detail.class)
    @Schema(description = "城市")
    private String city = "";

    @JsonView(ResourceViews.Detail.class)
    @Schema(description = "区县")
    private String district = "";

    @JsonView(ResourceViews.Detail.class)
    @Schema(description = "详细地址")
    private String address = "";

    @JsonView(ResourceViews.Detail.class)
    @Schema(description = "部门id")
    private Long departmentId;

    @JsonView(ResourceViews.Detail.class)
    @Schema(description = "所属员工id列表")
    private String belongToUids = "";

    @JsonView(ResourceViews.Detail.class)
    @Schema(description = "标签")
    private List<String> tags = List.of();

    @JsonView(ResourceViews.Detail.class)
    @Schema(description = "客户组id列表")
    private String groupIds;

    @JsonView(ResourceViews.Detail.class)
    @Schema(description = "自定义字段")
    private Map<String, Object> extendFields = new HashMap<>();

    @Schema(hidden = true)
    private String departmentNames = "";
    @Schema(hidden = true)
    private List<String> belongToUidNames = List.of();

    public List<String> getTags() {
        if (tags == null) {
            tags = new ArrayList<>();
        }
        return tags;
    }
}
