package cn.hanyi.ctm.dto;

import org.befun.core.dto.BaseDTO;
import cn.hanyi.ctm.entity.Template;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Map;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class PushResponseDto extends BaseDTO {

    @JsonPropertyDescription("计划发送总数")
    private int planNumber = 0;

    @JsonPropertyDescription("实际发送总数")
    private int actualNumber = 0;

    @JsonPropertyDescription("预计消费短信数量")
    private int consumedSmsNumber = 0;

    @JsonPropertyDescription("使用模版信息")
    private Template template;

    private Map<String, Object> debugInformation;
    private Map<String, Map<String, Object>> detailInformation;
}
