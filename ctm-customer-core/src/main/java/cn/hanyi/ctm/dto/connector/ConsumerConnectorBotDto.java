package cn.hanyi.ctm.dto.connector;

import cn.hanyi.ctm.constant.connector.ConnectorType;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.befun.core.dto.BaseDTO;
import org.befun.core.rest.view.ResourceViews;

import java.util.List;


/**
 * <AUTHOR>
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ConsumerConnectorBotDto extends BaseDTO {

    @Schema(description = "机器人类型")
    @JsonView(ResourceViews.Basic.class)
    ConnectorType type;
    @Schema(description = "推送设置Id")
    @JsonView(ResourceViews.Basic.class)
    private Long connectorId;
    @Schema(description = "开启、关闭推送")
    @JsonView(ResourceViews.Basic.class)
    private Boolean enable = true;
    @Schema(description = "推送地址")
    @JsonView(ResourceViews.Basic.class)
    String url;
    @Schema(description = "自定义内容")
    @JsonView(ResourceViews.Basic.class)
    ConnectorConsumerBotContentDto template;
    @Schema(description = "推送自定义参数")
    @JsonView(ResourceViews.Basic.class)
    private List<ConnectorParamsDto> connectorParams;


    public ConsumerConnectorBotDto(Long connectorId, Boolean enable) {
        this.connectorId = connectorId;
        this.enable = enable;

    }

    public ConsumerConnectorBotDto(Long connectorId, Boolean enable, ConnectorType type) {
        this.connectorId = connectorId;
        this.enable = enable;
        this.type = type;

    }

    public ConsumerConnectorBotDto(Long connectorId, Boolean enable, ConnectorType type,List<ConnectorParamsDto> connectorParams ) {
        this.connectorId = connectorId;
        this.enable = enable;
        this.type = type;
        this.connectorParams = connectorParams;
    }

    public ConsumerConnectorBotDto(ConnectorType type, Long connectorId, Boolean enable, String url, ConnectorConsumerBotContentDto template) {
        this.type = type;
        this.connectorId = connectorId;
        this.enable = enable;
        this.url = url;
        this.template = template;
    }
}
