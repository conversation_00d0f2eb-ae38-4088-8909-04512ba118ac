package cn.hanyi.ctm.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

@Data
@AllArgsConstructor
@Getter
public class TicketRequestDto {
    private String timestamp;
    private String nonce;
    private String signature;

    @JsonProperty("encrypt_type")
    private String encryptType;

    @JsonProperty("msg_signature")
    private String msgSignature;
}