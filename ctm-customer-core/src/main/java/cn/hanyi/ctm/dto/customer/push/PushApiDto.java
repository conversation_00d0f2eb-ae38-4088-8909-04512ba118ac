package cn.hanyi.ctm.dto.customer.push;

import lombok.Getter;
import lombok.Setter;
import org.apache.http.entity.ContentType;
import org.springframework.http.HttpMethod;

import java.util.Map;

@Getter
@Setter
public class PushApiDto extends BasePushDto {

    private String url;
    private HttpMethod method = HttpMethod.POST;
    private Map<String, Object> headers;
    private ContentType contentType = ContentType.APPLICATION_JSON;
    private Map<String, Object> templateContent;
}
