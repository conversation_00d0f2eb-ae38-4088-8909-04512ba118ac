package cn.hanyi.ctm.dto.customer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.converter.HashMapConverter;

import javax.persistence.Convert;
import javax.validation.constraints.NotEmpty;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class SyncCustomerDto {

    @NotEmpty
    @Schema(description = "客户编号", required = true)
    private String externalUserId;

    @NotEmpty
    @Schema(description = "姓名", required = true)
    private String username;

    @Schema(description = "手机")
    private String mobile;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "性别：1 男 2 女")
    private String gender;

    @Schema(description = "出生年月")
    private String birthday;

    @Schema(description = "省")
    private String province;

    @Schema(description = "市")
    private String city;

    @Schema(description = "县")
    private String district;

    @Schema(description = "详细地址")
    private String address;

    @Schema(description = "所属层级")
    private String departmentCode;

    @Schema(description = "所属分组")
    private String groupCode;

    @Schema(description = "标签")
    private List<String> tags = List.of();

    @Schema(description = "微信用户openId")
    private String weChatOpenId;

    @Schema(description = "微信公众号appId，默认值为第一个绑定的公众号appId")
    private String weChatAppId;

    @Schema(description = "extendFields 支持")
    @Convert(converter = HashMapConverter.class)
    private Map<String,Object> extendFields = new HashMap<>();

}




















