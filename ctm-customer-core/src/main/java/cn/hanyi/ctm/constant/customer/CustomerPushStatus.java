package cn.hanyi.ctm.constant.customer;

import cn.hanyi.ctm.constant.SendStatus;

public enum CustomerPushStatus {
    INIT,
    SUCCESS,
    FAILURE,
    ;

    public SendStatus mapToSendStatus() {
        if (this == INIT) {
            return SendStatus.UNCHECKED;
        } else if (this == SUCCESS) {
            return SendStatus.SUCCESS;
        } else if (this == FAILURE) {
            return SendStatus.FAILED;
        }
        return SendStatus.FAILED;
    }
}
