package cn.hanyi.ctm.constant.customer;

import cn.hanyi.ctm.constant.InteractionCollectorType;

public enum CustomerPushType {
    SMS,
    WECHAT,
    API,
    ;

    public InteractionCollectorType mapToInteractionCollectorType() {
        if (this == SMS) {
            return InteractionCollectorType.SMS;
        } else if (this == WECHAT) {
            return InteractionCollectorType.WECHAT;
        } else if (this == API) {
            return InteractionCollectorType.APP;
        }
        return InteractionCollectorType.SCENE_INTERACTION;
    }
}
