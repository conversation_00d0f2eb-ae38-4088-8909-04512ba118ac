package cn.hanyi.ctm.event;

import cn.hanyi.ctm.entity.CustomerJourneyRecord;
import org.befun.core.constant.EntityEventType;
import org.befun.core.hibernate.BaseEntityChangeEvent;

/**
 * The class description
 *
 * <AUTHOR>
 */
public class CustomerJourneyRecordChangeEvent extends BaseEntityChangeEvent<CustomerJourneyRecord> {
    public CustomerJourneyRecordChangeEvent(Object source) {
        super(source);
    }

    public CustomerJourneyRecordChangeEvent(Object source, CustomerJourneyRecord entity, EntityEventType eventType) {
        super(source, entity, eventType);
    }
}
