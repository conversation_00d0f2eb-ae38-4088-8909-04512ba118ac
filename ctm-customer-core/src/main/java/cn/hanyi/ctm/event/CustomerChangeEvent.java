package cn.hanyi.ctm.event;

import cn.hanyi.ctm.entity.Customer;
import org.befun.core.constant.EntityEventType;
import org.befun.core.hibernate.BaseEntityChangeEvent;

/**
 * The class description
 *
 * <AUTHOR>
 */
public class CustomerChangeEvent extends BaseEntityChangeEvent<Customer> {
    public CustomerChangeEvent(Object source) {
        super(source);
    }

    public CustomerChangeEvent(Object source, Customer entity, EntityEventType eventType) {
        super(source, entity, eventType);
    }
}
