package cn.hanyi.ctm.event;

import cn.hanyi.ctm.entity.CustomerAnswers;
import org.befun.core.constant.EntityEventType;
import org.befun.core.hibernate.BaseEntityChangeEvent;

/**
 * The class description
 *
 * <AUTHOR>
 */
public class CustomerAnswersChangeEvent extends BaseEntityChangeEvent<CustomerAnswers> {
    public CustomerAnswersChangeEvent(Object source) {
        super(source);
    }

    public CustomerAnswersChangeEvent(Object source, CustomerAnswers entity, EntityEventType eventType) {
        super(source, entity, eventType);
    }
}
