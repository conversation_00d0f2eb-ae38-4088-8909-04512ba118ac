package cn.hanyi.ctm.service;

import cn.hanyi.common.file.storage.FileInfo;
import cn.hanyi.common.file.storage.FileStorageService;
import cn.hanyi.ctm.constant.ExcelType;
import cn.hanyi.ctm.constant.error.CtmErrorCode;
import cn.hanyi.ctm.dto.CustomerStatDto;
import cn.hanyi.ctm.dto.customer.*;
import cn.hanyi.ctm.entity.*;
import cn.hanyi.ctm.exception.CtmErrorException;
import cn.hanyi.ctm.repository.CustomerRepository;
import cn.hanyi.ctm.workertrigger.ICtmEventTrigger;
import cn.hanyi.ctm.workertrigger.ICtmTaskTrigger;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.poi.ooxml.POIXMLException;
import org.befun.auth.constant.QueryTypeEmptyFlag;
import org.befun.auth.constant.UserConfigType;
import org.befun.auth.dto.DepartmentTreeDto;
import org.befun.auth.dto.orgconfig.OrgConfigExtendCustomerFieldDto;
import org.befun.auth.dto.userconfig.UserConfigBuilderDto;
import org.befun.auth.dto.userconfig.UserConfigCustomerQueryBuilderDto;
import org.befun.auth.dto.userconfig.UserConfigCustomerQueryItemDto;
import org.befun.auth.entity.Department;
import org.befun.auth.entity.Organization;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.*;
import org.befun.auth.service.auth.AuthWechatOpenService;
import org.befun.auth.service.auth.config.WechatOpenConfig;
import org.befun.auth.service.orgconfig.OrgConfigExtendCustomerFieldService;
import org.befun.core.constant.EntityScopeStrategyType;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.EmbeddedFieldContext;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.BaseService;
import org.befun.core.service.CrudService;
import org.befun.core.utils.JsonHelper;
import org.befun.core.utils.RegHelper;
import org.befun.extension.nativesql.SqlBuilder;
import org.befun.extension.service.NativeSqlHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.Duration;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static org.befun.auth.constant.QueryTypeEmptyFlag.*;
import static org.befun.core.rest.context.FieldRelationType.ONE_TO_MANY;
import static org.befun.core.utils.NumberHelper.unbox;


@Service
@Slf4j
public class CustomerService extends BaseService<Customer, CustomerDto, CustomerRepository> {

    @Lazy
    @Autowired
    private CustomerService self;
    @Autowired
    private UserService userService;
    @Autowired
    private DepartmentService departmentService;
    @Value("${befun.extension.upload.customer-return-size:50}")
    private Integer customerReturnSize;
    @Value("${befun.extension.upload.customer-file-max-size:5}")
    private Integer customerFileMaxSize;
    @Autowired
    private CustomerGroupService customerGroupService;
    @Autowired
    private TreeConvertService treeConvertService;
    @Autowired
    private CustomerStatService customerStatService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private CrudService crudService;
    @Autowired
    private NativeSqlHelper nativeSqlHelper;
    @Autowired
    private ThirdPartyCustomerService thirdPartyCustomerService;
    @Autowired
    private ICtmEventTrigger ctmEventTrigger;
    @Autowired
    private ICtmTaskTrigger ctmTaskTrigger;
    @Autowired
    private UserTaskService userTaskService;
    @Autowired
    private OrgConfigExtendCustomerFieldService orgConfigExtendCustomerFieldService;
    @Autowired
    private CustomerMessageService customerMessageService;
    @Autowired
    private AuthWechatOpenService authWechatOpenService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private UserConfigService userConfigService;
    @Autowired
    private EntityManager entityManager;
    @Autowired
    private FileStorageService fileStorageService;
    @Autowired
    private CustomerExperienceIndicatorService customerExperienceIndicatorService;

    @Override
    protected void addCustomRelations(Map<String, EmbeddedFieldContext> relationMaps) {
        try {
            // 把 CustomerAnswers 模拟成 Customer 的下一级
            EmbeddedFieldContext answers = new EmbeddedFieldContext(null, CustomerAnswers.class.getDeclaredField("customer"), "customer", ONE_TO_MANY);
            relationMaps.put("answers", answers);
            // 把 CustomerJourneyRecord 模拟成 Customer 的下一级
            EmbeddedFieldContext journeyRecords = new EmbeddedFieldContext(null, CustomerJourneyRecord.class.getDeclaredField("customerId"), "customerId", ONE_TO_MANY);
            relationMaps.put("journeyRecords", journeyRecords);
            // 把 CustomerHistoryRecord 模拟成 Customer 的下一级
            EmbeddedFieldContext historyRecords = new EmbeddedFieldContext(null, CustomerHistoryRecord.class.getDeclaredField("customerId"), "customerId", ONE_TO_MANY);
            relationMaps.put("historyRecords", historyRecords);
        } catch (NoSuchFieldException e) {
            throw new RuntimeException(e);
        }
    }

    public Page<CustomerDto> combineSearch(CustomerSearchDto searchDto) {
        SqlBuilder sqlBuilder = combineSearchSqlBuilder(TenantContext.getCurrentTenant(), TenantContext.getCurrentUserId(), searchDto.getSelectConditions()).limit(searchDto.getPage(), searchDto.getLimit());
        sqlBuilder.orderByDesc("c.id");
        return nativeSqlHelper.queryDtoPage(sqlBuilder, (type, ids) -> {
            List<Customer> entity = scopeQuery(EntityScopeStrategyType.NONE, () -> repository.findByIdInOrderByCreateTimeDesc(ids));
            List<CustomerDto> dto = mapToDto(entity);
            afterMapToDto(entity, dto);
            return dto;
        });
    }

    @Override
    public void afterMapToDto(List<Customer> entity, List<CustomerDto> dto) {
        if (CollectionUtils.isEmpty(dto)) {
            return;
        }
        boolean updateStat = dto.size() == 1;
        Set<Long> ids = new HashSet<>();
        Set<Long> userIds = new HashSet<>();
        Set<Long> departmentIds = new HashSet<>();
        Set<Long> thirdPartyCustomerIds = new HashSet<>();
        dto.forEach(i -> {
            Optional.ofNullable(i.getId()).ifPresent(ids::add);
            Optional.ofNullable(i.getDepartmentId()).ifPresent(departmentIds::add);
            Optional.ofNullable(i.getCreatedByUid()).ifPresent(userIds::add);
            Optional.ofNullable(i.getModifiedByUid()).ifPresent(userIds::add);
            Optional.ofNullable(i.getThirdPartyCustomerId()).ifPresent(thirdPartyCustomerIds::add);
            Optional.of(parseBelongToUids(i.getBelongToUids())).ifPresent(j -> {
                i.setBelongToUserIds(j);
                userIds.addAll(j);
            });
        });
        // 所有相关的部门信息
        Map<Long, Department> departmentMap = departmentService.getGroupMapByIds(departmentIds, Department::getId, Function.identity());
        // 所有相关的用户信息
        Map<Long, SimpleUser> userMap = userService.getSimpleMapByIds(userIds);
        // 所有相关的客户组信息
        Map<Long/*customerId*/, List<CustomerGroupDto>> customerGroupMap = customerGroupService.getCustomerGroupsByCustomerIds(ids);
        // 所有相关的微信客户信息
        Map<Long, ThirdPartyCustomerDto> thirdPartyCustomerMap = thirdPartyCustomerService.getGroupMapByIds(thirdPartyCustomerIds, ThirdPartyCustomer::getId);
        thirdPartyCustomerService.afterMapToDto(new ArrayList<>(), new ArrayList<>(thirdPartyCustomerMap.values()));
        // 企业内容的所有自定义字段
        Map<String, OrgConfigExtendCustomerFieldDto.ExtendField> extendFieldMap = orgConfigExtendCustomerFieldService.extendFieldMap();
        // 新版体验指标
        Map<Long, CustomerStatExperienceIndicatorDto> customerStatExperienceIndicatorDtoMap = customerExperienceIndicatorService.experienceIndicatorScore(ids, dto.size(), true).stream().collect(Collectors.toMap(IndicatorCustomerResponseDto::getCustomerId, value -> new CustomerStatExperienceIndicatorDto(value.getName(), value.getValue())));

        dto.forEach(i -> {
            Optional.ofNullable(userMap.get(i.getCreatedByUid())).ifPresent(j -> i.setCreateUserName(j.getTruename()));
            Optional.ofNullable(userMap.get(i.getModifiedByUid())).ifPresent(j -> i.setModifyUserName(j.getTruename()));
            Optional.ofNullable(thirdPartyCustomerMap.get(i.getThirdPartyCustomerId())).ifPresent(j -> {
                i.setOpenId(j.getOpenId());
                i.setThirdPartyCustomer(j);
            });
            i.setDepartmentNames(departmentIdToDepartmentNames(i.getDepartmentId(), departmentMap));
            i.setDepartmentName(departmentIdToDepartmentName(i.getDepartmentId(), departmentMap));
            i.setBelongToUidNames(userIdsToUserNames(i.getBelongToUserIds(), userMap));

            AtomicReference<CustomerStatDto> customerStat = new AtomicReference<CustomerStatDto>();
            if (updateStat) {
                customerStat.set(customerStatService.customerStat(i.getId()));
            } else {
                customerStat.set(customerStatService.getCustomerStat(i.getId()));
            }

            Optional.ofNullable(customerStatExperienceIndicatorDtoMap.get(i.getId())).ifPresent(c -> {
                customerStat.get().setExperienceIndicatorName(c.getExperienceIndicatorName());
                customerStat.get().setExperienceIndicatorScore(c.getExperienceIndicatorScore());
            });

            i.setCustomerStat(customerStat.get());
            i.setGroups(customerGroupMap.getOrDefault(i.getId(), List.of()));
            if (MapUtils.isNotEmpty(i.getExtendFields())) {
                Map<String, String> convertLabelMap = new HashMap<>();
                Map<String, Map<String, String>> fieldOptionMap = new HashMap<>();
                i.getExtendFields().forEach((k, v) -> {
                    OrgConfigExtendCustomerFieldDto.ExtendField extendField = extendFieldMap.get(k);
                    if (extendField != null
                            && extendField.getType() != null
                            && extendField.getType().isHasOptions()
                            && CollectionUtils.isNotEmpty(extendField.getOptions())
                            && v instanceof String
                    ) {
                        Map<String, String> optionMap = fieldOptionMap.computeIfAbsent(k, m -> new HashMap<>());
                        if (optionMap.isEmpty()) {
                            extendField.getOptions().forEach(o -> optionMap.put(o.getValue(), o.getLabel()));
                        }
                        String values = (String) v;
                        String labels = Arrays.stream(values.split(",")).map(optionMap::get).filter(Objects::nonNull).collect(Collectors.joining(","));
                        convertLabelMap.put(k + "@label", labels);
                    }
                });
                if (!convertLabelMap.isEmpty()) {
                    i.getExtendFields().putAll(convertLabelMap);
                }
            }
        });
    }

    private Set<Long> parseBelongToUids(String belongToUids) {
        Set<Long> belongToUserIds = new HashSet<>();
        if (StringUtils.isNotEmpty(belongToUids)) {
            // 以前的数据是用 / 分隔的，这里需要兼容旧数据
            String sp = belongToUids.contains("/") ? "/" : ",";
            Arrays.stream(belongToUids.split(sp)).forEach(j -> {
                if (NumberUtils.isDigits(j)) {
                    Long uid = Long.parseLong(j);
                    belongToUserIds.add(uid);
                }
            });
        }
        return belongToUserIds;
    }

    private List<String> userIdsToUserNames(Set<Long> userIds, Map<Long, SimpleUser> userMap) {
        List<String> userNames = new ArrayList<>();
        if (CollectionUtils.isEmpty(userIds)) {
            return userNames;
        }
        Map<Long, SimpleUser> userMap2 = userMap != null ? userMap : userService.getSimpleMapByIds(userIds);
        userIds.forEach(i -> Optional.ofNullable(userMap2.get(i)).map(SimpleUser::getTruename).ifPresent(userNames::add));
        return userNames;
    }

    private String departmentIdToDepartmentName(Long departmentId, Map<Long, Department> departmentMap) {
        if (departmentId == null || departmentId <= 0) {
            return "";
        }
        departmentMap = departmentMap != null ? departmentMap : departmentService.getGroupMapByIds(List.of(departmentId), Department::getId, Function.identity());
        return Optional.ofNullable(departmentMap.get(departmentId)).map(Department::getTitle).orElse("");
    }

    private String departmentIdToDepartmentNames(Long departmentId, Map<Long, Department> departmentMap) {
        if (departmentId == null || departmentId <= 0) {
            return "";
        }
        departmentMap = departmentMap != null ? departmentMap : departmentService.getGroupMapByIds(List.of(departmentId), Department::getId, Function.identity());
        return Optional.ofNullable(departmentMap.get(departmentId)).map(Department::getParentNames).orElse("");
    }

    @Override
    @Transactional
    public <S extends BaseEntityDTO<Customer>> CustomerDto create(S data) {
        Long orgId = TenantContext.getCurrentTenant();
        Long userId = TenantContext.getCurrentUserId();
        CustomerSaveDto save = (CustomerSaveDto) data;
        checkUniqueExternalUserId(orgId, save.getExternalUserId(), null);
        Customer entity = new Customer();
        crudService.mapToEntity(save, entity);
        entity.setId(null);
        entity.setCreatedByUid(userId);
        entity.setModifiedByUid(userId);
        entity.setDepartmentNames(departmentIdToDepartmentNames(entity.getDepartmentId(), null));
        entity.setBelongToUidNames(userIdsToUserNames(parseBelongToUids(entity.getBelongToUids()), null));
        if (entity.getDepartmentId() == null || entity.getDepartmentId() < 0) {
            entity.setDepartmentId(0L);
        }
        repository.save(entity);
        customerGroupService.updateCustomerGroups(entity.getId(), save.getGroupIds());
        CustomerDto dto = mapToDto(entity);
        afterMapToDto(dto.getEntity(), dto);
        ctmEventTrigger.customerCreate(orgId, userId, entity.getId());
        return dto;
    }

    /**
     * 创建客户
     */
    public Customer createCustomer(Long orgId, String externalUserId, String customerName, String customerGender, Long departmentId, String departmentCode) {
        return createCustomer(orgId, externalUserId, customerName, customerGender, departmentId, departmentCode, null, null, null);
    }

    /**
     * 创建客户
     */
    public Customer createCustomer(Long orgId, String externalUserId, String customerName, String customerGender, String mobile) {
        return createCustomer(orgId, externalUserId, customerName, customerGender, null, null, mobile, null, null);
    }

    /**
     * 创建客户
     */
    public Customer createCustomer(Long orgId, String externalUserId, String customerName, String customerGender,
                                   Long departmentId, String departmentCode,
                                   String mobile, String appId, String openId) {
        Customer customer = new Customer();
        customer.setOrgId(orgId);
        String username = customerName == null ? externalUserId : customerName;
        if (username == null) {
            username = "";
        } else if (username.length() > 32) {
            username = username.substring(0, 32);
        }
        customer.setUsername(username);
        customer.setNickname(username);
        if (mobile != null) {
            customer.setMobile(mobile);
        }
        if (customerGender != null) {
            customer.setGender(customerGender);
        }
        customer.setCreatedByUid(0L);
        customer.setModifiedByUid(0L);
        try {
            checkUniqueExternalUserId(orgId, externalUserId, null);
            customer.setExternalUserId(externalUserId);
        } catch (Throwable e) {
            log.warn("企业({})的外部客户id({})已存在，此次新增客户取消设置外部客户id", orgId, externalUserId);
        }
        if (StringUtils.isNotEmpty(openId)) {
            //设置微信用户
            WechatOpenConfig wechatOpenConfig = getWechatOpenConfig(appId);
            if (wechatOpenConfig != null) {
                ThirdPartyCustomer thirdPartyCustomer = thirdPartyCustomerService.getThirdpartyCustomer(orgId, wechatOpenConfig.getConfigId(), openId);
                if (thirdPartyCustomer == null) {
                    // 没有这个微信客户，创建一个，并绑定到新的客户
                    thirdPartyCustomer = thirdPartyCustomerService.add(orgId, wechatOpenConfig.getConfigId(), openId);
                    customer.setThirdPartyCustomerId(thirdPartyCustomer.getId());
                }
            }
        }
        if (departmentId != null || StringUtils.isNotEmpty(departmentCode)) {
            // 设置部门信息
            Department department = departmentService.getByIdOrCodeOrRoot(orgId, departmentId, departmentCode, false);
            if (department != null) {
                customer.setDepartmentId(department.getId());
                customer.setDepartmentNames(department.getParentNames());
            }
        }
        if (customer.getDepartmentId() == null) {
            customer.setDepartmentId(0L);
        }
        repository.save(customer);
        ctmEventTrigger.customerCreate(orgId, null, customer.getId());
        return customer;
    }

    private Pair<String, String> compareValue(List<String> oldValue, List<String> newValue) {
        return compareValue(
                oldValue == null ? null : ("[" + String.join(",", oldValue) + "]"),
                newValue == null ? null : ("[" + String.join(",", newValue) + "]")
        );
    }

    private Pair<String, String> compareValue(String oldValue, String newValue) {
        if (Objects.equals(oldValue, newValue)) {
            return null;
        }
        if (StringUtils.isEmpty(oldValue)) {
            oldValue = "--/--";
        }
        if (StringUtils.isEmpty(newValue)) {
            newValue = "--/--";
        }
        return Pair.of(oldValue, newValue);
    }

    private Pair<String, String> compareDepartment(Long oldValue, Long newValue) {
        if (oldValue == null && newValue == null) {
            return null;
        } else if (oldValue != null && oldValue.equals(newValue)) {
            return null;
        }
        String oldValueStr = null;
        if (oldValue != null) {
            Department department = departmentService.get(oldValue);
            if (department != null) {
                oldValueStr = department.getParentNames();
            }
        }
        String newValueStr = null;
        if (newValue != null) {
            Department department = departmentService.get(newValue);
            if (department != null) {
                newValueStr = department.getParentNames();
            }
        }
        return compareValue(oldValueStr, newValueStr);
    }

    private Pair<String, String> compareBelongUids(String oldValue, String newValue) {
        Set<Long> oldUserIds = parseBelongToUids(oldValue);
        Set<Long> newUserIds = parseBelongToUids(newValue);
        if (oldUserIds.equals(newUserIds)) {
            return null;
        }
        Set<Long> userIds = new HashSet<>(oldUserIds);
        userIds.addAll(newUserIds);
        Map<Long, SimpleUser> userMap = userService.getSimpleMapByIds(userIds);

        List<String> oldUsernames = userIdsToUserNames(oldUserIds, userMap);
        List<String> newUsernames = userIdsToUserNames(newUserIds, userMap);
        return compareValue(oldUsernames, newUsernames);
    }

    private Map<String, Pair<String, String>> calcChanges(Customer o, CustomerSaveDto n) {
        Map<String, Pair<String, String>> changes = new HashMap<>();
        Optional.ofNullable(compareValue(o.getUsername(), n.getUsername())).ifPresent(p -> changes.put("姓名", p));
        Optional.ofNullable(compareValue(o.getNickname(), n.getNickname())).ifPresent(p -> changes.put("昵称", p));
        Optional.ofNullable(compareValue(o.getGender(), n.getGender())).ifPresent(p -> changes.put("性别", p));
        Optional.ofNullable(compareValue(o.getEmail(), n.getEmail())).ifPresent(p -> changes.put("邮箱", p));
        Optional.ofNullable(compareValue(o.getBirthday(), n.getBirthday())).ifPresent(p -> changes.put("出生日期", p));
        Optional.ofNullable(compareValue(o.getAvatar(), n.getAvatar())).ifPresent(p -> changes.put("头像", p));
        Optional.ofNullable(compareValue(o.getMobile(), n.getMobile())).ifPresent(p -> changes.put("手机号码", p));
        Optional.ofNullable(compareValue(o.getProvince(), n.getProvince())).ifPresent(p -> changes.put("省份", p));
        Optional.ofNullable(compareValue(o.getCity(), n.getCity())).ifPresent(p -> changes.put("城市", p));
        Optional.ofNullable(compareValue(o.getDistrict(), n.getDistrict())).ifPresent(p -> changes.put("区县", p));
        Optional.ofNullable(compareValue(o.getAddress(), n.getAddress())).ifPresent(p -> changes.put("详细地址", p));
        Optional.ofNullable(compareDepartment(o.getDepartmentId(), n.getDepartmentId())).ifPresent(p -> changes.put("所属层级", p));
        Optional.ofNullable(compareBelongUids(o.getBelongToUids(), n.getBelongToUids())).ifPresent(p -> changes.put("所属员工", p));
        Optional.ofNullable(compareValue(o.getTags(), n.getTags())).ifPresent(p -> changes.put("标签", p));
        return changes;
    }

    private Map<String, Pair<String, String>> calcChanges(Customer o, List<String> tags) {
        Map<String, Pair<String, String>> changes = new HashMap<>();
        Optional.ofNullable(compareValue(o.getTags(), tags)).ifPresent(p -> changes.put("标签", p));
        return changes;
    }

    @Transactional
    public Boolean saveTags(Long id, List<String> tags) {
        Long orgId = TenantContext.getCurrentTenant();
        Long userId = TenantContext.getCurrentUserId();
        Customer entity = require(id);
        checkIsCurrentOrg(entity);
        Map<String, Pair<String, String>> changes = calcChanges(entity, tags);
        entity.setTags(Optional.ofNullable(tags).orElse(List.of()));
        entity.setModifiedByUid(userId);
        repository.save(entity);
        ctmEventTrigger.customerUpdate(orgId, userId, entity.getId(), changes);
        return true;
    }

    public void resetCurrentDepartmentIds() {
        long orgId = TenantContext.requireCurrentTenant();
        long userId = TenantContext.requireCurrentUserId();
        List<Long> departmentIds = getAllDepartmentIds(orgId, userId, null);
        TenantContext.setCurrentDepartmentIds(departmentIds);
        TenantContext.setCurrentSubDepartmentIds(departmentIds);
    }

    @Override
    public Boolean deleteOne(long id) {
        Boolean r = super.deleteOne(id);
        if (r) {
            customerGroupService.clearCustomerGroups(id);
            ctmEventTrigger.customerDelete(TenantContext.getCurrentTenant(), TenantContext.getCurrentUserId(), id);
        }
        return r;
    }

    @Override
    @Transactional
    public <S extends BaseEntityDTO<Customer>> CustomerDto updateOne(long id, S change) {
        Long orgId = TenantContext.getCurrentTenant();
        Long userId = TenantContext.getCurrentUserId();
        Customer entity = require(id);
        checkIsCurrentOrg(entity);
        CustomerSaveDto save = (CustomerSaveDto) change;
        checkUniqueExternalUserId(orgId, save.getExternalUserId(), entity);
        save.setDepartmentNames(departmentIdToDepartmentNames(entity.getDepartmentId(), null));
        save.setBelongToUidNames(userIdsToUserNames(parseBelongToUids(entity.getBelongToUids()), null));
        Map<String, Pair<String, String>> changes = calcChanges(entity, save);
        crudService.mapToEntity(save, entity);
        // 复制属性时，如果是null会被忽略
        if (save.getDepartmentId() == null) {
            entity.setDepartmentId(0L);
        }
        entity.setId(id);
        entity.setModifiedByUid(userId);
        repository.save(entity);
        customerGroupService.updateCustomerGroups(id, save.getGroupIds());
        CustomerDto dto = mapToDto(entity);
        afterMapToDto(dto.getEntity(), dto);
        ctmEventTrigger.customerUpdate(orgId, userId, entity.getId(), changes);
        return dto;
    }

    /**
     * 检验客户编号在企业内唯一
     */
    private void checkUniqueExternalUserId(Long orgId, String externalUserId, Customer current) {
        if (StringUtils.isNotEmpty(externalUserId) && !isUniqueInOrg(orgId, "externalUserId", externalUserId, current)) {
            throw new BadRequestException("客户编号已存在");
        }
    }

    public List<Customer> getByIds(List<Long> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            Set<Long> s = ids.stream().filter(Objects::nonNull).collect(Collectors.toSet());
            if (!s.isEmpty()) {
                return repository.findAllById(s);
            }
        }
        return List.of();
    }

    public List<CustomerDepartmentGroupDto> departmentsAndGroups(boolean hasMobile, boolean hasWechat, boolean hasEmail) {
        Long orgId = TenantContext.getCurrentTenant();
        Long userId = TenantContext.getCurrentUserId();
        DepartmentTreeDto dto = departmentService.fullTreeByUser(orgId, userId);
        if (dto == null) {
            return List.of();
        }
        Organization org = organizationService.requireCurrent();
        String departmentIdsJoinString = getAllDepartmentIdsJoinString(orgId, userId, departmentService.treeIds(dto));
        Map<Long, Integer> idCountMap = countCustomerByDepartment(orgId, departmentIdsJoinString, hasMobile, hasWechat, hasEmail);
        CustomerDepartmentGroupDto all = new CustomerDepartmentGroupDto(-1L, org.getName(), idCountMap.getOrDefault(-1L, 0), 1, true);
        CustomerDepartmentGroupDto noDepartment = new CustomerDepartmentGroupDto(-2L, "未分组客户", idCountMap.getOrDefault(-2L, 0), 1, true);
        CustomerDepartmentGroupDto departmentTree = buildDepartmentTree(idCountMap, dto);
        CustomerDepartmentGroupDto groupList = buildGroupList(orgId, departmentIdsJoinString, customerGroupService.findAllEntities(), hasMobile, hasWechat, hasEmail);
        if (CollectionUtils.isEmpty(dto.getSubDepartments())) {
            return List.of(all, noDepartment, groupList);
        } else {
            return List.of(all, noDepartment, departmentTree, groupList);
        }
    }

    private CustomerDepartmentGroupDto buildDepartmentTree(Map<Long, Integer> idCountMap, DepartmentTreeDto tree) {
        List<CustomerDepartmentGroupDto> list = treeConvertService.treeToTree(List.of(tree), i -> {
            CustomerDepartmentGroupDto dto = new CustomerDepartmentGroupDto();
            dto.setId(i.getId());
            dto.setName(i.getTitle());
            dto.setCode(i.getCode());
            dto.setCount(idCountMap.getOrDefault(i.getId(), 0));
            dto.setType(1);
            dto.setRoot(false);
            return dto;
        });
        CustomerDepartmentGroupDto root = list.get(0);
        root.setName("组织架构");
        root.setRoot(true);
        return root;
    }

    private CustomerDepartmentGroupDto buildGroupList(Long orgId, String departmentIdsJoinString, List<CustomerGroup> groups, boolean hasMobile, boolean hasWechat, boolean hasEmail) {
        CustomerDepartmentGroupDto groupList = new CustomerDepartmentGroupDto(null, "自定义分组", null, 2, true);
        if (CollectionUtils.isNotEmpty(groups)) {
            groups.forEach(i -> {
                groupList.getChildren().add(
                        new CustomerDepartmentGroupDto(i.getId(), i.getName(), i.getCode(),
                                countCustomerByGroup(orgId, departmentIdsJoinString, i.getId(), hasMobile, hasWechat, hasEmail), 2, false));
            });
        }
        return groupList;
    }

    /**
     * 重置部门被删除的客户
     * worker CUSTOMER_RESET_DEPARTMENT 回调此方法
     */
    @Transactional(rollbackFor = Exception.class)
    public void resetCustomerDepartment(Long orgId) {
        repository.resetCustomerDepartment(orgId);
        repository.resetCustomerDepartmentNull(orgId);
    }

    /**
     * 每天最多触发一次
     */
    public void triggerResetCustomerDepartmentTask(Long orgId, Long userId) {
        String key = String.format("customer-reset-department:%d:%s", orgId, LocalDate.now());
        Long times = stringRedisTemplate.opsForValue().increment(key);
        stringRedisTemplate.expire(key, Duration.ofDays(1));
        if (times != null && times == 1) {
            ctmTaskTrigger.customerResetDepartment(orgId, userId);
        }
    }

    /**
     * 批量上传客户
     * 通过上传excel文件
     */
    public UploadCustomerResponseDto createByUpload(MultipartFile multipartFile, boolean preview) {
        createByUploadCheck(multipartFile);

        List<UploadCustomerDto> resultDto = new ArrayList<>();
        AtomicInteger total = new AtomicInteger(0);
        SimpleUser user = userService.requireCurrentSimple();
        Long orgId = TenantContext.getCurrentTenant();
        List<String> externalUserIdList = repository.findAllexternalUserId(TenantContext.getCurrentTenant()).stream().filter(Objects::nonNull).collect(Collectors.toList());
        // 无法被spring管理的对象，需要手动创建
        UploadCustomerListener uploadCustomerListener = null;
        try {
            uploadCustomerListener = new UploadCustomerListener(
                    resultDto,
                    customerReturnSize,
                    user,
                    orgId,
                    total,
                    repository,
                    externalUserIdList,
                    departmentService.treeByOrg(orgId),
                    userService.getAllUsersInOrg(orgId),
                    preview
            );
            EasyExcel.read(multipartFile.getInputStream(), UploadCustomerDto.class, uploadCustomerListener).sheet().headRowNumber(2).doRead();

        } catch (IOException e) {
            throw new CtmErrorException(CtmErrorCode.EXCEL_ERROR, e.getMessage());
        } catch (POIXMLException e) {
            throw new CtmErrorException(CtmErrorCode.EXCEL_NOT_SUPPORT_ERROR, CtmErrorCode.EXCEL_NOT_SUPPORT_ERROR.getMessage());
        }


        return new UploadCustomerResponseDto(total.get(), resultDto);
    }

    private void createByUploadCheck(MultipartFile multipartFile) {
        if (Arrays.stream(ExcelType.values()).filter(value -> value.getValue().equals(multipartFile.getContentType().toLowerCase())).count() == 0) {
            throw new CtmErrorException(CtmErrorCode.EXCEL_TYPE_ERROR);
        }
        if (multipartFile.getSize() > customerFileMaxSize * 1024 * 1024) {
            throw new CtmErrorException(CtmErrorCode.EXCEL_SIZE_ERROR);
        }
    }

    public Customer getCustomerById(Long orgId, Long customerId) {
        return repository.findCustomerByOrgIdAndId(orgId, customerId).orElse(null);
    }

    public Customer getCustomerByExternalUserId(Long orgId, String externalUserId) {
        return repository.findFirstByOrgIdAndExternalUserId(orgId, externalUserId).orElse(null);
    }

    public Customer getCustomerByExternalUserId(String externalUserId) {
        return repository.findFirstByOrgIdAndExternalUserId(TenantContext.getCurrentTenant(), externalUserId).orElse(null);
    }

    public Customer getCustomerByThirdpartyCustomerId(Long orgId, Long thirdpartyCustomerId) {
        List<Customer> customers = repository.findByOrgIdAndThirdPartyCustomerId(orgId, thirdpartyCustomerId);
        if (CollectionUtils.isNotEmpty(customers)) {
            return customers.get(0);
        }
        return null;
    }

    public Customer getCustomerByMobile(Long orgId, String mobile) {
        return repository.findFirstByOrgIdAndMobileOrderByIdDesc(orgId, mobile).orElse(null);
    }

    /**
     * 根据答题信息获取客户
     *
     * @param
     * @return
     */
    public Customer getCustomerFromResponse(
            Long orgId,
            String externalUserId,
            Long customerId,
            Long departmentId,
            String departmentCode,
            String mobile,
            String customerName,
            String customerGender,
            String customerGroupCode,
            List<String> responseTags
    ) {
        Customer customer = null;
        try {
            // 优先使用 customerId
            if (customerId != null) {
                Optional<Customer> customerOptional = repository.findById(customerId);
                if (customerOptional.isPresent()) {
                    customer = customerOptional.get();
                }
            }
            // customer 不存在 ，且如果存在 externalUserId
            if (customer == null && orgId != null && StringUtils.isNotEmpty(externalUserId)) {
                Optional<Customer> customerOptional = repository.findFirstByOrgIdAndExternalUserId(orgId, externalUserId);
                // 创建客户
                customer = customerOptional.orElseGet(() -> createCustomer(orgId, externalUserId, customerName, customerGender, departmentId, departmentCode));
            }
            // customer 不存在 ，且如果存在 mobile
            if (customer == null && orgId != null && StringUtils.isNotEmpty(mobile)) {
                Optional<Customer> customerOptional = repository.findFirstByOrgIdAndMobileOrderByIdDesc(orgId, mobile);
                // 创建客户
                customer = customerOptional.orElseGet(() -> createCustomer(orgId, externalUserId, customerName, customerGender, mobile));
            }
        } catch (Exception e) {
            log.error("获取客户失败", e);
        }
        if (customer != null) {
            boolean saveCustomer = false;
            if (CollectionUtils.isNotEmpty(responseTags)) {
                Set<String> tags = new HashSet<>(responseTags);
                if (CollectionUtils.isNotEmpty(customer.getTags())) {
                    tags.addAll(customer.getTags());
                }
                customer.setTags(tags.stream().distinct().collect(Collectors.toList()));
                saveCustomer = true;
            }
            if (StringUtils.isNotEmpty(mobile) && StringUtils.isEmpty(customer.getMobile())) {
                customer.setMobile(mobile);
                saveCustomer = true;
            }
            if (saveCustomer) {
                repository.save(customer);
            }
            customerGroupService.addCustomerGroup(orgId, customer.getId(), customerGroupCode);
        }
        return customer;
    }

    private Map<String, String> customerPropertyMap(Long orgId) {
        Map<String, String> map = new HashMap<>();
        // customer
        map.put("username", "c.username");
        map.put("latestJourneyRecord", "c.latest_journey_record");
        map.put("mobile", "c.mobile");
        map.put("email", "c.email");
        map.put("gender", "c.gender");
        map.put("birthday", "c.birthday");
        map.put("province", "c.province");
        map.put("city", "c.city");
        map.put("district", "c.district");
        map.put("address", "c.address");
        map.put("departmentId", "c.department_id");
        map.put("belongToUids", "c.belong_to_uids");
        map.put("tags", "c.tags");
        map.put("createdByUid", "c.created_by_uid");
        map.put("createTime", "c.create_time");
        map.put("modifiedByUid", "c.modified_by_uid");
        map.put("modifyTime", "c.modify_time");
        map.put("externalUserId", "c.external_user_id");
        // customerStat
        map.put("journeyIndicatorScore", "cs.journey_indicator_score");
        map.put("mostEventRuleTimes", "cs.most_event_rule_times");
        map.put("countJourneyRecord", "cs.count_journey_record");
        map.put("countSendSurvey", "cs.count_send_survey");
        map.put("countJoinSurvey", "cs.count_join_survey");
        map.put("countCompleteSurvey", "cs.count_complete_survey");
        map.put("countEvent", "cs.count_event");
        // thirdpartyCustomer
        map.put("openId", "tc.open_id");
        map.put("wechatConfigId", "tc.thirdparty_auth_id");
        // customerGroup
        map.put("groupId", "cgr.group_id");
        // customer extend fields
        orgConfigExtendCustomerFieldService.extendFields(orgId).forEach(i -> {
            String prop = i.getProp();
            if (NumberUtils.isDigits(i.getProp())) {
                prop = "\"" + prop + "\"";
            }
            map.put(i.getProp(), String.format("json_extract(extend_fields,'$.%s')", prop));
        });
        return map;
    }

    private Map<String, List<QueryTypeEmptyFlag>> emptyFlagsMap() {
        return Map.of("tags", List.of(NULL, NULL_STRING, ZERO_LENGTH_ARRAY, ZERO_LENGTH_STRING),
                "createTime", List.of(NULL),
                "modifyTime", List.of(NULL));
    }

    public SqlBuilder combineSearchSqlBuilder(Long orgId, Long userId, CustomerCombineSearchDto condition) {
        CustomerCombineSearchDto dto = condition != null ? condition : new CustomerCombineSearchDto();
        long groupId = unbox(dto.getSelectGroupId());
        long departmentId = unbox(dto.getSelectDepartmentId());
        String searchMobile = null;
        String searchUsername = null;
        if (StringUtils.isNotEmpty(dto.getQ())) {
            if (RegHelper.isMobile(dto.getQ())) {
                searchMobile = dto.getQ();
            } else {
                searchUsername = dto.getQ();
            }
        }
        boolean hasGroup = CollectionUtils.isNotEmpty(dto.getItems()) || groupId > 0 || departmentId == -2;
        boolean hasStat = CollectionUtils.isNotEmpty(dto.getItems());
        boolean hasThirdparty = CollectionUtils.isNotEmpty(dto.getItems());
        boolean hasMobile = dto.isHasMobile();
        boolean hasWechat = dto.isHasWechat();
        boolean hasEmail = dto.isHasEmail();
        String indexHint = getIndexHint(hasMobile, hasWechat, hasEmail, () -> StringUtils.isNotEmpty(dto.getQ()) || CollectionUtils.isNotEmpty(dto.getItems()));
        SqlBuilder sqlBuilder = SqlBuilder.select("select distinct c.id,c.org_id orgId from customer c")
                .countSelect("select %1$s count(distinct c.id) from customer c", indexHint)
                .join("left join customer_group_relation cgr on c.id=cgr.customer_id ").ifTrue(hasGroup)
                .join("left join customer_stat cs on c.id=cs.customer_id ").ifTrue(hasStat)
                .join("left join thirdparty_customer tc on c.thirdparty_customer_id=tc.id ").ifTrue(hasThirdparty)
                .where("c.org_id=%d", orgId).alwaysTrue()
                .where("c.is_delete=0").alwaysTrue()
                .where("c.mobile='%1$s'", searchMobile).ifTrue(StringUtils.isNotEmpty(searchMobile))
                .where("c.username='%1$s'", searchUsername).ifTrue(StringUtils.isNotEmpty(searchUsername))
                .where("c.mobile is not null and c.mobile != ''").ifTrue(hasMobile)
                .where("c.email is not null and c.email != ''").ifTrue(hasEmail)
                .where("c.thirdparty_customer_id is not null").ifTrue(hasWechat);
        if (groupId > 0) {
            // 如果是查询分组，则把部门条件调整为全部
            sqlBuilder.where("cgr.group_id=%d", groupId).alwaysTrue();
            departmentId = -1;
        }
        if (departmentId == -1) {
            // 如果是查询全部客户
            String allDepartmentIdsJoinString = getAllDepartmentIdsJoinString(orgId, userId, null);
            sqlBuilder.where("(c.department_id in (%s))", allDepartmentIdsJoinString).alwaysTrue();
        } else if (departmentId == -2) {
            // 如果是查询未分组客户
            sqlBuilder.where("c.department_id = 0 and cgr.id is null").alwaysTrue();
        } else if (departmentId > 0) {
            // 如果是查询指定部门的客户
            sqlBuilder.where("c.department_id=%d", departmentId).alwaysTrue();
        }

        if (StringUtils.isNotEmpty(dto.getLogic()) && CollectionUtils.isNotEmpty(dto.getItems())) {
            Map<String, String> propertyMap = customerPropertyMap(orgId);
            Map<String, List<QueryTypeEmptyFlag>> emptyFlagsMap = emptyFlagsMap();
            List<String> combineWhere = new ArrayList<>();
            for (UserConfigCustomerQueryItemDto i : dto.getItems()) {
                String column = propertyMap.get(i.getPropertyName());
                if (column != null) {
                    List<QueryTypeEmptyFlag> emptyFlags = emptyFlagsMap.get(i.getPropertyName());
                    if (emptyFlags == null) {
                        combineWhere.add(i.getQueryValueType().query(i.getQueryType(), column, i.getQueryValue()));
                    } else {
                        combineWhere.add(i.getQueryValueType().query(i.getQueryType(), column, i.getQueryValue(), emptyFlags));
                    }
                }
            }
            sqlBuilder.where(() -> combineWhere.stream().collect(Collectors.joining(String.format(" %s ", dto.getLogic()), "(", ")"))).ifTrue(!combineWhere.isEmpty());
        }
        return sqlBuilder;
    }

    /**
     * 当前用户的所有部门
     */
    public List<Long> getAllDepartmentIds(Long orgId, Long userId, List<Long> departmentIds) {
        if (departmentIds == null) {
            departmentIds = departmentService.getFullDepartmentIdList(orgId, userId);
        }
        if (departmentIds == null) {
            departmentIds = new ArrayList<>();
        }
        departmentIds.add(0L);// 添加无部门，无部门时为0
        return departmentIds;
    }

    /**
     * 当前用户的所有部门的拼接(,)字符串
     */
    public String getAllDepartmentIdsJoinString(Long orgId, Long userId, List<Long> departmentIds) {
        return getAllDepartmentIds(orgId, userId, departmentIds).stream().filter(Objects::nonNull).map(Objects::toString).collect(Collectors.joining(","));
    }

    private String getIndexHint(boolean hasMobile, boolean hasWechat, boolean hasEmail, Supplier<Boolean> otherConditions) {
        String indexHint = "/*+INDEX(c idx_org_department) */"; // idx_org_department 这个索引在没有任何条件时是最优的索引
        if (hasMobile || hasWechat || hasEmail || (otherConditions != null && otherConditions.get())) {
            indexHint = "";
        }
        return indexHint;
    }

    public Map<Long, Integer> countCustomerByDepartment(Long orgId, String departmentIdsJoinString, boolean hasMobile, boolean hasWechat, boolean hasEmail) {
        String indexHint = getIndexHint(hasMobile, hasWechat, hasEmail, null);
        SqlBuilder sqlBuilder = SqlBuilder
                .select("select %s department_id departmentId,count(id) count from customer c", indexHint)
                .where("c.org_id = %d", orgId).alwaysTrue()
                .where("c.is_delete=0").alwaysTrue()
                .where("c.department_id in (%s)", departmentIdsJoinString).alwaysTrue()
                .where("c.mobile is not null and c.mobile != ''").ifTrue(hasMobile)
                .where("c.email is not null and c.email != ''").ifTrue(hasEmail)
                .where("c.thirdparty_customer_id is not null").ifTrue(hasWechat)
                .groupBy("c.department_id");
        String sql = sqlBuilder.buildSelectSql();
        log.info("countCustomerByDepartment：{}", sql);
        List<CustomerCountDto> count = nativeSqlHelper.queryListObject(sql, CustomerCountDto.class);
        int all = 0;
        Map<Long, Integer> countMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(count)) {
            for (CustomerCountDto i : count) {
                all += i.getCount();
                countMap.put(i.getDepartmentId(), i.getCount());
            }
        }
        countMap.put(-1L, all);
        countMap.put(-2L, countNoDepartmentAndGroups(orgId, hasMobile, hasWechat, hasEmail));
        return countMap;
    }

    /**
     * 统计组内客户数量
     */
    private int countCustomerByGroup(Long orgId, String departmentIdsJoinString, Long groupId, boolean hasMobile, boolean hasWechat, boolean hasEmail) {
        if (groupId == null || groupId <= 0) {
            return 0;
        }
        SqlBuilder sqlBuilder = SqlBuilder.select("select count(1) from customer_group_relation cgr")
                .join("inner join customer c on cgr.customer_id=c.id").alwaysTrue()
                .where("cgr.group_id=%d", groupId).alwaysTrue()
                .where("c.org_id = %d", orgId).alwaysTrue()
                .where("c.is_delete=0").alwaysTrue()
                .where("c.department_id in (%s)", departmentIdsJoinString).alwaysTrue()
                .where("c.mobile is not null and c.mobile != ''").ifTrue(hasMobile)
                .where("c.email is not null and c.email != ''").ifTrue(hasEmail)
                .where("c.thirdparty_customer_id is not null").ifTrue(hasWechat);
        return (int) nativeSqlHelper.count(sqlBuilder.buildSelectSql());
    }


    private int countNoDepartmentAndGroups(Long orgId, boolean hasMobile, boolean hasWechat, boolean hasEmail) {
        String indexHint = getIndexHint(hasMobile, hasWechat, hasEmail, null);
        SqlBuilder sqlBuilder = SqlBuilder.select("select %s count(c.id) from customer c", indexHint)
                .join("left join customer_group_relation cgr on c.id=cgr.customer_id").alwaysTrue()
                .where("c.org_id = %d", orgId).alwaysTrue()
                .where("c.is_delete = 0").alwaysTrue()
                .where("c.department_id = 0").alwaysTrue()
                .where("cgr.id is null").alwaysTrue()
                .where("c.mobile is not null and c.mobile != ''").ifTrue(hasMobile)
                .where("c.email is not null and c.email != ''").ifTrue(hasEmail)
                .where("c.thirdparty_customer_id is not null").ifTrue(hasWechat);
        String sql = sqlBuilder.buildSelectSql();
        log.info("countNoDepartmentAndGroups：{}", sql);
        return (int) nativeSqlHelper.count(sqlBuilder.buildSelectSql());
    }

    private String joinListLong(List<Long> listLong1, List<Long> listLong2) {
        Set<Long> list = new HashSet<>();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(listLong1)) {
            list.addAll(listLong1);
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(listLong2)) {
            list.addAll(listLong2);
        }
        return list.stream().filter(Objects::nonNull).map(Objects::toString).collect(Collectors.joining(","));
    }

    public int countSelectCustomer(Long orgId, Long userId, CustomerSelectedDto dto) {
        int selectType = dto.getSelectType();
        if (selectType == 0) {
            return Optional.ofNullable(dto.getSelectCustomerIds()).map(List::size).orElse(0);
        } else if (selectType == 1) {
            SqlBuilder sqlBuilder = combineSearchSqlBuilder(orgId, userId, dto.getSelectConditions());
            String selectSql = sqlBuilder.buildCountSql();
            return (int) nativeSqlHelper.count(selectSql);
        } else if (selectType == 2) {
            List<Customer> uploadCustomers = parseUploadSendCustomer(dto);
            return CollectionUtils.isNotEmpty(uploadCustomers) ? uploadCustomers.size() : 0;
        }
        return 0;
    }

    public void consumerSelectCustomerIds(Long orgId, Long userId, CustomerSelectedDto dto, Consumer<List<Long>> consumerIds) {
        consumerSelectCustomerIds(orgId, userId, dto, consumerIds, null);
    }

    public void consumerSelectCustomerIds(Long orgId, Long userId, CustomerSelectedDto dto, Consumer<List<Long>> consumerIds, Consumer<Customer> consumerUploadCustomer) {
        int selectType = dto.getSelectType();
        if (selectType == 0) {
            if (CollectionUtils.isNotEmpty(dto.getSelectCustomerIds())) {
                // 处理选择的客户id
                List<Long> ids = repository.findAllById(dto.getSelectCustomerIds()).stream().map(Customer::getId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(ids)) {
                    consumerIds.accept(ids);
                }
            }
        } else if (selectType == 1) {
            // 处理搜索条件
            Set<Long> existsIds = new HashSet<>();
            SqlBuilder sqlBuilder = combineSearchSqlBuilder(orgId, userId, dto.getSelectConditions());
            foreachPage(
                    page -> {
                        sqlBuilder.limit(page, 100);
                        return nativeSqlHelper.queryList(sqlBuilder, Function.identity(), (ignore, customerIds) -> customerIds);
                    },
                    ids -> {
                        Set<Long> addIds = new HashSet<>();
                        ids.forEach(cid -> {
                            if (existsIds.add(cid)) {
                                addIds.add(cid);
                            }
                        });
                        if (!addIds.isEmpty()) {
                            consumerIds.accept(ids);
                        }
                    });
        } else if (selectType == 2 && consumerUploadCustomer != null) {
            List<Customer> uploadCustomers = parseUploadSendCustomer(dto);
            if (CollectionUtils.isNotEmpty(uploadCustomers)) {
                uploadCustomers.forEach(consumerUploadCustomer);
            }
        }
    }

    private List<Customer> parseUploadSendCustomer(CustomerSelectedDto dto) {
        List<Customer> uploadCustomers = new ArrayList<>();
        parseUploadUrlSendCustomer(dto.getUploadUrl(), null, uploadCustomers::add);
        return uploadCustomers;
    }

    /**
     * @param uploadUrl 文件地址
     * @param headers   key label
     */
    public void parseUploadUrlSendCustomer(String uploadUrl, LinkedHashMap<String, String> headers, Consumer<Customer> customerConsumer) {
        try (InputStream is = new ByteArrayInputStream(fileStorageService.download(uploadUrl).bytes())) {
            EasyExcel.read(is, new AnalysisEventListener<Map<Integer, String>>() {

                private final Map<Integer, String> parsedHeadMap = new HashMap<>();

                @Override
                public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {
                    parsedHeadMap.putAll(headMap);
                    if (headers != null) {
                        for (int i = 0; i < headMap.size(); i++) {
                            switch (i) {
                                case 0:
                                    headers.put("username", headMap.getOrDefault(i, "姓名"));
                                    break;
                                case 1:
                                    headers.put("externalUserId", headMap.getOrDefault(i, "客户编号"));
                                    break;
                                case 2:
                                    headers.put("mobile", headMap.getOrDefault(i, "手机号"));
                                    break;
                                case 3:
                                    headers.put("email", headMap.getOrDefault(i, "邮箱"));
                                    break;
                                case 4:
                                    headers.put("appId", headMap.getOrDefault(i, "appId"));
                                    break;
                                case 5:
                                    headers.put("openId", headMap.getOrDefault(i, "openId"));
                                    break;
                                default:
                                    Optional.ofNullable(headMap.get(i)).ifPresent(j -> headers.put(j, j));
                                    break;
                            }
                        }
                    }
                }

                /**
                 * 0 username
                 * 1 externalUserId
                 * 2 mobile
                 * 3 email
                 * 4 appId
                 * 5 openId
                 * ... custom params
                 */
                @Override
                public void invoke(Map<Integer, String> data, AnalysisContext context) {
                    Customer customer = new Customer();
                    for (int i = 0; i < data.size(); i++) {
                        switch (i) {
                            case 0:
                                customer.setUsername(data.get(0));
                                break;
                            case 1:
                                customer.setExternalUserId(data.get(1));
                                break;
                            case 2:
                                customer.setMobile(RegHelper.isMobile(data.get(2)) ? data.get(2) : null);
                                break;
                            case 3:
                                customer.setEmail(RegHelper.isEmail(data.get(3)) ? data.get(3) : null);
                                break;
                            case 4:
                                customer.getWechatParams().setAppId(data.get(4));
                                break;
                            case 5:
                                customer.getWechatParams().setOpenId(data.get(5));
                                break;
                            default:
                                String head = parsedHeadMap.get(i);
                                if (head != null) {
                                    customer.getUrlCustomParams().put(head, data.get(i));
                                }
                                break;
                        }
                    }
                    if (StringUtils.isEmpty(customer.getMobile())
                            && StringUtils.isEmpty(customer.getEmail())
                            && (customer.getWechatParams() != null
                            && (
                            StringUtils.isEmpty(customer.getWechatParams().getAppId())
                                    && StringUtils.isEmpty(customer.getWechatParams().getOpenId())))
                    ) {
                        return;
                    }
                    customerConsumer.accept(customer);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                }
            }).sheet().doRead();
        } catch (Throwable e) {
            log.error("parse upload customer error, url: {}", uploadUrl, e);
        }
    }

    @Transactional
    public void uploadSendCustomerFile(long maxMb, MultipartFile file, LinkedHashMap<String, String> headers, Consumer<String> consumerUrl, Consumer<Customer> customerConsumer) {
        if (file.getSize() > (maxMb * 1024 * 1024)) {
            throw new BadRequestException(String.format("文件必须少于%sM", maxMb));
        }
        FileInfo fileInfo = fileStorageService.of(file)
                .setObjectId("0")
                .upload();
        consumerUrl.accept(fileInfo.getUrl());
        parseUploadUrlSendCustomer(fileInfo.getUrl(), headers, customerConsumer);
    }

    private void foreachPage(Function<Integer, List<Long>> getPageList, Consumer<List<Long>> consumerPageList) {
        boolean hasNext = true;
        int page = 1;
        while (hasNext) {
            List<Long> ids = getPageList.apply(page);
            if (CollectionUtils.isNotEmpty(ids)) {
                consumerPageList.accept(ids);
            } else {
                hasNext = false;
            }
            page++;
        }
    }

    public boolean updateCustomerGroups(CustomerUpdateGroupDto dto) {
        long orgId = TenantContext.requireCurrentTenant();
        long userId = TenantContext.requireCurrentUserId();
        Set<Long> allIds = new HashSet<>();
        consumerSelectCustomerIds(orgId, userId, dto, allIds::addAll);
        operateByIds(allIds, batchIds -> {
            customerGroupService.addGroupCustomers(dto.getTargetGroupIds(), batchIds);
        });
        return true;
    }

    public boolean batchUpdateDepartment(CustomerUpdateDepartmentDto dto) {
        long orgId = TenantContext.requireCurrentTenant();
        long userId = TenantContext.requireCurrentUserId();
        Department department = departmentService.get(dto.getTargetDepartmentId());
        if (department == null) {
            throw new BadRequestException("层级已被删除");
        }
        departmentService.checkIsCurrentOrg(department);
        Long departmentId = department.getId();
        Set<Long> allIds = new HashSet<>();
        consumerSelectCustomerIds(orgId, userId, dto, allIds::addAll);
        operateByIds(allIds, batchIds -> {
            self.batchUpdate(List.of("department_id=?"), List.of(departmentId.toString()), batchIds);
        });
        return true;
    }

    public boolean batchUpdateBelongToUsers(CustomerUpdateBelongToUsersDto dto) {
        long orgId = TenantContext.requireCurrentTenant();
        long userId = TenantContext.requireCurrentUserId();
        List<Long> userIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dto.getTargetUserIds())) {
            List<SimpleUser> users = userService.getSimpleByIds(new HashSet<>(dto.getTargetUserIds()));
            if (CollectionUtils.isNotEmpty(users)) {
                users.forEach(i -> {
                    userIds.add(i.getId());
                });
            }
        }
        String userIdsString = userIds.stream().map(Objects::toString).collect(Collectors.joining(","));
        Set<Long> allIds = new HashSet<>();
        consumerSelectCustomerIds(orgId, userId, dto, allIds::addAll);
        operateByIds(allIds, batchIds -> {
            self.batchUpdate(List.of("belong_to_uids=?"), List.of(userIdsString), batchIds);
        });
        return true;
    }

    @Transactional
    public void batchUpdate(List<String> columns, List<String> values, Set<Long> ids) {
        String updateColumns = String.join(",", columns);
        String idsString = ids.stream().map(Objects::toString).collect(Collectors.joining(", "));
        String sql = String.format("update customer set %s where id in (%s)", updateColumns, idsString);
        Query query = entityManager.createNativeQuery(sql);
        for (int i = 0; i < values.size(); i++) {
            query.setParameter(i + 1, values.get(i));
        }
        query.executeUpdate();
    }

    private void operateByIds(Set<Long> allIds, Consumer<Set<Long>> applyIds) {
        if (!allIds.isEmpty()) {
            if (allIds.size() <= 500) {
                applyIds.accept(allIds);
            } else {
                Set<Long> batchIds = new HashSet<>();
                allIds.forEach(id -> {
                    batchIds.add(id);
                    if (batchIds.size() == 500) {
                        applyIds.accept(batchIds);
                        batchIds.clear();
                    }
                });
                if (!batchIds.isEmpty()) {
                    applyIds.accept(batchIds);
                }
            }
        }
    }


    @Transactional
    public boolean deleteCustomers(CustomerSelectedDto dto) {
        long orgId = TenantContext.requireCurrentTenant();
        long userId = TenantContext.requireCurrentUserId();
        Set<Long> allIds = new HashSet<>();
        consumerSelectCustomerIds(orgId, userId, dto, allIds::addAll);
        operateByIds(allIds, batchIds -> {
            repository.deleteByIdIn(batchIds);
        });
        return true;
    }

    public Customer findCustomer(Long orgId, Long customerId, String externalUserId, String appId, String openId) {
        return findCustomer(orgId, customerId, externalUserId, appId, openId, null);
    }

    /**
     * 优先级
     * customerId
     * externalUserId
     * appId，openId
     * mobile
     */
    public Customer findCustomer(Long orgId, Long customerId, String externalUserId, String appId, String openId, String mobile) {
        Customer customer = null;
        if (customerId != null && customerId > 0) {
            customer = getCustomerById(orgId, customerId);
        }
        if (customer == null && StringUtils.isNotEmpty(externalUserId)) {
            customer = getCustomerByExternalUserId(orgId, externalUserId);
        }
        if (customer == null && StringUtils.isNotEmpty(openId)) {
            WechatOpenConfig wechatOpenConfig = getWechatOpenConfig(appId);
            if (wechatOpenConfig != null) {
                customer = customerMessageService.getWechatCustomer(orgId, wechatOpenConfig.getConfigId(), openId);
            }
        }
        if (customer == null && StringUtils.isNotEmpty(mobile)) {
            customer = getCustomerByMobile(orgId, mobile);
        }
        return customer;
    }


    public WechatOpenConfig getWechatOpenConfig(String appId) {
        List<WechatOpenConfig> wechatOpenConfigs = authWechatOpenService.getListConfig("cem");
        if (CollectionUtils.isEmpty(wechatOpenConfigs)) {
            return null;
        }
        if (StringUtils.isEmpty(appId)) {
            return wechatOpenConfigs.get(0);
        } else {
            return wechatOpenConfigs.stream().filter(i -> appId.equals(i.getAppId())).findFirst().orElse(null);
        }
    }

    public WechatOpenConfig getWechatOpenConfig(Long configId) {
        return authWechatOpenService.getConfig(configId);
    }

    /**
     * 只有配置一个公众号时，才返回
     */
    public WechatOpenConfig getWechatOpenSingleConfig() {
        List<WechatOpenConfig> wechatOpenConfigs = authWechatOpenService.getListConfig("cem");
        if (CollectionUtils.isEmpty(wechatOpenConfigs) || wechatOpenConfigs.size() > 1) {
            return null;
        }
        return wechatOpenConfigs.get(0);
    }

    public boolean batchUpdateInfo(CustomerUpdateBatchDto dto) {
        long orgId = TenantContext.requireCurrentTenant();
        long userId = TenantContext.requireCurrentUserId();
        if (dto.getTargetValues() == null || dto.getTargetValues().isEmpty()) {
            return false;
        }

        UserConfigBuilderDto userConfigBuilderDto = userConfigService.getConfigBuilder(UserConfigType.customerQuery);
        List<UserConfigCustomerQueryBuilderDto> fields = userConfigBuilderDto.getCustomerQuery();
        if (fields == null) {
            return false;
        }
        Map<String, UserConfigCustomerQueryBuilderDto> batchUpdateFields = fields.stream()
                .filter(UserConfigCustomerQueryBuilderDto::isEnableBatchUpdate)
                .collect(Collectors.toMap(UserConfigCustomerQueryBuilderDto::getPropertyName, j -> j));
        if (batchUpdateFields.isEmpty()) {
            return false;
        }
        Map<String, String> columns = new HashMap<>();
        Map<String, String> extend = new HashMap<>();
        List<Long> targetGroupIds = new ArrayList<>();
        dto.getTargetValues().forEach((k, v) -> {
            if (v == null) {
                return;
            }
            UserConfigCustomerQueryBuilderDto config = batchUpdateFields.get(k);
            if (config == null) {
                throw new BadRequestException("客户信息 " + k + " 不支持批量修改");
            }
            if (checkBatchUpdateGroupParams(k, v, targetGroupIds)) {
                return;
            }
            // 值转换
            String value;
            // string date datetime long arrayString
            if (config.getPropertyType().equals("arrayString")) {
                value = JsonHelper.toJson(v);
            } else {
                value = v.toString();
            }
            if ("customer".equals(config.getPropertySource())) { // customer stat group extend
                columns.put(config.getPropertyColumn(), value);
            } else {
                extend.put(config.getPropertyName(), value);
            }
        });

        List<String> values = new ArrayList<>();
        List<String> updateSql = new ArrayList<>();
        if (!columns.isEmpty()) {
            columns.forEach((k, v) -> {
                updateSql.add(k + "=?");
                values.add(v);
            });
        }
        if (!extend.isEmpty()) {
            List<String> updateExtends = new ArrayList<>();
            extend.forEach((k, v) -> {
                String s = k;
                if (NumberUtils.isDigits(k)) {
                    s = "\"" + s + "\"";
                }
                updateExtends.add("'$." + s + "', ?");
                values.add(v);
            });
            String updateExtendsSql = "extend_fields=json_set(extend_fields," + String.join(", ", updateExtends) + ")";
            updateSql.add(updateExtendsSql);
        }
        if (values.isEmpty() && targetGroupIds.isEmpty()) {
            return false;
        }

        Set<Long> allIds = new HashSet<>();
        consumerSelectCustomerIds(orgId, userId, dto, allIds::addAll);
        operateByIds(allIds, batchIds -> {
            if (!values.isEmpty()) {
                self.batchUpdate(updateSql, values, batchIds);
            }
            if (!targetGroupIds.isEmpty()) {
                customerGroupService.addGroupCustomers(targetGroupIds, batchIds);
            }
        });
        return true;
    }

    private boolean checkBatchUpdateGroupParams(String k, Object v, List<Long> targetGroupIds) {
        if ("groupId".equals(k)) {
            if (v instanceof List) {
                List<?> groupIds = (List<?>) v;
                if (!groupIds.isEmpty()) {
                    groupIds.forEach(i -> {
                        if (i instanceof Long) {
                            targetGroupIds.add((Long) i);
                        }
                    });
                }
            } else if (v instanceof String) {
                String groupIds = (String) v;
                for (String s : groupIds.split(",")) {
                    if (NumberUtils.isDigits(s)) {
                        targetGroupIds.add(Long.parseLong(s));
                    }
                }
            }
            return true;
        }
        return false;
    }

}
