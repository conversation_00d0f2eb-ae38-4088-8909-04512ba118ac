package cn.hanyi.ctm.service;

import cn.hanyi.ctm.constant.InteractionCollectorType;
import cn.hanyi.ctm.constant.RecordType;
import cn.hanyi.ctm.constant.SendStatus;
import cn.hanyi.ctm.constant.customer.CustomerBatchOperationType;
import cn.hanyi.ctm.constant.survey.SurveyChannelType;
import cn.hanyi.ctm.dto.AppMassPushRequestDto;
import cn.hanyi.ctm.dto.PushResponseDto;
import cn.hanyi.ctm.dto.customer.BatchJourneyTriggerRequestDto;
import cn.hanyi.ctm.dto.customer.CustomerBatchRequestDto;
import cn.hanyi.ctm.dto.journey.InteractionResponseDto;
import cn.hanyi.ctm.dto.journey.PushMomentDto;
import cn.hanyi.ctm.entity.*;
import cn.hanyi.ctm.repository.*;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.befun.auth.entity.User;
import org.befun.auth.repository.DepartmentRepository;
import org.befun.auth.service.DepartmentService;
import org.befun.auth.service.UserService;
import org.befun.core.constant.EntityEventType;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.utils.DateHelper;
import org.befun.core.utils.EntityUtility;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@Service
@Slf4j
public class CustomerRecordService {

    @Autowired
    PushService pushService;
    @Autowired
    CustomerRepository customerRepository;

    @Autowired
    CustomerAnswersRepository customerAnswersRepository;

    @Autowired
    UserService userService;

    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private DepartmentRepository departmentRepository;

    @Autowired
    CustomerJourneyRecordsRepository customerJourneyRecordsRepository;

    @Autowired
    CustomerHistoryRecordsRepository customerHistoryRecordsRepository;

    @Autowired
    CustomerBatchOperationRepository customerBatchOperationRepository;

    @Value("${feige.template.customer:}")
    private String smsTemplateName = "";

    @Value("${befun.extension.upload.customer-return-size:50}")
    private Integer customerReturnSize;

    @Value("${befun.extension.upload.customer-file-max-size:5}")
    private Integer customerFileMaxSize;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private CustomerService customerService;

    /**
     * 事件响应(添加历史记录、触发体验互动)
     */
//    TODO : update change event repoistory save dosent work
//    @EventListener(classes = {
//            CustomerChangeEvent.class,
//            CustomerAnswersChangeEvent.class,
//            CustomerJourneyRecordChangeEvent.class
//    })
//    @Async
//    public void handleChangeEvent(BaseEntityChangeEvent changeEvent) {
//        if (changeEvent instanceof CustomerChangeEvent
//                || (changeEvent instanceof CustomerAnswersChangeEvent && changeEvent.getEventType() == EntityEventType.UPDATE)) {
//
//            addHistoryRecord(changeEvent.getEntity(), changeEvent.getEventType(), changeEvent.getChanges());
//
//        } else if (changeEvent instanceof CustomerJourneyRecordChangeEvent
//                && changeEvent.getEventType() == EntityEventType.CREATE) {
//            updateCustomerLatestJourneyRecord((CustomerJourneyRecord) changeEvent.getEntity());
//            CustomerJourneyRecord customerJourneyRecord = (CustomerJourneyRecord) changeEvent.getEntity();
//            addHistoryRecord(changeEvent.getEntity(), changeEvent.getEventType(), null);
//            if (customerJourneyRecord.getDisturbStatus() == DisturbStatus.INIT_OPEN) {
//                // 接口触发不需要使用task触发互动
//                return;
//            }
//            JourneyTriggerTaskDetailDto journeyTriggerTaskDetailDto = JourneyTriggerTaskDetailDto.builder()
//                    .customerId(customerJourneyRecord.getCustomerId())
//                    .journeyId(customerJourneyRecord.getJourneyId())
//                    .departmentId(customerJourneyRecord.getDepartmentId())
//                    .createdByUid(customerJourneyRecord.getCreatedByUid())
//                    .journeyRecordId(customerJourneyRecord.getId())
//                    .build();
//            journeyTriggerExecutor.performAsync(journeyTriggerTaskDetailDto);
//
//        }
//    }
    private void updateCustomerLatestJourneyRecord(CustomerJourneyRecord customerJourneyRecord) {
        Customer customer = customerService.require(customerJourneyRecord.getCustomerId());
        String course = String.format("%s@%s", customerJourneyRecord.getJourneyTitle(), customerJourneyRecord.getDepartmentTitle());
        customer.setLatestJourneyRecord(course);
        customerRepository.save(customer);
    }

    /**
     * 响应事件添加日志记录
     *
     * @param entity
     * @param eventType
     * @param changes
     */
    public void addHistoryRecord(
            BaseEntity entity, EntityEventType eventType, Map changes) {
        CustomerHistoryRecord customerHistoryRecord = new CustomerHistoryRecord();
        RecordType recordType;
        Long createByUid;
        Customer customer;
        String customerName;
        String userName = "";
        String course = "";

        if (entity instanceof Customer) {
            customer = (Customer) entity;
            createByUid = ((Customer) entity).getCreatedByUid();
            customerName = ((Customer) entity).getUsername();
            if (createByUid == null) {
                log.warn("add history record {} createdByUid is null", entity);
                return;
            }
            User user = userService.get(((Customer) entity).getCreatedByUid());
            if (user != null) {
                userName = user.getTruename();
            }
            if (eventType == EntityEventType.CREATE) {
                recordType = RecordType.ADDCUSTOMER;
            } else if (eventType == EntityEventType.UPDATE) {
                recordType = RecordType.UPDATECUSTOMER;
            } else return;

        } else if (entity instanceof CustomerJourneyRecord && eventType == EntityEventType.CREATE) {
            recordType = RecordType.ADDJOURNEY;
            customer = customerService.require(((CustomerJourneyRecord) entity).getCustomerId());
            customerName = customer.getUsername();
            userName = ((CustomerJourneyRecord) entity).getCreatedByName();
            course = String.format("%s@%s", ((CustomerJourneyRecord) entity).getJourneyTitle(), ((CustomerJourneyRecord) entity).getDepartmentTitle());
            createByUid = ((CustomerJourneyRecord) entity).getCreatedByUid();
            customerHistoryRecord.setJourneyRecordId(entity.getId());
        } else return;

        String recordContent = buildRecordContent(recordType, userName, customerName, course, "", changes, entity);

        if (!recordContent.isEmpty()) {
            customerHistoryRecord.setRecordType(recordType);
            customerHistoryRecord.setCustomerId(customer.getId());
            customerHistoryRecord.setCreateByUid(createByUid);
            customerHistoryRecord.setRecordContent(recordContent);
            customerHistoryRecordsRepository.save(customerHistoryRecord);
        }
    }

    public String buildRecordContent(RecordType recordType, String userName, String customerName, String course, String surveyName, Map<String, Object[]> changes, BaseEntity entity) {
        String content = "";
        switch (recordType) {
            case ADDCUSTOMER:
                content = String.format("%s 添加了客户 %s", userName, customerName);
                break;
            case UPDATECUSTOMER:
                List<String> contentList = new ArrayList<>();
                changes.forEach((key, value) -> {
                    if (EntityUtility.getFieldsByClass(entity.getClass()).get(key).getAnnotation(
                            JsonPropertyDescription.class) != null) {
                        String fieldName = EntityUtility.getFieldsByClass(entity.getClass()).get(key).getAnnotation(
                                JsonPropertyDescription.class).value();
                        String oldValue = value[0].toString().isEmpty() ? "--/--" : value[0].toString();
                        String newValue = value[1].toString().isEmpty() ? "--/--" : value[1].toString();
                        contentList.add(String.format("%s：由 %s 修改为 %s", fieldName, oldValue, newValue));
                    }
                });
                content = String.join("; ", contentList);
                break;
            case ADDJOURNEY:
                content = String.format("%s 给 %s 记录历程 %s", userName, customerName, course);
                break;
            case SENDSURVEY:
                content = String.format("发送问卷 %s ", surveyName);
                break;
            case FILLEDSURVEY:
                content = String.format("%s 填答问卷 %s", customerName, surveyName);
                break;
        }
        return content;
    }

    public String buildUrl(String rowUrl, Long departmentId, Map<String, Object> params) {
        return buildUrl(rowUrl, departmentId, null, params);
    }

    public String buildUrl(String rowUrl, Long departmentId, LocalDateTime expireTime, Map<String, Object> params) {
        rowUrl = String.format("%s?customerId=${customer.id}&departmentId=%d", rowUrl, departmentId);
        for (String key : params.keySet()) {
            if ("journey_record_id".equals(key) && params.get("journey_record_id") != null) {
                rowUrl =
                        rowUrl.concat(
                                String.format("&trackId=record::%s", params.get("journey_record_id").toString()));

            } else if ("utm_campaign".equals(key) && params.get("utm_campaign") != null) {
                rowUrl =
                        rowUrl.concat(
                                String.format("&trackId=batch::%s", params.get("utm_campaign").toString()));
            } else {
                rowUrl = rowUrl.concat(String.format("&%s=%s", key, params.get(key)));
            }
        }
        rowUrl = rowUrl.concat("&collectorMethod=SCENE_INTERACTION&clientId=");

        if (expireTime != null) {
            rowUrl = rowUrl.concat("&expireTime=" + DateHelper.format(expireTime, DateHelper.DATE_TIME_FORMATTER2));
        }
        return rowUrl;
    }

    /**
     * 发送问卷后添加答卷信息和历史记录
     */
    public void afterSendSurveytoCustomers(List<Customer> customers,
                                           List<InteractionResponseDto> dto, String sid, String surveyName, Long createdUid,
                                           Long departmentId, Long journeyRecordId, Long batchOperationId) {
        List<CustomerHistoryRecord> customerHistoryRecordList = new ArrayList<>();
        List<CustomerAnswers> customerAnswersList = new ArrayList<>();

        customers.forEach(c -> dto.forEach(res -> {
            if (c != null) {
                String content = buildRecordContent(RecordType.SENDSURVEY,
                        "", "", "", surveyName, null, null);
                if (res.getSendStatus() == SendStatus.SUCCESS) {
                    CustomerHistoryRecord customerHistoryRecord =
                            new CustomerHistoryRecord();
                    customerHistoryRecord.setCustomerId(c.getId());
                    customerHistoryRecord.setRecordContent(content);
                    customerHistoryRecord.setRecordType(RecordType.SENDSURVEY);
                    customerHistoryRecord.setCreateByUid(createdUid);
                    customerHistoryRecordList.add(customerHistoryRecord);
                }
                if (res.getSendStatus() != SendStatus.NOTDISTURB) {
                    // 勿扰模式不用写入回答的信息表
                    CustomerAnswers customerAnswers =
                            new CustomerAnswers(c, sid, surveyName, String.valueOf(departmentId),
                                    res.getCollector(), res.getSendStatus());
                    if (journeyRecordId != null) {
                        customerAnswers.setJourneyRecordId(journeyRecordId);
                    }
                    if (batchOperationId != null) {
                        customerAnswers.setUtmCampaign(batchOperationId + "");
                    }
                    customerAnswersList.add(customerAnswers);
                }
            }
        }));
        if (!customerAnswersList.isEmpty()) {
            customerAnswersRepository.saveAll(customerAnswersList);
        }
        if (!customerHistoryRecordList.isEmpty()) {
            customerHistoryRecordsRepository.saveAll(customerHistoryRecordList);
        }
    }

    public InteractionCollectorType getInteractionCollectorType(Long channelId) {
        if (channelId != null && channelId > 0) {
            List<Integer> channelType = jdbcTemplate.queryForList("select type from survey_channel where id = " + channelId, Integer.class);
            Integer type;
            if (CollectionUtils.isNotEmpty(channelType) && (type = channelType.get(0)) != null) {
                return SurveyChannelType.mapToInteractionType(type);
            }
        }
        return InteractionCollectorType.COMMON;
    }

    private Long buildBatchOperation(CustomerBatchOperationType type) {
        CustomerBatchOperation customerBatchOperation =
                new CustomerBatchOperation();
        customerBatchOperation.setType(type);
        customerBatchOperationRepository.save(customerBatchOperation);
        return customerBatchOperation.getId();
    }


    /**
     * 批量添加客户历程
     *
     * @param dto
     * @return
     */
    public List<CustomerJourneyRecord> batchAddJourneyRecord(List<Customer> customers, BatchJourneyTriggerRequestDto dto) {
        List<CustomerJourneyRecord> customerJourneyRecordList = new ArrayList<>();
        customers.forEach(
                c -> {
                    CustomerJourneyRecord customerJourneyRecord = new CustomerJourneyRecord();
                    customerJourneyRecord.setCustomerId(c.getId());
                    customerJourneyRecord.setJourneyId(dto.getJourneyId());
                    customerJourneyRecord.setJourneyTitle(dto.getJourneyTitle());
                    customerJourneyRecord.setDepartmentId(dto.getDepartmentId());
                    customerJourneyRecord.setDepartmentTitle(dto.getDepartmentTitle());
                    customerJourneyRecord.setDetails(dto.getDetails());
                    customerJourneyRecord.setCreatedByUid(dto.getCreatedByUid());
                    customerJourneyRecord.setCreatedByName(dto.getCreatedByName());
                    customerJourneyRecordList.add(customerJourneyRecord);
                });
        customerJourneyRecordsRepository.saveAll(customerJourneyRecordList);

        return customerJourneyRecordList;
    }

    /**
     * 批量发送消息
     */
    public PushResponseDto batchSendMessage(List<Customer> customers,
                                            CustomerBatchRequestDto requestDto) {
        Long orgId = TenantContext.getCurrentTenant();
        Long userId = TenantContext.getCurrentUserId();
        List<InteractionResponseDto> result = new ArrayList<>();

        Long batchOperationId = buildBatchOperation(CustomerBatchOperationType.SENDSMS);
        Map<String, Object> urlParams = new HashMap<>() {{
            put("utm_campaign", batchOperationId);
        }};
        String url = buildUrl(requestDto.getSurveyUrl(), null, urlParams);
        List<Long> customerIds = new ArrayList<>();
        customers.forEach(customer -> customerIds.add(customer.getId()));

        Map<String, Object> parameters = new HashMap<>() {{
            put("survey", new HashMap<>() {{
                put("title", requestDto.getSurveyName());
            }});
        }};
        AppMassPushRequestDto appMassPushRequestDto =
                new AppMassPushRequestDto(orgId, url, false, customerIds, null, parameters,
                        new PushMomentDto(), smsTemplateName);
        appMassPushRequestDto.setTemplateId(requestDto.getTemplateId());

        PushResponseDto res = new PushResponseDto();
        InteractionResponseDto responseDto = new InteractionResponseDto();
        responseDto.setCollector(requestDto.getChannel());
        result.add(responseDto);
        try {
            res = pushService.sendMessageByApp(appMassPushRequestDto);
            responseDto.setInteractionResponse(JsonHelper.toMap(res));
            responseDto.setSendStatus(SendStatus.SUCCESS);
            afterSendSurveytoCustomers(customers, result, requestDto.getSid(), requestDto.getSurveyName(),
                    userId, null, null, batchOperationId);
            return res;
        } catch (Exception ex) {
            log.warn("push message error, {}, {}", appMassPushRequestDto, ex.getStackTrace());
            responseDto.setSendStatus(SendStatus.FAILED);
            afterSendSurveytoCustomers(customers, result, requestDto.getSid(), requestDto.getSurveyName(),
                    userId, null, null, batchOperationId);
            throw ex;
        }
    }

    /**
     * 批量评估发送消息
     */
    public PushResponseDto batchEvaluateSendMessage(List<Customer> customers,
                                                    CustomerBatchRequestDto requestDto) {
        Long orgId = TenantContext.getCurrentTenant();

        Long batchOperationId = buildBatchOperation(CustomerBatchOperationType.SENDSMS);
        Map<String, Object> urlParams = new HashMap<>() {{
            put("utm_campaign", batchOperationId);
        }};
        String url = buildUrl(requestDto.getSurveyUrl(), null, urlParams);
        List<Long> customerIds = new ArrayList<>();
        customers.forEach(customer -> customerIds.add(customer.getId()));
        AppMassPushRequestDto appMassPushRequestDto =
                new AppMassPushRequestDto(orgId, url, false, customerIds, null, new HashMap<>(),
                        new PushMomentDto(), smsTemplateName);
        appMassPushRequestDto.setTemplateId(requestDto.getTemplateId());

        return pushService.evaluateByApp(appMassPushRequestDto);
    }

    /**
     * 获取客户旅程历史推送
     *
     * @return
     */
    public List<CustomerJourneyRecord> getCustomerJourneyRecords(Long customerId, Long journeyId) {
        return customerJourneyRecordsRepository.findByCustomerIdAndJourneyId(customerId, journeyId);
    }

    /**
     * 保存免打扰状态
     */
    public CustomerJourneyRecord saveCustomerJourneyRecord(CustomerJourneyRecord customerJourneyRecord) {
        return customerJourneyRecordsRepository.save(customerJourneyRecord);
    }

}
