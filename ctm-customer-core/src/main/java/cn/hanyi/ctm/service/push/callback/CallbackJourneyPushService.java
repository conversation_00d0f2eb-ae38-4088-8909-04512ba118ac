//package cn.hanyi.ctm.service.push.callback;
//
//import cn.hanyi.ctm.constant.customer.CustomerPushRelationType;
//import cn.hanyi.ctm.entity.CustomerPushRecord;
//import cn.hanyi.ctm.service.CustomerAnswersService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//@Service
//public class CallbackJourneyPushService implements ICallbackCustomerPushService {
//
//    @Autowired
//    private CustomerAnswersService customerAnswersService;
//
//    @Override
//    public boolean pushCompleted(CustomerPushRecord record) {
//        if (CustomerPushRelationType.JOURNEY.name().equals(record.getRelationType())) {
//            if (record.getSurveyId() != null && record.getSurveyId() > 0 && record.getCustomerId() != null && record.getCustomerId() > 0) {
//                customerAnswersService.addByJourneyPush(record);
//                return true;
//            }
//        }
//        return false;
//    }
//}
