package cn.hanyi.ctm.service;

import cn.hanyi.ctm.constant.AnswerStatus;
import cn.hanyi.ctm.constant.InteractionCollectorType;
import cn.hanyi.ctm.constant.SendStatus;
import cn.hanyi.ctm.constant.UtmMedium;
import cn.hanyi.ctm.entity.CtmSurvey;
import cn.hanyi.ctm.entity.Customer;
import cn.hanyi.ctm.entity.CustomerAnswers;
import cn.hanyi.ctm.entity.CustomerAnswersDto;
import cn.hanyi.ctm.repository.CtmSurveyRepository;
import cn.hanyi.ctm.repository.CustomerAnswersRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.core.service.CustomEmbeddedService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

import static cn.hanyi.ctm.constant.InteractionCollectorType.COMMON;
import static cn.hanyi.ctm.constant.InteractionCollectorType.SCENE_INTERACTION;

@Service
public class CustomerAnswersService extends CustomEmbeddedService<CustomerAnswers, CustomerAnswersDto, CustomerAnswersRepository> {

    @Autowired
    private CustomerService customerService;
    @Autowired
    private CtmSurveyRepository ctmSurveyRepository;

    @Override
    protected Object requireParent(long l) {
        return customerService.require(l);
    }

    /**
     * 给客户通过渠道发送问卷之后，需要写入填答记录
     */
    public void addBySurveyChannel(Customer customer, Long surveyId, String surveyName, InteractionCollectorType channel, String clientId) {
        CustomerAnswers answers = new CustomerAnswers();
        answers.setCustomer(customer);
        answers.setChannel(channel);
        answers.setSurveyName(surveyName);
        answers.setSid(surveyId + "");
        answers.setSendStatus(SendStatus.UNCHECKED);
        answers.setUtmMedium(UtmMedium.SURVEY_CHANNEL.name());
        answers.setUtmCampaign(clientId);
        repository.save(answers);
    }

    /**
     * 给客户通过旅程发送问卷之后，需要写入填答记录
     */
    public void addByJourney(Customer customer, Long surveyId, String surveyName, String clientId) {
        CustomerAnswers answers = new CustomerAnswers();
        answers.setCustomer(customer);
        answers.setChannel(SCENE_INTERACTION);
        answers.setSurveyName(surveyName);
        answers.setSid(surveyId + "");
        answers.setSendStatus(SendStatus.UNCHECKED);
        answers.setUtmMedium(UtmMedium.JOURNEY_INTERACTION.name());
        answers.setUtmCampaign(clientId);
        repository.save(answers);
    }

    /**
     * 通用链接携带客户信息答题时，需要写入填答记录
     */
    public void addByCommon(Customer customer, Long surveyId, String surveyName, String clientId) {
        CustomerAnswers answers = new CustomerAnswers();
        answers.setCustomer(customer);
        answers.setChannel(COMMON);
        answers.setSurveyName(surveyName);
        answers.setSid(surveyId + "");
        answers.setSendStatus(SendStatus.SUCCESS);
        answers.setUtmMedium(UtmMedium.COMMON.name());
        answers.setUtmCampaign(clientId);
        repository.save(answers);
    }

    /**
     * 客户填答问卷之后，更新填答状态
     */
    public void updateBySurveyResponse(Long customerId, Long surveyId, String clientId, Long responseId, Integer durationSeconds) {
        List<CustomerAnswers> list = repository.findAll((root, query, builder) -> builder.and(
                builder.equal(root.get("customer"), customerId),
                builder.equal(root.get("sid"), surveyId + ""),
                builder.equal(root.get("utmCampaign"), clientId)
        ));
        if (CollectionUtils.isNotEmpty(list)) {
            CustomerAnswers answers = list.get(0);
            answers.setAnswerId(responseId);
            answers.setAnswerTime(new Date());
            answers.setDurationSeconds(durationSeconds);
            answers.setSendStatus(SendStatus.SUCCESS);
            answers.setAnswerStatus(AnswerStatus.SUBMITTED);
            repository.save(answers);
        } else {
            Customer customer;
            CtmSurvey survey;
            if ((customer = customerService.get(customerId)) != null
                    && (survey = ctmSurveyRepository.findById(surveyId).orElse(null)) != null) {
                CustomerAnswers answers = new CustomerAnswers();
                answers.setCustomer(customer);
                answers.setChannel(COMMON);
                answers.setSurveyName(survey.getTitle());
                answers.setSid(surveyId + "");
                answers.setSendStatus(SendStatus.SUCCESS);
                answers.setUtmMedium(UtmMedium.COMMON.name());
                answers.setAnswerId(responseId);
                answers.setAnswerTime(new Date());
                answers.setDurationSeconds(durationSeconds);
                answers.setSendStatus(SendStatus.SUCCESS);
                answers.setAnswerStatus(AnswerStatus.SUBMITTED);
                answers.setUtmCampaign(clientId);
                repository.save(answers);
            }
        }
    }
}
