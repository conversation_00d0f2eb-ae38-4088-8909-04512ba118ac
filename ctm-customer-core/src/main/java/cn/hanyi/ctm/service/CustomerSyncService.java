package cn.hanyi.ctm.service;

import cn.hanyi.ctm.constant.connector.ConnectorAuthorizeStatus;
import cn.hanyi.ctm.constant.customer.CustomerConnectionStatus;
import cn.hanyi.ctm.dto.connector.ThirdPartyCustomerDto;
import cn.hanyi.ctm.dto.customer.DeleteCustomerDto;
import cn.hanyi.ctm.dto.customer.SyncCustomerDto;
import cn.hanyi.ctm.entity.Connector;
import cn.hanyi.ctm.entity.Customer;
import cn.hanyi.ctm.entity.ThirdPartyCustomer;
import cn.hanyi.ctm.repository.CustomerRepository;
import cn.hanyi.ctm.repository.ThirdPartyCustomerRepository;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.befun.auth.constant.ExtendCustomerFieldFormat;
import org.befun.auth.constant.ExtendCustomerFieldType;
import org.befun.auth.constant.OrganizationConfigType;
import org.befun.auth.dto.orgconfig.OrgConfigCustomerVisibleDto;
import org.befun.auth.dto.orgconfig.OrgConfigDto;
import org.befun.auth.dto.orgconfig.OrgConfigExtendCustomerFieldDto;
import org.befun.auth.entity.Department;
import org.befun.auth.entity.OrganizationConfig;
import org.befun.auth.repository.OrganizationConfigRepository;
import org.befun.auth.service.DepartmentService;
import org.befun.auth.service.OrganizationConfigService;
import org.befun.auth.service.auth.config.WechatOpenConfig;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.utils.DateHelper;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.awt.*;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.List;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;


@Service
@Slf4j
public class CustomerSyncService {

    @Autowired
    private CustomerService customerService;
    @Autowired
    private CustomerRepository customerRepository;
    @Autowired
    private ThirdPartyCustomerRepository tpCustomerRepository;
    @Autowired
    private CustomerWechatService customerWechatService;
    @Autowired
    private OrganizationConfigRepository organizationConfigRepository;
    @Autowired
    private OrganizationConfigService organizationConfigService;
    @Autowired
    private DepartmentService departmentService;

    @Autowired
    private CustomerGroupService customerGroupService;

    @Value("${ctm.customer.address-file:false}")
    private Boolean addressFile;


    private static List<String> provinceList = new ArrayList<>();
    private static List<String> cityList = new ArrayList<>();
    private static List<String> distinctList = new ArrayList<>();
    private static String addressJson = "";

    @PostConstruct
    public void init() {
        if (addressFile) {
            try {
                addressJson = new String(new ClassPathResource("static/address.json").getInputStream().readAllBytes(), StandardCharsets.UTF_8);
                List<Map> list = JSONArray.parseArray(addressJson, Map.class);
                for (Map<String, Object> provinceMap : list) {
                    provinceList.add(String.valueOf(provinceMap.get("name")));
                    List<Map> cityMapList = (List<Map>) provinceMap.get("list");
                    for (Map cityMap : cityMapList) {
                        cityList.add(String.valueOf(cityMap.get("name")));
                        List<Map> distinctMapList = (List<Map>) cityMap.get("list");
                        for (Map distinctMap : distinctMapList) {
                            distinctList.add(String.valueOf(distinctMap.get("name")));
                        }
                    }
                }
            } catch (IOException e) {
                log.error("static/address.json not exist");
            }
        }
    }


    public Customer deleteCustomer(Long orgId, DeleteCustomerDto data) {
        if (orgId == null || orgId <= 0 || data == null || StringUtils.isEmpty(data.getExternalUserId())) {
            log.error("删除客户信息失败, orgId={}, data={}", orgId, (data == null || StringUtils.isEmpty(data.getExternalUserId())) ? "" : data.getExternalUserId());
            throw new BadRequestException("删除客户信息失败");
        }
        Optional<Customer> deleteCustomer = customerRepository.findFirstByOrgIdAndExternalUserId(orgId, data.getExternalUserId());
        return deleteCustomer.map(i -> {
            customerRepository.delete(i);
            return i;
        }).orElseThrow(() -> new BadRequestException("客户不存在"));
    }

    /**
     * 从外部导入的客户信息
     * 1 通过 externalUserId 查询出客户(X)
     * 2 如果有 openId，则查出微信用户
     * 3 如果客户(X)不存在，如果有微信用户，则查出所有和此微信用户绑定的客户
     * 4 如果这些客户中有 externalUserId 为空的客户(A)，则客户(A)为此次导入的客户(X)
     * 5 如果客户(X)不存在，则新增，否则更新客户(X)
     * 6 如果有 openId，将微信用户和此次导入的客户(X)绑定
     * 7 如果没有 openId，删除客户(X)和微信用户的绑定关系
     */
    public Customer syncCustomer(Long orgId, SyncCustomerDto data) {
        if (orgId == null || orgId <= 0 || data == null || StringUtils.isEmpty(data.getExternalUserId())) {
            log.error("同步客户信息失败, orgId={}, data={}", orgId, data == null ? "" : data.getExternalUserId());
            throw new BadRequestException("同步客户信息失败");
        }
        // 通过 externalUserId 查询出客户(X)
        Customer customer = customerRepository.findFirstByOrgIdAndExternalUserId(orgId, data.getExternalUserId()).orElse(null);
        WechatOpenConfig wechatOpenConfig = customerService.getWechatOpenConfig(data.getWeChatAppId());
        ThirdPartyCustomer thirdPartyCustomer = null;
        if (wechatOpenConfig != null && StringUtils.isNotEmpty(data.getWeChatOpenId())) {
            // 如果有 openId，则查出微信用户
            thirdPartyCustomer = tpCustomerRepository.findFirstByThirdpartyAuthIdAndOpenId(wechatOpenConfig.getConfigId(), data.getWeChatOpenId()).orElse(null);
        }
        if (customer == null && thirdPartyCustomer != null) {
            // 如果客户(X)不存在，如果有微信用户，则查出所有和此微信用户绑定的客户
            List<Customer> customers = customerRepository.findByOrgIdAndThirdPartyCustomerId(orgId, thirdPartyCustomer.getId());
            // 如果这些客户中有 externalUserId 为空的客户(A)，则客户(A)为此次导入的客户(X)
            customer = Optional.ofNullable(customers)
                    .stream()
                    .flatMap(Collection::stream)
                    .filter(c -> StringUtils.isEmpty(c.getExternalUserId()))
                    .findFirst()
                    .orElse(null);
        }
        // 合并数据（如果客户(X)不存在，则新增，否则更新客户(X)）
        customer = mergeImportCustomer(orgId, customer, data);
        if (wechatOpenConfig != null && StringUtils.isNotEmpty(data.getWeChatOpenId())) {
            // 如果有 openId，将微信用户和此次导入的客户(X)绑定
            if (thirdPartyCustomer == null) {
                // 现在还没有这个微信用户，初始化一个 ThirdPartyCustomer
                thirdPartyCustomer = new ThirdPartyCustomer(orgId, wechatOpenConfig.getConfigId(), data.getWeChatOpenId());
                tpCustomerRepository.save(thirdPartyCustomer);
            }
            customer.setThirdPartyCustomerId(thirdPartyCustomer.getId());
        } else {
            // 如果没有 openId，删除客户(X)和微信用户的绑定关系
            customer.setThirdPartyCustomerId(null);
        }
        //customer.setDepartmentId(0L);
        customerRepository.save(customer);
        // 需要同步微信用户信息
        if (wechatOpenConfig != null && StringUtils.isNotEmpty(data.getWeChatOpenId())) {
            customerWechatService.asyncSingleCustomer(
                    orgId,
                    TenantContext.requireCurrentUserId(),
                    wechatOpenConfig.getConfigId(),
                    wechatOpenConfig.getAppId(),
                    data.getWeChatOpenId()
            );
        }
        return customer;
    }

    public Customer mergeImportCustomer(Long orgId, Customer customer, SyncCustomerDto dto) {
        if (customer == null) {
            customer = new Customer();
            customer.setOrgId(orgId);
            customer.setCreatedByUid(0L);
            customer.setModifiedByUid(0L);
        }
        customer.setExternalUserId(dto.getExternalUserId());
        customer.setUsername(dto.getUsername());
        customer.setNickname(dto.getUsername());
        customer.setExtendFields(syncExtendFields(dto.getExtendFields()));
        Supplier<String> getDefaultString = () -> "";
        Supplier<List<String>> getDefaultList = List::of;
        Supplier<Department> root = ()-> departmentService.getRoot(TenantContext.getCurrentTenant());
        updateCustomerProperty(dto::getMobile, customer::getMobile, customer::setMobile, getDefaultString,true);
        updateCustomerProperty(dto::getEmail, customer::getEmail, customer::setEmail, getDefaultString,true);
        updateCustomerProperty(dto::getGender, customer::getGender, customer::setGender, getDefaultString,true);
        updateCustomerProperty(dto::getBirthday, customer::getBirthday, customer::setBirthday, getDefaultString,true);
        boolean existProvince = provinceList.contains(dto.getProvince());
        updateCustomerProperty(dto::getProvince, customer::getProvince, customer::setProvince, getDefaultString,existProvince);
        boolean existCity = cityList.contains(dto.getCity());
        updateCustomerProperty(dto::getCity, customer::getCity, customer::setCity, getDefaultString,existProvince && existCity);
        boolean existDistinct =  distinctList.contains(dto.getDistrict());
        updateCustomerProperty(dto::getDistrict, customer::getDistrict, customer::setDistrict, getDefaultString, existProvince && existCity && existDistinct);
        updateCustomerProperty(dto::getAddress, customer::getAddress, customer::setAddress, getDefaultString,true);
        updateCustomerProperty(dto::getTags, customer::getTags, customer::setTags, getDefaultList,true);
        customerGroupService.addCustomerGroup(TenantContext.getCurrentTenant(),customer.getId(),dto.getGroupCode());
        this.addDepartmentCode(dto.getDepartmentCode(),customer);

        //若参数值未传入或不存在，则departmentCode默认在最高层级；groupCode不做所属分组的添加
        //若参数值传入且存在，则departmentCode按所属层级进行匹配（若客户已有层级则是修改）；groupCode则做所属分组的添加（若已在分组内则不必重复）


        return customer;
    }

    private <T> void updateCustomerProperty(Supplier<T> getNewValue, Supplier<T> getOldValue, Consumer<T> setOldValue, Supplier<T> getDefaultValue,Boolean condition) {
        T newValue = getNewValue.get();
        if (condition && newValue != null) { // 如果有新的值，则替换
            setOldValue.accept(newValue);
        } else if (getOldValue.get() == null) { // 否则如果旧的值也不存在，则设置一个默认值
            setOldValue.accept(getDefaultValue.get());
        }
    }

    private <T> void updateCustomerProperty(Supplier<T> getNewValue, Supplier<T> getOldValue, Consumer<T> setOldValue, Supplier<T> getDefaultValue, Predicate<T> condition) {
        T newValue = getNewValue.get();
        if (condition.test((T) condition) && newValue != null) { // 如果有新的值，则替换
            setOldValue.accept(newValue);
        } else if (getOldValue.get() == null) { // 否则如果旧的值也不存在，则设置一个默认值
            setOldValue.accept(getDefaultValue.get());
        }
    }

    private void addDepartmentCode(String departmentCode,Customer customer) {
        Department department = departmentService.getByCode(TenantContext.getCurrentTenant(), departmentCode);
        if(department == null) {
            Department root = departmentService.getRoot(TenantContext.getCurrentTenant());
            customer.setDepartmentId(0L);
            customer.setDepartmentNames(root.getTitle());
        }else {
            customer.setDepartmentId(department.getId());
            customer.setDepartmentNames(department.getTitle());
        }
    }

    /**
     * 同步extendField
     * @param extendFieldMap
     */
    public Map<String,Object> syncExtendFields(Map<String,Object> extendFieldMap){
        if(Objects.isNull(extendFieldMap)){
            return Map.of();
        }
        OrgConfigDto config = organizationConfigService.getConfig(OrganizationConfigType.extendCustomerField.name());
        if(config == null || config.getExtendCustomerField() == null){
            return Map.of();
        }
        OrgConfigExtendCustomerFieldDto extendCustomerField = config.getExtendCustomerField();
        Map<String, OrgConfigExtendCustomerFieldDto.ExtendField> fieldMap = extendCustomerField.getExtendFields().stream()
                .collect(Collectors.toMap(OrgConfigExtendCustomerFieldDto.ExtendField::getProp, Function.identity()));

        Map<String,Object> parseResultMap = new HashMap<>();
        extendFieldMap.forEach((key,value)->{
            //传入的prop在数据库中存在，更新
            if(fieldMap.containsKey(key)){
                OrgConfigExtendCustomerFieldDto.ExtendField extendField = fieldMap.get(key);
                if(extendField.getType()== ExtendCustomerFieldType.DATE){
                    if(extendField.getFormat() == ExtendCustomerFieldFormat.FORMAT_YEAR && isDate(String.valueOf(value), "yyyy")){
                        parseResultMap.put(key,value);
                    }else if(extendField.getFormat() == ExtendCustomerFieldFormat.FORMAT_YEAR_MONTH && isDate(String.valueOf(value), "yyyy-MM")){
                        parseResultMap.put(key,value);
                    }else if(extendField.getFormat() == ExtendCustomerFieldFormat.FORMAT_DATE && isDate(String.valueOf(value), "yyyy-MM-dd")){
                        parseResultMap.put(key,value);
                    }else if(extendField.getFormat() == ExtendCustomerFieldFormat.FORMAT_DATE_TIME && isDate(String.valueOf(value), "yyyy-MM-dd HH:mm:ss")){
                        parseResultMap.put(key,value);
                    }
                }else if(extendField.getType()== ExtendCustomerFieldType.NUMBER){
                    if(StringUtils.isNumeric(String.valueOf(value))){
                        parseResultMap.put(key,value);
                    }
                }else if(extendField.getType()== ExtendCustomerFieldType.SINGLE_CHOICE) {
                    List<OrgConfigExtendCustomerFieldDto.FieldOption> options = extendField.getOptions();
                    for (OrgConfigExtendCustomerFieldDto.FieldOption option : options) {
                        if (option.getLabel().equals(value)){ //文本内容相同
                            parseResultMap.put(key, option.getValue());
                            continue;
                       }
                    }
                }else if(extendField.getType()== ExtendCustomerFieldType.MULTIPLE_CHOICE){
                    List<OrgConfigExtendCustomerFieldDto.FieldOption> options = extendField.getOptions();
                    StringJoiner joiner = new StringJoiner(",");
                    List<String> labels = Arrays.asList((String.valueOf(value)).split(","));
                    for (OrgConfigExtendCustomerFieldDto.FieldOption option : options) {
                        if (labels.contains(option.getLabel())){ //文本内容相同
                            joiner.add(option.getValue());
                        }
                    }
                    parseResultMap.put(key, joiner.toString());
                }else {
                    parseResultMap.put(key, value);
                }
            }
        });
        return parseResultMap;
    }

    public boolean isDate(String dateStr,String formatPattern){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern(formatPattern);
        try {
            formatter.parse(dateStr);
            return true;
        }catch (Exception e){
            return false;
        }
    }

    /**
     * 从微信同步的客户信息
     */
    @Deprecated
    public Customer syncThirdPartyCustomer(
            Long orgId,
            Long connectorId,
            ThirdPartyCustomerDto tpCustomerDto) {

        Connector connector = new Connector();
        connector.setId(connectorId);
        connector.setOrgId(orgId);

        Optional<ThirdPartyCustomer> tpCustomer = tpCustomerRepository
                .findThirdPartyCustomerByConnectorAndOpenId(
                        connector,
                        tpCustomerDto.getOpenId()
                );

        // 新增或更新 微信用户
        ThirdPartyCustomer thirdPartyCustomer;
        if (tpCustomer.isPresent()) {
            thirdPartyCustomer = tpCustomer.get();
            thirdPartyCustomer = tpCustomerDto.mapToEntity(thirdPartyCustomer);
            thirdPartyCustomer.setSubscribed(true);
            thirdPartyCustomer.setAuthorizeStatus(ConnectorAuthorizeStatus.AUTHORIZED);
        } else {
            thirdPartyCustomer = tpCustomerDto.mapToEntity(connector);
        }
        tpCustomerRepository.save(thirdPartyCustomer);

        List<Customer> customers = customerRepository.findByOrgIdAndThirdPartyCustomerId(orgId, thirdPartyCustomer.getId());
        if (CollectionUtils.isNotEmpty(customers)) {
            // 如果有客户和微信用户关联，则同时更新客户的状态信息
            customers.forEach(c -> {
                boolean update = false;
                if (c.getStatus() != CustomerConnectionStatus.SUBSCRIBED) {
                    c.setStatus(CustomerConnectionStatus.SUBSCRIBED);
                    update = true;
                }
                if (c.getAuthorizeStatus() != ConnectorAuthorizeStatus.AUTHORIZED) {
                    c.setAuthorizeStatus(ConnectorAuthorizeStatus.AUTHORIZED);
                    update = true;
                }
                if (update) {
                    customerRepository.save(c);
                }
            });
            return customers.get(0);
        } else {
            // 新增客户
            Customer customer = new Customer(thirdPartyCustomer, orgId);
            customerRepository.save(customer);
            return customer;
        }
    }

}
