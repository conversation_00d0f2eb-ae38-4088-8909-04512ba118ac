package cn.hanyi.ctm.service;

import cn.hanyi.ctm.entity.ThirdPartyCustomer;
import cn.hanyi.ctm.entity.ThirdPartyCustomerDto;
import cn.hanyi.ctm.repository.ThirdPartyCustomerRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.auth.service.auth.AuthWechatOpenService;
import org.befun.auth.service.auth.config.WechatOpenConfig;
import org.befun.core.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ThirdPartyCustomerService extends BaseService<ThirdPartyCustomer, ThirdPartyCustomerDto, ThirdPartyCustomerRepository> {

    @Autowired
    private AuthWechatOpenService authWechatOpenService;

    @Override
    public void afterMapToDto(List<ThirdPartyCustomer> entity, List<ThirdPartyCustomerDto> dto) {
        if (CollectionUtils.isEmpty(dto)) {
            return;
        }
        Map<Long, WechatOpenConfig> configMap = new HashMap<>();
        List<WechatOpenConfig> configs = authWechatOpenService.getListConfig("cem");
        if (configs != null) {
            configs.forEach(i -> {
                configMap.put(i.getConfigId(), i);
            });
        }
        dto.forEach(i -> {
            i.setWechatOpenConfig(configMap.get(i.getThirdpartyAuthId()));
        });
    }

    public ThirdPartyCustomer add(Long orgId, Long wechatConfigId, String openId) {
        ThirdPartyCustomer thirdPartyCustomer = new ThirdPartyCustomer(orgId, wechatConfigId, openId);
        repository.save(thirdPartyCustomer);
        return thirdPartyCustomer;
    }

    public ThirdPartyCustomer getThirdpartyCustomer(Long orgId, Long wechatConfigId, String openId) {
        return repository.findFirstByOrgIdAndThirdpartyAuthIdAndOpenId(orgId, wechatConfigId, openId);
    }
}
