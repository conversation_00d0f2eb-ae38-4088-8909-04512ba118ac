//package cn.hanyi.ctm.service.push;
//
//import cn.hanyi.ctm.constant.customer.CustomerPushType;
//import cn.hanyi.ctm.dto.ShortUrlDto;
//import cn.hanyi.ctm.dto.customer.push.BasePushDto;
//import cn.hanyi.ctm.dto.customer.push.CustomerPushTargetDto;
//import cn.hanyi.ctm.dto.task.CustomerPushPageableTaskDetailDto;
//import cn.hanyi.ctm.dto.task.CustomerPushTaskDetailDto;
//import cn.hanyi.ctm.entity.CtmSurvey;
//import cn.hanyi.ctm.entity.Customer;
//import cn.hanyi.ctm.entity.CustomerPushRecord;
//import cn.hanyi.ctm.repository.CtmSurveyRepository;
//import cn.hanyi.ctm.service.CustomerPushRecordService;
//import cn.hanyi.ctm.service.CustomerService;
//import cn.hanyi.ctm.service.ShortUrlService;
//import cn.hanyi.ctm.service.push.callback.ICallbackCustomerPushService;
//import cn.hanyi.ctm.tasks.ICustomerPushExecutor;
//import cn.hanyi.ctm.tasks.ICustomerPushPageableExecutor;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.MapUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.commons.lang3.math.NumberUtils;
//import org.befun.auth.entity.Department;
//import org.befun.auth.service.DepartmentService;
//import org.befun.auth.utils.StringHelper;
//import org.befun.core.utils.JsonHelper;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//
//import java.time.Duration;
//import java.time.LocalDate;
//import java.time.ZoneId;
//import java.util.*;
//import java.util.function.BiFunction;
//import java.util.stream.Collectors;
//
//import static cn.hanyi.ctm.constant.customer.CustomerPushStatus.FAILURE;
//import static cn.hanyi.ctm.constant.customer.CustomerPushStatus.SUCCESS;
//
//@Slf4j
//public abstract class CustomerPushService<P extends BasePushDto> {
//
//    @Autowired
//    private CustomerService customerService;
//    @Autowired
//    private CustomerPushRecordService customerPushRecordService;
//    @Autowired
//    private ICustomerPushExecutor customerPushExecutor;
//    @Autowired
//    private ICustomerPushPageableExecutor customerPushPageableExecutor;
//    @Autowired
//    private DepartmentService departmentService;
//    @Autowired
//    private CtmSurveyRepository ctmSurveyRepository;
//    @Autowired
//    private ShortUrlService shortUrlService;
//    @Value("${survey.survey-url-prefix.root:}")
//    private String liteUrlPrefix;
//
//    @Autowired(required = false)
//    private List<ICallbackCustomerPushService> pushCallbackServices;
//
//    public abstract CustomerPushType getPushType();
//
//    public abstract Class<P> getPushParamClass();
//
//    public abstract boolean send(CustomerPushRecord pushRecord, Customer customer, P pusParam, Map<String, Object> contentParams);
//
//
//    //*********************************************************************************************
//    //*********************************** 添加发送任务 **********************************************
//    //*********************************************************************************************
//
//    public boolean addPush(Long orgId, Long customerId, P pushParam) {
//        return addPush(orgId, CustomerPushTargetDto.builder().customerId(customerId).build(), pushParam);
//    }
//
//    public boolean addPush(Long orgId, String customerEuid, P pushParam) {
//        return addPush(orgId, CustomerPushTargetDto.builder().customerEuid(customerEuid).build(), pushParam);
//    }
//
//    public boolean addPush(Long orgId, String customerEuid, String customerName, P pushParam) {
//        return addPush(orgId, CustomerPushTargetDto.builder().customerEuid(customerEuid).customerName(customerName).build(), pushParam);
//    }
//
//    public boolean addPush(Long orgId, CustomerPushTargetDto target, P pushParam) {
//        CustomerPushRecord entity = customerPushRecordService.add(orgId, target, getPushType(), pushParam);
//        CustomerPushTaskDetailDto taskDto = new CustomerPushTaskDetailDto();
//        taskDto.setPushId(entity.getId());
//        if (pushParam.getTimed() != null) {
//            customerPushExecutor.performAt(taskDto, pushParam.getTimed());
//        } else if (pushParam.getDelay() != null) {
//            customerPushExecutor.performAsyncDelay(taskDto, pushParam.getDelay());
//        } else {
//            customerPushExecutor.performAsync(taskDto);
//        }
//        return true;
//    }
//
//    public boolean addPushTask(boolean isAppend, String taskId, List<Long> pushIds, Duration delay) {
//        if (isAppend) {
//            customerPushPageableExecutor.appendTotal(taskId, pushIds.size());
//        } else {
//            customerPushPageableExecutor.reset(taskId);
//            customerPushPageableExecutor.updateTotal(taskId, pushIds.size());
//        }
//        BiFunction<Integer, Integer, CustomerPushPageableTaskDetailDto> getPageDataDto = (page, size) -> {
//            CustomerPushPageableTaskDetailDto dto = new CustomerPushPageableTaskDetailDto();
//            dto.formatIds(customerPushPageableExecutor.splitPageList(pushIds, page, size));
//            return dto;
//        };
//
//        if (delay != null) {
//            // 定时发送
//            customerPushPageableExecutor.performPageDelay(taskId, pushIds.size(), delay, getPageDataDto);
//        } else {
//            // 立即发送
//            customerPushPageableExecutor.performPage(taskId, pushIds.size(), getPageDataDto);
//        }
//        return true;
//    }
//
//
//    //*********************************************************************************************
//    //************************************** 发送 **************************************************
//    //*********************************************************************************************
//
//    public boolean push(CustomerPushRecord entity) {
//        P pushParam = JsonHelper.toObject(entity.getPushParams(), getPushParamClass());
//        if (pushParam == null) {
//            return false;
//        }
//        Customer customer = customerService.get(entity.getCustomerId());
//        if (customer == null && StringUtils.isNotEmpty(entity.getCustomerEuid())) {
//            customer = new Customer();
//            customer.setExternalUserId(entity.getCustomerEuid());
//        }
//        if (customer == null) {
//            return false;
//        }
//        if (StringUtils.isNotEmpty(entity.getCustomerMobile())) {
//            customer.setMobile(entity.getCustomerMobile());
//        }
//        if (StringUtils.isNotEmpty(entity.getCustomerName())) {
//            customer.setUsername(entity.getCustomerName());
//        }
//        Map<String, Object> contentParams = buildContentParams(pushParam, customer);
//        boolean result = false;
//        try {
//            result = send(entity, customer, pushParam, contentParams);
//        } catch (Throwable e) {
//            log.error("{}", e.getMessage());
//        }
//        entity.setPushStatus(result ? SUCCESS : FAILURE);
//        customerPushRecordService.update(entity);
//        // 发送结果回调
//        if (pushCallbackServices != null) {
//            for (ICallbackCustomerPushService callback : pushCallbackServices) {
//                if (callback.pushCompleted(entity)) {
//                    break;
//                }
//            }
//        }
//        return result;
//    }
//
//    private Map<String, Object> buildContentParams(P pushParam, Customer customer) {
//        Map<String, Object> contentParams = new HashMap<>();
//        buildContentParamsByDepartmentId(contentParams, pushParam.getContentParams());
//        buildContentParamsBySurveyId(contentParams, pushParam.getSurveyId(), pushParam.getUrlParams());
//        buildContentParamsByCustomer(contentParams, customer);
//        if (MapUtils.isNotEmpty(pushParam.getContentParams())) {
//            contentParams.putAll(pushParam.getContentParams());
//        }
//        return contentParams;
//    }
//
//    private void buildContentParamsBySurveyId(Map<String, Object> contentParams, Long surveyId, Map<String, Object> urlParams) {
//        if (surveyId != null && surveyId > 0) {
//            Optional<CtmSurvey> survey = ctmSurveyRepository.findById(surveyId);
//            if (survey.isPresent()) {
//                Map<String, Object> innerParams = new HashMap<>();
//                innerParams.put("title", survey.get().getTitle());
//                innerParams.put("id", surveyId);
//                contentParams.put("survey", innerParams);
//                contentParams.put("surveyId", surveyId);
//                expandedInnerParams(contentParams, "survey", innerParams);
//                buildContentParamsByUrl(contentParams, survey.get(), urlParams);
//            }
//        }
//    }
//
//    private void buildContentParamsByUrl(Map<String, Object> contentParams, CtmSurvey survey, Map<String, Object> urlParams) {
//        String url = StringHelper.concatUrl(liteUrlPrefix, survey.getId().toString());
//        if (MapUtils.isNotEmpty(urlParams)) {
//            String params = urlParams.entrySet()
//                    .stream()
//                    .filter(i -> i.getKey() != null && i.getValue() != null)
//                    .map(i -> i.getKey() + "=" + i.getValue())
//                    .collect(Collectors.joining("&"));
//            url = StringHelper.concatUrlParams(url, params);
//        }
//
//        // confirm clientId
//        if (!url.contains("clientId=")) {
//            String clientId = UUID.randomUUID().toString();
//            url = StringHelper.concatUrlParams(url, "clientId=" + clientId);
//        }
//
//        String encryptUrl = encryptUrl(url);
//        ShortUrlDto shortUrlDto = shortUrlService.generate(encryptUrl);
//        Map<String, Object> innerParams = new HashMap<>();
//        innerParams.put("original", url);
//        innerParams.put("encrypted", encryptUrl);
//        innerParams.put("short", shortUrlDto.getUrl());
//        innerParams.put("code", shortUrlDto.getId());
//
//        contentParams.put("url", innerParams);
//        contentParams.put("_short", shortUrlDto.getUrl());
//        contentParams.put("_encrypted", encryptUrl);
//
//        expandedInnerParams(contentParams, "url", innerParams);
//    }
//
//    private String encryptUrl(String url) {
//        String[] urlList = url.split("\\?");
//        String path = urlList[0];
//        String param = "";
//        if (urlList.length > 1) {
//            param = urlList[1];
//            param = Base64.getUrlEncoder().encodeToString(param.getBytes());
//        }
//        return String.format("%s?__t=%s", path, param);
//    }
//
//
//    private void buildContentParamsByDepartmentId(Map<String, Object> contentParams, Map<String, Object> originalContentParams) {
//        Object departmentId = originalContentParams.get("departmentId");
//        if (departmentId != null && NumberUtils.isDigits(departmentId.toString())) {
//            Department department = departmentService.get(Long.parseLong(departmentId.toString()));
//            if (department != null) {
//                Map<String, Object> innerParams = new HashMap<>();
//                innerParams.put("id", department.getId());
//                innerParams.put("title", department.getTitle());
//                Optional.ofNullable(department.getCode()).ifPresent(i -> innerParams.put("code", i));
//                contentParams.put("department", innerParams);
//                expandedInnerParams(contentParams, "department", innerParams);
//            }
//        }
//    }
//
//    private void buildContentParamsByCustomer(Map<String, Object> contentParams, Customer customer) {
//        Map<String, Object> innerParams = new HashMap<>();
//        innerParams.put("id", customer.getId());
//        innerParams.put("orgId", customer.getOrgId());
//        innerParams.put("username", customer.getUsername());
//        innerParams.put("nickname", customer.getNickname());
//        innerParams.put("district", customer.getDistrict());
//        innerParams.put("city", customer.getCity());
//        innerParams.put("province", customer.getProvince());
//        innerParams.put("gender", customer.getGender());
//        innerParams.put("departmentIds", customer.getDepartmentId());
//        innerParams.put("latestJourneyRecord", customer.getLatestJourneyRecord());
//        if (customer.getLatestActiveTime() != null) {
//            innerParams.put("latestActiveTime", LocalDate.ofInstant(customer.getLatestActiveTime().toInstant(), ZoneId.systemDefault()).toString());
//        } else {
//            innerParams.put("latestActiveTime", "");
//        }
//        contentParams.put("customer", innerParams);
//        expandedInnerParams(contentParams, "customer", innerParams);
//    }
//
//    private void expandedInnerParams(Map<String, Object> contentParams, String innerKey, Map<String, Object> innerParams) {
//        innerParams.forEach((k, v) -> contentParams.put(innerKey + "." + k, v));
//    }
//}
