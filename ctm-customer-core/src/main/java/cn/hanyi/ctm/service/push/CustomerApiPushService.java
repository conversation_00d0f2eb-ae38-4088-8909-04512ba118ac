//package cn.hanyi.ctm.service.push;
//
//import cn.hanyi.ctm.constant.customer.CustomerPushType;
//import cn.hanyi.ctm.dto.customer.push.PushApiDto;
//import cn.hanyi.ctm.entity.Customer;
//import cn.hanyi.ctm.entity.CustomerPushRecord;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.http.HttpEntity;
//import org.apache.http.HttpResponse;
//import org.apache.http.NameValuePair;
//import org.apache.http.client.fluent.Content;
//import org.apache.http.client.fluent.Request;
//import org.apache.http.entity.ContentType;
//import org.apache.http.message.BasicNameValuePair;
//import org.apache.http.util.EntityUtils;
//import org.befun.core.template.TemplateEngine;
//import org.befun.core.utils.JsonHelper;
//import org.befun.extension.service.SmsService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.http.HttpMethod;
//import org.springframework.stereotype.Service;
//
//import java.nio.charset.StandardCharsets;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//import java.util.Optional;
//import java.util.stream.Collectors;
//
//@Slf4j
//@Service
//public class CustomerApiPushService extends CustomerPushService<PushApiDto> {
//
//    @Autowired
//    private SmsService smsService;
//
//    @Override
//    public Class<PushApiDto> getPushParamClass() {
//        return PushApiDto.class;
//    }
//
//    @Override
//    public CustomerPushType getPushType() {
//        return CustomerPushType.API;
//    }
//
//    @Override
//    public boolean send(CustomerPushRecord pushRecord, Customer customer, PushApiDto pushParam, Map<String, Object> contentParams) {
//        if (StringUtils.isEmpty(pushParam.getUrl())) {
//            log.warn("客户api推送失败，url 为空，pushParam={}", JsonHelper.toJson(pushParam));
//            return false;
//        }
//
//        Map<String, Object> pushContent = TemplateEngine.renderJsonTemplate(pushParam.getTemplateContent(), contentParams);
//        HttpMethod method = Optional.ofNullable(pushParam.getMethod()).orElse(HttpMethod.POST);
//        ContentType contentType = Optional.ofNullable(pushParam.getContentType()).orElse(ContentType.APPLICATION_JSON);
//        Request request = null;
//        String bodyRecord = null;
//        if (method == HttpMethod.POST) {
//            if (contentType == ContentType.APPLICATION_FORM_URLENCODED) {
//                List<String> bodyParams = new ArrayList<>();
//                List<NameValuePair> body = pushContent.entrySet().stream()
//                        .filter(i -> i.getKey() != null && i.getValue() != null)
//                        .peek(i -> bodyParams.add(i.getKey() + "=" + i.getValue()))
//                        .map(i -> new BasicNameValuePair(i.getKey(), i.getValue().toString())).collect(Collectors.toList());
//                bodyRecord = JsonHelper.toJson(bodyParams);
//                request = Request.Post(pushParam.getUrl()).bodyForm(body, StandardCharsets.UTF_8);
//            } else {
//                String body = JsonHelper.toJson(pushContent);
//                bodyRecord = body;
//                request = Request.Post(pushParam.getUrl()).bodyByteArray(body.getBytes(StandardCharsets.UTF_8), contentType);
//            }
//        } else if (method == HttpMethod.GET) {
//            request = Request.Get(pushParam.getUrl());
//        }
//        boolean result = false;
//        if (request != null) {
//            try {
//                String headerRecord = null;
//                if (pushParam.getHeaders() != null) {
//                    List<String> headerParams = new ArrayList<>();
//                    for (Map.Entry<String, Object> e : pushParam.getHeaders().entrySet()) {
//                        if (e.getKey() != null && e.getValue() != null) {
//                            headerParams.add(e.getKey() + "=" + e.getValue());
//                            request.addHeader(e.getKey(), e.getValue().toString());
//                        }
//                    }
//                    headerRecord = JsonHelper.toJson(headerParams);
//                }
//                recordPushContent(pushRecord, method, pushParam.getUrl(), bodyRecord, headerRecord);
//                HttpResponse httpResponse = request.execute().returnResponse();
//                HttpEntity entity = httpResponse.getEntity();
//                Content content = entity != null ? new Content(EntityUtils.toByteArray(entity), ContentType.getOrDefault(entity)) : Content.NO_CONTENT;
//                pushRecord.setPushResponse(content.toString());
//                result = httpResponse.getStatusLine().getStatusCode() == 200;
//            } catch (Throwable e) {
//                log.warn("客户api推送失败: pushParam={}", JsonHelper.toJson(pushParam), e);
//            }
//        }
//        return result;
//    }
//
//    private void recordPushContent(CustomerPushRecord pushRecord, HttpMethod method, String url, String body, String header) {
//        StringBuilder contentBuilder = new StringBuilder();
//        contentBuilder.append(method.name());
//        contentBuilder.append(", url=").append(url);
//        Optional.ofNullable(header).ifPresent(i -> contentBuilder.append(", header=").append(header));
//        Optional.ofNullable(body).ifPresent(i -> contentBuilder.append(", body=").append(body));
//        pushRecord.setPushContent(contentBuilder.toString());
//    }
//
//}
