package cn.hanyi.ctm.service;

import cn.hanyi.ctm.connector.IConnectorManager;
import cn.hanyi.ctm.constant.connector.ConnectorProviderType;
import cn.hanyi.ctm.constant.connector.ConnectorType;
import cn.hanyi.ctm.constant.customer.CustomerConnectionStatus;
import cn.hanyi.ctm.dto.AuthorizeRequestDto;
import cn.hanyi.ctm.dto.CallbackRequestDto;
import cn.hanyi.ctm.entity.Connector;
import cn.hanyi.ctm.entity.Customer;
import cn.hanyi.ctm.entity.ThirdPartyCustomer;
import cn.hanyi.ctm.repository.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
public class ConnectorWechatOpenService {

    @Autowired
    TemplateRepository templateRepository;

    @Autowired
    ThirdPartyTemplateRepository thirdPartyTemplateRepository;

    @Autowired
    PushRepository pushRepository;

    @Autowired
    PushLogRepository pushLogRepository;

    @Autowired
    ConnectorRepository connectorRepository;

    @Autowired
    CustomerRepository customerRepository;

    @Autowired
    ThirdPartyCustomerRepository thirdPartyCustomerRepository;

    @Autowired
    Map<String, IConnectorManager> connectorManagerMap;

    public String handleCallback(String appId, CallbackRequestDto requestDto, String body) {
//        WxMpXmlMessage message = wechatOpenConenctorProvider.parseMpMessage(
//                appId,
//                body,
//                requestDto.getMsg_signature(),
//                requestDto.getTimestamp().toString(),
//                requestDto.getNonce()
//        );
//        if (message.getEvent() != null) {
//            if (message.getEvent().equals(WxConsts.EventType.SUBSCRIBE)) {
//                // subscribe.
//                String openId = message.getFromUser();
//                log.info("new customer subscribe app:{} openId:{}", appId, openId);
//                updateThirdPartyCustomer(appId, openId);
//            } else if (message.getEvent().equals(WxConsts.EventType.UNSUBSCRIBE)) {
//                String openId = message.getFromUser();
//                log.info("new customer unsubscribe app:{} openId:{}", appId, openId);
//                disableThirdPartyCustomer(openId);
//            }
//        }
        return "";
    }

    @Async
    void disableThirdPartyCustomer(String openId) {
        List<ThirdPartyCustomer> tpCustomers = thirdPartyCustomerRepository.findAllByOpenId(openId);

        tpCustomers.forEach(tpCustomer -> {
            log.debug("disable tp customer {}", tpCustomer.getId());
            tpCustomer.setSubscribed(false);
            thirdPartyCustomerRepository.save(tpCustomer);

            Optional<Customer> customer = customerRepository
                    .findCustomerByThirdPartyCustomerId(tpCustomer.getId());
            if (customer.isPresent()) {
                Customer rc = customer.get();
                log.debug("disable for customer {}", rc.getId());
                rc.setStatus(CustomerConnectionStatus.UNSUBSCRIBED);
                customerRepository.save(rc);
            }
        });
    }

    @Async
    void updateThirdPartyCustomer(String appId, String openId) {
        List<Connector> connectors = connectorRepository.findAllByAppId(appId);

        connectors.forEach(connector -> {
            log.debug("update tp customer for connector {}", connector.getId());
            IConnectorManager connectorManager = connectorManagerMap.get(connector.getType().name());
            connectorManager.syncOneCustomer(connector.getOrgId(), null, connector, openId);
        });
    }

    public String handleTicket(String body, String signature, String encType, String msgSignature, String timestamp, String nonce) {
//        WxOpenXmlMessage message = wechatOpenConenctorProvider.parseMessage(body, msgSignature, timestamp, nonce);
//        return wechatOpenConenctorProvider.processMessage(message);
        return "";
    }

    public String preAuthenticate(ConnectorProviderType providerType) {
        IConnectorManager connectorManager = connectorManagerMap.get(ConnectorType.PLATFORM.name());
        return connectorManager.preAuthenticate(providerType);
    }

    public Optional<Connector> authorize(ConnectorProviderType providerType, AuthorizeRequestDto requestDto) {
        IConnectorManager connectorManager = connectorManagerMap.get(ConnectorType.PLATFORM.name());
        return connectorManager.authorize(providerType, requestDto);
    }
}
