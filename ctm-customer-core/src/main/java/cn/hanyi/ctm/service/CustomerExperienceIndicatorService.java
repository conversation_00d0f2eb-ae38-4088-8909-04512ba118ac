package cn.hanyi.ctm.service;

import cn.hanyi.ctm.constant.survey.QuestionType;
import cn.hanyi.ctm.dto.customer.CustomerExperienceIndicatorSqlDto;
import cn.hanyi.ctm.dto.customer.IndicatorCustomerResponseDto;
import cn.hanyi.ctm.entity.CustomerExperienceIndicator;
import cn.hanyi.ctm.entity.CustomerExperienceIndicatorDto;
import cn.hanyi.ctm.repository.CustomerExperienceIndicatorRepository;
import cn.hanyi.survey.core.entity.SurveyQuestion;
import cn.hanyi.survey.core.repository.SurveyQuestionRepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.ResourceBatchUpdateRequestDto;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.BaseService;
import org.befun.core.utils.DateHelper;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.service.NativeSqlHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CustomerExperienceIndicatorService extends BaseService<CustomerExperienceIndicator, CustomerExperienceIndicatorDto , CustomerExperienceIndicatorRepository> {


    @Autowired
    private CustomerService customerService;

    @Autowired
    private NativeSqlHelper nativeSqlHelper;
    @Autowired
    private SurveyQuestionRepository surveyQuestionRepository;

    @Override
    public <S extends BaseEntityDTO<CustomerExperienceIndicator>> CustomerExperienceIndicatorDto create(S data) {
        CustomerExperienceIndicatorDto customerExperienceIndicatorDto = (CustomerExperienceIndicatorDto) data;
        return super.create(data);
    }


    public List<IndicatorCustomerResponseDto> experienceIndicatorScore(Set<Long> customerIds, Integer limit, Boolean isRank) {
        List<IndicatorCustomerResponseDto> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(customerIds)) {
            return list;
        }
        List<CustomerExperienceIndicator> customerExperienceIndicatorDtoList = repository.findAll();
        if (CollectionUtils.isEmpty(customerExperienceIndicatorDtoList)) {
            return list;
        }
        String indicatorSurveyIds = customerExperienceIndicatorDtoList.stream().map(x -> x.getSurveyId().toString()).collect(Collectors.joining(","));
        String customerIdsStr = customerIds.stream().map(Object::toString).collect(Collectors.joining(","));

        List<SurveyQuestion> questionList = surveyQuestionRepository.findAllById(customerExperienceIndicatorDtoList.stream().map(CustomerExperienceIndicator::getQuestionId).collect(Collectors.toList()));

        String sql = String.format("WITH latest_indicators AS( SELECT DISTINCT cei.question_id FROM customer_experience_indicator cei " +
                "WHERE cei.survey_id IN (%s)), ranked_answers AS ( SELECT src.id, ca.customer_id, src.type, src.i_val, src.s_val, src.j_val, " +
                "ca.answer_time, cei.question_id, cei.item_id, cei.name, CASE WHEN %s THEN ROW_NUMBER() " +
                "OVER ( PARTITION BY ca.customer_id ORDER BY ca.answer_time DESC ) " +
                "ELSE 1 END AS rn FROM latest_indicators li JOIN customer_experience_indicator cei " +
                "ON cei.question_id = li.question_id JOIN customer_answers ca " +
                "ON ca.customer_id IN (%s) JOIN survey_response_cell src ON src.s_id = ca.sid " +
                "AND src.q_id = cei.question_id AND src.r_id = ca.answer_id WHERE ca.answer_status = 1 ) " +
                "SELECT id, customer_id, type, i_val, s_val, j_val, answer_time, question_id, item_id, name " +
                "FROM ranked_answers WHERE rn = 1 ORDER BY answer_time DESC LIMIT %s;", indicatorSurveyIds, isRank.toString(), customerIdsStr, limit);
        log.info("查询客户统计数据, 指标数据, 客户填答记录 sql={}", sql);
        List<CustomerExperienceIndicatorSqlDto> customerAnswers = nativeSqlHelper.queryListObject(sql, CustomerExperienceIndicatorSqlDto.class);


        for (CustomerExperienceIndicatorSqlDto customerAnswer : customerAnswers) {
            IndicatorCustomerResponseDto indicatorCustomerResponseDto = new IndicatorCustomerResponseDto();
            QuestionType questionType = QuestionType.values()[Integer.parseInt(customerAnswer.getType())];
            AtomicReference<String> value = new AtomicReference<>("");
            switch (questionType) {
                case MATRIX_SCORE:
                    Optional.of(questionList.stream().filter(q -> q.getId().equals(customerAnswer.getQuestionId())).findFirst())
                            .flatMap(q -> q.get().getItems().stream().filter(i -> i.getId().equals(customerAnswer.getItemId())).findFirst())
                            .ifPresent(item -> {
                                value.set(Objects.toString(JsonHelper.toMap(customerAnswer.getJ_val()).get(item.getValue()), ""));
                            });
                    break;
                case SCORE_EVALUATION:
                    Optional.of(questionList.stream().filter(q -> q.getId().equals(customerAnswer.getQuestionId())).findFirst())
                            .flatMap(q -> q.get().getItems().stream().filter(i -> i.getValue().equals(customerAnswer.getS_val())).findFirst())
                            .ifPresent(item -> {
                                value.set(item.getSequence().toString());
                            });
                    break;
                default:
                    value.set(customerAnswer.getI_val());
            }
            indicatorCustomerResponseDto.setName(customerAnswer.getName());
            indicatorCustomerResponseDto.setValue(value.get());
            indicatorCustomerResponseDto.setDate(DateHelper.formatDate(customerAnswer.getAnswerTime()));
            indicatorCustomerResponseDto.setCustomerId(customerAnswer.getCustomerId());
            list.add(indicatorCustomerResponseDto);
        }
        return list;
    }

    @Override
    public <S extends BaseEntityDTO<CustomerExperienceIndicator>> List<CustomerExperienceIndicatorDto> batchUpdate(ResourceBatchUpdateRequestDto<S> batchChangeDto) {
        // 没有id的新增的  需要创建
        List<CustomerExperienceIndicator> adds = batchChangeDto.getChanges().stream().filter(change -> change.getId() == null).map(x -> {
            S data = x.getData();
            CustomerExperienceIndicatorDto dto = (CustomerExperienceIndicatorDto) data;
            CustomerExperienceIndicator customerExperienceIndicator = new CustomerExperienceIndicator();
            customerExperienceIndicator.setName(dto.getName());
            customerExperienceIndicator.setItemId(dto.getItemId());
            customerExperienceIndicator.setQuestionId(dto.getQuestionId());
            customerExperienceIndicator.setSurveyId(dto.getSurveyId());
            customerExperienceIndicator.setCustomerId(dto.getCustomerId());
            return customerExperienceIndicator;
        }).collect(Collectors.toList());

        // batchChangeDto删除id为null的数据
        batchChangeDto.getChanges().removeIf(change -> change.getId() == null);

        List<CustomerExperienceIndicatorDto> customerExperienceIndicatorDtos = super.batchUpdate(batchChangeDto);

        // 不在list中的就删除
        repository.deleteByOrgIdAndIdNotIn(TenantContext.getCurrentTenant(), customerExperienceIndicatorDtos.stream().map(CustomerExperienceIndicatorDto::getId).collect(Collectors.toList()));

        List<CustomerExperienceIndicator> added = repository.saveAll(adds);
        // added 不为空就转成dto添加到customerExperienceIndicatorDtos
        if (CollectionUtils.isNotEmpty(added)) {
            customerExperienceIndicatorDtos.addAll(added.stream().map(this::mapToDto).collect(Collectors.toList()));
        }
        return customerExperienceIndicatorDtos;
    }
}
