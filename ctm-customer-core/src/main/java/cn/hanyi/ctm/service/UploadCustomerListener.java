package cn.hanyi.ctm.service;

import cn.hanyi.ctm.constant.error.CtmErrorCode;
import cn.hanyi.ctm.dto.customer.UploadCustomerDto;
import cn.hanyi.ctm.entity.Customer;
import cn.hanyi.ctm.exception.CtmErrorException;
import cn.hanyi.ctm.repository.CustomerRepository;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.util.ListUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.befun.auth.dto.DepartmentTreeDto;
import org.befun.auth.entity.UserDto;
import org.befun.auth.projection.SimpleUser;
import org.befun.core.utils.JsonHelper;
import org.befun.core.utils.ListHelper;
import org.befun.core.utils.RegHelper;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.FileCopyUtils;

import java.io.IOException;
import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;


/**
 * <AUTHOR>
 * 无法被spring管理
 */
@Slf4j
public class UploadCustomerListener implements ReadListener<UploadCustomerDto> {


    /**
     * 每隔5条存储数据库，实际使用中可以100条，然后清理list ，方便内存回收
     */
    private static final int BATCH_COUNT = 100;
    private List<UploadCustomerDto> cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
    private List<UploadCustomerDto> resultList = new ArrayList<>(BATCH_COUNT);
    private int customerReturnSize;
    private SimpleUser user;
    private Long orgId;
    private AtomicInteger total;
    private CustomerRepository customerRepository;
    private List<String> externalUserIdList;
    private static Map regin;
    private boolean preview;
    private DepartmentTreeDto departmentTree;
    private Map<String, UserDto> allUserMap;

    static {
        try {
            regin = new ObjectMapper().readValue(new String(FileCopyUtils.copyToByteArray(new ClassPathResource("static/region.json").getInputStream()), StandardCharsets.UTF_8), Map.class);
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    UploadCustomerListener(
            List<UploadCustomerDto> resultDto,
            int customerReturnSize,
            SimpleUser user,
            Long orgId,
            AtomicInteger total,
            CustomerRepository customerRepository,
            List<String> externalUserIdList,
            DepartmentTreeDto departmentTree,
            List<UserDto> allUsers,
            boolean preview
    ) throws IOException {
        this.resultList = resultDto;
        this.customerReturnSize = customerReturnSize;
        this.customerRepository = customerRepository;
        this.user = user;
        this.orgId = orgId;
        this.total = total;
        this.externalUserIdList = externalUserIdList;
        this.preview = preview;
        this.departmentTree = departmentTree;
        this.allUserMap = allUsers == null ? new HashMap<>() : allUsers.stream().collect(Collectors.toMap(UserDto::getTruename, j -> j, (o1, o2) -> o1));
    }


    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        //跳过第一行
        if(context.readRowHolder().getRowIndex() == 0){
            return;
        }
//        Field[] dtoFields = UploadCustomerDto.class.getDeclaredFields();
//        List<Field> fieldList = Arrays.stream(dtoFields)
//                .filter(field -> field.getAnnotation(ExcelProperty.class) != null)
//                .sorted(Comparator.comparingInt(field -> field.getAnnotation(ExcelProperty.class).index()))
//                .collect(Collectors.toList());
//
//        if (headMap.values().stream().anyMatch(h -> h.getStringValue() == null)) {
//            throw new CtmErrorException(CtmErrorCode.EXCEL_HEAD_ERROR);
//        }
//
//        if (headMap.size() != fieldList.size()) {
//            throw new CtmErrorException(CtmErrorCode.EXCEL_HEAD_ERROR);
//        }
//
//        for (int i = 0; i < headMap.size(); i++) {
//            if (!headMap.get(i).getStringValue().equals(fieldList.get(i).getAnnotation(ExcelProperty.class).value()[0])) {
//                throw new CtmErrorException(CtmErrorCode.EXCEL_HEAD_ERROR, CtmErrorCode.EXCEL_HEAD_ERROR.getMessage() + headMap.get(i).getStringValue());
//            }
//        }
    }

    /**
     * 这个每一条数据解析都会来调用
     *
     * @param data    one row value. Is is same as {@link AnalysisContext#readRowHolder()}
     * @param context
     */
    @Override
    public void invoke(UploadCustomerDto data, AnalysisContext context) {
        log.info("解析到一条数据:{}", JsonHelper.toJson(data));

        if (!isValid(data)) {
            return;
        }
        cachedDataList.add(data);
        total.incrementAndGet();
        if (resultList.size() <= customerReturnSize) {
            resultList.add(data);
        }
        // 达到BATCH_COUNT了，需要去存储一次数据库，防止数据几万条数据在内存，容易OOM
        if (cachedDataList.size() >= BATCH_COUNT) {
            if (!preview) {
                saveData();
            }
            // 存储完成清理 list
            cachedDataList = ListUtils.newArrayListWithExpectedSize(BATCH_COUNT);
        }

    }

    /**
     * 所有数据解析完成了 都会来调用
     *
     * @param context
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 这里也要保存数据，确保最后遗留的数据也存储到数据库
        if (!preview) {
            saveData();
        }
        log.info("所有数据解析完成！");
    }

    /**
     * 加上存储数据库
     */
    private void saveData() {
        log.info("{}条数据，开始存储数据库！", cachedDataList.size());

        List<Customer> customerList = Arrays.asList(new Customer[cachedDataList.size()]);
        IntStream.range(0, cachedDataList.size()).parallel().forEach(i -> {
            UploadCustomerDto u = cachedDataList.get(i);
            Customer customer = u.convertToCustomer();
            customer.setOrgId(orgId);
            customer.setCreatedByUid(user.getId());
            belongUsers(u, customer);
            departmentId(u, customer);
            customerList.set(i, customer);

        });

        customerRepository.saveAll(customerList);

        log.info("存储数据库成功！");
    }

    private void departmentId(UploadCustomerDto u, Customer customer) {
        if (u.getDepartmentName() != null && departmentTree != null) {
            // 中国/广东/深圳/南山
            String[] as = u.getDepartmentName().split("/");
            int deep = as.length - 1; // 3
            int step = 0;
            List<DepartmentTreeDto> nodes = ListHelper.arrayList(departmentTree);
            while (CollectionUtils.isNotEmpty(nodes) && step <= deep) {
                String name = as[step];
                DepartmentTreeDto find = nodes.stream().filter(k -> name.equals(k.getTitle())).findFirst().orElse(null);
                if (find != null) {
                    if (step == deep) {
                        customer.setDepartmentId(find.getId());
                        return;
                    } else {
                        step++;
                        nodes = find.children();
                    }
                } else {
                    return;
                }
            }
        }
    }

    private void belongUsers(UploadCustomerDto u, Customer customer) {
        if (u.getBelongUserTruenames() != null) {
            List<Long> userIds = new ArrayList<>();
            String[] as = u.getBelongUserTruenames().split(",");
            Arrays.stream(as).forEach(j -> {
                Optional.ofNullable(allUserMap.get(j)).ifPresent(m -> {
                    if (!userIds.contains(m.getId())) {
                        userIds.add(m.getId());
                    }
                });
            });
            if (!userIds.isEmpty()) {
                customer.setBelongToUids(userIds.stream().map(Objects::toString).collect(Collectors.joining(",")));
            }
        }
    }

    /**
     * 验证数据有效性
     *
     * @return
     */
    private Boolean isValid(UploadCustomerDto data) {
        // 姓名必填 没有就过滤掉  且小于20字符
        if (StringUtils.isEmpty(data.getUsername()) || data.getUsername().length() > 20) {
            return false;
        }
        if (externalUserIdList.contains(data.getExternalUserId())) {
            data.setExternalUserId(null);
        }

        // 检查省市区正确性
        boolean cleanRegion = false;

        Map cities = (Map) regin.get(data.getProvince());
        if (cities == null) {
            cleanRegion = true;
        } else {
            List counties = (List) cities.get(data.getCity());
            cleanRegion = counties == null || !counties.contains(data.getDistrict());
        }

        if (cleanRegion) {
            data.setProvince(null);
            data.setCity(null);
            data.setDistrict(null);
        }

        if (!RegHelper.isMobile(data.getMobile())) {
            data.setMobile(null);
        }

        if (!RegHelper.isEmail(data.getEmail())) {
            data.setEmail(null);
        }

        data.setBirthday(castBirthday(data.getBirthday()));

        return true;
    }

    private String castBirthday(String birthday) {
        //  1999  1999/1  1999/01  1999/1/1  1999/01/01  1999-1  1999-01  1999-1-1  1999-01-01
        if (StringUtils.isNotEmpty(birthday)) {
            List<Integer> as = null;
            if (birthday.contains("/")) {
                as = Arrays.stream(birthday.split("/")).map(i -> NumberUtils.isDigits(i) ? Integer.parseInt(i) : 0).collect(Collectors.toList());
            } else if (birthday.contains("-")) {
                as = Arrays.stream(birthday.split("-")).map(i -> NumberUtils.isDigits(i) ? Integer.parseInt(i) : 0).collect(Collectors.toList());
            } else if (NumberUtils.isDigits(birthday)) {
                as = List.of(Integer.parseInt(birthday));
            }
            if (as != null && as.size() > 0) {
                int y = as.get(0);
                int m = as.size() > 1 ? as.get(1) : 1;
                int d = as.size() > 2 ? as.get(2) : 1;
                if (y > 1900 && y < 3000 && m > 0 && m < 13 && d > 0 && d < 32) {
                    return y + "/" + m + "/" + d;
                }
            }
        }
        return "";
    }
}
