package cn.hanyi.ctm.service;

import cn.hanyi.ctm.connector.IConnectorManager;
import cn.hanyi.ctm.constant.SourceType;
import cn.hanyi.ctm.constant.connector.ConnectorType;
import cn.hanyi.ctm.dto.*;
import cn.hanyi.ctm.dto.journey.PushMomentDto;
import cn.hanyi.ctm.entity.*;
import cn.hanyi.ctm.repository.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.entity.Department;
import org.befun.auth.repository.OrganizationWalletRepository;
import org.befun.auth.service.DepartmentService;
import org.befun.core.exception.BadRequestException;
import org.befun.core.exception.OverLimitException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.BaseService;
import org.befun.core.template.TemplateEngine;
import org.befun.extension.sms.ISmsAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class PushService extends BaseService<Push, PushDto, PushRepository> {
    @Autowired
    ConnectorService connectorService;

    @Autowired
    ConnectorRepository connectorRepository;

    @Autowired
    PushRepository pushRepository;

    @Autowired
    TemplateRepository templateRepository;

    @Autowired
    PushLogRepository logRepository;

    @Autowired
    CustomerRepository customerRepository;

    @Autowired
    ShortUrlService shortUrlService;

    @Autowired
    OrganizationWalletRepository organizationWalletRepository;

    @Autowired
    Map<String, IConnectorManager> connectorManagerMap;

    @Autowired
    DepartmentService departmentService;

    @Autowired
    ISmsAccountService smsAccountService;

    @Autowired
    ObjectMapper objectMapper;

    /**
     * 构建内置参数，支持内置customer,url信息
     *
     * @param params
     * @param customer
     * @return {
     * "customer": {
     * "username": ""
     * },
     * "url": {
     * "orginal": "http://www.baidu.com",
     * "short": "https://t.xmplus.cn/1",
     * "code": 1
     * }
     * }
     */
    private Map<String, Object> buildNativeParams(Map<String, Object> params, String url, Customer customer, Long departmentId, Boolean shouldShortUrl) {
        Map<String, Object> result = new HashMap<>(params);

        // 第一步 先展开customer信息
        DateFormat dateFormat = new SimpleDateFormat("yyyy-mm-dd");

        Map<String, Object> customerExpand = new HashMap<>();
        customerExpand.put("id", customer.getId());
        customerExpand.put("orgId", customer.getOrgId());
        customerExpand.put("username", customer.getUsername());
        customerExpand.put("nickname", customer.getNickname());
        customerExpand.put("district", customer.getDistrict());
        customerExpand.put("city", customer.getCity());
        customerExpand.put("province", customer.getProvince());
        customerExpand.put("gender", customer.getGender());
        customerExpand.put("departmentIds", customer.getDepartmentId());
        customerExpand.put("latestJourneyRecord", customer.getLatestJourneyRecord());
        customerExpand.put("latestActiveTime", "");
        if (customer.getLatestActiveTime() != null) {
            customerExpand.put("latestActiveTime", dateFormat.format(customer.getLatestActiveTime()));
        }

        result.put("customer", customerExpand);
        if (url != null && !url.isEmpty()) {
            StringBuilder sb = new StringBuilder(url);

            if (url.contains("${customer.id}")) {
                sb.append(String.format("&customerName=%s", customer.getUsername()));
                sb.append(String.format("&customerGender=%s", customer.getGender()));
            }
            if (departmentId != null) {
                Department department = departmentService.get(departmentId);
                if (department != null) {
                    sb.append(String.format("&departmentName=%s", department.getTitle()));
                }
            }
            url = sb.toString();
        }

        // 添加clientId
        if (StringUtils.isNotEmpty(url) && !url.contains("clientId=")) {
            String clientId = UUID.randomUUID().toString();
            if (url.contains("?")) {
                url = url + "&clientId=" + clientId;
            } else {
                url = url + "?clientId=" + clientId;
            }
        }

        // 第二步 展开URL，并且做短链支持
        String expandUrl = url;
        Map<String, Object> urlExpand = new HashMap<>();
        urlExpand.put("original", url);

        if (url != null && !url.isEmpty() && url.contains("${")) {
            expandUrl = TemplateEngine.renderTextTemplate(url, result);
            urlExpand.put("expand", expandUrl);
            log.debug("expand url from {} => {}", url, expandUrl);
        }
        // 生成clientId参数及base64加密
        String encryptedUrl = expandUrl;
        encryptedUrl = encrypteUrl(expandUrl);
        urlExpand.put("encrypted", encryptedUrl);

        urlExpand.put("short", "");
        urlExpand.put("code", "");

        if (shouldShortUrl && expandUrl != null && !expandUrl.isEmpty()) {
            ShortUrlDto shortUrlDto = shortUrlService.generate(encryptedUrl);
            log.debug("short url from {} => {}", expandUrl, shortUrlDto.getUrl());
            urlExpand.put("short", shortUrlDto.getUrl());
            urlExpand.put("code", shortUrlDto.getId());
        }

        // 合并
        result.put("url", urlExpand);
        result.put("_short", urlExpand.get("short"));
        result.put("_encrypted", urlExpand.get("encrypted"));

        return result;
    }

    List<Customer> buildCustomers(Long orgId, MassPushRequestDto dto) {
        List<Customer> customers = null;
        switch (dto.getStrategy()) {
            case BATCH:
                customers = customerRepository.findAllByOrgIdAndIdIn(orgId, dto.getCustomerIds());
                break;
            case ALL:
                customers = customerRepository.findAllByOrgId(orgId);
                break;
            default:
                throw new BadRequestException();
        }
        return customers;
    }

    private String encrypteUrl(String url) {
        if (url == null) {
            return null;
        }
        String[] urlList = url.split("\\?");
        String encryptedParams = "";
        if (urlList.length > 1) {
            String privateParams = urlList[1];
            encryptedParams = Base64.getUrlEncoder().encodeToString(privateParams.getBytes());
        }

        return String.format("%s?__t=%s", url.split("\\?")[0], encryptedParams);
    }

    public PushResponseDto sendMessageByApp(AppMassPushRequestDto dto) {
        List<Customer> customers = buildCustomers(dto.getOrgId(), dto);

        Optional<Template> template = null;
        if (dto.getTemplateId() != null) {
            if (dto.getTemplateId() == 0) {
                Optional<Connector> connector = connectorRepository.findByOrgIdAndType(dto.getOrgId(), ConnectorType.API);
                Assert.isTrue(connector.isPresent(), "api connector dose not exist");
                template = Optional.of(new Template());
                template.get().setConnector(connector.get());
            } else {
                template = templateRepository.findById(dto.getTemplateId());

            }
        } else if (dto.getTemplateName() != null) {
            template = templateRepository.findByName(dto.getTemplateName());
        } else {
            template = templateRepository.findTemplateBySourceTypeAndType(SourceType.SYSTEM, ConnectorType.SMS);
        }
        Assert.isTrue(template.isPresent(), "wrong template");

        String realUrl = dto.getUrl().isEmpty() ? template.get().getUrl() : dto.getUrl();

        PushResponseDto result = internalSendMessage(
                template.get().getConnector(),
                dto.getOrgId(),
                customers,
                dto.getDepartmentId(),
                template.get(),
                null,
                dto.getParameters(),
                realUrl, false, dto.getIsDetail(), true,
                dto.getMomentDto(), dto.getSceneId(), dto.getSkipLimiter());

        result.setTemplate(template.get());
        return result;
    }

    public ApiPushResponseDto sendMessageByExternalApp(ApiPushRequestDto dto) {
        ApiPushResponseDto apiPushResponseDto = new ApiPushResponseDto();

        Optional<Connector> connector = connectorRepository.findByOrOrgIdAndType(dto.getOrgId(), ConnectorType.API);
        Assert.notNull(connector, "invalid connector");

        if (connector.isEmpty()) {
            throw new BadRequestException("invaild connector");
        }

        Optional<Customer> customer = Optional.empty();
        if (dto.getCustomerId() != null && dto.getCustomerId() > 0) {
            customer = customerRepository.findCustomerByOrgIdAndId(dto.getOrgId(), dto.getCustomerId());
        }
        IConnectorManager connectorManager = connectorManagerMap.get(connector.get().getType().name());

        if (customer.isEmpty()) {
            customer = Optional.of(new Customer());
        }

        Map<String, Object> mergedParams = buildNativeParams(dto.getParameters(), dto.getUrl(), customer.get(),
                dto.getDepartmentId(), true);
        String shortUrl = (String) mergedParams.getOrDefault("_short", dto.getUrl());
        dto.setSurveyUrl(shortUrl);
        connectorManager.sendApiMessage(connector.get(), customer.get(), dto.getDepartmentId(), dto.getSceneId(), dto.getSurveyUrl(), dto.getExternalUserId(), dto.getMomentDto());
        return apiPushResponseDto;
    }

    public PushResponseDto sendTextMessageByApp(TextPushRequestDto dto) {
        if (dto.getProviderType() == null || dto.getConnectorType() == null || dto.getOrgId() == null) {
            throw new BadRequestException();
        }

        if (dto.getConnectorType() == ConnectorType.SMS) {
            // SMS not support for this moment
            throw new BadRequestException();
        }

        Optional<Connector> connector = connectorRepository.findByOrOrgIdAndProviderType(dto.getOrgId(), dto.getProviderType());
        if (connector.isEmpty()) {
            throw new BadRequestException();
        }

        List<Customer> customers = customerRepository.findAllByOrgIdAndIdIn(dto.getOrgId(), dto.getCustomerIds());

        return internalSendMessage(
                connector.get(),
                dto.getOrgId(),
                customers,
                null,
                null,
                dto.getContent(),
                dto.getParameters(),
                "", false, dto.getIsDetail(), true, dto.getMomentDto(), null, false);
    }

    public Boolean sendMessageAsync(MassPushRequestDto dto) {
        final Long tenantId = TenantContext.getCurrentTenant();
        Assert.notNull(tenantId, "missing tenantId");

        List<Customer> customers = buildCustomers(tenantId, dto);

        Optional<Template> template = templateRepository.findById(dto.getTemplateId());
        if (!template.isPresent()) {
            throw new BadRequestException();
        }

        if (template.get().getSourceType() == SourceType.USER && !template.get().getOrgId().equals(tenantId)) {
            throw new BadRequestException();
        }

        log.info("will push message around {} customers by template {}", customers.size(), template.get().getId());

        return internalSendMessageAsync(
                template.get(),
                customers,
                dto.getDepartmentId(),
                dto.getParameters(),
                dto.getUrl(), false, false, true, dto.getMomentDto());
    }

    public PushResponseDto evaluateByApp(AppMassPushRequestDto dto) {
        Boolean isDebug = dto.getIsDebug();

        List<Customer> customers = buildCustomers(dto.getOrgId(), dto);

        Optional<Template> template = null;
        if (dto.getTemplateId() != null) {

            if (dto.getTemplateId() == 0) {
                Optional<Connector> connector = connectorRepository.findByOrgIdAndType(dto.getOrgId(), ConnectorType.API);
                Assert.isTrue(connector.isPresent(), "api connector dose not exist");
                template = Optional.of(new Template());
                template.get().setConnector(connector.get());
            } else {
                template = templateRepository.findById(dto.getTemplateId());

            }
        } else if (dto.getTemplateName() != null) {
            template = templateRepository.findByName(dto.getTemplateName());
        } else {
            template = templateRepository.findTemplateBySourceTypeAndType(SourceType.SYSTEM, ConnectorType.SMS);
        }
        Assert.isTrue(template.isPresent(), "wrong template");

        String realUrl = dto.getUrl().isEmpty() ? template.get().getUrl() : dto.getUrl();

        PushResponseDto result = internalEvaluateMessage(
                template.get().getConnector(),
                customers,
                dto.getDepartmentId(),
                template.get(),
                dto.getParameters(),
                realUrl,
                isDebug,
                false
        );

        result.setTemplate(template.get());
        return result;
    }

    public PushResponseDto evaluate(MassPushRequestDto dto) {
        final Long tenantId = TenantContext.getCurrentTenant();
        Assert.notNull(tenantId, "missing tenantId");

        Boolean isDebug = dto.getIsDebug();
        List<Customer> customers = buildCustomers(tenantId, dto);
        Optional<Template> template = templateRepository.findById(dto.getTemplateId());

        if (!template.isPresent()) {
            throw new BadRequestException();
        }

        if (template.get().getSourceType() == SourceType.USER && template.get().getOrgId() != tenantId) {
            throw new BadRequestException();
        }

        String realUrl = dto.getUrl().isEmpty() ? template.get().getUrl() : dto.getUrl();

        return internalEvaluateMessage(
                template.get().getConnector(),
                customers,
                dto.getDepartmentId(),
                template.get(),
                dto.getParameters(),
                realUrl,
                isDebug,
                false
        );
    }

    public Boolean internalSendMessageAsync(
            Template template,
            List<Customer> customers,
            Long departmentId,
            Map<String, Object> params,
            String url,
            Boolean isDebug,
            Boolean isDetail,
            Boolean shouldShortUrl,
            PushMomentDto momentDto
    ) {
        if (params == null) {
            throw new BadRequestException();
        }
        String realUrl = url.isEmpty() ? template.getUrl() : url;
        internalSendMessage(template.getConnector(), template.getConnector().getOrgId(), customers, departmentId, template,
                null, params, realUrl, isDebug, isDetail, shouldShortUrl, momentDto, null, false);
        return true;
    }

    public PushResponseDto internalSendMessage(
            Connector connector,
            Long orgId,
            List<Customer> customers,
            Long departmentId,
            Template template,
            Map<String, Object> content,
            Map<String, Object> params,
            String url,
            Boolean isDebug,
            Boolean isDetail,
            Boolean shouldShortUrl,
            PushMomentDto momentDto,
            Long sceneId,
            Boolean skipLimiter
    ) {

        Assert.notNull(connector, "invalid connector");

        IConnectorManager connectorManager = connectorManagerMap.get(connector.getType().name());
        PushResponseDto responseDto = internalEvaluateMessage(
                connector,
                customers,
                departmentId,
                template,
                params,
                url,
                isDebug,
                shouldShortUrl
        );

        Map<String, Map<String, Object>> detailInformations = new HashMap<>();

        if (connector.getType() == ConnectorType.SMS && skipLimiter != null && !skipLimiter) {
            if (!smsAccountService.hasBalance(orgId, responseDto.getConsumedSmsNumber())) {
                throw new OverLimitException();
            }
            smsAccountService.consumer(orgId, responseDto.getConsumedSmsNumber());
        }
        for (Customer customer : customers) {
            Map<String, Object> mergedParams = buildNativeParams(params, url, customer, departmentId, shouldShortUrl);
            String shortUrl = (String) mergedParams.getOrDefault("_short", url);
            Map<String, Object> result = new HashMap<>();
            if (template != null) {
                if (template.getId() == null) {
                    connectorManager.sendApiMessage(connector, customer, departmentId, sceneId, shortUrl,
                            customer.getExternalUserId(), momentDto);
                } else {
                    result = connectorManager.sendTemplateMessage(orgId, connector, template, customer, mergedParams,
                            shortUrl, momentDto);
                }
            } else {
                result = connectorManager.sendTextMessage(orgId, connector, customer, content, mergedParams,
                        momentDto);
            }
            if (isDetail) {
                detailInformations.put(customer.getId().toString(), result);
            }
        }
        if (connector.getType() == ConnectorType.SMS) {
            smsAccountService.syncToDb(orgId);
        }
        responseDto.setDetailInformation(detailInformations);

        return responseDto;
    }

    public PushResponseDto internalEvaluateMessage(
            Connector connector,
            List<Customer> customers,
            Long departmentId,
            Template template,
            Map<String, Object> params,
            String url,
            Boolean isDebug,
            Boolean shouldShortUrl
    ) {
        int actualNumber = 0;
        int consumedSmsCount = 0;
        PushResponseDto responseDto = new PushResponseDto();

        IConnectorManager connectorManager = connectorManagerMap.get(connector.getType().name());
        for (Customer customer : customers) {
            if (connectorManager.isValidCustomer(customer)) {
                actualNumber += 1;
                Map<String, Object> mergedParams = buildNativeParams(params, url, customer, departmentId, shouldShortUrl);

                if (isDebug) {
                    responseDto.setDebugInformation(mergedParams);
                }
                if (connector.getType() == ConnectorType.SMS && template != null) { // sms 才需要计算短信数量
                    consumedSmsCount += connectorManager.evaluateCost(connector, template, customer, mergedParams);
                }
            }
        }
        responseDto.setActualNumber(actualNumber);
        responseDto.setConsumedSmsNumber(consumedSmsCount);

        return responseDto;
    }

//    public void webhookSend(Connector connector, ResponseTaskDetailDto sendData, DataWarningDto dataWarning) {
//        sendData.getData().forEach(data ->
//                data.setType(String.valueOf(QuestionType.valueOf(data.getType()).getText()))
//        );
//        sendData.setDataWarning(dataWarning);
//
//        WebhookBaseTaskDto taskDto = WebhookBaseTaskDto.builder()
//                .orgId(sendData.getOrgId())
//                .connectorId(connector.getId())
//                .url(connector.getGateway())
//                .method(connector.getHttpMethod())
//                .body(JsonHelper.toJson(sendData))
//                .build();
//
//        Push push = new Push();
//        push.setOrgId(taskDto.getOrgId());
//        push.setName(connector.getName());
//        push.setConnector(connector);
//        push.setType(ConnectorType.WEBHOOK);
//        push.setAddress(taskDto.getUrl());
//        push.setContent(taskDto.getBody());
//        push.setStatus(PushStatus.FAILED);
//        pushRepository.save(push);
//
//        taskDto.setPushId(push.getId());
//        log.info("webhook send data push:{} orgId:{} connectorId:{}", push.getId(), taskDto.getOrgId(), taskDto.getConnectorId());
//        webhookSendExecutor.performAsync(taskDto);
//    }
}
