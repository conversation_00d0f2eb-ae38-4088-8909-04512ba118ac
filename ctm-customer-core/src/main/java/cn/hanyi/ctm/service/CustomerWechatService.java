package cn.hanyi.ctm.service;

import cn.hanyi.ctm.dto.WechatOpenSyncCustomerStatusDto;
import cn.hanyi.ctm.entity.ThirdPartyTemplate;
import cn.hanyi.ctm.workertrigger.CtmTaskTrigger;
import cn.hanyi.ctm.workertrigger.dto.WechatOpenSyncCustomerInfoSingleDto;
import cn.hanyi.ctm.workertrigger.dto.WechatOpenSyncCustomerListDto;
import cn.hanyi.ctm.workertrigger.dto.WechatOpenSyncTemplateDto;
import org.befun.auth.constant.UserTaskType;
import org.befun.auth.service.OrganizationService;
import org.befun.auth.service.UserTaskService;
import org.befun.auth.service.auth.AuthWechatOpenService;
import org.befun.auth.service.auth.config.WechatOpenConfig;
import org.befun.core.entity.BaseEntity;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.befun.task.dto.TaskProgressDto;
import org.befun.task.entity.TaskProgress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class CustomerWechatService {

    @Autowired
    private AuthWechatOpenService authWechatOpenService;
    @Autowired
    private ThirdPartyTemplateService thirdPartyTemplateService;
    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private CtmTaskTrigger ctmTaskTrigger;
    @Autowired
    private UserTaskService userTaskService;

    public boolean enableAutoSyncCustomer() {
        return organizationService.enableAutoSyncCustomer(TenantContext.requireCurrentTenant());
    }

    public boolean asyncCustomer(long configId, boolean mock) {
        return asyncCustomer(TenantContext.getCurrentTenant(), TenantContext.getCurrentUserId(), configId, mock);
    }

    public boolean asyncCustomer(Long orgId, Long userId, long configId, boolean mock) {
        WechatOpenConfig config = authWechatOpenService.getConfig(configId);
        if (config == null || !config.isAuthorized()) {
            throw new BadRequestException("公众号未授权");
        }
        if (userTaskService.lastTaskIsCompleted(orgId, userId, UserTaskType.syncWechatOpenSyncCustomer)) {
            TaskProgress progress = userTaskService.createTask(orgId, userId, UserTaskType.syncWechatOpenSyncCustomer, 0,
                    Map.of("thirdpartyAuthId", configId, "appId", config.getAppId()));
            if (mock) {
                ctmTaskTrigger.wechatOpenSyncCustomerList(new WechatOpenSyncCustomerListDto(orgId, userId, progress.getId(), configId, "mockSync"));
            } else {
                ctmTaskTrigger.wechatOpenSyncCustomerList(new WechatOpenSyncCustomerListDto(orgId, userId, progress.getId(), configId, config.getAppId()));
            }
        } else {
            throw new BadRequestException("正在同步中");
        }
        return true;
    }

    public boolean asyncSingleCustomer(Long orgId, Long userId, long configId, String appId, String openId) {
        WechatOpenSyncCustomerInfoSingleDto dto = new WechatOpenSyncCustomerInfoSingleDto(orgId, userId, configId, appId, openId);
        ctmTaskTrigger.wechatOpenSyncCustomerInfoSingle(dto);
        return true;
    }

    public WechatOpenSyncCustomerStatusDto syncCustomerProgress(long configId) {
        Long orgId = TenantContext.getCurrentTenant();
        Long userId = TenantContext.getCurrentUserId();
        WechatOpenConfig config = authWechatOpenService.getConfig(configId);
        if (config == null || !config.isAuthorized()) {
            throw new BadRequestException("公众号未授权");
        }
        Long taskProgressId = userTaskService.lastTask(orgId, userId, UserTaskType.syncWechatOpenSyncCustomer).map(BaseEntity::getId).orElse(0L);
        TaskProgressDto progress = userTaskService.progress(taskProgressId, 60);
        return new WechatOpenSyncCustomerStatusDto(progress.getStatus().isCompleted());
    }

    public List<ThirdPartyTemplate> syncTemplate(long configId) {
        return thirdPartyTemplateService.syncTemplate(TenantContext.requireCurrentTenant(), configId);
    }

    public boolean asyncTemplate(Long orgId, Long userId, long configId) {
        return asyncTemplate(orgId, userId, configId, false);
    }

    public boolean asyncTemplateAll() {
        return asyncTemplate(TenantContext.requireCurrentTenant(), TenantContext.requireCurrentUserId(), null, true);
    }

    public boolean asyncTemplate(Long orgId, Long userId, Long configId, boolean all) {
        if (userTaskService.lastTaskIsCompleted(orgId, userId, UserTaskType.syncWechatOpenSyncTemplate)) {
            TaskProgress progress = userTaskService.createTask(orgId, userId, UserTaskType.syncWechatOpenSyncTemplate, 0, null);
            ctmTaskTrigger.wechatOpenSyncTemplate(new WechatOpenSyncTemplateDto(orgId, userId, progress.getId(), configId, all));
        } else {
            throw new BadRequestException("正在同步中");
        }
        return true;
    }

    public WechatOpenSyncCustomerStatusDto syncTemplateAllProgress() {
        Long orgId = TenantContext.getCurrentTenant();
        Long userId = TenantContext.getCurrentUserId();
        Long taskProgressId = userTaskService.lastTask(orgId, userId, UserTaskType.syncWechatOpenSyncTemplate).map(BaseEntity::getId).orElse(0L);
        TaskProgressDto progress = userTaskService.progress(taskProgressId,60);
        return new WechatOpenSyncCustomerStatusDto(progress.getStatus().isCompleted());
    }
}
