package cn.hanyi.ctm.service;

import cn.hanyi.ctm.entity.CustomerJourneyRecord;
import cn.hanyi.ctm.entity.CustomerJourneyRecordDto;
import cn.hanyi.ctm.repository.CustomerJourneyRecordsRepository;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.befun.auth.entity.Department;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.DepartmentService;
import org.befun.auth.service.UserService;
import org.befun.core.service.CustomEmbeddedService;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.service.NativeSqlHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class CustomerJourneyRecordService extends CustomEmbeddedService<CustomerJourneyRecord, CustomerJourneyRecordDto, CustomerJourneyRecordsRepository> {

    @Autowired
    private DepartmentService departmentService;
    @Autowired
    private UserService userService;
    @Autowired
    private NativeSqlHelper nativeSqlHelper;
    @Autowired
    private CustomerHistoryRecordService customerHistoryRecordService;

    @Override
    protected Object requireParent(long l) {
        return l;
    }

    @Override
    public void afterMapToDto(List<CustomerJourneyRecord> entity, List<CustomerJourneyRecordDto> dto) {
        if (CollectionUtils.isEmpty(dto)) {
            return;
        }
        Set<Long> userIds = new HashSet<>();
        Set<Long> departmentIds = new HashSet<>();
        Set<Long> journeyIds = new HashSet<>();
        dto.forEach(i -> {
            Optional.ofNullable(i.getDepartmentId()).ifPresent(departmentIds::add);
            Optional.ofNullable(i.getCreatedByUid()).ifPresent(userIds::add);
            Optional.ofNullable(i.getJourneyId()).ifPresent(journeyIds::add);
        });
        // 所有相关的部门信息
        Map<Long, Department> departmentMap = departmentService.getGroupMapByIds(departmentIds, Department::getId, Function.identity());
        // 所有相关的用户信息
        Map<Long, SimpleUser> userMap = userService.getSimpleMapByIds(userIds);
        // 所有相关的场景信息
        Map<Long, JourneyTitle> journeyMap = getJourneyMap(journeyIds);
        dto.forEach(i -> {
            Optional.ofNullable(userMap.get(i.getCreatedByUid())).ifPresent(j -> i.setCreatedByName(j.getTruename()));
            Optional.ofNullable(departmentMap.get(i.getDepartmentId())).ifPresent(j -> i.setDepartmentTitle(j.getTitle()));
            Optional.ofNullable(journeyMap.get(i.getJourneyId())).ifPresent(j -> i.setJourneyTitle(j.getJourneyName()));
        });
    }

    @Getter
    @Setter
    public static class JourneyTitle {
        private Long journeyId;
        private String journeyName;
    }

    private Map<Long, JourneyTitle> getJourneyMap(Set<Long> journeyIds) {
        Map<Long, JourneyTitle> journeyMap = new HashMap<>();
        if (CollectionUtils.isEmpty(journeyIds)) {
            return journeyMap;
        }
        String sql = String.format("SELECT id journeyId,journey_name journeyName FROM journey_publish where id in (%s)", journeyIds.stream().map(Objects::toString).collect(Collectors.joining(",")));
        List<JourneyTitle> list = nativeSqlHelper.queryListObject(sql, JourneyTitle.class);
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(i -> journeyMap.put(i.getJourneyId(), i));
        }
        return journeyMap;
    }

    private String getDepartmentTitle(Long departmentId) {
        Department department = null;
        if (departmentId != null && departmentId > 0) {
            department = departmentService.get(departmentId);
        }
        if (department != null) {
            return department.getTitle();
        }
        return null;
    }

    private String getJourneyTitle(Long journeyId) {
        if (journeyId == null || journeyId <= 0) {
            return null;
        }
        return Optional.ofNullable(getJourneyMap(Set.of(journeyId)).get(journeyId)).map(JourneyTitle::getJourneyName).orElse(null);
    }

    public List<CustomerJourneyRecord> addJourneyRecord(Long userId, Long taskProgressId, List<Long> customerIds, Long departmentId, Long journeyId, Map<String, Object> urlCustomParams) {
        String departmentTitle = getDepartmentTitle(departmentId);
        String journeyTitle = getJourneyTitle(journeyId);
        String params = MapUtils.isEmpty(urlCustomParams) ? null : JsonHelper.toJson(urlCustomParams);
        List<CustomerJourneyRecord> list = customerIds.stream().map(cid -> {
            CustomerJourneyRecord entity = new CustomerJourneyRecord();
            entity.setTaskProgressId(taskProgressId);
            entity.setCustomerId(cid);
            entity.setJourneyId(journeyId);
            entity.setJourneyTitle(journeyTitle);
            entity.setDepartmentId(departmentId);
            entity.setDepartmentTitle(departmentTitle);
            entity.setCreatedByUid(userId);
            entity.setUrlParams(params);
            return entity;
        }).collect(Collectors.toList());
        if (!list.isEmpty()) {
            repository.saveAll(list);
            list.forEach(i -> customerHistoryRecordService.addByCreateJourney(userId, i.getCustomerId(), i.getId(), journeyTitle, departmentId));
            return list;
        }
        return List.of();
    }

    /**
     * 开放接口新建
     */
    public CustomerJourneyRecord addJourneyRecord(Long userId, Long customerId, Long journeyId, Department department) {
        Long departmentId = department == null ? null : department.getId();
        String departmentTitle = department == null ? null : department.getTitle();
        String journeyTitle = getJourneyTitle(journeyId);
        CustomerJourneyRecord entity = new CustomerJourneyRecord();
        entity.setCustomerId(customerId);
        entity.setJourneyId(journeyId);
        entity.setJourneyTitle(journeyTitle);
        entity.setDepartmentId(departmentId);
        entity.setDepartmentTitle(departmentTitle);
        entity.setCreatedByUid(userId);
        repository.save(entity);
        customerHistoryRecordService.addByCreateJourney(userId, customerId, entity.getId(), journeyTitle, departmentId);
        return entity;
    }

    /**
     * 开放接口新建
     */
    public CustomerJourneyRecord addJourneyRecord(Long userId, Long customerId, Long journeyId, Long departmentId) {
        String departmentTitle = getDepartmentTitle(departmentId);
        String journeyTitle = getJourneyTitle(journeyId);
        CustomerJourneyRecord entity = new CustomerJourneyRecord();
        entity.setCustomerId(customerId);
        entity.setJourneyId(journeyId);
        entity.setJourneyTitle(journeyTitle);
        entity.setDepartmentId(departmentId);
        entity.setDepartmentTitle(departmentTitle);
        entity.setCreatedByUid(userId);
        repository.save(entity);
        customerHistoryRecordService.addByCreateJourney(userId, customerId, entity.getId(), journeyTitle, departmentId);
        return entity;
    }
}
