package cn.hanyi.ctm.service;

import cn.hanyi.ctm.constant.connector.ConnectorType;
import cn.hanyi.ctm.dto.TemplateInfoDto;
import cn.hanyi.ctm.dto.wechattemplate.WechatOpenTemplateDto;
import cn.hanyi.ctm.dto.wechattemplate.WechatOpenTemplateStatusDto;
import cn.hanyi.ctm.dto.wechattemplate.WechatTemplateCreateDto;
import cn.hanyi.ctm.entity.ThirdPartyTemplate;
import cn.hanyi.ctm.entity.ThirdPartyTemplateDto;
import cn.hanyi.ctm.entity.WechatTemplate;
import cn.hanyi.ctm.entity.WechatTemplateDto;
import cn.hanyi.ctm.repository.WechatTemplateRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.dto.auth.WechatOpenAuthDto;
import org.befun.auth.service.auth.AuthWechatOpenService;
import org.befun.auth.service.auth.config.WechatOpenConfig;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.exception.BadRequestException;
import org.befun.core.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class WechatTemplateService extends BaseService<WechatTemplate, WechatTemplateDto, WechatTemplateRepository> {

    @Autowired
    private ThirdPartyTemplateService thirdPartyTemplateService;
    @Autowired
    private AuthWechatOpenService authWechatOpenService;

    @Override
    public <S extends BaseEntityDTO<WechatTemplate>> WechatTemplateDto create(S data) {
        WechatTemplateCreateDto dto = (WechatTemplateCreateDto) data;
        ThirdPartyTemplate thirdPartyTemplate = thirdPartyTemplateService.getWithFilter(dto.getSourceThirdpartyTemplateId());
        if (thirdPartyTemplate == null || thirdPartyTemplate.getConnectorType() != ConnectorType.PLATFORM) {
            throw new BadRequestException("模版来源不存在");
        }
        WechatTemplate entity = new WechatTemplate();
        entity.setName(thirdPartyTemplate.getName());
        entity.setExample(dto.getExample());
        entity.setContent(dto.getContent());
        entity.setParameters(thirdPartyTemplate.getParameters());
        entity.setSourceThirdpartyTemplateId(dto.getSourceThirdpartyTemplateId());
        entity.getThirdpartyTemplateIds().add(dto.getSourceThirdpartyTemplateId());
        save(entity);
        return mapToDto(entity);
    }

    public List<WechatOpenTemplateDto> getAllWechatOpenTemplates() {
        List<WechatOpenTemplateDto> result = new ArrayList<>();
        List<WechatOpenAuthDto> list = authWechatOpenService.getList("cem");
        if (CollectionUtils.isNotEmpty(list)) {
            List<ThirdPartyTemplate> templates = thirdPartyTemplateService.getAllWechatTemplate();
            Map<Long/*thirdpartyAuthId*/, List<ThirdPartyTemplateDto>> templateMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(templates)) {
                templates.forEach(t -> templateMap.computeIfAbsent(t.getThirdpartyAuthId(), k -> new ArrayList<>()).add(thirdPartyTemplateService.mapToDto(t)));
            }
            list.forEach(i -> {
                if (i.getConfig() != null) {
                    result.add(new WechatOpenTemplateDto(i.getConfig(), templateMap.get(i.getConfig().getConfigId())));
                }
            });
        }
        return result;
    }

    public WechatOpenTemplateStatusDto bindStatus(long id) {
        WechatTemplate entity = requireWithFilter(id);
        List<WechatOpenTemplateDto> allWechatOpenTemplates = getAllWechatOpenTemplates();
        Map<Long/*thirdPartyTemplateId*/, WechatOpenTemplateDto> wechatOpenTemplateMap = new HashMap<>();
        Map<Long/*thirdPartyTemplateId*/, ThirdPartyTemplateDto> templateMap = new HashMap<>();

        allWechatOpenTemplates.forEach(i -> {
            if (CollectionUtils.isNotEmpty(i.getTemplates())) {
                i.getTemplates().forEach(j -> {
                    wechatOpenTemplateMap.put(j.getId(), i);
                    templateMap.put(j.getId(), j);
                });
            }
        });

        WechatOpenTemplateStatusDto status = new WechatOpenTemplateStatusDto();
        status.setWechatTemplate(mapToDto(entity));
        WechatOpenTemplateDto sourceConfig = wechatOpenTemplateMap.get(entity.getSourceThirdpartyTemplateId());
        if (sourceConfig != null && sourceConfig.getConfig() != null) {
            status.setSourceConfig(sourceConfig.getConfig());
            status.setSourceTemplate(templateMap.get(entity.getSourceThirdpartyTemplateId()));
        }
        Set<Long> bindConfigIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(entity.getThirdpartyTemplateIds())) {
            entity.getThirdpartyTemplateIds().forEach(i -> {
                WechatOpenTemplateDto config = wechatOpenTemplateMap.get(i);
                ThirdPartyTemplateDto thirdPartyTemplate = templateMap.get(i);
                if (config != null && !bindConfigIds.contains(config.getConfig().getConfigId()) && thirdPartyTemplate != null) {
                    config.setBindTemplate(thirdPartyTemplate);
                    bindConfigIds.add(config.getConfig().getConfigId());
                    status.getBind().add(config);
                }
            });
        }
        allWechatOpenTemplates.forEach(i -> {
            if (!bindConfigIds.contains(i.getConfig().getConfigId())) {
                status.getUnbind().add(i);
            }
        });
        return status;
    }

    @Transactional
    public WechatOpenTemplateStatusDto bindUpdate(long id) {
        WechatTemplate entity = requireWithFilter(id);

        WechatOpenTemplateStatusDto status = bindStatus(id);
        List<WechatOpenTemplateDto> unbind = status.getUnbind();
        List<WechatOpenTemplateDto> bind = status.getBind();
        String sourceName = getSourceTemplateName(status);
        if (StringUtils.isNotEmpty(sourceName) && CollectionUtils.isNotEmpty(unbind)) {
            Iterator<WechatOpenTemplateDto> iterator = unbind.iterator();
            while (iterator.hasNext()) {
                WechatOpenTemplateDto i = iterator.next();
                if (CollectionUtils.isNotEmpty(i.getTemplates())) {
                    ThirdPartyTemplateDto nameMatched = i.getTemplates().stream().filter(j -> sourceName.equals(j.getName())).findFirst().orElse(null);
                    if (nameMatched != null) {
                        i.setBindTemplate(nameMatched);
                        bind.add(i);
                        iterator.remove();
                    }
                }
            }
        }
        List<Long> newBindIds = bind.stream().filter(i -> i.getBindTemplate() != null).map(i -> i.getBindTemplate().getId()).collect(Collectors.toList());
        entity.setThirdpartyTemplateIds(newBindIds);
        repository.save(entity);
        return status;
    }

    private String getSourceTemplateName(WechatOpenTemplateStatusDto status) {
        ThirdPartyTemplateDto sourceTemplate = status.getSourceTemplate();
        if (sourceTemplate != null) {
            return sourceTemplate.getName();
        }
        if (CollectionUtils.isNotEmpty(status.getBind())) {
            ThirdPartyTemplateDto bind = status.getBind().get(0).getBindTemplate();
            if (bind != null) {
                return bind.getName();
            }
        }
        return null;
    }
}
