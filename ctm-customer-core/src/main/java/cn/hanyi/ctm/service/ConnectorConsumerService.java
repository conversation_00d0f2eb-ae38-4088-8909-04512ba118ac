package cn.hanyi.ctm.service;

import cn.hanyi.ctm.constant.connector.ConnectorPushCondition;
import cn.hanyi.ctm.constant.connector.ConnectorPushType;
import cn.hanyi.ctm.dto.connector.ConnectorConsumerBotContentDto;
import cn.hanyi.ctm.dto.connector.ConnectorConsumerRelationIdsDto;
import cn.hanyi.ctm.dto.connector.ConnectorParamsDto;
import cn.hanyi.ctm.dto.connector.ConsumerConnectorDto;
import cn.hanyi.ctm.entity.Connector;
import cn.hanyi.ctm.entity.ConnectorConsumer;
import cn.hanyi.ctm.entity.ConnectorConsumerDto;
import cn.hanyi.ctm.repository.*;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.core.dto.ResourcePageResponseDto;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.exception.BadRequestException;
import org.befun.core.exception.EntityNotFoundException;
import org.befun.core.rest.query.GenericSpecification;
import org.befun.core.service.BaseService;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Service
public class ConnectorConsumerService extends BaseService<ConnectorConsumer, ConnectorConsumerDto, ConnectorConsumerRepository> {

    @Autowired
    private ConnectorService connectorService;

    @Autowired
    PushRepository pushRepository;

    @Autowired
    PushLogRepository pushLogRepository;

    @Autowired
    ConnectorRepository connectorRepository;

    @Autowired
    CustomerRepository customerRepository;

    @Autowired
    ThirdPartyCustomerRepository thirdPartyCustomerRepository;

    @Autowired
    ConnectorConsumerRepository connectorConsumerRepository;

    /**
     * 需要在推送的connector
     */
    public List<Connector> webhookConnector(Set<Long> relationIds, ConnectorPushType pushType, ConnectorPushCondition pushCondition) {
        List<Connector> list = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(relationIds)) {
            List<ConnectorConsumer> consumers = connectorConsumerRepository.findByRelationIdInAndEnable(relationIds, true);
            if (CollectionUtils.isNotEmpty(consumers)) {
                Set<Long> ids = consumers.stream().map(c -> c.getConnector().getId()).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(ids)) {
                    List<Connector> connectors = connectorRepository.findByIdInAndPushType(ids, pushType);
                    if (CollectionUtils.isNotEmpty(connectors)) {
                        connectors.forEach(c -> {
                            if (CollectionUtils.isNotEmpty(c.getCondition()) && c.getCondition().contains(pushCondition)) {
                                list.add(c);
                            }
                        });
                    }
                }
            }
        }
        return list;
    }

    /**
     * 开启/关闭推送
     *
     * @return
     */
    public ConnectorConsumer consumerEnable(Long id, boolean enable) {
        ConnectorConsumer connectorConsumer = connectorConsumerRepository.findById(id).orElseThrow(EntityNotFoundException::new);
        connectorConsumer.setEnable(enable);
        return connectorConsumerRepository.save(connectorConsumer);
    }

    public ConnectorConsumer getConsumer(Connector connector) {
        return connectorConsumerRepository.findByConnector(connector).orElse(null);
    }

    public ConnectorConsumer getConsumerByConnectorAndRelationId(Connector connector, Long relationId) {
        return connectorConsumerRepository.findByConnectorAndRelationId(connector, relationId).orElse(null);
    }

    public ConnectorConsumer getOrCreateConsumer(Connector connector, Long relationId) {
        AtomicReference<ConnectorConsumer> connectorConsumer = new AtomicReference<>();
        connectorConsumerRepository.findByConnector(connector).ifPresentOrElse(
                c -> connectorConsumer.set(c),
                () -> {
                    connectorConsumer.set(connectorConsumerRepository.save(new ConnectorConsumer(connector, relationId, true)));
                }
        );

        return connectorConsumer.get();
    }

    public ConnectorConsumer getOrCreateConsumer(Connector connector, Long relationId, ConnectorConsumerBotContentDto template) {
        AtomicReference<ConnectorConsumer> connectorConsumer = new AtomicReference<>();
        connectorConsumerRepository.findByConnector(connector).ifPresentOrElse(
                c -> {
                    c.setContent(JsonHelper.toJson(template));
                    connectorConsumer.set(c);
                },
                () -> {
                    connectorConsumer.set(connectorConsumerRepository.save(new ConnectorConsumer(connector, relationId, true, JsonHelper.toJson(template))));
                }
        );

        return connectorConsumer.get();
    }

    /**
     * 修改推送
     *
     * @return
     */
    public ConnectorConsumer consumerUpdate(Long id, ConsumerConnectorDto consumerDto) {
        ConnectorConsumer connectorConsumer = connectorConsumerRepository.findById(id).orElseThrow(EntityNotFoundException::new);
        connectorConsumer.setConnector(connectorService.require(consumerDto.getConnectorId()));
        return connectorConsumerRepository.save(connectorConsumer);
    }


    /**
     * 创建推送
     *
     * @return
     */
    public List<ConnectorConsumer> consumer(Long connectorId, ConnectorConsumerRelationIdsDto connectorConsumerRelationIdsDto) {
        Connector connector = connectorService.require(connectorId);
        List<ConnectorConsumer> connectorConsumers = new ArrayList<>();
        connectorConsumerRelationIdsDto.getRelationIds().forEach(relationId -> {
            connectorConsumerRepository.findByConnectorAndRelationId(connector, relationId).ifPresentOrElse(
                    c -> {
                        if (!connectorConsumerRelationIdsDto.getParams().isEmpty()) {
                            c.setParams(connectorConsumerRelationIdsDto.getParams());
                            connectorConsumerRepository.save(c);
                            connectorConsumers.add(c);
                        }
                    },
                    () -> connectorConsumers.add(connectorConsumerRepository.save(new ConnectorConsumer(connector, relationId, connectorConsumerRelationIdsDto.getParams())))
            );
        });
        return connectorConsumers;
    }


    /**
     * 创建推送
     *
     * @param connector
     * @return
     */
    public ConnectorConsumer consumer(Connector connector, Long relationId, Boolean enable) {
        return connectorConsumerRepository.save(new ConnectorConsumer(connector, relationId, enable));
    }

    /**
     * 创建推送
     *
     * @param connector
     * @return
     */
    public ConnectorConsumer consumer(Connector connector, Long relationId, Boolean enable, List<ConnectorParamsDto> params) {
        return connectorConsumerRepository.save(new ConnectorConsumer(connector, relationId, enable, params));
    }


    /**
     * 创建推送
     *
     * @param connector
     * @return
     */
    public ConnectorConsumer consumer(Connector connector, Long relationId, Boolean enable, String content) {
        return connectorConsumerRepository.save(new ConnectorConsumer(connector, relationId, enable, content));
    }

    /**
     * 获取推送详情
     */
    public ResourcePageResponseDto consumers(ResourceEntityQueryDto<ConnectorConsumerDto> queryDto) {
        GenericSpecification specification = new GenericSpecification(queryDto);
        PageRequest pageRequest = PageRequest.of(queryDto.getPage() - 1, queryDto.getLimit(), queryDto.getSorts());
        Page<ConnectorConsumer> all = connectorConsumerRepository.findAll(specification, pageRequest);

        List<ConnectorConsumer> allContent = new ArrayList<>(all.getContent());

        long total = all.getTotalElements();

        Iterator<ConnectorConsumer> it = allContent.iterator();
        while (it.hasNext()) {
            ConnectorConsumer c = it.next();
            if (c.getConnector() == null) {
                it.remove();
                total--;
                connectorConsumerRepository.deleteById(c.getId());
            }
        }

        Page<ConnectorConsumerDto> allList = new PageImpl<>(mapToDto(allContent), all.getPageable(), total);
        return new ResourcePageResponseDto(allList);
    }

    /**
     * 根据做了推送的Id获取推送详情
     *
     * @return
     */
    public List<ConnectorConsumer> consumers(Set<Long> relationsIds) {
        return connectorConsumerRepository.findByRelationIdInAndEnable(relationsIds, true);
    }

    /**
     * 做了关联的推送不能删除
     */
    public void tryDelete(Connector connector) {
        if (connector != null) {
            connectorConsumerRepository.findByConnector(connector).ifPresent(
                    connectorConsumer -> {
                        throw new BadRequestException("该推送已经做了关联，不能删除");
                    }
            );
        }
    }

    /**
     * 获取推送
     *
     * @return
     */
    public ConnectorConsumer getConsumer(Long id) {
        return connectorConsumerRepository.findById(id).orElse(null);
    }

    /**
     * 删除推送时把推送配置删除
     */
    public void deleteConsumers(Connector connector) {
        connectorConsumerRepository.deleteByConnector(connector);
    }


    /**
     * 删除推送
     */
    public void deleteConsumers(Long id) {
        Optional.ofNullable(id).ifPresent(i -> {
            connectorConsumerRepository.deleteById(i);
        });
    }

}
