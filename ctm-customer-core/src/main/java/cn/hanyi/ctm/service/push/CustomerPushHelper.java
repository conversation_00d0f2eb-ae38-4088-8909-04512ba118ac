//package cn.hanyi.ctm.service.push;
//
//import cn.hanyi.ctm.constant.customer.CustomerPushRelationTypes;
//import cn.hanyi.ctm.constant.customer.CustomerPushStatus;
//import cn.hanyi.ctm.constant.customer.CustomerPushType;
//import cn.hanyi.ctm.dto.customer.push.*;
//import cn.hanyi.ctm.entity.CustomerPushRecord;
//import cn.hanyi.ctm.service.CustomerPushRecordService;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.befun.core.utils.JsonHelper;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.PostConstruct;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.Optional;
//import java.util.function.Function;
//import java.util.stream.Collectors;
//
//@SuppressWarnings("unchecked")
//@Slf4j
//@Service
//public class CustomerPushHelper {
//
//    @Autowired
//    private CustomerSmsPushService customerSmsPushService;
//    @Autowired
//    private CustomerWechatPushService customerWechatPushService;
//    @Autowired
//    private CustomerApiPushService customerApiPushService;
//    @Autowired
//    private CustomerPushRecordService customerPushRecordService;
//
//    private Map<CustomerPushType, CustomerPushService<?>> customerPushServiceMap;
//
//    @PostConstruct
//    public void postConstruct() {
//        customerPushServiceMap = new HashMap<>();
//        Optional.ofNullable(customerSmsPushService).ifPresent(i -> customerPushServiceMap.put(CustomerPushType.SMS, i));
//        Optional.ofNullable(customerWechatPushService).ifPresent(i -> customerPushServiceMap.put(CustomerPushType.WECHAT, i));
//        Optional.ofNullable(customerApiPushService).ifPresent(i -> customerPushServiceMap.put(CustomerPushType.API, i));
//    }
//
//    /**
//     * 通过问卷渠道发送短信给客户
//     */
//    public boolean pushSmsBySurveyChannel(PushSurveyChannelSmsDto info, List<Long> surveySendRecordIds, Function<Long/*surveySendRecordId*/, CustomerPushTargetDto> getTarget) {
//        PushSmsDto dto = info.mapToPushDto();
//        List<Long> pushIds = surveySendRecordIds.stream().map(recordId -> {
//            dto.setRelationId(recordId);
//            CustomerPushTargetDto target = getTarget.apply(recordId);
//            return customerPushRecordService.add(info.getOrgId(), target, CustomerPushType.SMS, dto).getId();
//        }).collect(Collectors.toList());
//        return customerSmsPushService.addPushTask(info.isAppend(), info.getTaskId(), pushIds, info.getDelay());
//    }
//
//    /**
//     * 通过问卷渠道发送微信给客户
//     */
//    public boolean pushWechatBySurveyChannel(PushSurveyChannelWechatDto info, List<Long> surveySendRecordIds, Function<Long/*surveySendRecordId*/, CustomerPushTargetDto> getTarget) {
//        PushWechatDto dto = info.mapToPushDto();
//        List<Long> pushIds = surveySendRecordIds.stream().map(recordId -> {
//            dto.setRelationId(recordId);
//            CustomerPushTargetDto target = getTarget.apply(recordId);
//            return customerPushRecordService.add(info.getOrgId(), target, CustomerPushType.WECHAT, dto).getId();
//        }).collect(Collectors.toList());
//        return customerSmsPushService.addPushTask(info.isAppend(), info.getTaskId(), pushIds, info.getDelay());
//    }
//
//    /**
//     * 通过旅程互动发送(短信|微信|Api)给客户
//     */
//    public void pushByJourney(List<PushJourneyBaseDto> info, List<Long> journeyRecordIds, Function<Long/*journeyRecordId*/, CustomerPushTargetDto> getTarget) {
//        PushSmsDto sms = null;
//        PushWechatDto wechat = null;
//        PushApiDto api = null;
//        for (PushJourneyBaseDto base : info) {
//            if (base instanceof PushJourneySmsDto) {
//                sms = ((PushJourneySmsDto) base).mapToPushDto();
//            } else if (base instanceof PushJourneyWechatDto) {
//                wechat = ((PushJourneyWechatDto) base).mapToPushDto();
//            } else if (base instanceof PushJourneyApiDto) {
//                api = ((PushJourneyApiDto) base).mapToPushDto();
//            }
//        }
//        for (Long recordId : journeyRecordIds) {
//            CustomerPushTargetDto target = getTarget.apply(recordId);
//            if (sms != null) {
//                sms.setRelationId(recordId);
//                customerSmsPushService.addPush(sms.getOrgId(), target, sms);
//            }
//            if (wechat != null) {
//                wechat.setRelationId(recordId);
//                customerWechatPushService.addPush(wechat.getOrgId(), target, wechat);
//            }
//            if (api != null) {
//                api.setRelationId(recordId);
//                customerApiPushService.addPush(api.getOrgId(), target, api);
//            }
//        }
//    }
//
//    /**
//     * 通过旅程互动发送短信给客户
//     */
//    public void pushSmsByJourney(PushJourneySmsDto info, List<Long> journeyRecordIds, Function<Long/*journeyRecordId*/, CustomerPushTargetDto> getTarget) {
//        pushByJourney(List.of(info), journeyRecordIds, getTarget);
//    }
//
//    /**
//     * 通过旅程互动发送微信给客户
//     */
//    public void pushWechatByJourney(PushJourneyWechatDto info, List<Long> journeyRecordIds, Function<Long/*journeyRecordId*/, CustomerPushTargetDto> getTarget) {
//        pushByJourney(List.of(info), journeyRecordIds, getTarget);
//    }
//
//    /**
//     * 通过旅程互动发送Api给客户
//     */
//    public void pushApiByJourney(PushJourneyApiDto info, List<Long> journeyRecordIds, Function<Long/*journeyRecordId*/, CustomerPushTargetDto> getTarget) {
//        pushByJourney(List.of(info), journeyRecordIds, getTarget);
//    }
//
//    public boolean pushSmsByCustomer(Long orgId, Long customerId, Long surveyId,
//                                     String templateName, String templateContent,
//                                     Map<String, Object> contentParams, Map<String, Object> urlParams,
//                                     CustomerPushRelationTypes relationType, Long relationId) {
//        return pushSmsByCustomers(orgId, List.of(customerId), surveyId, templateName, templateContent, contentParams, urlParams, relationType, relationId);
//    }
//
//    public boolean pushSmsByCustomers(Long orgId, List<Long> customerIds, Long surveyId,
//                                      String templateName, String templateContent,
//                                      Map<String, Object> contentParams, Map<String, Object> urlParams,
//                                      CustomerPushRelationTypes relationType, Long relationId) {
//        if (CollectionUtils.isNotEmpty(customerIds)) {
//            PushSmsDto dto = new PushSmsDto();
//            dto.setSurveyId(surveyId);
//            dto.setTemplateName(templateName);
//            dto.setTemplateContent(templateContent);
//            dto.setContentParams(contentParams);
//            dto.setUrlParams(urlParams);
//            dto.setRelationType(relationType.name());
//            dto.setRelationId(relationId);
//            customerIds.forEach(customerId -> customerSmsPushService.addPush(orgId, customerId, dto));
//            return true;
//        }
//        return false;
//    }
//
//    public boolean pushWechatByCustomer(Long orgId, Long customerId, Long surveyId,
//                                        String appId, String templateId, Map<String, Object> templateContent,
//                                        Map<String, Object> contentParams, Map<String, Object> urlParams,
//                                        CustomerPushRelationTypes relationType, Long relationId) {
//        return pushWechatByCustomers(orgId, List.of(customerId), surveyId, appId, templateId, templateContent, contentParams, urlParams, relationType, relationId);
//    }
//
//    public boolean pushWechatByCustomers(Long orgId, List<Long> customerIds, Long surveyId,
//                                         String appId, String templateId, Map<String, Object> templateContent,
//                                         Map<String, Object> contentParams, Map<String, Object> urlParams,
//                                         CustomerPushRelationTypes relationType, Long relationId) {
//        if (CollectionUtils.isNotEmpty(customerIds)) {
//            PushWechatDto dto = new PushWechatDto();
//            dto.setSurveyId(surveyId);
//            dto.setAppId(appId);
//            dto.setTemplateId(templateId);
//            dto.setTemplateContent(templateContent);
//            dto.setContentParams(contentParams);
//            dto.setUrlParams(urlParams);
//            dto.setRelationType(relationType.name());
//            dto.setRelationId(relationId);
//            customerIds.forEach(customerId -> customerWechatPushService.addPush(orgId, customerId, dto));
//            return true;
//        }
//        return false;
//    }
//
//
//    public boolean pushApiByCustomer(Long orgId, Long customerId, Long surveyId,
//                                     String url, Map<String, Object> templateContent,
//                                     Map<String, Object> contentParams, Map<String, Object> urlParams,
//                                     CustomerPushRelationTypes relationType, Long relationId) {
//        return pushApiByCustomers(orgId, List.of(customerId), surveyId, url, templateContent, contentParams, urlParams, relationType, relationId);
//    }
//
//    public boolean pushApiByCustomers(Long orgId, List<Long> customerIds, Long surveyId,
//                                      String url, Map<String, Object> templateContent,
//                                      Map<String, Object> contentParams, Map<String, Object> urlParams,
//                                      CustomerPushRelationTypes relationType, Long relationId) {
//        if (CollectionUtils.isNotEmpty(customerIds)) {
//            PushApiDto dto = new PushApiDto();
//            dto.setSurveyId(surveyId);
//            dto.setUrl(url);
//            dto.setTemplateContent(templateContent);
//            dto.setContentParams(contentParams);
//            dto.setUrlParams(urlParams);
//            dto.setRelationType(relationType.name());
//            dto.setRelationId(relationId);
//            customerIds.forEach(customerId -> customerApiPushService.addPush(orgId, customerId, dto));
//            return true;
//        }
//        return false;
//    }
//
//    public <P extends BasePushDto> P parsePushParam(CustomerPushRecord record) {
//        if (record != null) {
//            return (P) Optional.ofNullable(customerPushServiceMap.get(record.getPushType())).map(i -> JsonHelper.toObject(record.getPushParams(), i.getPushParamClass())).orElse(null);
//        }
//        return null;
//    }
//
//
//    public void realPush(Long pushId) {
//        CustomerPushRecord record = customerPushRecordService.get(pushId);
//        if (record == null) {
//            log.warn("客户推送记录不存在, pushId={}", pushId);
//            return;
//        }
//        if (record.getPushStatus() != null && record.getPushStatus() == CustomerPushStatus.SUCCESS) {
//            log.warn("客户推送已成功, pushId={}", pushId);
//            return;
//        }
//        CustomerPushService<?> pushService = customerPushServiceMap.get(record.getPushType());
//        if (pushService == null) {
//            log.warn("客户推送类型不存在, pushId={}, pushType={}", pushId, record.getPushType());
//            return;
//        }
//        pushService.push(record);
//    }
//
//    public void realPush(List<Long> pushIds) {
//        if (CollectionUtils.isEmpty(pushIds)) {
//            return;
//        }
//        pushIds.forEach(this::realPush);
//    }
//
//}
