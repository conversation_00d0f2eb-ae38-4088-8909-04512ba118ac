package cn.hanyi.ctm.service;

import cn.hanyi.ctm.constant.RecordType;
import cn.hanyi.ctm.entity.CustomerHistoryRecord;
import cn.hanyi.ctm.entity.CustomerHistoryRecordDto;
import cn.hanyi.ctm.repository.CustomerHistoryRecordsRepository;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.befun.auth.entity.Department;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.DepartmentService;
import org.befun.auth.service.UserService;
import org.befun.core.service.CustomEmbeddedService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class CustomerHistoryRecordService extends CustomEmbeddedService<CustomerHistoryRecord, CustomerHistoryRecordDto, CustomerHistoryRecordsRepository> {

    @Autowired
    private UserService userService;
    @Autowired
    private DepartmentService departmentService;

    @Override
    protected Object requireParent(long l) {
        return l;
    }

    /**
     * [createUserId | createUser] 不能都为null
     */
    public void addByCreateCustomer(Long createUserId, SimpleUser createUser, Long customerId) {
        if (customerId == null || (createUser = getSimpleUser(createUserId, createUser)) == null) {
            return;
        }
        String content = buildAddCustomerContent(createUser.getTruename());
        addCustomerHistoryRecord(createUser.getId(), customerId, RecordType.ADDCUSTOMER, null, null, content);
    }

    /**
     * [createUserId | createUser] 不能都为null
     */
    public void addByUpdateCustomer(Long createUserId, Long customerId, Map<String, Pair<String, String>> changes) {
        if (customerId == null || MapUtils.isEmpty(changes)) {
            return;
        }
        String content = buildUpdateCustomerContent(changes);
        addCustomerHistoryRecord(createUserId, customerId, RecordType.UPDATECUSTOMER, null, null, content);
    }

    /**
     * [createUserId | createUser] 不能都为null
     */
    public void addByCreateJourney(Long createUserId, Long customerId, Long journeyRecordId, String journeyName, Long departmentId) {
        if (customerId == null) {
            return;
        }
        SimpleUser user = getSimpleUser(createUserId, null);
        if (user == null) {
            return;
        }
        Department department = departmentService.get(departmentId);
        String content = buildAddJourneyContent(user.getTruename(), journeyName, department == null ? null : department.getTitle());
        addCustomerHistoryRecord(createUserId, customerId, RecordType.ADDJOURNEY, null, journeyRecordId, content);
    }

    public void addBySendSurveyInteraction(Long createUserId, Long customerId, Long surveyId, String surveyName) {
        if (customerId == null) {
            return;
        }
        String content = buildSendSurveyInteractionContent(surveyName);
        addCustomerHistoryRecord(createUserId, customerId, RecordType.SENDSURVEY, surveyId, null, content);
    }

    public void addBySendSurveyChannel(Long createUserId, Long customerId, Long surveyId, String surveyName, String surveyChannelName, String surveyChannelType) {
        if (customerId == null) {
            return;
        }
        String content = buildSendSurveyChannelContent(surveyName, surveyChannelName, surveyChannelType);
        addCustomerHistoryRecord(createUserId, customerId, RecordType.SENDSURVEY, surveyId, null, content);
    }

    public void addBySubmitSurvey(Long customerId, String customerName, Long surveyId, String surveyName) {
        if (customerId == null) {
            return;
        }
        String content = buildResponseSurveyContent(customerName, surveyName);
        addCustomerHistoryRecord(null, customerId, RecordType.FILLEDSURVEY, surveyId, null, content);
    }

    private String buildAddCustomerContent(String username) {
        return String.format("%s 添加了客户", username);
    }

    private String buildUpdateCustomerContent(Map<String, Pair<String, String>> changes) {
        if (MapUtils.isNotEmpty(changes)) {
            return changes.entrySet().stream().map(i -> String.format("%s：由 %s 修改为 %s", i.getKey(), i.getValue().getLeft(), i.getValue().getRight())).collect(Collectors.joining(";"));
        }
        return null;
    }

    private String buildAddJourneyContent(String username, String journeyName, String departmentName) {
        if (StringUtils.isEmpty(departmentName)) {
            return String.format("%s 记录历程 %s", username, journeyName);
        }
        return String.format("%s 记录历程 %s@%s", username, journeyName, departmentName);
    }

    private String buildSendSurveyInteractionContent(String surveyName) {
        return String.format("发送问卷 %s ", surveyName);
    }

    private String buildSendSurveyChannelContent(String surveyName, String surveyChannelName, String surveyChannelType) {
        return String.format("通过 %s(%s) 发送问卷 %s ", surveyChannelName, surveyChannelType, surveyName);
    }

    private String buildResponseSurveyContent(String customerName, String surveyName) {
        return String.format("%s 填答问卷 %s", customerName, surveyName);
    }

    private SimpleUser getSimpleUser(Long userId, SimpleUser user) {
        return Optional.ofNullable(user).orElseGet(() -> Optional.ofNullable(userId).flatMap(i -> userService.getSimple(i)).orElse(null));
    }

    private void addCustomerHistoryRecord(Long createUserId, Long customerId, RecordType recordType, Long surveyId, Long journeyRecordId, String content) {
        CustomerHistoryRecord history = new CustomerHistoryRecord();
        history.setCustomerId(customerId);
        history.setSid(surveyId + "");
        history.setRecordContent(content);
        history.setRecordType(recordType);
        history.setJourneyRecordId(journeyRecordId);
        history.setCreateByUid(createUserId);
        repository.save(history);
    }
}
