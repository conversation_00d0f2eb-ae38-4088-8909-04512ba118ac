package cn.hanyi.ctm.service;

import cn.hanyi.ctm.constant.TemplateCreateType;
import cn.hanyi.ctm.dto.CustomerSendCostDto;
import cn.hanyi.ctm.dto.SurveyLinkDto;
import cn.hanyi.ctm.dto.TemplateInfoDto;
import cn.hanyi.ctm.dto.customer.CustomerClientIdParamDto;
import cn.hanyi.ctm.entity.*;
import cn.hanyi.ctm.repository.ThirdPartyTemplateRepository;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.ThirdPartyAuthType;
import org.befun.auth.entity.ThirdPartyAuth;
import org.befun.auth.service.ThirdPartyAuthService;
import org.befun.auth.service.auth.AuthWechatOpenService;
import org.befun.auth.service.auth.config.WechatOpenConfig;
import org.befun.core.exception.BadRequestException;
import org.befun.core.exception.BusinessException;
import org.befun.core.template.TemplateEngine;
import org.befun.core.utils.DateHelper;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.entity.Link;
import org.befun.extension.property.MailTemplateProperty;
import org.befun.extension.property.SmsTemplateProperty;
import org.befun.extension.service.LinkService;
import org.befun.extension.service.NativeSqlHelper;
import org.befun.extension.sms.ISmsAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CustomerMessageService {

    @Autowired
    private NativeSqlHelper nativeSqlHelper;
    @Autowired
    private ThirdPartyTemplateService thirdPartyTemplateService;
    @Autowired
    private ThirdPartyTemplateRepository thirdPartyTemplateRepository;
    @Autowired
    private ThirdPartyCustomerService thirdPartyCustomerService;
    @Autowired
    private ThirdPartyAuthService thirdPartyAuthService;
    @Autowired
    private LinkService linkService;
    @Autowired
    private ISmsAccountService smsAccountService;
    @Autowired
    private TemplateService templateService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private AuthWechatOpenService authWechatOpenService;
    @Autowired
    private WechatTemplateService wechatTemplateService;
    @Lazy
    @Autowired
    private CustomerService customerService;

    public void checkSmsCost(Long orgId, TemplateInfoDto templateInfo, int customerSize) {
        if (templateInfo == null) {
            throw new BadRequestException("消息模版已被删除，请重新选择新模版");
        }
        // 计算短信额度
        int availableBalance = smsAccountService.balance(orgId);
        CustomerSendCostDto cost = new CustomerSendCostDto(customerSize, templateInfo.getSmsLength(), smsAccountService.calcNumberByLength(templateInfo.getSmsLength()), availableBalance);
        checkSmsCost(cost);
    }

    /**
     * 校验短信额度是否足够
     */
    private void checkSmsCost(CustomerSendCostDto cost) {
        if (cost != null && cost.getAllCost() > 0 && cost.getBalance() < cost.getAllCost()) {
            throw new BadRequestException("短信余量不足");
        }
    }

    /**
     * 查询发送的短信模板
     */
    public TemplateInfoDto getSmsTemplate(Long thirdTemplateId, String replaceContent) {
        ThirdPartyTemplate thirdPartyTemplate = thirdPartyTemplateService.get(thirdTemplateId);
        return getSmsTemplate(thirdPartyTemplate, replaceContent);
    }

    public TemplateInfoDto getSmsTemplate(Template template) {
        ThirdPartyTemplate thirdPartyTemplate = null;
        if (template != null && template.getThirdPartyTemplate() != null) {
            thirdPartyTemplate = template.getThirdPartyTemplate();
        }
        if (thirdPartyTemplate == null) {
            throw new BadRequestException("模板不存在");
        }
        String replaceContent = null;
        Object templateContent = template.getContent().get("content");
        if (templateContent != null) {
            replaceContent = templateContent.toString();
        }
        return getSmsTemplate(thirdPartyTemplate, replaceContent);
    }

    /**
     * 查询发送的短信模板
     */
    public TemplateInfoDto getSmsTemplate(ThirdPartyTemplate template, String replaceContent) {
        if (template == null) {
            throw new BusinessException("消息模版已被删除，请重新选择新模版");
        }
        if (template.getCreateType() == TemplateCreateType.ORGANIZATION) {
            return TemplateInfoDto.createSms(
                    template.getId(),
                    template.getOpenId(),
                    template.getCreateType().name(),
                    template.getSignatureId(),
                    thirdPartyTemplateService.getSmsTemplateRealSign(),
                    StringUtils.isNotEmpty(replaceContent) ? replaceContent : template.getExample());
        } else {
            SmsTemplateProperty smsTemplateProperty = thirdPartyTemplateService.getPlatformSmsTemplate(template.getCreateType().name());
            if (smsTemplateProperty == null) {
                throw new BusinessException("消息模版已被删除，请重新选择新模版");
            }
            return TemplateInfoDto.createSms(
                    0L,
                    smsTemplateProperty.getId(),
                    template.getCreateType().name(),
                    smsTemplateProperty.getSignature(),
                    smsTemplateProperty.getRealSignature(),
                    StringUtils.isNotEmpty(replaceContent) ? replaceContent : smsTemplateProperty.getContent());
        }
    }

    /**
     * 查询发送的微信模板
     */
    public TemplateInfoDto getWeChatTemplate(Template template, Map<String, Object> content) {
        ThirdPartyTemplate thirdPartyTemplate = null;
        if (template != null && template.getThirdPartyTemplate() != null) {
            thirdPartyTemplate = template.getThirdPartyTemplate();
        }
        return getWeChatTemplate(thirdPartyTemplate, content);
    }

    /**
     * 查询发送的微信模板
     */
    public TemplateInfoDto getWeChatTemplate(Long thirdTemplateId, Map<String, Object> content) {
        return getWeChatTemplate(thirdPartyTemplateService.get(thirdTemplateId), content);
    }

    /**
     * 查询发送的微信模板
     */
    public TemplateInfoDto getWeChatTemplate(ThirdPartyTemplate template, Map<String, Object> content) {
        if (template == null || StringUtils.isEmpty(template.getOpenId())) {
            throw new BusinessException("消息模版已被删除，请重新选择新模版");
        }
        WechatOpenConfig config = authWechatOpenService.getConfig(template.getThirdpartyAuthId());
        if (config == null || StringUtils.isEmpty(config.getAppId())) {
            throw new BusinessException("消息模版已被删除，请重新选择新模版");
        }
        if (MapUtils.isNotEmpty(content)) {
            return TemplateInfoDto.createWeChat(template.getId(), config.getConfigId(), config.isAuthorized(), config.getAppId(), template.getOpenId(), content);
        }
        Map<String, Object> weChatContent = new HashMap<>();
        if (CollectionUtils.isNotEmpty(template.getParameters())) {
            template.getParameters().forEach(i -> {
                Object name = i.get("name");
                if (name != null) {
                    weChatContent.put(name.toString(), "");
                }
            });
        }
        return TemplateInfoDto.createWeChat(template.getId(), config.getConfigId(), config.isAuthorized(), config.getAppId(), template.getOpenId(), weChatContent);
    }

    /**
     * 通过平台微信模版id 获得所有绑定的第三方模版
     */
    public TemplateInfoDto getWeChatTemplate(Long wechatTemplateId, Long wechatConfigId, Map<String, Object> content) {
        WechatTemplate wechatTemplate = wechatTemplateService.get(wechatTemplateId);
        if (wechatTemplate == null) {
            throw new BusinessException("消息模版已被删除，请重新选择新模版");
        }
        WechatOpenConfig config = authWechatOpenService.getConfig(wechatConfigId);
        if (config == null || StringUtils.isEmpty(config.getAppId())) {
            throw new BusinessException("消息模版已被删除，请重新选择新模版");
        }
        ThirdPartyTemplate template = thirdPartyTemplateRepository.findFirstByThirdpartyAuthIdAndIdIn(wechatConfigId, wechatTemplate.getThirdpartyTemplateIds());
        if (template == null || StringUtils.isEmpty(template.getOpenId())) {
            throw new BusinessException("消息模版已被删除，请重新选择新模版");
        }
        if (MapUtils.isNotEmpty(content)) {
            return TemplateInfoDto.createWeChat(template.getId(), config.getConfigId(), config.isAuthorized(), config.getAppId(), template.getOpenId(), content);
        }
        return TemplateInfoDto.createWeChat(template.getId(), config.getConfigId(), config.isAuthorized(), config.getAppId(), template.getOpenId(), wechatTemplate.getContent());
    }

    /**
     * 通过平台微信模版id 获得所有绑定的第三方模版
     *
     * @param wechatTemplateId {@link cn.hanyi.ctm.entity.WechatTemplate}
     * @return key wechatConfigId
     */
    public Map<Long, TemplateInfoDto> getWeChatTemplateMap(Long wechatTemplateId, Map<String, Object> content) {
        WechatTemplate wechatTemplate = wechatTemplateService.get(wechatTemplateId);
        if (wechatTemplate == null) {
            throw new BusinessException("消息模版已被删除，请重新选择新模版");
        }
        Map<String, Object> weChatContent = MapUtils.isNotEmpty(content) ? content : wechatTemplate.getContent();
        Map<Long, TemplateInfoDto> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(wechatTemplate.getThirdpartyTemplateIds())) {
            List<ThirdPartyTemplate> templates = thirdPartyTemplateRepository.findAllById(wechatTemplate.getThirdpartyTemplateIds());
            if (CollectionUtils.isNotEmpty(templates)) {
                Map<Long, WechatOpenConfig> configMap = new HashMap<>();
                List<ThirdPartyAuth> wechatConfigs = thirdPartyAuthService.getListByOrg(wechatTemplate.getOrgId(), ThirdPartyAuthType.WECHAT_OPEN, "cem");
                if (CollectionUtils.isNotEmpty(wechatConfigs)) {
                    wechatConfigs.forEach(i -> {
                        WechatOpenConfig config = authWechatOpenService.getConfig(i);
                        if (config != null) {
                            configMap.put(config.getConfigId(), config);
                        }
                    });
                }
                templates.forEach(i -> {
                    WechatOpenConfig config = configMap.get(i.getThirdpartyAuthId());
                    if (config != null) {
                        TemplateInfoDto template = TemplateInfoDto.createWeChat(i.getId(), config.getConfigId(), config.isAuthorized(), config.getAppId(), i.getOpenId(), weChatContent);
                        map.put(config.getConfigId(), template);
                    }
                });
            }
        }
        return map;
    }

    public String getOpenId(Customer customer) {
        String openId = null;
        Long thirdPartyCustomerId = customer.getThirdPartyCustomerId();
        if (thirdPartyCustomerId != null) {
            openId = nativeSqlHelper.queryString("select open_id from thirdparty_customer where id = " + thirdPartyCustomerId);
        }
        if (openId == null) {
            openId = "";
        }
        return openId;
    }

    public ThirdPartyCustomer getWechatCustomer(Customer customer) {
        Long thirdPartyCustomerId = customer.getThirdPartyCustomerId();
        if (thirdPartyCustomerId != null) {
            ThirdPartyCustomer thirdPartyCustomer = thirdPartyCustomerService.get(thirdPartyCustomerId);
            if (thirdPartyCustomer != null && thirdPartyCustomer.isSubscribed()) {
                return thirdPartyCustomer;
            }
        }
        return null;
    }

    public Customer getWechatCustomer(Long orgId, Long wechatOpenConfigId, String openId) {
        if (orgId == null || wechatOpenConfigId == null || StringUtils.isEmpty(openId)) {
            return null;
        }
        ThirdPartyCustomer thirdPartyCustomer = thirdPartyCustomerService.getThirdpartyCustomer(orgId, wechatOpenConfigId, openId);
        if (thirdPartyCustomer != null) {
            return customerService.getCustomerByThirdpartyCustomerId(orgId, thirdPartyCustomer.getId());
        }
        return null;
    }

    /**
     * 查询发送的邮件模板
     */
    public TemplateInfoDto getEmailTemplate(Long thirdTemplateId, String sender, String replaceTitle, String replaceContent) {
        ThirdPartyTemplate thirdPartyTemplate = thirdPartyTemplateService.get(thirdTemplateId);
        return getEmailTemplate(thirdPartyTemplate, sender, replaceTitle, replaceContent);
    }

    /**
     * 查询发送的邮件模板
     */
    public TemplateInfoDto getEmailTemplate(ThirdPartyTemplate template, String sender, String replaceTitle, String replaceContent) {
        if (template == null) {
            throw new BusinessException("消息模版已被删除，请重新选择新模版");
        }
        if (template.getCreateType() == TemplateCreateType.ORGANIZATION) {
            return TemplateInfoDto.createEmail(
                    template.getId(),
                    sender,
                    StringUtils.isNotEmpty(replaceTitle) ? replaceTitle : template.getDescription(),
                    StringUtils.isNotEmpty(replaceContent) ? replaceContent : template.getExample());
        } else {
            MailTemplateProperty mailTemplateProperty = thirdPartyTemplateService.getPlatformMailTemplate(template.getCreateType().name());
            if (mailTemplateProperty == null) {
                throw new BusinessException("消息模版已被删除，请重新选择新模版");
            }
            return TemplateInfoDto.createEmail(
                    0L,
                    sender,
                    StringUtils.isNotEmpty(replaceTitle) ? replaceTitle : mailTemplateProperty.getSubject(),
                    StringUtils.isNotEmpty(replaceContent) ? replaceContent : mailTemplateProperty.getContent());
        }
    }

    public String getShortUrlOrDefault(Map<String, Object> parameters, String defaultUrl) {
        Object shortUrl;
        if (parameters != null && (shortUrl = parameters.get("url.short")) != null && StringUtils.isNotEmpty(shortUrl.toString())) {
            return shortUrl.toString();
        } else {
            return defaultUrl;
        }
    }

    /**
     * 构建邮件发送内容
     */
    public Map<String, Object> buildEmailContent(Map<String, Object> contentTemplate, Map<String, Object> parameters) {
        Map<String, Object> content = TemplateEngine.renderJsonTemplate(contentTemplate, parameters);
        return content;
    }

    /**
     * 构建短信发送内容
     */
    public String buildSmsContent(Map<String, Object> contentTemplate, Map<String, Object> parameters) {
        Map<String, Object> content = TemplateEngine.renderJsonTemplate(contentTemplate, parameters);
        return (String) content.get("content");
    }

    /**
     * 构建短信发送内容
     */
    public String buildSmsContent(String contentTemplate, Map<String, Object> parameters) {
        return TemplateEngine.renderTextTemplate(contentTemplate, parameters);
    }

    /**
     * 构建微信发送内容
     */
    public String buildWechatContent(String templateId, String openId, String url, Map<String, Object> contentTemplate, Map<String, Object> parameters) {
        Map<String, Object> content = TemplateEngine.renderJsonTemplate(contentTemplate, parameters);
        WxMpTemplateMessage message = new WxMpTemplateMessage();
        message.setUrl(url);
        message.setTemplateId(templateId);
        message.setToUser(openId);
        List<WxMpTemplateData> data = Optional.ofNullable(content)
                .stream()
                .flatMap(j -> j.entrySet().stream())
                .filter(m -> m.getKey() != null && m.getValue() != null)
                .map(k -> new WxMpTemplateData(k.getKey(), k.getValue().toString()))
                .collect(Collectors.toList());
        message.setData(data);
        return JsonHelper.toJson(message);

    }

    /**
     * 构建旅程互动发送地址
     */
    public SurveyLinkDto buildSurveyUrl(Long surveyId, Long customerId, Long departmentId, String clientId, Map<String, Object> extParams, Long journeyRecordId, LocalDateTime expireTime) {
        Map<String, Object> params = new HashMap<>();
        params.put("customerId", customerId);
        params.put("departmentId", departmentId);
        params.put("collectorMethod", "SCENE_INTERACTION");
        if (StringUtils.isNotEmpty(clientId)) {
            params.put("clientId", clientId);
        }
        if (expireTime != null) {
            params.put("expireTime", DateHelper.format(expireTime, DateHelper.DATE_TIME_FORMATTER2));
            stringRedisTemplate.opsForHash().put(CustomerClientIdParamDto.clientIdParamKey(clientId), "expireTime", DateHelper.format(expireTime, DateHelper.DATE_TIME_FORMATTER));
        }
        if (journeyRecordId != null) {
            params.put("trackId", "record::" + journeyRecordId);
        }
        if (MapUtils.isNotEmpty(extParams)) {
            Map<String, Object> parameters = new HashMap<>();
            extParams.forEach((k, v) -> {
                if (v != null) {
                    if (k.startsWith("_")) {
                        parameters.put(k, v);
                    } else if (!params.containsKey(k)) {
                        params.put(k, v);
                    }
                }
            });
            if (!parameters.isEmpty()) {
                params.put("parameters", parameters);
            }
        }
        Link link = linkService.createLink(surveyId, params);
        SurveyLinkDto dto = new SurveyLinkDto();
        dto.setLinkId(link.getId());
        dto.setOriginUrl(linkService.toOriginUrl(link));
        dto.setShortUrl(linkService.toShortUrl(link));
        dto.setShortCode(linkService.toShortCode(link));
        return dto;
    }

    public Map<String, Object> buildNativeParams(SurveyLinkDto link, String surveyTitle, Customer customer, Map<String, Object> params) {
        Map<String, Object> result = new HashMap<>();


        // 第一步 先展开customer信息
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        Map<String, Object> customerExpand = new HashMap<>();
        Map<String, Object> urlExpand = new HashMap<>();

        customerExpand.put("id", customer.getId());
        customerExpand.put("orgId", customer.getOrgId());
        customerExpand.put("username", customer.getUsername());
        customerExpand.put("nickname", customer.getNickname());
        customerExpand.put("district", customer.getDistrict());
        customerExpand.put("city", customer.getCity());
        customerExpand.put("province", customer.getProvince());
        customerExpand.put("gender", customer.getGender());
        customerExpand.put("departmentIds", customer.getDepartmentId());
        customerExpand.put("latestJourneyRecord", customer.getLatestJourneyRecord());
        customerExpand.put("latestActiveTime", "");
        if (customer.getLatestActiveTime() != null) {
            customerExpand.put("latestActiveTime", dateFormat.format(customer.getLatestActiveTime()));
        }

        String shortUrl = "";
        urlExpand.put("short", "");
        urlExpand.put("code", "");
        urlExpand.put("original", "");
        if (link != null) {
            shortUrl = link.getShortUrl();
            urlExpand.put("short", shortUrl);
            urlExpand.put("code", link.getShortCode());
            urlExpand.put("original", link.getOriginUrl());
        }

        Map<String, Object> surveyExpand = new HashMap<>();
        surveyExpand.put("title", surveyTitle);
        surveyExpand.put("url", shortUrl);

        // 合并
        result.put("url", urlExpand);
        result.put("customer", customerExpand);
        result.put("survey", surveyExpand);
        result.put("username", customer.getUsername());
        result.put("url.short", shortUrl);

        if (MapUtils.isNotEmpty(customer.getContentParams())) {
            result.putAll(customer.getContentParams());
        }
        if (MapUtils.isNotEmpty(customer.getUrlCustomParams())) {
            result.putAll(customer.getUrlCustomParams());
        }
        if (MapUtils.isNotEmpty(params)) {
            result.putAll(params);
        }
        return result;
    }

}
