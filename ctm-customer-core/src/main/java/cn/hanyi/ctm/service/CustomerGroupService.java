package cn.hanyi.ctm.service;

import cn.hanyi.ctm.dto.customer.group.CustomerGroupSaveDto;
import cn.hanyi.ctm.entity.CustomerGroup;
import cn.hanyi.ctm.entity.CustomerGroupDto;
import cn.hanyi.ctm.entity.CustomerGroupRelation;
import cn.hanyi.ctm.repository.CustomerGroupRelationRepository;
import cn.hanyi.ctm.repository.CustomerGroupRepository;
import cn.hanyi.ctm.utils.ListHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.BaseNoPageService;
import org.befun.extension.service.NativeSqlHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.BiPredicate;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class CustomerGroupService extends BaseNoPageService<CustomerGroup, CustomerGroupDto, CustomerGroupRepository> {

    @Autowired
    private NativeSqlHelper nativeSqlHelper;
    @Autowired
    private CustomerGroupRelationRepository customerGroupRelationRepository;

    @Override
    public <S extends BaseEntityDTO<CustomerGroup>> CustomerGroupDto create(S data) {
        Long orgId = TenantContext.getCurrentTenant();
        CustomerGroupSaveDto save = (CustomerGroupSaveDto) data;
        checkUniqueName(orgId, save.getName(), null);
        checkUniqueCode(orgId, save.getCode(), null);
        return super.create(data);
    }

    @Override
    public <S extends BaseEntityDTO<CustomerGroup>> CustomerGroupDto updateOne(long id, S change) {
        Long orgId = TenantContext.getCurrentTenant();
        CustomerGroup current = require(id);
        checkIsCurrentOrg(current);
        CustomerGroupSaveDto save = (CustomerGroupSaveDto) change;
        checkUniqueName(orgId, save.getName(), current);
        checkUniqueCode(orgId, save.getCode(), current);
        return super.updateOne(id, change);
    }

    /**
     * 检验组名称在企业内唯一
     */
    private void checkUniqueName(Long orgId, String name, CustomerGroup current) {
        if (!isUniqueInOrg(orgId, "name", name, current)) {
            throw new BadRequestException("名称已存在");
        }
    }

    /**
     * 检验组编号在企业内唯一
     */
    private void checkUniqueCode(Long orgId, String code, CustomerGroup current) {
        if (StringUtils.isNotEmpty(code) && !isUniqueInOrg(orgId, "code", code, current)) {
            throw new BadRequestException("编号已存在");
        }
    }

    @Override
    @Transactional
    public Boolean deleteOne(long id) {
        boolean r = super.deleteOne(id);
        if (r) {
            customerGroupRelationRepository.deleteByGroupId(id);
        }
        return r;
    }

    /**
     * 查询指定客户的组列表
     */
    public Map<Long/*customerId*/, List<CustomerGroupDto>> getCustomerGroupsByCustomerIds(Set<Long> customerIds) {
        Map<Long/*customerId*/, List<CustomerGroupDto>> map = new HashMap<>();
        if (CollectionUtils.isEmpty(customerIds)) {
            return map;
        }
        Map<Long, CustomerGroupDto> allGroupMap = findAllEntities().stream().map(this::mapToDto).collect(Collectors.toMap(CustomerGroupDto::getId, Function.identity()));
        if (MapUtils.isEmpty(allGroupMap)) {
            return map;
        }
        List<CustomerGroupRelation> relations = customerGroupRelationRepository.findByCustomerIdIn(customerIds);
        if (CollectionUtils.isNotEmpty(relations)) {
            relations.forEach(i -> {
                Optional.ofNullable(allGroupMap.get(i.getGroupId())).ifPresent(g -> {
                    map.computeIfAbsent(i.getCustomerId(), j -> new ArrayList<>()).add(g);
                });
            });
        }
        return map;
    }

    /**
     * 添加客户的组（如果已经添加了，会忽略）
     */
    public void addCustomerGroup(Long orgId, Long customerId, String groupCode) {
        if (StringUtils.isEmpty(groupCode)) {
            return;
        }
        CustomerGroup group = repository.findFirstByOrgIdAndCode(orgId, groupCode);
        addCustomerGroups(customerId, group);
    }

    /**
     * 添加客户的组（如果已经添加了，会忽略）
     */
    public void addCustomerGroup(Long customerId, Long groupId) {
        addCustomerGroups(customerId, get(groupId));
    }

    /**
     * 添加客户的组（如果已经添加了，会忽略）
     */
    public void addCustomerGroups(Long customerId, CustomerGroup group) {
        if (customerId == null || customerId <= 0 || group == null) {
            return;
        }
        List<CustomerGroupRelation> existsList = customerGroupRelationRepository.findByGroupIdAndCustomerId(group.getId(), customerId);
        if (CollectionUtils.isEmpty(existsList)) {
            customerGroupRelationRepository.save(new CustomerGroupRelation(customerId, group.getId()));
        }
    }

    /**
     * 更新客户的组（比较出新增的和删除的组，然后同步到数据库）
     */
    public void updateCustomerGroups(Long customerId, String groupIds) {
        if (customerId == null) {
            return;
        }
        if (StringUtils.isEmpty(groupIds)) {
            updateCustomerGroups(customerId, List.of());
        } else {
            updateCustomerGroups(customerId, Arrays.stream(groupIds.split(",")).filter(NumberUtils::isDigits).map(Long::parseLong).collect(Collectors.toList()));
        }
    }

    /**
     * 更新客户的组（比较出新增的和删除的组，然后同步到数据库）
     */
    public void updateCustomerGroups(Long customerId, List<Long> groupIds) {
        if (customerId == null || customerId <= 0) {
            return;
        }
        List<CustomerGroupRelation> oldList = customerGroupRelationRepository.findByCustomerId(customerId);
        List<CustomerGroupRelation> newList = CollectionUtils.isEmpty(groupIds) ? new ArrayList<>() : groupIds.stream().map(groupId -> {
            return new CustomerGroupRelation(customerId, groupId);
        }).collect(Collectors.toList());
        BiPredicate<CustomerGroupRelation, CustomerGroupRelation> predicate = (o1, o2) -> o1.getGroupId().equals(o2.getGroupId());
        List<CustomerGroupRelation> add = ListHelper.minus(newList, oldList, predicate);
        List<CustomerGroupRelation> delete = ListHelper.minus(oldList, newList, predicate);
        if (CollectionUtils.isNotEmpty(add)) {
            customerGroupRelationRepository.saveAll(add);
        }
        if (CollectionUtils.isNotEmpty(delete)) {
            customerGroupRelationRepository.deleteAll(delete);
        }
    }

    /**
     * 组批量添加客户
     */
    @Transactional
    public boolean addGroupCustomers(Long groupId, List<Long> customerIds) {
        return addGroupCustomers(List.of(groupId), customerIds);
    }

    /**
     * 组批量添加客户
     */
    @Transactional
    public boolean addGroupCustomers(List<Long> groupIds, Collection<Long> customerIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return false;
        }
        groupIds.forEach(groupId -> checkIsCurrentOrg(require(groupId)));
        if (CollectionUtils.isNotEmpty(customerIds)) {
            List<CustomerGroupRelation> add = new ArrayList<>();
            groupIds.forEach(groupId -> {
                List<CustomerGroupRelation> list = customerGroupRelationRepository.findByGroupIdAndCustomerIdIn(groupId, new ArrayList<>(customerIds));
                Set<Long> existsCustomerIds = new HashSet<>();
                if (CollectionUtils.isNotEmpty(list)) {
                    list.forEach(i -> existsCustomerIds.add(i.getCustomerId()));
                }
                customerIds.forEach(customerId -> {
                    if (!existsCustomerIds.contains(customerId)) {
                        add.add(new CustomerGroupRelation(customerId, groupId));
                    }
                });
            });
            if (CollectionUtils.isNotEmpty(add)) {
                customerGroupRelationRepository.saveAll(add);
            }
        }
        return true;
    }

    /**
     * 组批量移除客户
     */
    @Transactional
    public boolean removeGroupCustomers(Long groupId, List<Long> customerIds) {
        checkIsCurrentOrg(require(groupId));
        if (CollectionUtils.isNotEmpty(customerIds)) {
            List<CustomerGroupRelation> list = customerGroupRelationRepository.findByGroupIdAndCustomerIdIn(groupId, customerIds);
            if (CollectionUtils.isNotEmpty(list)) {
                customerGroupRelationRepository.deleteAll(list);
            }
        }
        return true;
    }

    /**
     * 移除组内全部客户
     */
    @Transactional
    public boolean clearGroupCustomers(Long groupId) {
        checkIsCurrentOrg(require(groupId));
        customerGroupRelationRepository.deleteByGroupId(groupId);
        return true;
    }

    /**
     * 移除客户的全部组
     */
    @Transactional
    public void clearCustomerGroups(Long customerId) {
        customerGroupRelationRepository.deleteByCustomerId(customerId);
    }

}
