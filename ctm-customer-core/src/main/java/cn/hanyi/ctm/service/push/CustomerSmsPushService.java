//package cn.hanyi.ctm.service.push;
//
//import cn.hanyi.ctm.constant.customer.CustomerPushType;
//import cn.hanyi.ctm.dto.customer.push.PushSmsDto;
//import cn.hanyi.ctm.entity.Customer;
//import cn.hanyi.ctm.entity.CustomerPushRecord;
//import cn.hanyi.ctm.service.ISmsAccountService;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang3.StringUtils;
//import org.befun.core.utils.JsonHelper;
//import org.befun.core.utils.RegHelper;
//import org.befun.extension.dto.MessageSendResponseInfo;
//import org.befun.extension.service.SmsService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.Map;
//
//@Slf4j
//@Service
//public class CustomerSmsPushService extends CustomerPushService<PushSmsDto> {
//
//    @Autowired
//    private SmsService smsService;
//    @Autowired
//    private ISmsAccountService smsAccountService;
//
//    @Override
//    public Class<PushSmsDto> getPushParamClass() {
//        return PushSmsDto.class;
//    }
//
//    @Override
//    public CustomerPushType getPushType() {
//        return CustomerPushType.SMS;
//    }
//
//    @Override
//    public boolean send(CustomerPushRecord pushRecord, Customer customer, PushSmsDto pushParam, Map<String, Object> contentParams) {
//        String mobile = customer.getMobile();
//        if (!RegHelper.isMobile(mobile)) {
//            log.warn("客户短信推送失败, 手机号不正确，pushParam={}", JsonHelper.toJson(pushParam));
//            return false;
//        }
//        if (pushParam.isCost() && !smsAccountService.hasBalance(pushRecord.getOrgId(), 1)) {
//            log.warn("客户短信推送失败, 余额不足，orgId={}, balance={}", pushRecord.getOrgId(), smsAccountService.balance(pushRecord.getOrgId()));
//            return false;
//        }
//        boolean result = false;
//        try {
//            MessageSendResponseInfo response;
//            if (StringUtils.isEmpty(pushParam.getTemplateName())) {
//                response = smsService.sendMessageByText2(mobile, pushParam.getTemplateContent(), contentParams);
//            } else if (StringUtils.isEmpty(pushParam.getTemplateContent())) {
//                response = smsService.sendMessageByTemplate2(pushParam.getTemplateName(), mobile, contentParams);
//            } else {
//                response = smsService.sendMessageByTemplate2(pushParam.getTemplateName(), pushParam.getTemplateContent(), mobile, contentParams);
//            }
//            if (response != null) {
//                pushRecord.setPushContent(response.getContent());
//                pushRecord.setPushResponse(response.getResponse());
//                result = response.isSuccess();
//                int cost = 0;
//                if (result && pushParam.isCost()) {
//                    cost = smsAccountService.calcNumberByText(response.getContent());
//                    if (cost > 0) {
//                        smsAccountService.consumer(pushRecord.getOrgId(), cost);
//                    }
//                }
//                pushRecord.setSmsCost(cost);
//            }
//        } catch (Throwable e) {
//            log.warn("客户短信推送失败，pushParam={}", JsonHelper.toJson(pushParam), e);
//        }
//        return result;
//    }
//
//
//}
