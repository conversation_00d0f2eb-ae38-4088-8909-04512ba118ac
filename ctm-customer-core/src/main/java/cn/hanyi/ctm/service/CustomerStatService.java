package cn.hanyi.ctm.service;

import cn.hanyi.ctm.constant.survey.QuestionType;
import cn.hanyi.ctm.dto.CustomerStatDto;
import cn.hanyi.ctm.entity.CustomerStat;
import cn.hanyi.ctm.repository.CustomerStatRepository;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.MapperService;
import org.befun.core.utils.DateHelper;
import org.befun.core.utils.JsonHelper;
import org.befun.core.utils.NumberHelper;
import org.befun.extension.service.NativeSqlHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;


@Service
@Slf4j
public class CustomerStatService {

    @Autowired
    private NativeSqlHelper nativeSqlHelper;
    @Autowired
    private CustomerStatRepository customerStatRepository;
    @Autowired
    private MapperService mapperService;

    public CustomerStatDto customerStat(long id) {
        CustomerStat entity = updateCustomerStat(id);
        return mapperService.map(entity, CustomerStatDto.class);
    }

    public CustomerStatDto getCustomerStat(long id) {
        CustomerStat entity = customerStatRepository.findByCustomerId(id);
        if (entity == null) {
            entity = updateCustomerStat(id);
        }
        return mapperService.map(entity, CustomerStatDto.class);
    }

    public CustomerStat updateCustomerStat(long id) {
        return updateCustomerStat(id, TenantContext.getCurrentTenant());
    }

    public CustomerStat updateCustomerStat(long id, long orgId) {
        CustomerStat entity = customerStatRepository.findByCustomerId(id);
        if (entity == null) {
            entity = new CustomerStat();
            entity.setCustomerId(id);
        }

        fillJourneyRecord(id, entity);
        fillSurveyStat(id, entity);
        fillEventStat(id, entity);
        fillJourneyIndicator(orgId, id, entity);
        entity.setModifyTime(new Date());
        customerStatRepository.save(entity);
        return entity;
    }

    private void fillJourneyRecord(long id, CustomerStat stat) {
        String sql1 = "SELECT count(id) count, max(create_time) lastTime FROM customer_journey_record where customer_id=" + id;
        log.info("查询客户统计数据, 客户历程, sql={}", sql1);
        CustomerStat2 stat1 = nativeSqlHelper.queryObject(sql1, CustomerStat2.class);
        stat.setCountJourneyRecord(stat1.getCount());
        stat.setLastJourneyRecordTime(stat1.getLastTime());
    }

    private void fillSurveyStat(long id, CustomerStat stat) {
        String sql2 = "select count(id) count, max(create_time) lastTime from customer_answers where customer_id=" + id;
        log.info("查询客户统计数据, 问卷发送, sql={}", sql2);
        CustomerStat2 stat2 = nativeSqlHelper.queryObject(sql2, CustomerStat2.class);
        stat.setCountSendSurvey(stat2.getCount());
        stat.setLastSendSurveyTime(stat2.getLastTime());

        String sql3 = "select count(id) count, max(answer_time) lastTime from customer_answers where answer_status != 0 and  customer_id=" + id;
        log.info("查询客户统计数据, 问卷填答, sql={}", sql3);
        CustomerStat2 stat3 = nativeSqlHelper.queryObject(sql3, CustomerStat2.class);
        stat.setCountCompleteSurvey(stat3.getCount());
        stat.setLastCompleteSurveyTime(stat3.getLastTime());

        String sql4 = "select count(distinct sid) count from customer_answers where answer_status != 0 and  customer_id=" + id;
        CustomerStat2 stat4 = nativeSqlHelper.queryObject(sql4, CustomerStat2.class);
        log.info("查询客户统计数据, 问卷参与, sql={}", sql4);
        stat.setCountJoinSurvey(stat4.getCount());
    }

    private void fillEventStat(long id, CustomerStat customerStat) {
        String sql5 = "select emr.id ruleId,emr.title ruleTitle, count(er.id) eventTimes, max(er.create_time) lastEventTime from event_result er" +
                " inner join event_result_rule_relation errr on er.id=errr.event_id" +
                " inner join event_monitor_rules emr on errr.rule_id=emr.id" +
                " where er.customer_id=" + id +
                " group by emr.id";
        log.info("查询客户统计数据, 预警数据, sql={}", sql5);
        List<CustomerEventStat> eventStats = nativeSqlHelper.queryListObject(sql5, CustomerEventStat.class);
        int countEvent = 0;
        Date lastEventTime = null;
        CustomerEventStat mostEventRule = null;
        if (CollectionUtils.isNotEmpty(eventStats)) {
            for (CustomerEventStat stat : eventStats) {
                countEvent += NumberHelper.unbox(stat.getEventTimes());
                lastEventTime = DateHelper.max(lastEventTime, stat.getLastEventTime());
                mostEventRule = NumberHelper.max(mostEventRule, stat, CustomerEventStat::getEventTimes);
            }
            customerStat.setCountEvent(countEvent);
            customerStat.setLastEventTime(lastEventTime);
            if (mostEventRule != null) {
                customerStat.setMostEventRuleTimes(mostEventRule.getEventTimes());
                customerStat.setMostEventRuleName(mostEventRule.getRuleTitle());
                customerStat.setMostEventRule(String.format("%d次（%s）", mostEventRule.getEventTimes(), mostEventRule.getRuleTitle()));
            }
        }
    }

    private void fillJourneyIndicator(long orgId, long id, CustomerStat stat) {
        // 查询客户统计数据 体验指标
        // 1 客户所有填答完成的问卷
        String sql1 = "select sid,answer_id rid from customer_answers ca where ca.answer_status=1 and ca.customer_id=" + id + " order by ca.answer_time desc";
        log.info("查询客户统计数据, 指标数据, 客户填答记录 sql={}", sql1);
        List<CustomerAnswer> customerAnswers = nativeSqlHelper.queryListObject(sql1, CustomerAnswer.class);
        if (CollectionUtils.isEmpty(customerAnswers)) {
            return;
        }
        // 2 所有旅程的体验指标的问卷
        String sql2 = "select eip.calculating_method method,eip.indicator_name `name`,eip.sids,eip.qids,eip.item_names itemNames,eip.weights weights " +
                " from journey_publish jp" +
                " inner join experience_indicator_publish eip on jp.id=eip.journey_id and eip.calculating_method!='percent'" +
                " inner join journey_component_publish jcp on jp.component_id=jcp.id and jcp.type='journey'" +
                " inner join journey_map jm on jcp.journey_map_id=jm.id and jm.org_id=" + orgId;
        log.info("查询客户统计数据, 指标数据，所有指标 sql={}", sql2);
        List<JourneyIndicator> indicators = nativeSqlHelper.queryListObject(sql2, JourneyIndicator.class);
        List<JourneyIndicatorItem> indicatorSids = parseIndicators(indicators);
        if (CollectionUtils.isEmpty(indicatorSids)) {
            return;
        }
        // 3 遍历填答过的问卷，如果是体验指标的问卷，则查询是否填答了体验指标的问题
        for (CustomerAnswer ca : customerAnswers) {
            if (NumberUtils.isDigits(ca.sid) && ca.rid != null && ca.rid > 0) {
                Long sid = Long.parseLong(ca.sid);
                List<JourneyIndicatorItem> items = indicatorSids.stream().filter(i -> i.getSid().equals(sid)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(items)) {
                    // 则查询是答卷否填答了体验指标的问题
                    String sql3 = "SELECT q_id qid, `type`, i_val ivalue, s_val svalue, j_val jvalue, cell_score score FROM survey_response_cell where s_id=%d and r_id=%d and q_id in (%s)";
                    log.info("查询客户统计数据, 指标数据，客户答题 sql={}", sql3);
                    sql3 = String.format(sql3, sid, ca.rid, items.stream().map(i -> i.qid + "").distinct().collect(Collectors.joining(",")));
                    List<CustomerSurveyResponse> responses = nativeSqlHelper.queryListObject(sql3, CustomerSurveyResponse.class);
                    if (CollectionUtils.isNotEmpty(responses)) {
                        Map<Long, CustomerSurveyResponse> responseMap = responses.stream().collect(Collectors.toMap(i -> i.qid, Function.identity(), (o1, o2) -> o1));
                        for (JourneyIndicatorItem item : items) {
                            CustomerSurveyResponse response = responseMap.get(item.qid);
                            if (response != null) {
                                if (List.of(QuestionType.SINGLE_CHOICE.ordinal(), QuestionType.SCORE_EVALUATION.ordinal()).contains(NumberHelper.unbox(response.type))) {
                                    if (response.score != null) {
                                        Double value = formatDouble(response.score * item.weight);
                                        String journeyIndicator = String.format("%f（%s）", value, item.name);
                                        stat.setJourneyIndicatorName(item.name);
                                        stat.setJourneyIndicatorScore(value);
                                        stat.setJourneyIndicator(journeyIndicator);
                                        return;
                                    }
                                } else if (List.of(QuestionType.SCORE.ordinal(), QuestionType.NPS.ordinal()).contains(NumberHelper.unbox(response.type))) {
                                    if (response.ivalue != null) {
                                        Double value = formatDouble(response.ivalue * item.weight);
                                        String journeyIndicator = String.format("%f（%s）", value, item.name);
                                        stat.setJourneyIndicatorName(item.name);
                                        stat.setJourneyIndicatorScore(value);
                                        stat.setJourneyIndicator(journeyIndicator);
                                        return;
                                    }
                                } else if (QuestionType.MATRIX_SCORE.ordinal() == NumberHelper.unbox(response.type)) {
                                    if (!StringUtils.isEmpty(item.itemName)) {
                                        Map<String, Object> j;
                                        Object v;
                                        if (StringUtils.isNotEmpty(response.jvalue)
                                                && (j = JsonHelper.toMap(response.jvalue)) != null
                                                && (v = j.get(item.itemName)) != null
                                                && v instanceof Number) {
                                            Double value = formatDouble(((Number) v).intValue() * item.weight);
                                            String journeyIndicator = String.format("%f（%s）", value, item.name);
                                            stat.setJourneyIndicatorName(item.name);
                                            stat.setJourneyIndicatorScore(value);
                                            stat.setJourneyIndicator(journeyIndicator);
                                            return;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private Double formatDouble(Integer value) {
        if (value == null) {
            return 0.0;
        }
        return Long.valueOf(Math.round(value * 100)).doubleValue() / 10000;
    }

    private List<JourneyIndicatorItem> parseIndicators(List<JourneyIndicator> indicators) {
        if (CollectionUtils.isEmpty(indicators)) {
            return null;
        }
        List<JourneyIndicatorItem> list = new ArrayList<>();
        indicators.forEach(i -> {
            List<Long> sids = parseListLong(i.sids);
            List<Long> qids = parseListLong(i.qids);
            if (sids != null && qids != null && sids.size() == qids.size()) {
                List<Integer> weights = parseWeights(i.method, qids, parseListInt(i.weights));
                List<String> itemNames = alignItems(qids, parseListString(i.itemNames));
                IntStream.range(0, sids.size()).forEach(index -> {
                    Long sid = sids.get(index);
                    Long qid = qids.get(index);
                    String itemName = itemNames.get(index);
                    int weight = weights.get(index);
                    list.add(new JourneyIndicatorItem(i.name, sid, qid, itemName, weight));
                });
            }
        });
        return list;
    }

    private List<Integer> parseWeights(String method, List<Long> qid, List<Integer> weights) {
        return IntStream.range(0, qid.size()).mapToObj(i -> {
            if (!"WeightAvg".equalsIgnoreCase(method) || weights == null || i >= weights.size()) {
                return 100;
            } else {
                return weights.get(i);
            }
        }).collect(Collectors.toList());
    }

    private List<Long> parseListLong(String s) {
        return StringUtils.isEmpty(s) ? null : Arrays.stream(s.replace("[", "").replace("]", "").replace("\"", "").split(","))
                .filter(NumberUtils::isDigits).map(Long::valueOf).collect(Collectors.toList());
    }

    private List<Integer> parseListInt(String s) {
        return StringUtils.isEmpty(s) ? null : Arrays.stream(s.replace("[", "").replace("]", "").replace("\"", "").split(","))
                .filter(NumberUtils::isDigits).map(Integer::valueOf).collect(Collectors.toList());
    }

    private List<String> parseListString(String s) {
        return StringUtils.isEmpty(s) ? null : Arrays.stream(s.replace("[", "").replace("]", "").split(","))
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    private List<String> alignItems(List<Long> qid, List<String> items) {
        return IntStream.range(0, qid.size()).mapToObj(i -> {
            if (items == null || i >= items.size() || StringUtils.isEmpty(items.get(i))) {
                return null;
            } else {
                return items.get(i);
            }
        }).collect(Collectors.toList());
    }

    @Getter
    @Setter
    public static class CustomerStat2 {
        private int count;
        private Date lastTime;
    }

    @Getter
    @Setter
    public static class CustomerEventStat {
        private Long ruleId;
        private String ruleTitle;
        private Integer eventTimes;
        private Date lastEventTime;
    }

    @Getter
    @Setter
    public static class CustomerAnswer {
        private String sid;
        private Long rid;
    }

    @Getter
    @Setter
    public static class JourneyIndicator {
        private String method;
        private String name;
        private String sids;
        private String qids;
        private String itemNames;
        private String weights;
    }

    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class JourneyIndicatorItem {
        private String name;
        private Long sid;
        private Long qid;
        private String itemName;
        private int weight;
    }

    @Getter
    @Setter
    public static class CustomerSurveyResponse {
        private Long qid;
        private Integer type;
        private Integer ivalue;
        private String svalue;
        private String jvalue;
        private Integer score;
    }

}
