package cn.hanyi.ctm.service;

import cn.hanyi.ctm.entity.Template;
import cn.hanyi.ctm.entity.TemplateDto;
import cn.hanyi.ctm.repository.TemplateRepository;
import cn.hanyi.ctm.repository.ThirdPartyTemplateRepository;
import org.befun.core.rest.context.TenantContext;
import cn.hanyi.ctm.connector.IConnectorManager;
import cn.hanyi.ctm.dto.connector.ThirdPartyTemplateDto;
import cn.hanyi.ctm.entity.Connector;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Map;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class TemplateService extends BaseService<Template, TemplateDto, TemplateRepository> {

    @Autowired
    TemplateRepository templateRepository;

    @Autowired
    ThirdPartyTemplateRepository thirdPartyTemplateRepository;

    @Autowired
    Map<String, IConnectorManager> connectorManagerMap;

    /**
     * syncTemplate
     * 通过第三方Connector获取模版，并且同步存储到ThirdPartyTemplate留作缓存. 模版一般很少修改.
     *
     * @param connector
     * @return
     */
    public void syncTemplate(Connector connector) {
        final Long tenantId = TenantContext.getCurrentTenant();
        Assert.notNull(tenantId, "missing tenantId");
        Assert.notNull(connector, "empty connector");

        IConnectorManager connectorManager = connectorManagerMap.get(connector.getType().name());

        // Fetching
        List<ThirdPartyTemplateDto> tpTemplateDtos = connectorManager.syncTemplate(connector);
        log.info("total fetch {} templates for connector {}", tpTemplateDtos.size(), connector.getId());
    }

}
