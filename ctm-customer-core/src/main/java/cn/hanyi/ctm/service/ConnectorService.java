package cn.hanyi.ctm.service;

import cn.hanyi.ctm.constant.connector.*;
import cn.hanyi.ctm.entity.Connector;
import cn.hanyi.ctm.entity.ConnectorDto;
import cn.hanyi.ctm.properties.ConnectorInitProperties;
import cn.hanyi.ctm.repository.*;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.service.auth.AuthEmailSenderService;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.BaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;

@Service
@Slf4j
public class ConnectorService extends BaseService<Connector, ConnectorDto, ConnectorRepository> {
    @Autowired
    TemplateRepository templateRepository;

    @Autowired
    ThirdPartyTemplateRepository thirdPartyTemplateRepository;

    @Autowired
    PushRepository pushRepository;

    @Autowired
    PushLogRepository pushLogRepository;

    @Autowired
    ConnectorRepository connectorRepository;

    @Autowired
    CustomerRepository customerRepository;

    @Autowired
    ThirdPartyCustomerRepository thirdPartyCustomerRepository;

    @Autowired
    ConnectorConsumerRepository connectorConsumerRepository;

    @Autowired
    private ConnectorInitProperties connectorInitProperties;
    @Autowired
    private AuthEmailSenderService authEmailSenderService;

    @Autowired
    private ConnectorConsumerService connectorConsumerService;

    @Override
    public Page<ConnectorDto> findAll(ResourceEntityQueryDto<ConnectorDto> queryDto) {
        confirmInitConnector();
        return super.findAll(queryDto);
    }

    public Optional<Connector> getWechatConnectorByOrgId(Long orgId) {
        return connectorRepository.findFirstByOrgIdAndTypeAndProviderType(orgId, ConnectorType.PLATFORM, ConnectorProviderType.WECHATOPEN);
    }

    @Transactional
    public String deleteConnector(Long connectorId, Boolean deleteUser) {
        try {
            Connector connector = require(connectorId);
            checkIsCurrentOrg(connector);
            Long orgId = TenantContext.getCurrentTenant();
            if (deleteUser != null && deleteUser) {
                // 删除微信关联的用户
                customerRepository.deleteThirdPartCustomer(orgId, connector.getId());
                thirdPartyCustomerRepository.deleteAllByConnector(connector);
            } else {
                // 设置微信关联用户的authorizeStatus为UNAUTHORIZED
                customerRepository.updateThirdPartCustomerAuthorizeStatus(orgId, connector.getId());
            }
            thirdPartyTemplateRepository.deleteAllByConnector(connector);
            templateRepository.deleteAllByConnector(connector);
            connectorRepository.deleteByOrgIdAndId(orgId, connector.getId());
            connectorConsumerService.deleteConsumers(connector);
            return "Success";
        } catch (Exception ex) {
            log.error(ex.getMessage());
            return ex.getMessage();
        }
    }

    public Connector getOrCreateSmsLocalConnector(Long orgId) {
        Connector connector = connectorRepository.findFirstByOrgIdAndTypeAndProviderType(orgId, ConnectorType.SMS, ConnectorProviderType.FEIGE).orElse(null);
        if (connector == null) {
            connector = new Connector();
            connector.setOrgId(orgId);
            connector.setName(connectorInitProperties.getConnectorName().get(ConnectorType.SMS));
            connector.setType(ConnectorType.SMS);
            connector.setProviderType(ConnectorProviderType.FEIGE);
            connectorRepository.save(connector);
        }
        return connector;
    }

    public Connector getOrCreateSmsLocalConnector() {
        return getOrCreateSmsLocalConnector(TenantContext.requireCurrentTenant());
    }

    public Connector getOrCreateEmailLocalConnector() {
        boolean enableEmailSender = authEmailSenderService.emailSenderEnable(TenantContext.requireCurrentTenant());
        ConnectorStatus status = enableEmailSender ? ConnectorStatus.READY : ConnectorStatus.DISABLED;
        Connector connector = connectorRepository.findFirstByOrgIdAndTypeAndProviderType(TenantContext.getCurrentTenant(), ConnectorType.EMAIL, ConnectorProviderType.EMAIL).orElse(null);
        if (connector == null) {
            connector = new Connector();
            connector.setName(connectorInitProperties.getConnectorName().get(ConnectorType.EMAIL));
            connector.setType(ConnectorType.EMAIL);
            connector.setProviderType(ConnectorProviderType.EMAIL);
            connector.setStatus(status);
            connectorRepository.save(connector);
        } else if (connector.getStatus() != status) {
            connector.setStatus(status);
            connectorRepository.save(connector);
        }
        return connector;
    }

    public Connector getOrCreateWarningBotLocalConnector(Long connectorId, String url, ConnectorType type) {
        if (connectorId == null) {
            connectorId = -1l;
        }

        AtomicReference<Connector> connector = new AtomicReference<>();
        connectorRepository.findById(connectorId).ifPresentOrElse(c -> {
            connector.set(c);
        }, () -> {
            Connector c = new Connector();
            c.setCondition(List.of(ConnectorPushCondition.WARNING));
            c.setName("预警机器人" + type.name());
            c.setProviderType(ConnectorProviderType.BOT);
            c.setPushType(ConnectorPushType.WARNING);
            c.setStatus(ConnectorStatus.SYNCING);
            c.setGateway(url);
            c.setType(type);
            connectorRepository.save(c);
            connector.set(c);
        });
        return connector.get();
    }


    public void confirmInitConnector() {
        getOrCreateSmsLocalConnector();
        getOrCreateEmailLocalConnector();
    }
}
