package cn.hanyi.ctm.service;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.converters.ReadConverterContext;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;

import java.util.ArrayList;
import java.util.Arrays;

/**
 * <AUTHOR>
 */
public class StringCommonListConverter implements Converter<Object> {
    @Override
    public Class<?> supportJavaTypeKey() {
        return Converter.super.supportJavaTypeKey();
    }

    @Override
    public Object convertToJavaData(ReadCellData<?> cellData, ExcelContentProperty contentProperty, GlobalConfiguration globalConfiguration) throws Exception {
        return Converter.super.convertToJavaData(cellData, contentProperty, globalConfiguration);
    }

    @Override
    public Object convertToJavaData(ReadConverterContext<?> context) throws Exception {
        Object value = context.getReadCellData().getStringValue() == null ? context.getReadCellData().getNumberValue() : context.getReadCellData().getStringValue();

        return value==null? new ArrayList<>() : Arrays.asList(value.toString().replace("，",",").split(","));
    }
}
