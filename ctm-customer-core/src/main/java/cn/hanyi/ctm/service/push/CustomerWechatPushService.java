//package cn.hanyi.ctm.service.push;
//
//import cn.hanyi.ctm.constant.customer.CustomerPushType;
//import cn.hanyi.ctm.dto.customer.push.PushWechatDto;
//import cn.hanyi.ctm.entity.Customer;
//import cn.hanyi.ctm.entity.CustomerPushRecord;
//import lombok.extern.slf4j.Slf4j;
//import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
//import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
//import org.apache.commons.collections.CollectionUtils;
//import org.apache.commons.collections.MapUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.befun.core.template.TemplateEngine;
//import org.befun.core.utils.JsonHelper;
//import org.befun.extension.dto.MessageSendResponseInfo;
//import org.befun.extension.service.WeChatOpenService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//import java.util.Map;
//import java.util.stream.Collectors;
//
//@Slf4j
//@Service
//public class CustomerWechatPushService extends CustomerPushService<PushWechatDto> {
//
//    @Autowired(required = false)
//    private WeChatOpenService weChatOpenService;
//
//    @Override
//    public Class<PushWechatDto> getPushParamClass() {
//        return PushWechatDto.class;
//    }
//
//    @Override
//    public CustomerPushType getPushType() {
//        return CustomerPushType.WECHAT;
//    }
//
//    @Override
//    public boolean send(CustomerPushRecord pushRecord, Customer customer, PushWechatDto pushParam, Map<String, Object> contentParams) {
//        if (weChatOpenService==null){
//            log.warn("客户微信模板消息推送失败, 未启用微信开放平台服务，pushParam={}", JsonHelper.toJson(pushParam));
//            return false;
//        }
//        String openId = getOpenId(customer);
//        if (StringUtils.isEmpty(openId)) {
//            log.warn("客户微信模板消息推送失败, 客户未绑定微信公众号，pushParam={}", JsonHelper.toJson(pushParam));
//            return false;
//        }
//        List<WxMpTemplateData> data = getData(pushParam, contentParams);
//        if (CollectionUtils.isEmpty(data)) {
//            log.warn("客户微信模板消息推送失败, 模板内容为空，pushParam={}", JsonHelper.toJson(pushParam));
//            return false;
//        }
//        String url = getUrl(contentParams);
//        MessageSendResponseInfo response = weChatOpenService.sendTemplateMsg2(pushParam.getAppId(), WxMpTemplateMessage.builder()
//                .templateId(pushParam.getTemplateId())
//                .toUser(openId)
//                .data(getData(pushParam, contentParams))
//                .url(url)
//                .build());
//        if (response != null) {
//            pushRecord.setPushContent(response.getContent());
//            pushRecord.setPushResponse(response.getResponse());
//            return response.isSuccess();
//        }
//        return false;
//    }
//
//    private List<WxMpTemplateData> getData(PushWechatDto pushParam, Map<String, Object> contentParams) {
//        if (MapUtils.isNotEmpty(pushParam.getTemplateContent())) {
//            return TemplateEngine.renderJsonTemplate(pushParam.getTemplateContent(), contentParams)
//                    .entrySet()
//                    .stream()
//                    .map(i -> new WxMpTemplateData(i.getKey(), i.getValue() == null ? "" : i.getValue().toString()))
//                    .collect(Collectors.toList());
//        }
//        return null;
//    }
//
//    public String getOpenId(Customer customer) {
////        if (customer.getThirdPartyCustomer() != null) {
////            return customer.getThirdPartyCustomer().getOpenId();
////        }
//        return null;
//    }
//
//    public String getUrl(Map<String, Object> contentParams) {
//        return contentParams.getOrDefault("url.original", "").toString();
//    }
//}
