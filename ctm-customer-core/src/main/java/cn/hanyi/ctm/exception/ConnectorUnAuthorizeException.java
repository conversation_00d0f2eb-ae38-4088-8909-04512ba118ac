package cn.hanyi.ctm.exception;

import cn.hanyi.ctm.constant.error.ConnectorErrorCode;
import org.befun.core.constant.ErrorCode;
import org.befun.core.exception.BaseException;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ConnectorUnAuthorizeException extends BaseException {

    public ConnectorUnAuthorizeException() {
        super(ConnectorErrorCode.UNAUTHORIZED.getValue(), "平台未授权");
    }

    public ConnectorUnAuthorizeException(String message, int internalCode, String detail) {
        super(ErrorCode.INTEGRATION_FAULT, message, internalCode, detail);
    }
}
