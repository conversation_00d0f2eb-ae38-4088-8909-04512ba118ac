package cn.hanyi.ctm.exception;

import org.befun.core.constant.ErrorCode;
import org.befun.core.exception.BaseException;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ConnectorException extends BaseException {

    public ConnectorException() {
        super(ErrorCode.INTEGRATION_FAULT, "集成错误");
    }

    public ConnectorException(String message, int internalCode, String detail) {
        super(ErrorCode.INTEGRATION_FAULT, message, internalCode, detail);
    }
}
