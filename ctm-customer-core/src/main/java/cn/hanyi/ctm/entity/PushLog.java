package cn.hanyi.ctm.entity;

import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.BaseEntity;
import org.befun.core.entity.EnterpriseEntity;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.entity.annotation.EntityScopeStrategy;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Getter
@Setter
@Table(name = "push_log")
@DtoClass(includeAllFields = true)
@EntityScopeStrategy
public class PushLog extends EnterpriseEntity {
    @Column(name = "user_id")
    private Long userId;


    private Long customer_id;
    private Long thirdparty_id;

    private int status;
}
