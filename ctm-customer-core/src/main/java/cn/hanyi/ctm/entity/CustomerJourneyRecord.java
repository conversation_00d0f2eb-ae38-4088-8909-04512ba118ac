package cn.hanyi.ctm.entity;

import cn.hanyi.ctm.dto.customer.DisturbStatus;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Table(name = "customer_journey_record")
@DtoClass(includeAllFields = true)
public class CustomerJourneyRecord extends BaseEntity {

    @Column(name = "customer_id")
    @JsonView(ResourceViews.Detail.class)
    private Long customerId;

    @Column(name = "department_id")
    private Long departmentId;

    @Column(name = "department_title")
    @JsonView(ResourceViews.Basic.class)
    private String departmentTitle = "";

    @Column(name = "journey_id")
    private Long journeyId;

    @Column(name = "journey_title")
    @JsonView(ResourceViews.Basic.class)
    private String journeyTitle = "";

    @JsonView(ResourceViews.Basic.class)
    private String details = "";

    @Column(name = "created_by_uid")
    @JsonView(ResourceViews.Basic.class)
    private Long createdByUid;

    @Column(name = "created_by_name")
    @JsonView(ResourceViews.Basic.class)
    private String createdByName = "";

    @Column(name = "send_status")
    @JsonView(ResourceViews.Basic.class)
    private DisturbStatus disturbStatus = DisturbStatus.INIT;

    @Column(name = "task_progress_id")
    @JsonView(ResourceViews.Basic.class)
    private Long taskProgressId;

    @Column(name = "survey_id")
    @JsonView(ResourceViews.Basic.class)
    private Long surveyId;

    @Column(name = "client_id")
    @JsonView(ResourceViews.Basic.class)
    private String clientId;

    @Column(name = "url_params")
    @JsonView(ResourceViews.Basic.class)
    private String urlParams;

    public DisturbStatus getSendStatus() {
        return disturbStatus == null ? DisturbStatus.INIT : disturbStatus;
    }

}
