package cn.hanyi.ctm.entity.spec;

import cn.hanyi.ctm.entity.ThirdPartyCustomer;
import cn.hanyi.ctm.entity.ThirdPartyTemplate;
import org.springframework.data.jpa.domain.Specification;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

/**
 * The class description
 *
 * <AUTHOR>
 */
public class ThirdPartyCustomerSpecs {

    public static Specification<ThirdPartyCustomer> openIdEqual(String openId){
        return new Specification<ThirdPartyCustomer>() {
            @Override
            public Predicate toPredicate(Root<ThirdPartyCustomer> root,
                                         CriteriaQuery<?> query,
                                         CriteriaBuilder cb) {
                return cb.equal(root.get("openId"), openId);
            }
        };
    }

    public static Specification<ThirdPartyCustomer> orgIdAndOpenIdEqual(Long orgId, String openId){
        return new Specification<ThirdPartyCustomer>() {
            @Override
            public Predicate toPredicate(Root<ThirdPartyCustomer> root,
                                         CriteriaQuery<?> query,
                                         CriteriaBuilder cb) {
                return cb.and(
                        cb.equal(root.get("orgId"), orgId),
                        cb.equal(root.get("openId"), openId)
                );
            }
        };
    }
}
