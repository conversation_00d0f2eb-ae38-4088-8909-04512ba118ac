package cn.hanyi.ctm.entity;

import cn.hanyi.ctm.constant.customer.CustomerBatchOperationType;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;

@Entity
@Getter
@Setter
@NoArgsConstructor
@JsonView(ResourceViews.Basic.class)
@EntityScopeStrategy
@Table(name = "batch_operation")
@DtoClass(includeAllFields = true)
public class CustomerBatchOperation extends EnterpriseEntity {
    private CustomerBatchOperationType type;
    private String medium = "survey";
}
