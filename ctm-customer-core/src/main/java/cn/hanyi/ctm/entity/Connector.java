package cn.hanyi.ctm.entity;

import cn.hanyi.ctm.constant.SourceType;
import cn.hanyi.ctm.constant.connector.*;
import cn.hanyi.ctm.dto.ConnectorAuthorizeResultDto;
import cn.hanyi.ctm.dto.ConnectorParamsDtoConverter;
import cn.hanyi.ctm.dto.ConnectorPushConditionConverter;
import cn.hanyi.ctm.dto.connector.ConnectorAuthorizeConfigDto;
import cn.hanyi.ctm.dto.connector.ConnectorAuthorizeConfigDtoConverter;
import cn.hanyi.ctm.dto.connector.ConnectorParamsDto;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.SQLDelete;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

@Entity
@Getter
@Setter
@Table(name = "connector")
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@SQLDelete(sql = "UPDATE connector SET is_delete = 1 WHERE id=?")
@Where(clause = "is_delete=0")
@EntityScopeStrategy
@DtoClass(includeAllFields = true)
public class Connector extends EnterpriseEntity {

    @Column(name="app_id")
    private String appId;

    @Column(name="app_secret")
    @JsonIgnore
    private String appSecret;

    @Enumerated(EnumType.ORDINAL)
    @JsonView(ResourceViews.Basic.class)
    private ConnectorStatus status = ConnectorStatus.READY;

    @Column(name = "is_delete")
    @JsonIgnore
    private Boolean isDelete = false;

    @Column(name="authorize_status")
    @Enumerated(EnumType.ORDINAL)
    @JsonView(ResourceViews.Basic.class)
    private ConnectorAuthorizeStatus authorizeStatus = ConnectorAuthorizeStatus.AUTHORIZED;

    @Enumerated(EnumType.ORDINAL)
    @JsonView(ResourceViews.Basic.class)
    private ConnectorType type = ConnectorType.SMS;

    @Enumerated(EnumType.ORDINAL)
    @JsonView(ResourceViews.Basic.class)
    private SourceType source = SourceType.USER;

    @Column(name="is_default")
    @JsonIgnore
    private Boolean isDefault = false;

    @Column(name="provider_type")
    @Enumerated(EnumType.ORDINAL)
    @JsonView(ResourceViews.Basic.class)
    private ConnectorProviderType providerType = ConnectorProviderType.FEIGE;

    @JsonView(ResourceViews.Basic.class)
    private String logo;

    @Column(name = "name")
    @JsonView(ResourceViews.Basic.class)
    private String name;

    @Column(name = "description")
    @JsonView(ResourceViews.Basic.class)
    private String description;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "gateway")
    private String gateway;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "authorize_config")
    @Convert(converter = ConnectorAuthorizeConfigDtoConverter.class)
    private ConnectorAuthorizeConfigDto authorizeConfig = new ConnectorAuthorizeConfigDto();

    @Column(name = "http_method", columnDefinition = "TINYINT(1)")
    @Schema(description = "请求方式")
    @JsonView(ResourceViews.Basic.class)
    private ConnectorHttpType httpMethod = ConnectorHttpType.POST;

    @Column(name = "data_type", columnDefinition = "TINYINT(1)")
    @Schema(description = "数据结构")
    @JsonView(ResourceViews.Basic.class)
    private ConnectorDataType dataType = ConnectorDataType.JSON;


    @JsonView(ResourceViews.Basic.class)
    @JsonIgnore
    @Column(name = "related_id")
//    @Convert(converter = LongListConverter.class)
    @Schema(description = "推送关联的id")
    private Long relatedId;

    @JsonView(ResourceViews.Basic.class)
    @Convert(converter = ConnectorPushConditionConverter.class)
    @Column(name = "conditions", columnDefinition = "VARCHAR(20)")
    @Schema(description = "推送条件")
    private List<ConnectorPushCondition> condition = new ArrayList<>();

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "push_type", columnDefinition = "TINYINT(1)")
    @Schema(description = "数据类型")
    private ConnectorPushType pushType;

    @JsonView(ResourceViews.Basic.class)
    @Convert(converter = ConnectorParamsDtoConverter.class)
    @Column(name = "params")
    @Schema(description = "附加数据")
    private List<ConnectorParamsDto> params;

    @Override
    public String toString() {
        return String.format(
                "User[id=%d, name='%s']",
                id, name);
    }

    public Connector(ConnectorAuthorizeResultDto dto, ConnectorProviderType providerType) {
        this.appId = dto.getAppId();
        this.logo = dto.getLogo();
        this.name = dto.getName();
        this.description = dto.getDescription();
        this.providerType = providerType;
        this.status = ConnectorStatus.READY;
        this.authorizeStatus = ConnectorAuthorizeStatus.AUTHORIZED;
        this.source = SourceType.USER;
        this.type = ConnectorType.PLATFORM;

    }
}
