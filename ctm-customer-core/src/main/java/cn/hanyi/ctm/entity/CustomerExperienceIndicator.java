package cn.hanyi.ctm.entity;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.constant.EntityScopeStrategyType;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import javax.validation.constraints.Size;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Table(name = "customer_experience_indicator")
@DtoClass(includeAllFields = true)
@EntityScopeStrategy({EntityScopeStrategyType.ORGANIZATION})
public class CustomerExperienceIndicator extends EnterpriseEntity {
    @Column(name = "customer_id")
    @JsonView(ResourceViews.Detail.class)
    private Long customerId;

    @Column(name = "survey_id")
    @JsonView(ResourceViews.Basic.class)
    private Long surveyId;

    @Column(name = "question_id")
    @JsonView(ResourceViews.Basic.class)
    private Long questionId;

    @Column(name = "item_id")
    @JsonView(ResourceViews.Basic.class)
    private Long itemId;

    @Column(name = "name")
    @Size(max = 20)
    @JsonView(ResourceViews.Basic.class)
    private String name;

}
