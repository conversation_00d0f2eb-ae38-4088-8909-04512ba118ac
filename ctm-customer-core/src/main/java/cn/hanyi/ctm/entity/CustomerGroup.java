package cn.hanyi.ctm.entity;

import cn.hanyi.ctm.dto.ext.CustomerGroupExtDto;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.constant.EntityScopeStrategyType;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Getter
@Setter
@Table(name = "customer_group")
@NoArgsConstructor
@EntityScopeStrategy({EntityScopeStrategyType.ORGANIZATION})
@DtoClass(includeAllFields = true, superClass = CustomerGroupExtDto.class)
public class CustomerGroup extends EnterpriseEntity {

    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    @Column(name = "name")
    private String name;

    @DtoProperty(jsonView = ResourceViews.Basic.class)
    @Column(name = "code")
    private String code;
}
