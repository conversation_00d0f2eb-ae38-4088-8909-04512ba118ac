package cn.hanyi.ctm.entity;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Getter
@Setter
@Table(name = "customer_stat")
@NoArgsConstructor
public class CustomerStat extends BaseEntity {

    @Column(name = "customer_id")
    private Long customerId;

    @Column(name = "count_journey_record")
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "客户历程数")
    private Integer countJourneyRecord;

    @Column(name = "last_journey_record_time")
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "最后历程时间")
    private Date lastJourneyRecordTime;

    @Column(name = "count_send_survey")
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "问卷发送数")
    private Integer countSendSurvey;

    @Column(name = "last_send_survey_time")
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "最后问卷发送时间")
    private Date lastSendSurveyTime;

    @Column(name = "count_complete_survey")
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "问卷完成数")
    private Integer countCompleteSurvey;

    @Column(name = "last_complete_survey_time")
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "最后问卷完成时间")
    private Date lastCompleteSurveyTime;

    @Column(name = "count_join_survey")
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "问卷参与数")
    private Integer countJoinSurvey;

    @Column(name = "count_event")
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "预警触发数")
    private Integer countEvent;

    @Column(name = "last_event_time")
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "最后预警触发时间")
    private Date lastEventTime;

    @Column(name = "most_event_rule")
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "预警统计：触发次数最多的预警规则，次数（预警规则名称）")
    private String mostEventRule;
    @Column(name = "most_event_rule_times")
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "预警统计：触发次数最多的预警规则，次数")
    private Integer mostEventRuleTimes;
    @Column(name = "most_event_rule_name")
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "预警统计：触发次数最多的预警规则，名称")
    private String mostEventRuleName;

    @Column(name = "journey_indicator")
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "体验指标：最近一次评价的指标和得分，分值（体验指标名称）")
    private String journeyIndicator;
    @Column(name = "journey_indicator_score")
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "体验指标：最近一次评价的指标和得分，分值")
    private Double journeyIndicatorScore;
    @Column(name = "journey_indicator_name")
    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "体验指标：最近一次评价的指标和得分，名称")
    private String journeyIndicatorName;
}
