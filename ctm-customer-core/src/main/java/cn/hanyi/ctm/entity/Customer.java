package cn.hanyi.ctm.entity;

import cn.hanyi.ctm.constant.connector.ConnectorAuthorizeStatus;
import cn.hanyi.ctm.constant.customer.CustomerConnectionStatus;
import cn.hanyi.ctm.constant.customer.CustomerSourceType;
import cn.hanyi.ctm.dto.ext.CustomerExtDto;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonPropertyDescription;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.constant.EntityScopeStrategyType;
import org.befun.core.converter.HashMapConverter;
import org.befun.core.converter.ListConverter;
import org.befun.core.converter.StringTreeListConverter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.UpdateTimestamp;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.util.*;

@Entity
@Getter
@Setter
@NoArgsConstructor
@Where(clause = "is_delete=0")
@JsonView(ResourceViews.Basic.class)
//@EntityQueryable({"username", "mobile"})
@Table(name = "customer")
@EntityScopeStrategy({EntityScopeStrategyType.DEPARTMENT})
@DtoClass(includeAllFields = true, superClass = CustomerExtDto.class)
public class Customer extends EnterpriseEntity {
    @JsonView(ResourceViews.Basic.class)
    @JsonPropertyDescription("姓名")
    private String username = "";
    @JsonView(ResourceViews.Basic.class)
    @JsonPropertyDescription("昵称")
    private String nickname = "";
    @JsonView(ResourceViews.Basic.class)
    @JsonPropertyDescription("性别")
    private String gender = "";

    @JsonPropertyDescription("邮箱")
    @JsonView(ResourceViews.Basic.class)
    private String email = "";
    @JsonView(ResourceViews.Basic.class)
    @JsonPropertyDescription("出生日期")
    private String birthday = "";
    @JsonView(ResourceViews.Basic.class)
    @JsonPropertyDescription("头像")
    private String avatar = "";
    @JsonPropertyDescription("手机号码")
    @JsonView(ResourceViews.Basic.class)
    private String mobile = "";
    @JsonView(ResourceViews.Basic.class)
    @JsonPropertyDescription("城市")
    private String city = "";
    @JsonView(ResourceViews.Basic.class)
    @JsonPropertyDescription("区县")
    private String district = "";
    @JsonView(ResourceViews.Basic.class)
    @JsonPropertyDescription("省份")
    private String province = "";
    @JsonView(ResourceViews.Basic.class)
    @JsonPropertyDescription("详细地址")
    private String address = "";
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "membership_level")
    private String membershipLevel = "";
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "department_id")
    private Long departmentId = 0L;
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "level_ids")
    private String levelIds = "";
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "department_names")
    @JsonPropertyDescription("所属层级")
    private String departmentNames = "";
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "belong_to_uids")
    private String belongToUids = "";
    @JsonView(ResourceViews.Basic.class)
    @Convert(converter = StringTreeListConverter.class)
    @Column(name = "belong_to_uid_names")
    @JsonPropertyDescription("所属员工")
    private List<String> belongToUidNames = List.of();
    @JsonView(ResourceViews.Basic.class)
    @Convert(converter = ListConverter.class)
    @JsonPropertyDescription("标签")
    private List<String> tags = List.of();
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "created_by_uid")
    private Long createdByUid;
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "modified_by_uid")
    private Long modifiedByUid;

    @Column(name = "is_delete")
    @JsonIgnore
    private Boolean isDelete = false;
    @JsonView(ResourceViews.Basic.class)
    @Temporal(TemporalType.TIMESTAMP)
    @UpdateTimestamp
    @Column(name = "latest_active_time")
    @JsonIgnore
    public Date latestActiveTime;
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "latest_journey_record")
    public String latestJourneyRecord;
    @JsonView(ResourceViews.Basic.class)
    @Enumerated(EnumType.ORDINAL)
    private CustomerSourceType source = CustomerSourceType.SYSTEM;
    @JsonView(ResourceViews.Basic.class)
    @Enumerated(EnumType.ORDINAL)
    @Column(name = "status")
    private CustomerConnectionStatus status = CustomerConnectionStatus.UNKNOWN;
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "authorize_status")
    @Enumerated(EnumType.ORDINAL)
    private ConnectorAuthorizeStatus authorizeStatus = ConnectorAuthorizeStatus.AUTHORIZED;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "thirdparty_customer_id")
    private Long thirdPartyCustomerId;

    @JsonView(ResourceViews.Basic.class)
    // 外部客户id，由企业自定义
    @Column(name = "external_user_id")
    private String externalUserId;

    @JsonView(ResourceViews.Basic.class)
    @Convert(converter = HashMapConverter.class)
    @Column(name = "extend_fields")
    private Map<String, Object> extendFields = new HashMap<>();

    // 发送客户问卷时的临时数据
    // 发送内容的占位符参数
    @Transient
    private Map<String, String> contentParams = new HashMap<>();
    // 问卷外参内部参数
    @Transient
    private UrlInternalParams urlInternalParams = new UrlInternalParams();
    // 问卷外参自定义参数
    @Transient
    private Map<String, String> urlCustomParams = new HashMap<>();
    // 客户的微信用户信息
    @Transient
    private WechatParams wechatParams = new WechatParams();

    @Getter
    @Setter
    public static class UrlInternalParams {
        private Long departmentId;
        private String departmentCode;
        private String externalCompanyId;
        private String expireTime;
    }

    @Getter
    @Setter
    public static class WechatParams {
        private Long configId;
        private String appId;
        private String openId;
    }

    public List<String> getTags() {
        if (tags == null) {
            tags = new ArrayList<>();
        }
        return tags;
    }

    public Customer(ThirdPartyCustomer tpCustomer, Long orgId) {
        this.orgId = orgId;
        this.nickname = tpCustomer.getNickname();
        this.username = tpCustomer.getNickname();
        this.avatar = tpCustomer.getAvatar();
        this.city = tpCustomer.getCity();
        this.province = tpCustomer.getProvince();
        this.thirdPartyCustomerId = tpCustomer.getId();
        this.source = CustomerSourceType.WECHATOPEN;
        this.status = tpCustomer.isSubscribed()
                ? CustomerConnectionStatus.SUBSCRIBED
                : CustomerConnectionStatus.UNSUBSCRIBED;
        if (tpCustomer.getSex() != null) {
            this.gender = tpCustomer.getSex().getLabel();
        }

    }
}
