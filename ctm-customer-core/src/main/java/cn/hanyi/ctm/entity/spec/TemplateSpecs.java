package cn.hanyi.ctm.entity.spec;

import cn.hanyi.ctm.entity.Template;
import org.springframework.data.jpa.domain.Specification;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

/**
 * The class description
 *
 * <AUTHOR>
 */
public class TemplateSpecs {

    public static Specification<Template> openIdEqual(String openId){
        return new Specification<Template>() {
            @Override
            public Predicate toPredicate(Root<Template> root,
                                         CriteriaQuery<?> query,
                                         CriteriaBuilder cb) {
                return cb.equal(root.get("openId"), openId);
            }
        };
    }
}
