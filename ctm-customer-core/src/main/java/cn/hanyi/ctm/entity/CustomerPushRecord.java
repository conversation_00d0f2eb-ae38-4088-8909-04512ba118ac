//package cn.hanyi.ctm.entity;
//
//import cn.hanyi.ctm.constant.customer.CustomerPushStatus;
//import cn.hanyi.ctm.constant.customer.CustomerPushType;
//import lombok.Getter;
//import lombok.Setter;
//import org.befun.core.dto.annotation.DtoClass;
//import org.befun.core.entity.EnterpriseEntity;
//
//import javax.persistence.*;
//
//@Setter
//@Getter
//@Entity
//@Table(name = "customer_push_record")
//@DtoClass
//public class CustomerPushRecord extends EnterpriseEntity {
//
//    @Column(name = "customer_id")
//    private Long customerId;
//
//    @Column(name = "customer_name")
//    private String customerName;
//
//    @Column(name = "customer_euid")
//    private String customerEuid;
//
//    @Column(name = "customer_mobile")
//    private String customerMobile;
//
//    @Column(name = "survey_id")
//    private Long surveyId;
//
//    @Column(name = "relation_type")
//    private String relationType;
//
//    @Column(name = "relation_id")
//    private Long relationId;
//
//    @Enumerated(value = EnumType.STRING)
//    @Column(name = "push_type")
//    private CustomerPushType pushType;
//
//    @Column(name = "push_params")
//    private String pushParams;
//
//    @Column(name = "push_content")
//    private String pushContent;
//
//    @Enumerated(value = EnumType.STRING)
//    @Column(name = "push_status")
//    private CustomerPushStatus pushStatus = CustomerPushStatus.INIT;
//
//    @Column(name = "push_response")
//    private String pushResponse;
//
//    @Column(name = "sms_cost")
//    private Integer smsCost;
//
//}