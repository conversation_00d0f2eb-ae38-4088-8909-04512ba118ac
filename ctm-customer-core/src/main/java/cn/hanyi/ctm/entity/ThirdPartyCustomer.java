package cn.hanyi.ctm.entity;

import cn.hanyi.ctm.constant.GenderType;
import cn.hanyi.ctm.constant.connector.ConnectorAuthorizeStatus;
import cn.hanyi.ctm.dto.connector.ThirdPartyCustomerDto;
import cn.hanyi.ctm.dto.ext.ThirdPartyCustomerExtDto;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;

@Entity
@Getter
@Setter
@Table(name = "thirdparty_customer")
@NoArgsConstructor
@EntityScopeStrategy
@DtoClass(includeAllFields = true, superClass = ThirdPartyCustomerExtDto.class)
public class ThirdPartyCustomer extends EnterpriseEntity {
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "open_id")
    private String openId;

    @Column(name = "union_id")
    private String unionId;

    @Column(name = "is_delete")
    private Boolean isDelete = false;

    private String name;
    @JsonView(ResourceViews.Basic.class)
    private String nickname;
    private String avatar;
    private GenderType sex;
    private String remark;
    private String city;
    private String province;
    private String country;

    @Column(name = "is_subscribed")
    private boolean isSubscribed;

    @Column(name = "subscribed_at")
    private Long subscribedAt;

    @DtoProperty(ignore = true)
    @OneToOne()
    @JoinColumn(name = "connector_id")
    @NotFound(action = NotFoundAction.IGNORE)
    private Connector connector;

    @Column(name = "thirdparty_auth_id")
    private Long thirdpartyAuthId;

    @Override
    public String toString() {
        return String.format(
                "User[id=%d, openid='%s']",
                id, openId);
    }

    @Column(name = "authorize_status")
    @Enumerated(EnumType.ORDINAL)
    private ConnectorAuthorizeStatus authorizeStatus = ConnectorAuthorizeStatus.AUTHORIZED;

    public ThirdPartyCustomer(String openId, Connector connector) {
        this.connector = connector;
        this.orgId = connector.getOrgId();
        this.openId = openId;
    }

    public ThirdPartyCustomer(Long orgId, Long thirdpartyAuthId, String openId) {
        this.orgId = orgId;
        this.thirdpartyAuthId = thirdpartyAuthId;
        this.openId = openId;
    }

    public ThirdPartyCustomer(ThirdPartyCustomerDto customerDto, Connector connector) {
        this.connector = connector;
        this.orgId = connector.getOrgId();
        this.unionId = customerDto.getUnionId();
        this.openId = customerDto.getOpenId();

        this.city = customerDto.getCity();
        this.province = customerDto.getProvince();
        this.country = customerDto.getCountry();
        this.avatar = customerDto.getAvatar();
        this.nickname = customerDto.getNickname();
        this.remark = customerDto.getRemark();
        this.isSubscribed = customerDto.isSubscribed();
        this.subscribedAt = customerDto.getSubscribedAt();
        this.sex = customerDto.getSex();
    }
}
