package cn.hanyi.ctm.entity;

import cn.hanyi.ctm.constant.AnswerStatus;
import cn.hanyi.ctm.constant.InteractionCollectorType;
import cn.hanyi.ctm.constant.SendStatus;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;
import javax.persistence.*;
import java.util.Date;

@Entity
@Getter
@Setter
@NoArgsConstructor
@DtoClass(includeAllFields = true)
public class CustomerAnswers extends BaseEntity {
    @ManyToOne
    @JoinColumn(name = "customer_id")
    @JsonView(ResourceViews.Detail.class)
    private Customer customer;

    @Column(name = "journey_record_id")
    private Long journeyRecordId = 0L;

    @JsonView(ResourceViews.Basic.class)
    private String sid;

    @Column(name = "survey_version_id")
    @JsonView(ResourceViews.Basic.class)
    private Long surveyVersionId;

    @Column(name = "answer_id")
    @JsonView(ResourceViews.Basic.class)
    private Long answerId = 0L;

    @Column(name = "survey_name")
    @JsonView(ResourceViews.Basic.class)
    private String surveyName;

    @JsonView(ResourceViews.Basic.class)
    private InteractionCollectorType channel = InteractionCollectorType.SMS;

    @Column(name = "send_status")
    @JsonView(ResourceViews.Basic.class)
    private SendStatus sendStatus = SendStatus.SUCCESS;

    @Column(name = "answer_status")
    @JsonView(ResourceViews.Basic.class)
    private AnswerStatus answerStatus = AnswerStatus.UNFILLED;

    @Column(name = "department_ids")
    private String departmentIds;

    @Column(name = "duration_seconds")
    @JsonView(ResourceViews.Basic.class)
    private int durationSeconds;

    @Column(name = "is_delete")
    private Boolean isDelete = Boolean.FALSE;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "answer_time")
    private Date answerTime;

    @Column(name = "utm_medium")
    private String utmMedium;

    @Column(name = "utm_campaign")
    private String utmCampaign;

    public CustomerAnswers(Customer customer, String sid, String surveyName, String departmentIds, InteractionCollectorType channel, SendStatus sendStatus) {
        this.customer = customer;
        this.sid = sid;
        this.surveyName = surveyName;
        this.departmentIds = departmentIds;
        this.channel = channel;
        this.sendStatus = sendStatus;
    }

}
