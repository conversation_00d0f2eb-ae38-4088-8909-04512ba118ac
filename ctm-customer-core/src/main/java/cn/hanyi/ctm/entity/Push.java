package cn.hanyi.ctm.entity;

import cn.hanyi.ctm.constant.PushStatus;
import cn.hanyi.ctm.constant.PushStrategy;
import cn.hanyi.ctm.constant.connector.ConnectorType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.Hidden;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.converter.LongListConverter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;
import java.util.ArrayList;
import java.util.List;

@Entity
@Getter
@Setter
@DtoClass(includeAllFields = true)
@EntityScopeStrategy
public class Push extends EnterpriseEntity {
    @Convert(converter = LongListConverter.class)
    @Hidden
    @JsonIgnore
    private List<Long> ids = new ArrayList<>();

    @Enumerated(EnumType.ORDINAL)
    private PushStrategy strategy;

    @JsonView(ResourceViews.Basic.class)
    private String name;

    @Enumerated(EnumType.ORDINAL)
    private ConnectorType type;

    @ManyToOne()
    @JoinColumn(name = "connector_id")
    private Connector connector;

    @JsonView(ResourceViews.Basic.class)
    private String content;

    @JsonView(ResourceViews.Basic.class)
    private String address;

    private int total;
    private int completed;

    @Column(name = "response", columnDefinition = "VARCHAR(500)")
    @Schema(description = "推送响应内容")
    @JsonView(ResourceViews.Basic.class)
    private String response;

    @Column(name = "status", columnDefinition = "TINYINT(1)")
    @Schema(description = "推送状态")
    @JsonView(ResourceViews.Basic.class)
    private PushStatus status;

    @Column(name = "retry")
    @Schema(description = "推送次数")
    @JsonView(ResourceViews.Basic.class)
    private int retry;

}
