package cn.hanyi.ctm.entity;

import cn.hanyi.ctm.dto.ConnectorParamsDtoConverter;
import cn.hanyi.ctm.dto.connector.ConnectorParamsDto;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;
import java.util.List;

@Entity
@Getter
@Setter
@Table(name = "connector_consumer")
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
@EntityScopeStrategy
@DtoClass(includeAllFields = true)
public class ConnectorConsumer extends EnterpriseEntity {

    @Schema(description = "用户id")
    @Column(name = "user_id")
    @JsonIgnore
    protected Long userId;

    @ManyToOne()
    @JoinColumn(name = "connector_id")
    @JsonView(ResourceViews.Basic.class)
    @NotFound(action = NotFoundAction.IGNORE)
    private Connector connector;

    @Schema(description = "需要推送的问卷id")
    @JoinColumn(name = "relation_id")
    @JsonView(ResourceViews.Basic.class)
    private Long relationId;

    @Schema(description = "开启、关闭推送")
    @JsonView(ResourceViews.Basic.class)
    private Boolean enable = true;

    @JsonView(ResourceViews.Basic.class)
    @Convert(converter = ConnectorParamsDtoConverter.class)
    @Column(name = "params")
    @Schema(description = "附加数据")
    private List<ConnectorParamsDto> params;


    @JsonView(ResourceViews.Basic.class)
    @Column(name = "content")
    @Schema(description = "自定义内容 需要是自行解析的json格式")
    private String content;

    public ConnectorConsumer(Connector connector, Long relationId) {
        this.connector = connector;
        this.relationId = relationId;
    }

    public ConnectorConsumer(Connector connector, Long relationId, List params) {
        this.connector = connector;
        this.relationId = relationId;
        this.params = params;
    }

    public ConnectorConsumer(Connector connector, Long relationId, Boolean enable) {
        this.connector = connector;
        this.relationId = relationId;
        this.enable = enable;
    }

    public ConnectorConsumer(Connector connector, Long relationId, Boolean enable, List<ConnectorParamsDto> params) {
        this.connector = connector;
        this.relationId = relationId;
        this.enable = enable;
        this.params = params;
    }


    public ConnectorConsumer(Connector connector, Long relationId, Boolean enable, String content) {
        this.connector = connector;
        this.relationId = relationId;
        this.enable = enable;
        this.content = content;
    }
}
