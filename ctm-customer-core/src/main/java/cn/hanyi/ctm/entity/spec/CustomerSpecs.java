package cn.hanyi.ctm.entity.spec;

import cn.hanyi.ctm.entity.Customer;
import cn.hanyi.ctm.entity.ThirdPartyCustomer;
import org.springframework.data.jpa.domain.Specification;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

/**
 * The class description
 *
 * <AUTHOR>
 */
public class CustomerSpecs {

    public static Specification<Customer> byTpCustomer(ThirdPartyCustomer tpCustomer){
        return new Specification<Customer>() {
            @Override
            public Predicate toPredicate(Root<Customer> root,
                                         CriteriaQuery<?> query,
                                         CriteriaBuilder cb) {
                return cb.equal(root.get("thirdPartyCustomer"), tpCustomer);
            }
        };
    }
}
