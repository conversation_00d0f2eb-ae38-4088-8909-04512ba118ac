package cn.hanyi.ctm.entity;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.converter.HashMapConverter;
import org.befun.core.converter.LongCommaListConverter;
import org.befun.core.converter.MapListConverter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Convert;
import javax.persistence.Entity;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EntityScopeStrategy
@DtoClass(includeAllFields = true)
public class WechatTemplate extends EnterpriseEntity {

    @DtoProperty(queryable = true, jsonView = ResourceViews.Basic.class)
    private String name = "";

    @JsonView(ResourceViews.Basic.class)
    private String example = "";

    @Convert(converter = HashMapConverter.class)
    @JsonView(ResourceViews.Basic.class)
    private Map<String, Object> content = new HashMap<>();

    @Convert(converter = MapListConverter.class)
    @JsonView(ResourceViews.Basic.class)
    private List<Map<String, Object>> parameters = new ArrayList<>();

    @Convert(converter = LongCommaListConverter.class)
    @JsonView(ResourceViews.Basic.class)
    @Column(name = "thirdparty_template_ids")
    private List<Long> thirdpartyTemplateIds = new ArrayList<>();

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "source_thirdparty_template_id")
    private Long sourceThirdpartyTemplateId;
}
