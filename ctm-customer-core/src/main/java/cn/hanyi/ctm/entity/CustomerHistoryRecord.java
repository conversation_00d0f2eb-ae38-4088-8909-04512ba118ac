package cn.hanyi.ctm.entity;

import cn.hanyi.ctm.constant.RecordType;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.Column;
import javax.persistence.Entity;

@Entity
@Getter
@Setter
@NoArgsConstructor
@DtoClass(includeAllFields = true)
public class CustomerHistoryRecord extends BaseEntity {

    @Column(name = "customer_id")
    private Long customerId;

    @Column(name = "sid")
    private String sid;

    @Column(name = "record_type")
    @JsonView(ResourceViews.Basic.class)
    private RecordType recordType;

    @Column(name = "record_content")
    @JsonView(ResourceViews.Basic.class)
    private String recordContent;

    @Column(name = "created_by_uid")
    private Long createByUid;

    @Column(name = "journey_record_id")
    private Long journeyRecordId = 0L;

    @Column(name = "is_delete")
    private Boolean isDelete = Boolean.FALSE;

}
