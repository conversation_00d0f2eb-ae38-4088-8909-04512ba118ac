package cn.hanyi.ctm.entity;

import cn.hanyi.ctm.constant.connector.ConnectorType;
import cn.hanyi.ctm.constant.MessageType;
import cn.hanyi.ctm.constant.SourceType;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.*;
import org.befun.core.converter.HashMapConverter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;
import java.util.HashMap;
import java.util.Map;

@Entity
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EntityScopeStrategy
@DtoClass(includeAllFields = true)
public class Template extends EnterpriseEntity {
    @ManyToOne()
    @JoinColumn(name = "connector_id")
    @JsonView(ResourceViews.Basic.class)
    @NotFound(action = NotFoundAction.IGNORE)
    private Connector connector;

    @Column(name = "is_delete")
    private Boolean isDelete = false;

    @OneToOne()
    @JoinColumn(name = "thirdparty_template_id")
    @JsonView(ResourceViews.Basic.class)
    @NotFound(action = NotFoundAction.IGNORE)
    private ThirdPartyTemplate thirdPartyTemplate;

    @JsonView(ResourceViews.Basic.class)
    private String name = "";
    @JsonView(ResourceViews.Basic.class)
    private String example = "";
    private String url = "";

    @Convert(converter = HashMapConverter.class)
    @JsonView(ResourceViews.Basic.class)
    private Map<String, Object> content = new HashMap<>();

    @Enumerated(EnumType.ORDINAL)
    @JsonView(ResourceViews.Basic.class)
    private ConnectorType type;

    @Enumerated(EnumType.ORDINAL)
    @JsonView(ResourceViews.Basic.class)
    private SourceType sourceType = SourceType.USER;

    @Column(name = "message_type")
    @Enumerated(EnumType.ORDINAL)
    @JsonView(ResourceViews.Basic.class)
    private MessageType messageType = MessageType.TEMPLATE;
}
