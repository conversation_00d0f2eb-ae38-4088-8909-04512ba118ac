package cn.hanyi.ctm.entity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.entity.BaseEntity;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Getter
@Setter
@Table(name = "customer_group_relation")
@NoArgsConstructor
public class CustomerGroupRelation extends BaseEntity {

    @Column(name = "customer_id")
    private Long customerId;

    @Column(name = "group_id")
    private Long groupId;

    public CustomerGroupRelation(Long customerId, Long groupId) {
        this.customerId = customerId;
        this.groupId = groupId;
    }
}
