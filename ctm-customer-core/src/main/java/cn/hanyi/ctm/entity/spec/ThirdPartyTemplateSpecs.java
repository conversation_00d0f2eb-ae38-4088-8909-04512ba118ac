package cn.hanyi.ctm.entity.spec;

import cn.hanyi.ctm.entity.Template;
import cn.hanyi.ctm.entity.ThirdPartyTemplate;
import org.springframework.data.jpa.domain.Specification;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

/**
 * The class description
 *
 * <AUTHOR>
 */
public class ThirdPartyTemplateSpecs {

    public static Specification<ThirdPartyTemplate> openIdEqual(Long orgId, String openId){
        return new Specification<ThirdPartyTemplate>() {
            @Override
            public Predicate toPredicate(Root<ThirdPartyTemplate> root,
                                         CriteriaQuery<?> query,
                                         CriteriaBuilder cb) {
                return cb.and(
                    cb.equal(root.get("orgId"), orgId),
                    cb.equal(root.get("openId"), openId)
                );
            }
        };
    }
}
