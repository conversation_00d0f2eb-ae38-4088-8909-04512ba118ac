package cn.hanyi.ctm.connector;

import cn.hanyi.ctm.connector.provider.ISmsProvider;
import cn.hanyi.ctm.constant.connector.ConnectorFeature;
import cn.hanyi.ctm.constant.connector.ConnectorType;
import cn.hanyi.ctm.dto.journey.PushMomentDto;
import cn.hanyi.ctm.entity.Connector;
import cn.hanyi.ctm.entity.Customer;
import cn.hanyi.ctm.service.ShortUrlService;
import cn.hanyi.ctm.workertrigger.ICtmTaskTrigger;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Getter
@Component("API")
@Slf4j
public class ApiConnectorManager implements IConnectorManager {
    private ConnectorType type = ConnectorType.API;

    @Autowired
    Map<String, ISmsProvider> allProviders;

    @Autowired
    ShortUrlService shortUrlService;

    //    @Autowired
//    IApiSendExecutor apiSendExecutor;
    @Autowired
    private ICtmTaskTrigger ctmTaskTrigger;

    private final List<ConnectorFeature> features = List.of();

    @Override
    public Boolean support(String provider) {
        return allProviders.containsKey(provider);
    }

    @Override
    public void sendApiMessage(Connector connector, Customer customer, Long departmentId, Long sceneId, String url,
                               Object externalUserId, PushMomentDto momentDto) {
//        ApiTaskDetailDto apiTaskDetailDto = new ApiTaskDetailDto();
//        apiTaskDetailDto.setOrgId(connector.getOrgId());
//        apiTaskDetailDto.setConnectorId(connector.getId());
//        apiTaskDetailDto.setCustomerId(customer.getId());
//        apiTaskDetailDto.setDepartmentId(departmentId);
//        apiTaskDetailDto.setSceneId(sceneId);
//        apiTaskDetailDto.setSurveyUrl(url);
//        apiTaskDetailDto.setExternalUserId(externalUserId);
        switch (momentDto.getMomentType()) {
            case IMMEDIATELY:
//                ctmTaskTrigger.customerSendApi(connector.orgId,
//                        null,
//                        null,
//                        connector.getId(),
//                        connector.getId(),
//                        url, sceneId, customer.getId(), externalUserId, departmentId, System.currentTimeMillis(), null, null);
//                apiSendExecutor.performAsync(apiTaskDetailDto);
                break;
            case LATER:
//                ctmTaskTrigger.customerSendApi(connector.orgId,
//                        null,
//                        null,
//                        connector.getId(),
//                        connector.getId(),
//                        url, sceneId, customer.getId(), externalUserId, departmentId, System.currentTimeMillis(), TimeUtils.parseDuration(momentDto.getDuration()), null);
//                apiSendExecutor.performAsyncDelay(apiTaskDetailDto, momentDto.getDuration());
                break;
            case TIMED:
//                TimedTaskDto taskDto = TimedTaskDto.builder()
//                        .timedType(momentDto.getTimedType())
//                        .hour(momentDto.getHour())
//                        .minute(momentDto.getMinute())
//                        .build();
//                String timed = String.format("%s/%s/%d/%d/0", momentDto.getTimedType().name(), String.join(",", momentDto.getWeeks()), momentDto.getHour(), momentDto.getMinute());
//                ctmTaskTrigger.customerSendApi(connector.orgId,
//                        null,
//                        null,
//                        connector.getId(),
//                        connector.getId(),
//                        url, sceneId, customer.getId(), externalUserId, departmentId, System.currentTimeMillis(), null, timed);
//                apiSendExecutor.performAt(apiTaskDetailDto, taskDto);
            default:
                log.error("unexpected moment type {}", momentDto.getMomentType());
        }
    }
}
