package cn.hanyi.ctm.connector.impl.wechatopen;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * The class description
 *
 * <AUTHOR>
 */
public class TemplateParser {
    private static final Pattern WX_PARAM_PATTERN = Pattern.compile("([^\\s]+)：[\\s\\{]{2,}(.+?)\\..+[\\}\\s]{2,}");

    /**
     * parseWechatTemplate
     * <p>
     * 参考链接: https://developers.weixin.qq.com/doc/offiaccount/Message_Management/Template_Message_Interface.html#3
     * {{result.DATA}}
     * 领奖金额:{ {withdrawMoney.DATA} }
     * 领奖时间:{ {withdrawTime.DATA} }
     * 银行信息:{ {cardInfo.DATA} }
     * 到账时间:  { {arrivedTime.DATA} }
     * {{remark.DATA}}
     */
    public static List<Map<String, Object>> parseWechatTemplate0(String body) {
        if (body == null || body.isEmpty()) {
            return null;
        }

        List<Map<String, Object>> params = new ArrayList<>();
        Matcher matcher = WX_PARAM_PATTERN.matcher(body);
        while (matcher.find()) {
            Map<String, Object> param = new HashMap<>();
            param.put("title", matcher.group(1));
            param.put("name", matcher.group(2));
            params.add(param);
        }

        return params;
    }

    public static List<Map<String, Object>> parseWechatTemplate(String body) {
        if (body == null || body.isEmpty()) {
            return null;
        }
        List<Map<String, Object>> params = new ArrayList<>();
        String[] ss = body.split("\\n");
        for (String string : ss) {
            String s = string.trim();
            if (StringUtils.isNotEmpty(s)) {
                String title = null;
                String name = null;
                if (s.contains(":")) {
                    String[] sss = s.split(":");
                    if (sss.length == 2) {
                        title = sss[0].trim();
                        name = sss[1].trim();
                    }
                } else if (s.contains("：")) {
                    String[] sss = s.split("：");
                    if (sss.length == 2) {
                        title = sss[0].trim();
                        name = sss[1].trim();
                    }
                } else {
                    name = s.trim();
                }
                if (StringUtils.isNotEmpty(name) && name.contains("{{")) {
                    name = name.replaceAll("\\{", "").replace("}", "").replaceAll(".DATA", "").trim();
                    if (StringUtils.isEmpty(title)) {
                        title = name;
                    }
                    Map<String, Object> param = new HashMap<>();
                    param.put("title", title);
                    param.put("name", name);
                    params.add(param);

                }
            }
        }
        return params;
    }

    public static void main(String[] args) {
      Object s =   parseWechatTemplate("开始时间:{{time2.DATA}}\\n结束时间:{{time3.DATA}}\\n拜访事由:{{thing4.DATA}}\\n拜访人:{{thing5.DATA}}\\n访问区域:{{thing6.DATA}}\\n");
        System.out.printf(s.toString());
    }
}
