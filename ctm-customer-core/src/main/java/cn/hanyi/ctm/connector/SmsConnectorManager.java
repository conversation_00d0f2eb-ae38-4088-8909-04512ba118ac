package cn.hanyi.ctm.connector;

import cn.hanyi.ctm.connector.provider.ISmsProvider;
import cn.hanyi.ctm.constant.connector.ConnectorFeature;
import cn.hanyi.ctm.constant.connector.ConnectorType;
import cn.hanyi.ctm.dto.journey.PushMomentDto;
import cn.hanyi.ctm.entity.Connector;
import cn.hanyi.ctm.entity.Customer;
import cn.hanyi.ctm.entity.Template;
import cn.hanyi.ctm.service.ShortUrlService;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.repository.OrganizationWalletRepository;
import org.befun.core.limiter.annotation.LimiterTryConsume;
import org.befun.core.template.TemplateEngine;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@Component("SMS")
@Slf4j
public class SmsConnectorManager implements IConnectorManager {
    private final ConnectorType type = ConnectorType.SMS;

    @Autowired
    OrganizationWalletRepository organizationWalletRepository;

    @Autowired
    Map<String, ISmsProvider> allProviders;

    @Autowired
    ShortUrlService shortUrlService;

    private final List<ConnectorFeature> features = List.of();

    @Override
    public Boolean support(String provider) {
        return allProviders.containsKey(provider);
    }

    @Override
    public int evaluateCost(Connector connector, Template template, Customer customer, Map<String, Object> parameters) {
        Assert.notNull(connector, "invalid connector");
        Assert.notNull(template, "invalid template");
        Assert.notNull(customer, "invalid customer");

        String content = TemplateEngine.renderTextTemplate(
                template.getContent().get("content").toString(),
                parameters
        );

        return (int) Math.ceil((shortUrlService.getEvaluateLength() + content.length()) / 70.0);
    }

    @Override
    public Boolean isValidCustomer(Customer customer) {
        if (customer == null) {
            return false;
        }
        return !customer.getMobile().isEmpty();
    }

    @Override
    public Map<String, Object> sendTemplateMessage(Long orgId, Connector connector, Template template,
                                                   Customer customer, Map<String, Object> parameters,
                                                   String url, PushMomentDto momentDto) {
        Map<String, Object> result = new HashMap<>();
        if (connector == null || template == null || customer == null) {
            return result;
        }

        ISmsProvider provider = allProviders.get(connector.getProviderType().name());

        String content = TemplateEngine.renderTextTemplate(
                template.getContent().get("content").toString(),
                parameters
        );

        result.put("content", content);

        // TBD, common stuff of sms
        provider.sendMessage(orgId, connector, template, customer, content, momentDto);
        return result;
    }

    @Override
    public Map<String, Object> sendTextMessage(Long orgId, Connector connector, Customer customer, Map<String, Object> content,
                                               Map<String, Object> parameters, PushMomentDto momentDto) {
        if (connector == null || customer == null) {
            return new HashMap<>();
        }
        ISmsProvider provider = allProviders.get(connector.getProviderType().name());
        return provider.sendTextMessage(connector, customer, content, parameters, momentDto);
    }

    @Override
    @LimiterTryConsume(
            key = "'smsbalance.' + #orgId",
            condition = "true",
            countExpression = "#cost",
            max = 100,
            message = "短信余额不足"
    )
    public void isSufficientBalance(Long orgId, int cost) {
    }
}
