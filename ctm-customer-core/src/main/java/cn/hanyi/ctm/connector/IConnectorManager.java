package cn.hanyi.ctm.connector;

import cn.hanyi.ctm.constant.connector.ConnectorFeature;
import cn.hanyi.ctm.constant.connector.ConnectorProviderType;
import cn.hanyi.ctm.constant.connector.ConnectorType;
import cn.hanyi.ctm.dto.AuthorizeRequestDto;
import cn.hanyi.ctm.dto.connector.ThirdPartyCustomerDto;
import cn.hanyi.ctm.dto.connector.ThirdPartyTemplateDto;
import cn.hanyi.ctm.dto.journey.PushMomentDto;
import cn.hanyi.ctm.entity.Connector;
import cn.hanyi.ctm.entity.Customer;
import cn.hanyi.ctm.entity.Template;
import org.apache.commons.lang3.NotImplementedException;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * The interface description
 *
 * <AUTHOR>
 */
public interface IConnectorManager {
    List<ConnectorFeature> getFeatures();

    Boolean support(String provider);

    ConnectorType getType();

    default Map<String, Object> sendTemplateMessage(Long orgId, Connector connector, Template template,
                                                    Customer customer, Map<String, Object> parameters,
                                                    String url, PushMomentDto momentDto) {
        throw new NotImplementedException();
    }

    default Map<String, Object> sendTextMessage(Long orgId, Connector connector, Customer customer,
                                                Map<String, Object> content,
                                                Map<String, Object> parameters, PushMomentDto momentDto) {
        throw new NotImplementedException();
    }

    default void sendApiMessage(Connector connector, Customer customer, Long departmentId, Long sceneId, String url,
                                Object externalUserId, PushMomentDto momentDto) {
        throw new NotImplementedException();
    }

    default Boolean isValidCustomer(Customer customer) {
        throw new NotImplementedException();
    }

    default String preAuthenticate(ConnectorProviderType providerType) {
        throw new NotImplementedException();
    }

    default Optional<Connector> authorize(ConnectorProviderType providerType, AuthorizeRequestDto requestDto) {
        throw new NotImplementedException();
    }

    default int evaluateCost(Connector connector, Template template, Customer customer, Map<String, Object> parameters) {
        throw new NotImplementedException();
    }

    // template part
    default List<ThirdPartyTemplateDto> syncTemplate(Connector connector) {
        throw new NotImplementedException();
    }

    // customer part
    default List<ThirdPartyCustomerDto> syncCustomer(Long orgId, Long userId, Connector connector) {
        throw new NotImplementedException();
    }

    default ThirdPartyCustomerDto syncOneCustomer(Long orgId, Long userId,Connector connector, String openId) {
        throw new NotImplementedException();
    }

    default void isSufficientBalance(Long orgId, int cost) {
        throw new NotImplementedException();
    }

}
