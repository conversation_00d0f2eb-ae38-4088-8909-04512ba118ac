//package cn.hanyi.ctm.connector.impl.wechatopen;
//
//import cn.hanyi.ctm.connector.IConnectorManager;
//import cn.hanyi.ctm.connector.PlatformConnectorManager;
//import cn.hanyi.ctm.connector.impl.wechatopen.config.RedisProperies;
//import cn.hanyi.ctm.connector.impl.wechatopen.config.WechatOpenProperties;
//import cn.hanyi.ctm.connector.provider.IPlatformProvider;
//import cn.hanyi.ctm.constant.connector.ConnectorFeature;
//import cn.hanyi.ctm.dto.AuthorizeRequestDto;
//import cn.hanyi.ctm.dto.ConnectorAuthorizeResultDto;
//import cn.hanyi.ctm.dto.RemoteCommandDto;
//import cn.hanyi.ctm.dto.connector.ThirdPartyCustomerDto;
//import cn.hanyi.ctm.dto.connector.ThirdPartyTemplateDto;
//import cn.hanyi.ctm.dto.journey.PushMomentDto;
//import cn.hanyi.ctm.entity.Connector;
//import cn.hanyi.ctm.entity.Customer;
//import cn.hanyi.ctm.entity.Template;
//import cn.hanyi.ctm.entity.ThirdPartyCustomer;
//import cn.hanyi.ctm.exception.ConnectorException;
//import cn.hanyi.ctm.exception.ConnectorUnAuthorizeException;
//import cn.hanyi.ctm.repository.*;
//import cn.hanyi.ctm.workertrigger.ICtmTaskTrigger;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.google.gson.JsonObject;
//import lombok.Getter;
//import lombok.SneakyThrows;
//import lombok.extern.slf4j.Slf4j;
//import me.chanjar.weixin.common.error.WxErrorException;
//import me.chanjar.weixin.common.redis.RedisTemplateWxRedisOps;
//import me.chanjar.weixin.mp.api.WxMpMessageRouter;
//import me.chanjar.weixin.mp.api.WxMpTemplateMsgService;
//import me.chanjar.weixin.mp.api.WxMpUserService;
//import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
//import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
//import me.chanjar.weixin.mp.bean.result.WxMpUser;
//import me.chanjar.weixin.mp.bean.result.WxMpUserList;
//import me.chanjar.weixin.mp.bean.template.WxMpTemplate;
//import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
//import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
//import me.chanjar.weixin.open.api.WxOpenMpService;
//import me.chanjar.weixin.open.api.impl.WxOpenInRedisConfigStorage;
//import me.chanjar.weixin.open.api.impl.WxOpenMessageRouter;
//import me.chanjar.weixin.open.api.impl.WxOpenServiceImpl;
//import me.chanjar.weixin.open.bean.message.WxOpenXmlMessage;
//import me.chanjar.weixin.open.bean.result.WxOpenAuthorizerInfoResult;
//import me.chanjar.weixin.open.bean.result.WxOpenQueryAuthResult;
//import org.apache.commons.lang3.StringUtils;
//import org.befun.core.constant.ErrorCode;
//import org.befun.core.template.TemplateEngine;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.boot.context.properties.EnableConfigurationProperties;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.data.redis.core.StringRedisTemplate;
//import org.springframework.stereotype.Component;
//import org.springframework.util.Assert;
//
//import javax.annotation.PostConstruct;
//import java.util.*;
//
///**
// * 微信开放平台实现
// *
// * <AUTHOR>
// */
//@Component("WECHATOPEN")
//@EnableConfigurationProperties({WechatOpenProperties.class, RedisProperies.class})
//@Slf4j
//@Configuration
//@Getter
//public class WechatOpenConenctorProvider extends WxOpenServiceImpl implements IPlatformProvider {
//
//    @Autowired
//    private ICtmTaskTrigger ctmTaskTrigger;
//
//    @Autowired
//    protected ObjectMapper objectMapper;
//
//    private final String START_PUSH_URL = "https://api.weixin.qq.com/cgi-bin/component/api_start_push_ticket";
//
//    private final List<ConnectorFeature> features = List.of(
//            ConnectorFeature.SYNC_TEMPLATE,
//            ConnectorFeature.SYNC_CUSTOMER
//    );
//
//    @Value("${wechat.open.callback:}")
//    private String callbackUrl;
//
//    @Autowired
//    private WechatOpenProperties wechatOpenProperties;
//    private WxOpenMessageRouter wxOpenMessageRouter;
//    private PlatformConnectorManager platformConnectorManager;
//
//    @Autowired
//    TemplateRepository templateRepository;
//
//    @Autowired
//    ThirdPartyTemplateRepository thirdPartyTemplateRepository;
//
//    @Autowired
//    CustomerRepository customerRepository;
//
//    @Autowired
//    ThirdPartyCustomerRepository thirdPartyCustomerRepository;
//
//    @Autowired
//    ConnectorRepository connectorRepository;
//    @Autowired
//    StringRedisTemplate stringRedisTemplate;
//
//    @PostConstruct
//    public void init() {
//        RedisTemplateWxRedisOps ops = new RedisTemplateWxRedisOps(stringRedisTemplate);
//        WxOpenInRedisConfigStorage storage = new WxOpenInRedisConfigStorage(ops, null);
//        storage.setComponentAppId(wechatOpenProperties.getComponentAppId());
//        storage.setComponentAppSecret(wechatOpenProperties.getComponentSecret());
//        storage.setComponentToken(wechatOpenProperties.getComponentToken());
//        storage.setComponentAesKey(wechatOpenProperties.getComponentAesKey());
//        setWxOpenConfigStorage(storage);
//        wxOpenMessageRouter = new WxOpenMessageRouter(this);
//        wxOpenMessageRouter.rule().handler((wxMpXmlMessage, map, wxMpService, wxSessionManager) -> {
//            return null;
//        }).next();
//    }
//
//    @Override
//    public void setConnectorManager(IConnectorManager platformConnectorManager) {
//        this.platformConnectorManager = (PlatformConnectorManager) platformConnectorManager;
//    }
//
//    public void exceptionHandler(WxErrorException ex, String appId) throws ConnectorException {
//
//        switch (ex.getError().getErrorCode()) {
//            case 61010:
//                throw new ConnectorException("微信平台错误", ErrorCode.EXPIRED.getValue(), "授权码已经过期，请重新尝试");
//            case 45015:
//                throw new ConnectorException("微信平台错误", ErrorCode.BAD_PARAMETER.getValue(), "客服消息需要48小时交互限制");
//            case 61003:
//            case 61007:
//                log.warn("connector unauthorized, appId:{}", appId);
//                this.platformConnectorManager.handleAuthorizeFailed(appId);
//                throw new ConnectorUnAuthorizeException("获取授权失败", ex.getError().getErrorCode(), ex.getError().getErrorMsg());
//            case 61023:
////                refresh token is invalid
//                log.warn("refresh token is invalid, appId:{}", appId);
//                throw new ConnectorException("微信平台错误", ex.getError().getErrorCode(), "refresh token 失效");
//            default:
//                log.error(ex.getMessage());
//                ex.printStackTrace();
//                throw new ConnectorException("微信平台错误", ex.getError().getErrorCode(), ex.getError().getErrorMsg());
//        }
//    }
//
//    public WxOpenMessageRouter getWxOpenMessageRouter() {
//        return wxOpenMessageRouter;
//    }
//
//    /**
//     * 预授权实现
//     *
//     * @return
//     */
//    public String preAuthenticate() {
//        try {
//            return getWxOpenComponentService().getPreAuthUrl(callbackUrl);
//        } catch (WxErrorException ex) {
//            log.error(ex.getMessage());
//            ex.printStackTrace();
//            throw new ConnectorException("微信平台错误", ex.getError().getErrorCode(), ex.getError().getErrorMsg());
//        }
//    }
//
//    @Override
//    public ConnectorAuthorizeResultDto authorize(AuthorizeRequestDto requestDto) {
//        String appId = "";
//        try {
//            WxOpenQueryAuthResult queryAuthResult = getWxOpenComponentService().getQueryAuth(requestDto.getAuthCode());
//            appId = queryAuthResult.getAuthorizationInfo().getAuthorizerAppid();
//            WxOpenAuthorizerInfoResult info = getWxOpenComponentService().getAuthorizerInfo(appId);
//            log.info("authorize appId: {}", appId);
//            ConnectorAuthorizeResultDto resultDto = ConnectorAuthorizeResultDto.builder()
//                    .appId(appId)
//                    .name(info.getAuthorizerInfo().getNickName())
//                    .description(info.getAuthorizerInfo().getSignature())
//                    .logo(info.getAuthorizerInfo().getHeadImg())
//                    .build();
//            return resultDto;
//        } catch (WxErrorException ex) {
//            exceptionHandler(ex, appId);
//        }
//        return null;
//    }
//
//    public WxOpenXmlMessage parseMessage(String body, String msgSignature, String timestamp, String nonce) {
//        return WxOpenXmlMessage.fromEncryptedXml(body, getWxOpenConfigStorage(), timestamp, nonce, msgSignature);
//    }
//
//    public WxMpXmlMessage parseMpMessage(String appId, String body, String msgSignature, String timestamp, String nonce) {
//        WxOpenMpService mpService = getWxOpenComponentService().getWxMpServiceByAppid(appId);
//        return WxMpXmlMessage.fromEncryptedXml(body, mpService.getWxMpConfigStorage(), timestamp, nonce, msgSignature);
//    }
//
//    public String processMessage(WxOpenXmlMessage message) {
//        String authorizerAppId = message.getAuthorizerAppid();
//        try {
//            log.info("process message[{}]", message.toString());
//            if (StringUtils.equalsIgnoreCase(message.getInfoType(), "unauthorized")) {
//                return this.platformConnectorManager.handleUnAuthorized(authorizerAppId);
//            } else {
//                return getWxOpenComponentService().route(message);
//            }
//        } catch (WxErrorException ex) {
//            log.error(ex.getMessage());
//            exceptionHandler(ex, authorizerAppId);
//        }
//        return "";
//    }
//
//    public WxMpXmlOutMessage processMpMessage(String appId, WxMpXmlMessage message) {
//        WxOpenMpService mpService = getWxOpenComponentService().getWxMpServiceByAppid(appId);
//        WxMpMessageRouter router = new WxMpMessageRouter(mpService);
//        return router.route(message);
//    }
//
//    public List<WxMpTemplateData> buildMessage(Template template, Map<String, Object> parameters) {
//        List<WxMpTemplateData> params = new ArrayList<>();
//
//        Map<String, Object> result = TemplateEngine.renderJsonTemplate(
//                template.getContent(),
//                parameters);
//
//        for (Map.Entry<String, Object> entry : result.entrySet()) {
//            params.add(new WxMpTemplateData(entry.getKey(), entry.getValue().toString()));
//        }
//
//        return params;
//    }
//
//    public List<ThirdPartyTemplateDto> fetchTemplate(Connector connector) {
//        String appId = connector.getAppId();
//        log.info("fetch template for connector id:{} appId:{}", connector.getId(), appId);
//
//        try {
//            WxMpTemplateMsgService templateMsgService = getWxOpenComponentService()
//                    .getWxMpServiceByAppid(appId)
//                    .getTemplateMsgService();
//            WxOpenAuthorizerInfoResult info = getWxOpenComponentService().getAuthorizerInfo(appId);
//
//            List<WxMpTemplate> wxMpTemplates = templateMsgService.getAllPrivateTemplate();
//            List<ThirdPartyTemplateDto> allTemplates = new ArrayList<>();
//            wxMpTemplates.forEach(x -> {
//                log.info("fetch template id:{} {} for connector {}", x.getTemplateId(), x.getTitle(), connector.getId());
//                if (!x.getTitle().equals("订阅模板消息")) {
//                    ThirdPartyTemplateDto tpTemplate = ThirdPartyTemplateDto.builder()
//                            .content(x.getContent())
//                            .example(x.getExample())
//                            .title(x.getTitle())
//                            .params(TemplateParser.parseWechatTemplate(x.getContent()))
//                            .openId(x.getTemplateId())
//                            .build();
//                    allTemplates.add(tpTemplate);
//                }
//            });
//            return allTemplates;
//        } catch (WxErrorException ex) {
//            exceptionHandler(ex, appId);
//        }
//        return null;
//    }
//
//    ThirdPartyCustomerDto buildCustomer(WxMpUser mpUser) {
//        ThirdPartyCustomerDto.ThirdPartyCustomerDtoBuilder builder = ThirdPartyCustomerDto.builder()
//                .avatar(mpUser.getHeadImgUrl())
////                .city(mpUser.getCity())
//                .openId(mpUser.getOpenId())
//                .unionId(mpUser.getUnionId())
////                .country(mpUser.getCountry())
//                .isSubscribed(mpUser.getSubscribe())
//                .subscribedAt(mpUser.getSubscribeTime())
////                .province(mpUser.getProvince())
//                .name(mpUser.getNickname())
//                .nickname(mpUser.getNickname())
//                .remark(mpUser.getRemark());
//
////        switch (mpUser.getSex()) {
////            // 用户的性别，值为1时是男性，值为2时是女性，值为0时是未知
////            case 1:
////                builder.sex(GenderType.MALE);
////                break;
////            case 2:
////                builder.sex(GenderType.FEMALE);
////        }
//        return builder.build();
//    }
//
//    public List<WxMpUser> getMpUsers(String appId, List<String> openIds) throws WxErrorException {
//        return getWxOpenComponentService()
//                .getWxMpServiceByAppid(appId)
//                .getUserService()
//                .userInfoList(openIds);
//    }
//
//    public WxMpUserList getMpUserIds(String appId, String nextId) throws WxErrorException {
//        return getWxOpenComponentService()
//                .getWxMpServiceByAppid(appId)
//                .getUserService()
//                .userList(nextId);
//    }
//
//    public Optional<ThirdPartyCustomerDto> fetchOneCustomerByOpenId(Connector connector, String openId) {
//        Assert.notNull(connector, "invalid connector");
//        Assert.notNull(openId, "invalid openid");
//
//        WxMpUserService userService = getWxOpenComponentService()
//                .getWxMpServiceByAppid(connector.getAppId())
//                .getUserService();
//
//        try {
//            WxMpUser mpUser = userService.userInfo(openId);
//            if (mpUser != null) {
//                ThirdPartyCustomerDto customerDto = buildCustomer(mpUser);
//                return Optional.ofNullable(customerDto);
//            } else {
//                log.warn("failed to fetch customer information connector {} openId {}", connector.getId(), openId);
//            }
//            return Optional.empty();
//        } catch (WxErrorException ex) {
//            log.warn("failed to fetch customer information openId:{} due to", openId, ex.getMessage());
//            exceptionHandler(ex, connector.getAppId());
//        }
//        return Optional.empty();
//    }
//
//    public List<ThirdPartyCustomerDto> fetchCustomer(Connector connector) {
//        String appId = connector.getAppId();
//        log.info("fetch customer for connector id:{} appId:{}", connector.getId(), appId);
//
//        WxOpenMpService mpService = getWxOpenComponentService().getWxMpServiceByAppid(appId);
//        WxMpUserService userService = mpService.getUserService();
//        String next = "";
//        long total = 0;
//        long count = 0;
//        int batchSize = 100;
//        List<WxMpUser> wxMpUsers = new ArrayList<>();
//        List<ThirdPartyCustomerDto> customerDtos = new ArrayList<>();
//
//        try {
//            while (count == 0 || count < total) {
//                WxMpUserList wxUserList = userService.userList(next);
//                List<String> openSidList = wxUserList.getOpenids();
//                next = wxUserList.getNextOpenid();
//                total = wxUserList.getTotal();
//                count += openSidList.size();
//                int batch = ((openSidList.size() % batchSize) == 0) ? (openSidList.size() / batchSize) : (openSidList.size() / batchSize + 1);
//                for (int i = 0; i < batch; i++) {
//                    int toIndex = Math.min((i + 1) * batchSize, openSidList.size());
//                    wxMpUsers.addAll(userService.userInfoList(openSidList.subList(i * batchSize, toIndex)));
//                }
//            }
//
//            log.info("total fetch customers {} for appId: {}", wxMpUsers.size(), appId);
//
//            for (WxMpUser mpUser : wxMpUsers) {
//                ThirdPartyCustomerDto customerDto = buildCustomer(mpUser);
//                customerDtos.add(customerDto);
//            }
//
//        } catch (WxErrorException ex) {
//            log.warn("sync fault due to error: {}", ex.getMessage());
//            exceptionHandler(ex, appId);
//        }
//
//        return customerDtos;
//    }
//
//    @Override
//    public String execute(RemoteCommandDto commandDto) {
//        String command = commandDto.getCommand();
//        Assert.notNull(command, "invalid command");
//        JsonObject jsonObject = new JsonObject();
//        jsonObject.addProperty("component_appid", getWxOpenConfigStorage().getComponentAppId());
//        jsonObject.addProperty("component_secret", getWxOpenConfigStorage().getComponentAppSecret());
//
//        String response = "";
//        String appId = "";
//        try {
//            switch (command) {
//                case "activate":
//                    response = post(START_PUSH_URL, jsonObject.toString());
//                    break;
//                case "ping":
//                    response = getWxOpenConfigStorage().getComponentAppId();
//                    break;
//                case "reauth":
//                    response = getWxOpenConfigStorage().getComponentAppId();
//                    WxOpenQueryAuthResult queryAuthResult = getWxOpenComponentService().getQueryAuth(commandDto.getContent());
//                    appId = queryAuthResult.getAuthorizationInfo().getAuthorizerAppid();
//                    WxOpenAuthorizerInfoResult info = getWxOpenComponentService().getAuthorizerInfo(appId);
//                    response = info.toString();
//                    break;
//                default:
//                    response = "empty";
//            }
//        } catch (WxErrorException ex) {
//            exceptionHandler(ex, appId);
//        }
//        return response;
//    }
//
//    @SneakyThrows
//    public Boolean internalSendTemplateMessage(Connector connector, Template template, Customer customer, ThirdPartyCustomer thirdPartyCustomer,
//                                               Map<String, Object> parameters, String url, PushMomentDto momentDto) {
//        List<WxMpTemplateData> params = buildMessage(template, parameters);
//        WxMpTemplateMessage message = WxMpTemplateMessage.builder()
//                .templateId(template.getThirdPartyTemplate().getOpenId())
//                .url(url)
//                .toUser(thirdPartyCustomer.getOpenId())
//                .data(params)
//                .build();
//        if (!url.isEmpty()) {
//            message.setUrl(url);
//        }
//        switch (momentDto.getMomentType()) {
//            case IMMEDIATELY:
////                ctmTaskTrigger.customerSendWechat(connector.orgId,
////                        null,
////                        connector.getId(),
////                        connector.getId(),
////                        null,
////                        connector.getAppId(),
////                        thirdPartyCustomer.getOpenId(),
////                        JsonHelper.toJson(message),
////                        url,
////                        null,
////                        null);
//                return true;
//            case LATER:
////                ctmTaskTrigger.customerSendWechat(connector.orgId,
////                        null,
////                        connector.getId(),
////                        connector.getId(),
////                        null,
////                        connector.getAppId(),
////                        thirdPartyCustomer.getOpenId(),
////                        JsonHelper.toJson(message),
////                        url,
////                        TimeUtils.parseDuration(momentDto.getDuration()),
////                        null);
//                return true;
//            case TIMED:
////                String timed = String.format("%s/%s/%d/%d/0", momentDto.getTimedType().name(), String.join(",", momentDto.getWeeks()), momentDto.getHour(), momentDto.getMinute());
////                ctmTaskTrigger.customerSendWechat(connector.orgId,
////                        null,
////                        connector.getId(),
////                        connector.getId(),
////                        null,
////                        connector.getAppId(),
////                        thirdPartyCustomer.getOpenId(),
////                        JsonHelper.toJson(message),
////                        url,
////                        null,
////                        timed);
//                return true;
//            default:
//                log.warn("unexpected moment type {}", momentDto.getMomentType());
//                return false;
//        }
//    }
//
////    @SneakyThrows
////    String internalSendKeFuMessage(Connector connector, Customer customer, ThirdPartyCustomer thirdPartyCustomer, Map<String, Object> content, Map<String, Object> parameters, PushMomentDto momentDto) {
////        Assert.notNull(content, "empty content");
////
////        String appId = connector.getAppId();
////        String openId = thirdPartyCustomer.getOpenId();
////        WeChatOpenTaskDetailDto detail = new WeChatOpenTaskDetailDto();
////
////        String text = TemplateEngine.renderTextTemplate(
////                content.get("content").toString(),
////                parameters
////        );
////        WxMpKefuMessage message = new WxMpKefuMessage();
////        message.setMsgType("text");
////        message.setContent(text);
////        message.setToUser(openId);
////
////        detail.setCustomerOpenId(openId);
////        detail.setMessageType(MessageType.KEFU);
////        detail.setAppId(appId);
////        detail.setMessage(objectMapper.writeValueAsString(message));
////        switch (momentDto.getMomentType()) {
////            case IMMEDIATELY:
////                sendMessageTask.performAsync(detail);
////                return text;
////            case LATER:
////                sendMessageTask.performAsyncDelay(detail, momentDto.getDuration());
////                return text;
////            case TIMED:
////                TimedTaskDto taskDto = TimedTaskDto.builder()
////                        .timedType(momentDto.getTimedType())
////                        .hour(momentDto.getHour())
////                        .minute(momentDto.getMinute())
////                        .build();
////                sendMessageTask.performAt(detail, taskDto);
////                return text;
////            default:
////                log.warn("unexpected moment type {}", momentDto.getMomentType());
////                return null;
////        }
//
////        } catch (PathNotFoundException ex) {
////            log.error("sendMessage got exception: {}", ex.getMessage());
////            ex.printStackTrace();
////            throw new BaseException(ErrorCode.BAD_PARAMETER, "无效参数");
////        }
////}
//
//    @Override
//    public Boolean sendTemplateMessage(Connector connector, Template template, Customer customer,
//                                       Map<String, Object> parameters, String url, PushMomentDto momentDto) {
//        if (customer.getThirdPartyCustomerId() == null || customer.getThirdPartyCustomerId() <= 0) {
//            log.error("invalid customer, missing tp customer information {}", customer.getId());
//            return false;
//        }
//        ThirdPartyCustomer thirdPartyCustomer = thirdPartyCustomerRepository.findById(customer.getThirdPartyCustomerId()).orElse(null);
//        if (thirdPartyCustomer == null) {
//            log.error("invalid customer, missing tp customer information {}", customer.getId());
//            return false;
//        }
//        switch (template.getMessageType()) {
//            case TEMPLATE:
//                return internalSendTemplateMessage(connector, template, customer, thirdPartyCustomer, parameters, url, momentDto);
//            case KEFU:
////                internalSendKeFuMessage(connector, customer, thirdPartyCustomer, template.getContent(), parameters, momentDto);
//                return true;
//            default:
//                log.warn("unexpected template message here {}", template.getId());
//        }
//
//        return false;
//    }
//
//    @Override
//    public Map<String, Object> sendTextMessage(Connector connector, Customer customer, Map<String, Object> content, Map<String, Object> parameters, PushMomentDto momentDto) {
//        if (customer.getThirdPartyCustomerId() == null || customer.getThirdPartyCustomerId() <= 0) {
//            log.error("invalid customer, missing tp customer information {}", customer.getId());
//            return null;
//        }
//        ThirdPartyCustomer thirdPartyCustomer = thirdPartyCustomerRepository.findById(customer.getThirdPartyCustomerId()).orElse(null);
//        if (thirdPartyCustomer == null) {
//            log.error("invalid customer, missing tp customer information {}", customer.getId());
//            return null;
//        }
//
//        Map<String, Object> result = new HashMap<>();
////        String text = internalSendKeFuMessage(connector, customer, thirdPartyCustomer, content, parameters, momentDto);
////        result.put("content", text);
//        return result;
//    }
//}
