package cn.hanyi.ctm.connector.provider;

import cn.hanyi.ctm.dto.journey.PushMomentDto;
import cn.hanyi.ctm.entity.Connector;
import cn.hanyi.ctm.entity.Customer;
import cn.hanyi.ctm.entity.Template;
import org.apache.commons.lang3.NotImplementedException;

/**
 * The interface description
 *
 * <AUTHOR>
 */
public interface ISmsProvider extends IConnectorProvider {
    default Boolean sendMessage(Long orgId, Connector connector, Template template, Customer customer, String content, PushMomentDto momentDto) {
        throw new NotImplementedException();
    }
}
