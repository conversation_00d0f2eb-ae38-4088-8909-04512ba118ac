package cn.hanyi.ctm.connector.provider;

import cn.hanyi.ctm.connector.IConnectorManager;
import cn.hanyi.ctm.dto.AuthorizeRequestDto;
import cn.hanyi.ctm.dto.ConnectorAuthorizeResultDto;
import cn.hanyi.ctm.dto.RemoteCommandDto;
import cn.hanyi.ctm.dto.connector.ThirdPartyCustomerDto;
import cn.hanyi.ctm.dto.connector.ThirdPartyTemplateDto;
import cn.hanyi.ctm.entity.Connector;

import java.util.List;
import java.util.Optional;

public interface IPlatformProvider extends IConnectorProvider {
    // Authentication & Authorization
    String preAuthenticate();
    ConnectorAuthorizeResultDto authorize(AuthorizeRequestDto requestDto);

    // template part
    List<ThirdPartyTemplateDto> fetchTemplate(Connector connector);

    // customer part
    List<ThirdPartyCustomerDto> fetchCustomer(Connector connector);

    Optional<ThirdPartyCustomerDto> fetchOneCustomerByOpenId(Connector connector, String openId);

    String execute(RemoteCommandDto command);

    void setConnectorManager(IConnectorManager platformConnectorManager); // todo

}
