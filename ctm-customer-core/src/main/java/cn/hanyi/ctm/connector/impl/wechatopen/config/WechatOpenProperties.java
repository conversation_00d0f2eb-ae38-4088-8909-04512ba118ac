package cn.hanyi.ctm.connector.impl.wechatopen.config;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties(prefix = "wechat.open")
public class WechatOpenProperties {
    /**
     * 设置微信三方平台的appid
     */
    @Value("${wechat.open.app_id:}")
    private String componentAppId;

    /**
     * 设置微信三方平台的app secret
     */
    @Value("${wechat.open.app_secret:}")
    private String componentSecret;

    /**
     * 设置微信三方平台的token
     */
    @Value("${wechat.open.token:}")
    private String componentToken;

    /**
     * 设置微信三方平台的EncodingAESKey
     */
    @Value("${wechat.open.aes_key:}")
    private String componentAesKey;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this,
                ToStringStyle.MULTI_LINE_STYLE);
    }

    public String getComponentAppId() {
        return componentAppId;
    }

    public void setComponentAppId(String componentAppId) {
        this.componentAppId = componentAppId;
    }

    public String getComponentSecret() {
        return componentSecret;
    }

    public void setComponentSecret(String componentSecret) {
        this.componentSecret = componentSecret;
    }

    public String getComponentToken() {
        return componentToken;
    }

    public void setComponentToken(String componentToken) {
        this.componentToken = componentToken;
    }

    public String getComponentAesKey() {
        return componentAesKey;
    }

    public void setComponentAesKey(String componentAesKey) {
        this.componentAesKey = componentAesKey;
    }
}

