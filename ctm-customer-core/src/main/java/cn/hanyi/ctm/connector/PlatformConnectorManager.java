package cn.hanyi.ctm.connector;

import cn.hanyi.ctm.connector.provider.IPlatformProvider;
import cn.hanyi.ctm.constant.MessageType;
import cn.hanyi.ctm.constant.SourceType;
import cn.hanyi.ctm.constant.connector.ConnectorAuthorizeStatus;
import cn.hanyi.ctm.constant.connector.ConnectorFeature;
import cn.hanyi.ctm.constant.connector.ConnectorProviderType;
import cn.hanyi.ctm.constant.connector.ConnectorType;
import cn.hanyi.ctm.constant.customer.CustomerConnectionStatus;
import cn.hanyi.ctm.dto.AuthorizeRequestDto;
import cn.hanyi.ctm.dto.ConnectorAuthorizeResultDto;
import cn.hanyi.ctm.dto.connector.ThirdPartyCustomerDto;
import cn.hanyi.ctm.dto.connector.ThirdPartyTemplateDto;
import cn.hanyi.ctm.dto.journey.PushMomentDto;
import cn.hanyi.ctm.entity.*;
import cn.hanyi.ctm.repository.*;
import cn.hanyi.ctm.workertrigger.CtmTaskTrigger;
import cn.hanyi.ctm.workertrigger.dto.WechatOpenSyncCustomerInfoSingleDto;
import cn.hanyi.ctm.workertrigger.dto.WechatOpenSyncCustomerListDto;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.befun.auth.constant.UserTaskType;
import org.befun.auth.service.OrganizationService;
import org.befun.auth.service.UserTaskService;
import org.befun.core.entity.BaseEntity;
import org.befun.core.rest.context.TenantContext;
import org.befun.task.entity.TaskProgress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.PostConstruct;
import javax.transaction.Transactional;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Getter
@Component("PLATFORM")
@Slf4j
public class PlatformConnectorManager implements IConnectorManager {
    private ConnectorType type = ConnectorType.PLATFORM;

    @Autowired(required = false)
    Map<String, IPlatformProvider> allProviders = new HashMap<>();

    @Autowired
    ConnectorRepository connectorRepository;

    @Autowired
    CustomerRepository customerRepository;

    @Autowired
    ThirdPartyTemplateRepository tpTemplateRepository;

    @Autowired
    ThirdPartyCustomerRepository tpCustomerRepository;

    @Autowired
    TemplateRepository templateRepository;

    @Autowired
    ThirdPartyTemplateRepository thirdPartyTemplateRepository;

    @Autowired
    ThirdPartyCustomerRepository thirdPartyCustomerRepository;

    @Autowired
    private OrganizationService organizationService;

    @Autowired
    private CtmTaskTrigger ctmTaskTrigger;

    @Autowired
    private UserTaskService userTaskService;

    private final List<ConnectorFeature> features = List.of();

    @Override
    public Boolean support(String provider) {
        return allProviders.containsKey(provider);
    }

    @PostConstruct
    public void setup() {
        allProviders.values().forEach(provider -> provider.setConnectorManager(this));
    }


    @Override
    public String preAuthenticate(ConnectorProviderType providerType) {
        IPlatformProvider provider = allProviders.get(providerType.name());
        return provider.preAuthenticate();
    }

    @Async
    void initSync(Long orgId, Long userId, Connector connector) {
        log.info("初始化同步 {}", connector.getId());
        syncTemplate(connector);
        if (organizationService.enableAutoSyncCustomer(connector.getOrgId())) {
            syncCustomer(orgId, userId, connector);
        }
        setupDefaultTemplate(connector);
    }

    void setupDefaultTemplate(Connector connector) {
        Assert.notNull(connector, "invalid connector");

        log.info("初始化模版 {}", connector.getId());
        Map<String, Object> defaultContent = new HashMap<>();
        defaultContent.put(
                "content",
                "尊敬的${customer.username}，非常抱歉给您造成不良的消费体验，我们会针对您的反馈进行改进，期盼您能再次光临。");

        Template template = Template.builder()
                .name("default_wechat_event")
                .connector(connector)
                .content(defaultContent)
                .type(ConnectorType.PLATFORM)
                .messageType(MessageType.KEFU)
                .isDelete(false)
                .sourceType(SourceType.SYSTEM)
                .build();
        template.setOrgId(connector.getOrgId());

        templateRepository.save(template);
        log.info("初始化模版完成 connector: {} template: {}", connector.getId(), template.getId());
    }

    @Override
    public Optional<Connector> authorize(ConnectorProviderType providerType, AuthorizeRequestDto requestDto) {
        final Long tenantId = TenantContext.getCurrentTenant();
        final Long userId = TenantContext.getCurrentUserId();
        Assert.notNull(tenantId, "missing tenantId");

        IPlatformProvider provider = allProviders.get(providerType.name());
        ConnectorAuthorizeResultDto authorizeResultDto = provider.authorize(requestDto);

        String appId = authorizeResultDto.getAppId();
        Optional<Connector> connector = connectorRepository.findByOrgIdAndAndAppId(tenantId, appId);
        if (connector.isPresent()) {
            // 已经注册，直接返回
            log.info("already registered {}", appId);
            if ((connector.get().getAuthorizeStatus() == ConnectorAuthorizeStatus.UNAUTHORIZED) ||
                    ((connector.get().getAuthorizeStatus() == ConnectorAuthorizeStatus.AUTHORIZEFAILED))) {
                // 重新注册
                log.info("reregister connector {}", appId);
                connector.get().setAuthorizeStatus(ConnectorAuthorizeStatus.AUTHORIZED);
                connectorRepository.save(connector.get());
                try {
                    initSync(tenantId, userId, connector.get());
                } catch (Exception e) {
                    log.error("init sync error", e);
                }
            }
            return connector;
        } else {
            // 尚未注册，保存
            log.info("{} not registered, just register connector", appId);
            Connector newConnector = new Connector(authorizeResultDto, providerType);
            connectorRepository.save(newConnector);
            try {
                initSync(tenantId, userId, newConnector);
            } catch (Exception e) {
                log.error("init sync error", e);
            }
            return Optional.ofNullable(newConnector);
        }
    }

    @Override
    public Boolean isValidCustomer(Customer customer) {
        return customer.getThirdPartyCustomerId() != null;
    }

    @Override
    public Map<String, Object> sendTemplateMessage(Long orgId, Connector connector, Template template,
                                                   Customer customer, Map<String, Object> parameters,
                                                   String url, PushMomentDto momentDto) {
        Assert.notNull(connector, "invalid connector");
        Assert.notNull(template, "invalid template");
        Assert.notNull(customer, "invalid customer");

        Map<String, Object> result = new HashMap<>();

        IPlatformProvider provider = allProviders.get(connector.getProviderType().name());
        if (provider == null) {
            log.warn("unknown provider {}", connector.getType().name());
            return result;
        }

        // TBD, common stuff of sms
        provider.sendTemplateMessage(connector, template, customer, parameters, url, momentDto);
        return result;
    }

    @Override
    public Map<String, Object> sendTextMessage(Long orgId, Connector connector, Customer customer, Map<String, Object> content,
                                               Map<String, Object> parameters, PushMomentDto momentDto) {
        Assert.notNull(connector, "invalid connector");
        Assert.notNull(customer, "invalid customer");

        IPlatformProvider provider = allProviders.get(connector.getProviderType().name());
        if (provider == null) {
            log.warn("unknown provider {}", connector.getType().name());
            return null;
        }

        return provider.sendTextMessage(connector, customer, content, parameters, momentDto);
    }

    @Override
    public List<ThirdPartyTemplateDto> syncTemplate(Connector connector) {
        Assert.notNull(connector, "invalid connector");
        final Long tenantId = connector.getOrgId();
        Assert.notNull(tenantId, "missing tenantId");

        log.info("初始化 Template {}", connector.getId());
        IPlatformProvider provider = allProviders.get(connector.getProviderType().name());

        List<ThirdPartyTemplateDto> tpTemplates = provider.fetchTemplate(connector);
        List<ThirdPartyTemplateDto> tpTemplateDtos = tpTemplates.stream().filter(x -> {
            Optional<ThirdPartyTemplate> remote = tpTemplateRepository.findByConnectorAndOpenId(connector, x.getOpenId());
            return remote.isEmpty();
        }).collect(Collectors.toList());

        for (ThirdPartyTemplateDto tpTemplateDto : tpTemplateDtos) {
            ThirdPartyTemplate tpTemplate = new ThirdPartyTemplate(tpTemplateDto, connector);
            thirdPartyTemplateRepository.save(tpTemplate);
        }
        return tpTemplateDtos;
    }

    void internalSyncCustomer(
            Connector connector,
            ThirdPartyCustomerDto tpCustomerDto) {

        Optional<ThirdPartyCustomer> tpCustomer = tpCustomerRepository
                .findThirdPartyCustomerByConnectorAndOpenId(
                        connector,
                        tpCustomerDto.getOpenId()
                );

        if (tpCustomer.isPresent()) {
            tpCustomer.get().setSubscribed(true);
            tpCustomer.get().setAuthorizeStatus(ConnectorAuthorizeStatus.AUTHORIZED);
            tpCustomerRepository.save(tpCustomer.get());
        } else {
            tpCustomer = Optional.ofNullable(new ThirdPartyCustomer(tpCustomerDto, connector));
            tpCustomerRepository.save(tpCustomer.get());
        }

        Optional<Customer> customer = customerRepository.findCustomerByThirdPartyCustomerId(tpCustomer.get().getId());
        if (customer.isPresent()) {
            customer.get().setStatus(CustomerConnectionStatus.SUBSCRIBED);
            customer.get().setAuthorizeStatus(ConnectorAuthorizeStatus.AUTHORIZED);
            customerRepository.save(customer.get());
        } else {
            customer = Optional.ofNullable(new Customer(tpCustomer.get(), connector.getOrgId()));
            customerRepository.save(customer.get());
        }
    }

    @Override
    public List<ThirdPartyCustomerDto> syncCustomer(Long orgId, Long userId, Connector connector) {
//        TaskProgress progress = userTaskService.createSyncWechatOpenCustomerTask(orgId, userId, connector.getId(), connector.getAppId());
//        ctmTaskTrigger.wechatOpenSyncCustomerList(orgId, userId, progress.getId(), connector.getId(), connector.getAppId());
        TaskProgress progress = userTaskService.createTask(orgId, userId, UserTaskType.syncWechatOpenSyncCustomer, 0,
                Map.of("connectorId", connector.getId(), "appId", connector.getAppId()));
        ctmTaskTrigger.wechatOpenSyncCustomerList(new WechatOpenSyncCustomerListDto(orgId, userId, progress.getId(), connector.getId(), connector.getAppId()));
        return null;
    }

    @Override
    public ThirdPartyCustomerDto syncOneCustomer(Long orgId, Long userId, Connector connector, String openId) {
        ctmTaskTrigger.wechatOpenSyncCustomerInfoSingle(new WechatOpenSyncCustomerInfoSingleDto(orgId, userId, connector.getId(), connector.getAppId(), openId));
        return null;
    }

    /**
     * @param appId
     * @return
     */
    public String handleUnAuthorized(String appId) {
        List<Connector> connectors = connectorRepository.findAllByAppId(appId);
        try {
            connectors.forEach(connector -> {
                log.debug("unauthorize connector {}", connector.getId());
                unAuthorizeConnectorRelatedResource(connector);
                connector.setAuthorizeStatus(ConnectorAuthorizeStatus.UNAUTHORIZED);
                connectorRepository.save(connector);
            });
            return "Success";
        } catch (Exception ex) {
            log.error(ex.getMessage());
            ex.printStackTrace();
            return ex.getMessage();
        }
    }

    public void handleAuthorizeFailed(String appId) {
        List<Connector> connectors = connectorRepository.findAllByAppId(appId);
        try {
            connectors.forEach(connector -> {
                log.debug("authorizeFailed connector {}", connector.getId());
                unAuthorizeConnectorRelatedResource(connector);
                connector.setAuthorizeStatus(ConnectorAuthorizeStatus.AUTHORIZEFAILED);
                connectorRepository.save(connector);
            });
        } catch (Exception ex) {
            log.error(ex.getMessage());
            ex.printStackTrace();
        }
    }

    @Async
    @Transactional
    public void unAuthorizeConnectorRelatedResource(Connector connector) {
        try {
            List<ThirdPartyTemplate> thirdPartyTemplates = thirdPartyTemplateRepository.findAllByConnector(connector);
            List<Template> templates = templateRepository.findAllByConnector(connector);
            List<ThirdPartyCustomer> thirdPartyCustomers = thirdPartyCustomerRepository.findALLByConnector(connector);
            List<Customer> customers = customerRepository.findAllByThirdPartyCustomerIdIn(thirdPartyCustomers.stream().map(BaseEntity::getId).collect(Collectors.toList()));

            thirdPartyTemplates.forEach(thirdPartyTemplate -> {
                thirdPartyTemplateRepository.deleteById(thirdPartyTemplate.getId());
            });
//       todo     templates.forEach(template -> {
//                templateRepository.deleteById(template.getId());
//                List<ExperienceInteraction> experienceInteractions = experienceInteractionRepository.
//                        findAllByCtmTemplateIdLike("%" + template.getId() + "%");
//                List<ExperienceInteractionPublish> experienceInteractionPublishes = experienceInteractionPublishRepository.
//                        findAllByCtmTemplateIdLike("%" + template.getId() + "%");
//                experienceInteractions.forEach(experienceInteraction -> {
//                    experienceInteraction.setIsValid(ExperienceInteractionStatus.UNVALID);
//                    experienceInteractionRepository.save(experienceInteraction);
//                });
//                experienceInteractionPublishes.forEach(experienceInteractionPublish -> {
//                    experienceInteractionPublish.setIsValid(ExperienceInteractionStatus.UNVALID);
//                    experienceInteractionPublishRepository.save(experienceInteractionPublish);
//                });
//            });
            thirdPartyCustomers.forEach(thirdPartyCustomer -> {
                thirdPartyCustomer.setAuthorizeStatus(ConnectorAuthorizeStatus.UNAUTHORIZED);
                thirdPartyCustomerRepository.save(thirdPartyCustomer);
            });
            customers.forEach(customer -> {
                customer.setAuthorizeStatus(ConnectorAuthorizeStatus.UNAUTHORIZED);
                customerRepository.save(customer);
            });

        } catch (Exception ex) {
            log.error("unAuthorize connector related resource failed; caused by {}", ex.getMessage());
            ex.printStackTrace();
        }
    }
}
