package cn.hanyi.ctm.connector.impl.sms;

import cn.hanyi.ctm.connector.provider.ISmsProvider;
import cn.hanyi.ctm.dto.journey.PushMomentDto;
import cn.hanyi.ctm.dto.task.SmsTaskDetailDto;
import cn.hanyi.ctm.entity.Connector;
import cn.hanyi.ctm.entity.Customer;
import cn.hanyi.ctm.entity.Template;
import cn.hanyi.ctm.entity.ThirdPartyTemplate;
import cn.hanyi.ctm.workertrigger.ICtmTaskTrigger;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.NotImplementedException;
import org.befun.task.utils.TimeUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component("FEIGE")
@Slf4j
@Getter
public class FeigeSmsProvider implements ISmsProvider {

    @Autowired
    private ICtmTaskTrigger ctmTaskTrigger;


    @Override
    public Boolean sendMessage(Long orgId, Connector connector, Template template, Customer customer, String content,
                               PushMomentDto momentDto) {
        ThirdPartyTemplate tpTemplate = template.getThirdPartyTemplate();
        if (tpTemplate == null) {
            log.error("invalid template for {}", template.getId());
            return false;
        }

        log.info("send to feige {} template:{}", customer.getMobile(), template.getId());

        SmsTaskDetailDto detail = new SmsTaskDetailDto();
        detail.setConnectorId(connector.getId());
        detail.setOrgId(orgId);
        detail.setContent(content);
        detail.setMobile(customer.getMobile());
        detail.setTemplateId(tpTemplate.getOpenId());
        detail.setSignId(tpTemplate.getSignatureId());
        log.debug(detail.toString());

        switch (momentDto.getMomentType()) {
            case IMMEDIATELY:
//                ctmTaskTrigger.customerSendSms(orgId, null, connector.getId(), connector.getId(), null,content, customer.getMobile(), tpTemplate.getOpenId(), tpTemplate.getSignatureId(), null, null);
//                sendMessageTask.performAsync(detail);
                return true;
            case LATER:

//                ctmTaskTrigger.customerSendSms(orgId, null, connector.getId(),connector.getId(), null, content, customer.getMobile(), tpTemplate.getOpenId(), tpTemplate.getSignatureId(), TimeUtils.parseDuration(momentDto.getDuration()), null);
//                sendMessageTask.performAsyncDelay(detail, momentDto.getDuration());
                return true;
            case TIMED:
//                String[] dayOfWeeks = momentDto.getWeeks() != null ? momentDto.getWeeks() : new String[0];
//                TimedTaskDto taskDto = TimedTaskDto.builder()
//                        .timedType(momentDto.getTimedType())
//                        .dayOfWeeks(dayOfWeeks)
//                        .hour(momentDto.getHour())
//                        .minute(momentDto.getMinute())
//                        .build();
//                String timed = String.format("%s/%s/%d/%d/0", momentDto.getTimedType().name(), String.join(",", momentDto.getWeeks()), momentDto.getHour(), momentDto.getMinute());
//                ctmTaskTrigger.customerSendSms(orgId, null, connector.getId(), connector.getId(), null,content, customer.getMobile(), tpTemplate.getOpenId(), tpTemplate.getSignatureId(), null, timed);
//                sendMessageTask.performAt(detail, taskDto);
                return true;
            default:
                log.warn("unexpected moment type {}", momentDto.getMomentType());
                return false;
        }
    }

    @Override
    public Boolean sendTemplateMessage(Connector connector, Template template, Customer customer, Map<String, Object> parameters, String url, PushMomentDto momentDto) {
        return false;
    }

    @Override
    public Map<String, Object> sendTextMessage(Connector connector, Customer customer, Map<String, Object> content,
                                               Map<String, Object> parameters, PushMomentDto momentDto) {
        throw new NotImplementedException();
    }
}
