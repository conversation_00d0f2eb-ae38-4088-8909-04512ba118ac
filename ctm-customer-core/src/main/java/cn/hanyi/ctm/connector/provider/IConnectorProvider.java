package cn.hanyi.ctm.connector.provider;

import cn.hanyi.ctm.dto.journey.PushMomentDto;
import cn.hanyi.ctm.entity.Connector;
import cn.hanyi.ctm.entity.Customer;
import cn.hanyi.ctm.entity.Template;
import org.apache.commons.lang3.NotImplementedException;

import java.util.Map;

/**
 * The interface description
 *
 * <AUTHOR>
 */
public interface IConnectorProvider {
   default Boolean sendTemplateMessage(Connector connector, Template template, Customer customer, Map<String, Object> parameters, String url, PushMomentDto momentDto){
       throw new NotImplementedException();
   }
    default Map<String, Object> sendTextMessage(Connector connector, Customer customer, Map<String, Object> content, Map<String, Object> parameters, PushMomentDto momentDto){
        throw new NotImplementedException();
    }
}
