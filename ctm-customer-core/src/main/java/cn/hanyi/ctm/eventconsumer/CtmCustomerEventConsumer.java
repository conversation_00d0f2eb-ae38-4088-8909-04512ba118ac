package cn.hanyi.ctm.eventconsumer;

import cn.hanyi.ctm.service.CustomerHistoryRecordService;
import cn.hanyi.ctm.workertrigger.ICtmEventConsumer;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class CtmCustomerEventConsumer implements ICtmEventConsumer {

    @Autowired
    private CustomerHistoryRecordService customerHistoryRecordService;

    @Override
    public void customerCreate(Long orgId, Long userId, Long customerId) {
        customerHistoryRecordService.addByCreateCustomer(userId, null, customerId);
    }

    @Override
    public void customerUpdate(Long orgId, Long userId, Long customerId, Map<String, Pair<String, String>> changes) {
        customerHistoryRecordService.addByUpdateCustomer(userId, customerId, changes);
    }
}
