## 部署环境变量配置
- MYSQL_URL ：MySQL链接字符串 默认：****************************************
- MYSQL_USER : 用户 默认 root
- MYSQL_PASSWORD : 密码 默认 123456
- PORT : 服务端口 默认 8080
- REDIS_HOST 默认 localhost
- REDIS_PORT 默认 6379
- REDIS_PASSWORD 默认 N/A
- REDIS_DATABASE 默认 1

## 应用配置选项
- WECHAT_OPEN_APP_ID
- WECHAT_OPEN_APP_SECRET
- WECHAT_OPEN_TOKEN
- WECHAT_OPEN_AES_KEY
- WECHAT_OPEN_CALLBACK

## 域名设置 (不需要经过任何内部网关)
- DEV: dev.xmplus.cn/api/ctm
- TEST: dev.xmplus.cn/api/ctm
- PROD: www.xmplus.cn/api/ctm
- LOCAL
