package cn.hanyi.ctm.workertrigger;


import org.apache.commons.lang3.tuple.Pair;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;

public interface ICtmEvent {

    default List<ICtmEventConsumer> getConsumers() {
        return null;
    }

    private void foreachConsumers(Consumer<ICtmEventConsumer> consumer) {
        Optional.ofNullable(getConsumers()).ifPresent(list -> list.forEach(consumer));
    }

    default void customerCreate(Long orgId, Long userId, Long customerId) {
        foreachConsumers(c -> c.customerCreate(orgId, userId, customerId));
    }

    default void customerUpdate(Long orgId, Long userId, Long customerId, Map<String, Pair<String, String>> changes) {
        foreachConsumers(c -> c.customerUpdate(orgId, userId, customerId, changes));
    }

    default void customerDelete(Long orgId, Long userId, Long customerId) {
        foreachConsumers(c -> c.customerDelete(orgId, userId, customerId));
    }

    /**
     * 事件中心协作
     */
    default void eventCooperate(Long orgId, Long warningId, Long fromUserId, Set<Long> toUserIds, String mark, List<String> notifyTypes) {
        foreachConsumers(c -> c.eventCooperate(orgId, warningId, fromUserId, toUserIds, mark, notifyTypes));
    }


    /**
     * 事件中心关闭
     */
    default void eventClose(Long orgId, Long warningId, Long userId) {
        foreachConsumers(c -> c.eventClose(orgId, warningId, userId));
    }

    /**
     * 事件中心关闭
     */
    default void eventRuleChange(Long orgId, Long userId, Long warningId) {
        foreachConsumers(c -> c.eventRuleChange(orgId, userId, warningId));
    }

    /**
     * 事件修改状态
     */
    default void eventChangeStatus(Long orgId, Long userId, Long eventId, Long actionId, String status) {
        foreachConsumers(c -> c.eventChangeStatus(orgId, userId, eventId, actionId, status));
    }

    /**
     * 客户旅程发布
     */
    default void journeyMapPublish(Long orgId, Long userId, Long journeyMapId) {
        foreachConsumers(c -> c.journeyMapPublish(orgId, userId, journeyMapId));
    }

    /**
     * 客户旅程删除
     */
    default void journeyMapDelete(Long orgId, Long userId, Long journeyMapId) {
        foreachConsumers(c -> c.journeyMapDelete(orgId, userId, journeyMapId));
    }

    /**
     * 客户创建体验指标
     */
    default void indicatorCreate(Long orgId, Long userId, Long journeyMapId, Long componentId, Long indicatorId) {
        foreachConsumers(c -> c.indicatorCreate(orgId, userId, journeyMapId, componentId, indicatorId));
    }

    /**
     * 数据导入
     */
    default void dataAccess(Long orgId, Long userId, Long accessId, Long cellId) {
        foreachConsumers(c -> c.dataAccess(orgId, userId, accessId, cellId));
    }
}
