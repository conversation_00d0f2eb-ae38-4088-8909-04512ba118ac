package cn.hanyi.ctm.workertrigger.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.Duration;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CustomerSendApiDto {
    private Long orgId;
    private Long userId;
    private Long taskProgressId;
    private Long journeyRecordId;
    private Long connectorId;
    private String surveyUrl;
    private Long sceneId;
    private Long customerId;
    private String externalUserId;
    private Long departmentId;
    private Duration delay;
    private String timed;
}
