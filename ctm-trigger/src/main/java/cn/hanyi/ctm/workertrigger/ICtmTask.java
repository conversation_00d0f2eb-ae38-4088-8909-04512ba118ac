package cn.hanyi.ctm.workertrigger;

import cn.hanyi.ctm.workertrigger.dto.*;

import java.time.Duration;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

public interface ICtmTask {

    default List<ICtmTaskConsumer> getConsumers() {
        return null;
    }

    private void foreachConsumers(Consumer<ICtmTaskConsumer> consumer) {
        Optional.ofNullable(getConsumers()).ifPresent(list -> list.forEach(consumer));
    }

    default void customerBatchAddJourney(CustomerBatchAddJourneyDto dto, Duration delay) {
        foreachConsumers(c -> c.customerBatchAddJourney(dto, delay));
    }

    default void customerSendSms(CustomerSendSmsDto dto) {
        foreachConsumers(c -> c.customerSendSms(dto));
    }

    default void customerSendWechat(CustomerSendWechatDto dto) {
        foreachConsumers(c -> c.customerSendWechat(dto));
    }

    default void customerSendComposited(Long orgId, Long userId, String sourceKey, Long sourceId, String composited, Duration delay) {
        foreachConsumers(c -> c.customerSendComposited(orgId, userId, sourceKey, sourceId, composited, delay));
    }

    default void wechatOpenSyncCustomerList(WechatOpenSyncCustomerListDto dto) {
        foreachConsumers(c -> c.wechatOpenSyncCustomerList(dto));
    }

    default void wechatOpenSyncCustomerInfoSingle(WechatOpenSyncCustomerInfoSingleDto dto) {
        foreachConsumers(c -> c.wechatOpenSyncCustomerInfoSingle(dto));
    }

    default void wechatOpenSyncTemplate(WechatOpenSyncTemplateDto dto) {
        foreachConsumers(c -> c.wechatOpenSyncTemplate(dto));
    }

    default void customerResetDepartment(Long orgId, Long userId) {
        foreachConsumers(c -> c.customerResetDepartment(orgId, userId));
    }

    default void eventRerun(Long orgId, Long userId, Long warningId, Long taskProgressId) {
        foreachConsumers(c -> c.eventRerun(orgId, userId, warningId, taskProgressId));
    }
}
