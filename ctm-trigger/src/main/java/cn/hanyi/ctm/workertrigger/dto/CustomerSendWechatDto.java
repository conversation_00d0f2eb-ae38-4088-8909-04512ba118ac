package cn.hanyi.ctm.workertrigger.dto;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.Duration;

@Getter
@Setter
@NoArgsConstructor
public class CustomerSendWechatDto {
    private Long orgId;
    private Long userId;
    private Long taskProgressId;
    private String from;
    private Long journeyRecordId;
    private Long eventId;
    private Long thirdpartyAuthId;
    private String appId;
    private String templateId;
    private String openId;
    private String message;
    private String url;
    private Duration delay;
    private String timed;

    public CustomerSendWechatDto(Long orgId, Long userId, Long taskProgressId, Long journeyRecordId, Long thirdpartyAuthId, String appId, String templateId, String openId, String message, String url, Duration delay, String timed) {
        this.orgId = orgId;
        this.userId = userId;
        this.taskProgressId = taskProgressId;
        this.from = "JOURNEY_INTERACTION";
        this.journeyRecordId = journeyRecordId;
        this.thirdpartyAuthId = thirdpartyAuthId;
        this.appId = appId;
        this.templateId = templateId;
        this.openId = openId;
        this.message = message;
        this.url = url;
        this.delay = delay;
        this.timed = timed;
    }

    public CustomerSendWechatDto(Long orgId, Long userId, Long taskProgressId, Long eventId, Long thirdpartyAuthId, String appId, String openId, String message) {
        this.orgId = orgId;
        this.userId = userId;
        this.taskProgressId = taskProgressId;
        this.from = "EVENT_NOTIFY_CUSTOMER";
        this.eventId = eventId;
        this.thirdpartyAuthId = thirdpartyAuthId;
        this.appId = appId;
        this.openId = openId;
        this.message = message;
    }
}
