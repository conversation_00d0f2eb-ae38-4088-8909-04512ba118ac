package cn.hanyi.ctm.workertrigger.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.Duration;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CustomerSendSmsDto {
    private Long orgId;
    private Long userId;
    private Long taskProgressId;
    private String from;    // JOURNEY_INTERACTION,EVENT_NOTIFY_CUSTOMER
    private Long journeyRecordId;
    private Long eventId;
    private Long thirdpartyAuthId;
    private String content;
    private String mobile;
    private String templateId;
    private String templateName;
    private String signId;
    private String realSign;
    private Duration delay;
    private String timed;
    
    public static CustomerSendSmsDto fromJourney(Long orgId, Long userId, Long taskProgressId, Long journeyRecordId, Long thirdpartyAuthId, String content, String mobile, String templateId, String templateName, String signId, String realSign, Duration delay, String timed) {
        CustomerSendSmsDto dto = new CustomerSendSmsDto();
        dto.orgId = orgId;
        dto.userId = userId;
        dto.taskProgressId = taskProgressId;
        dto.from="JOURNEY_INTERACTION";
        dto.journeyRecordId = journeyRecordId;
        dto.thirdpartyAuthId = thirdpartyAuthId;
        dto.content = content;
        dto.mobile = mobile;
        dto.templateId = templateId;
        dto.templateName = templateName;
        dto.signId = signId;
        dto.realSign = realSign;
        dto.delay = delay;
        dto.timed = timed;
        return dto;
    }

    public static CustomerSendSmsDto fromEvent(Long orgId, Long userId, Long taskProgressId, Long eventId, Long thirdpartyAuthId, String content, String mobile, String templateId, String templateName, String signId, String realSign, Duration delay, String timed) {
        CustomerSendSmsDto dto = new CustomerSendSmsDto();
        dto.orgId = orgId;
        dto.userId = userId;
        dto.taskProgressId = taskProgressId;
        dto.from="EVENT_NOTIFY_CUSTOMER";
        dto.eventId = eventId;
        dto.thirdpartyAuthId = thirdpartyAuthId;
        dto.content = content;
        dto.mobile = mobile;
        dto.templateId = templateId;
        dto.templateName = templateName;
        dto.signId = signId;
        dto.realSign = realSign;
        dto.delay = delay;
        dto.timed = timed;
        return dto;
    }
}
