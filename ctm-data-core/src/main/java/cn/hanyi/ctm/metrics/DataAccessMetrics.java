package cn.hanyi.ctm.metrics;

import cn.hanyi.ctm.constant.DataAccessType;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Timer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;


@Component
@ConditionalOnProperty(prefix = "data.access.metrics", value = "enabled", havingValue = "true")
public class DataAccessMetrics {

    @Autowired(required = false)
    private MeterRegistry meterRegistry;

    private final Map<String/*{name}.{id}*/, Counter> counterMap = new HashMap<>();
    private final Map<String/*{name}.{id}*/, Timer> timerMap = new HashMap<>();

    private static final String METRIC_NAME_PREFIX = "data.access";
    private static final String METRIC_NAME_RECEIVE_COUNTER = METRIC_NAME_PREFIX + ".receive";  // tags: id, type(datahub), namespace, deployment, pod
    private static final String METRIC_NAME_HANDLE_COUNTER = METRIC_NAME_PREFIX + ".handle";    // tags: id, type(datahub), namespace, deployment, pod
    private static final String METRIC_NAME_DISCARD_COUNTER = METRIC_NAME_PREFIX + ".discard";  // tags: id, type(datahub), namespace, deployment, pod
    private static final String METRIC_NAME_COST_TIMER = METRIC_NAME_PREFIX + ".timer";         // tags: id, type(datahub), namespace, deployment, pod

    public void receiveDataHub(Long dataAccessId, int amount) {
        counter(dataAccessId, DataAccessType.DATAHUB.name().toLowerCase(), METRIC_NAME_RECEIVE_COUNTER, amount);
    }

    public void handleDataHub(Long dataAccessId, int amount) {
        counter(dataAccessId, DataAccessType.DATAHUB.name().toLowerCase(), METRIC_NAME_HANDLE_COUNTER, amount);
    }

    public void discardDataHub(Long dataAccessId, int amount) {
        counter(dataAccessId, DataAccessType.DATAHUB.name().toLowerCase(), METRIC_NAME_DISCARD_COUNTER, amount);
    }

    public void costDataHub(Long dataAccessId, long startMs) {
        timer(dataAccessId, DataAccessType.DATAHUB.name().toLowerCase(), METRIC_NAME_COST_TIMER, startMs);
    }

    private void counter(Long id, String type, String metrics, int amount) {
        if (meterRegistry == null || id == null) {
            return;
        }
        String key = metrics + "." + id;
        Counter counter = counterMap.get(key);
        if (counter == null) {
            synchronized (counterMap) {
                counter = counterMap.get(key);
                if (counter == null) {
                    counter = createCounter(id, type, metrics);
                    counterMap.put(key, counter);
                }
            }
        }
        counter.increment(amount);
    }

    private Counter createCounter(Long id, String type, String metrics) {
        List<Tag> tags = new ArrayList<>();
        tags.add(Tag.of("id", id.toString()));
        tags.add(Tag.of("type", type));
        return meterRegistry.counter(metrics, tags);
    }


    private void timer(Long id, String type, String metrics, long startMs) {
        if (meterRegistry == null || id == null) {
            return;
        }
        String key = metrics + "." + id;
        Timer timer = timerMap.get(key);
        if (timer == null) {
            synchronized (timerMap) {
                timer = timerMap.get(key);
                if (timer == null) {
                    timer = createTimer(id, type, metrics);
                    timerMap.put(key, timer);
                }
            }
        }
        long ms = System.currentTimeMillis() - startMs;
        if (ms > 0) {
            timer.record(ms, TimeUnit.MILLISECONDS);
        }
    }

    private Timer createTimer(Long id, String type, String metrics) {
        List<Tag> tags = new ArrayList<>();
        tags.add(Tag.of("id", id.toString()));
        tags.add(Tag.of("type", type));
        return meterRegistry.timer(metrics, tags);
    }

    public static class Empty extends DataAccessMetrics {
        @Override
        public void receiveDataHub(Long dataAccessId, int amount) {

        }

        @Override
        public void handleDataHub(Long dataAccessId, int amount) {

        }

        @Override
        public void discardDataHub(Long dataAccessId, int amount) {

        }

        @Override
        public void costDataHub(Long dataAccessId, long ms) {

        }
    }
}