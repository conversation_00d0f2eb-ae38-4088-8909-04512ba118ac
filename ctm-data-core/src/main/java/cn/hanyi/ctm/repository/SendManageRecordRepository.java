package cn.hanyi.ctm.repository;

import cn.hanyi.ctm.constant.SendManageRecordStatus;
import cn.hanyi.ctm.entity.SendManageRecord;
import cn.hanyi.survey.core.constant.ReplyStatus;
import cn.hanyi.survey.core.constant.survey.ReceiveStatus;
import cn.hanyi.survey.core.constant.survey.SendStatus;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Repository
public interface SendManageRecordRepository extends ResourceRepository<SendManageRecord, Long> {


    long countByIdNotAndSendManageIdAndCustomerIdAndCreateTimeAfter(Long id, Long sendManageId, Long customerId, Date createTime);

    long countByIdNotAndSendManageIdAndCustomerIdAndStatusNotAndCreateTimeAfterAllIgnoreCase(Long id, Long sendManageId, Long customerId, SendManageRecordStatus status, Date createTime);

    List<SendManageRecord> findByIdNotAndSendManageIdAndCustomerIdOrderByIdDesc(Long id, Long sendManageId, Long customerId, Pageable pageable);

    Integer countBySendManageIdAndReplyStatus(Long id, ReplyStatus replyStatus);

    @Transactional
    @Modifying
    @Query("update SendManageRecord s set s.sendStatus = ?1, s.receiveStatus = ?2, s.replyStatus = ?3 where s.id = ?4")
    int updateSendStatusAndReceiveStatusAndReplyStatusById(SendStatus sendStatus, ReceiveStatus receiveStatus, ReplyStatus replyStatus, Long id);

    @Transactional
    @Modifying
    @Query("update SendManageRecord s set s.responseId = ?1 where s.id = ?2")
    int updateResponseIdById(Long responseId, Long id);

    Page<SendManageRecord> findAllBySendManageIdAndSendStatusAndReplyStatusNot(Long sendManageId, SendStatus sendStatus, ReplyStatus replyStatus, Pageable pageable);

}
