package cn.hanyi.ctm.repository;

import cn.hanyi.ctm.constant.SendManageTriggerType;
import cn.hanyi.ctm.entity.SendManage;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

@Repository
public interface SendManageRepository extends ResourceRepository<SendManage, Long> {
    //     JOURNEY 有唯一限制只会查询到一条
    //     ACCESS,TRIGGER 可以存在多关联
    List<SendManage> findByOrgIdAndTriggerTypeAndTriggerIdAndEnable(Long orgId, SendManageTriggerType triggerType, Long triggerId, Boolean enable);

    List<SendManage> findByOrgIdAndTriggerTypeAndTriggerIdIn(Long orgId, SendManageTriggerType triggerType, Set<Long> triggerId);

    SendManage findFirstByOrgIdAndSendToken(Long orgId, String sendToken);

    List<SendManage> findByTriggerTypeAndEnable(SendManageTriggerType triggerType, Boolean enable, Pageable pageable);

    List<SendManage> findByOrgIdAndTriggerTypeAndEnable(Long orgId, SendManageTriggerType triggerType, Boolean enable);

    Page<SendManage> findByEnableAndEnableRemind(Boolean enable, Boolean enableRemind, Pageable pageable);
}
