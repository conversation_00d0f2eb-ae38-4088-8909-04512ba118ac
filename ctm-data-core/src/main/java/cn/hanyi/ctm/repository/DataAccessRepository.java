package cn.hanyi.ctm.repository;

import cn.hanyi.ctm.constant.DataAccessType;
import cn.hanyi.ctm.dto.DataAccessSimpleDto;
import cn.hanyi.ctm.entity.DataAccess;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Repository
public interface DataAccessRepository extends ResourceRepository<DataAccess, Long> {

    List<DataAccess> findAllByEnable(Boolean enable);

    List<DataAccess> findAllByTypeAndEnable(DataAccessType type, Boolean enable);

    Page<DataAccessSimpleDto> findSimpleByOrgId(Long orgId, Pageable pageable);

    @Transactional
    @Modifying
    @Query("update DataAccess d set d.activeVersion = ?1, d.activeTime = ?2 where d.id = ?3")
    int updateActiveVersionAndActiveTimeById(Integer activeVersion, Date activeTime, Long id);
}
