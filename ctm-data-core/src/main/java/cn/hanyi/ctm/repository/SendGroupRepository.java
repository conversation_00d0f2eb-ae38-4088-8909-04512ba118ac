package cn.hanyi.ctm.repository;

import cn.hanyi.ctm.entity.SendGroup;
import org.befun.core.repository.ResourceRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;

@Repository
public interface SendGroupRepository extends ResourceRepository<SendGroup, Long> {

    @Async
    @Transactional
    @Modifying
    @Query("update SendGroup s set s.userId = ?1 where s.orgId = ?2 and s.id in ?3")
    void updateUserIdByOrgIdAndIdIn(Long userId, Long orgId, Collection<Long> ids);
}
