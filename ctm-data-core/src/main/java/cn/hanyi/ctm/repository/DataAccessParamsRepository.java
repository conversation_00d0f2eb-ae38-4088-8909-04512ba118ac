package cn.hanyi.ctm.repository;

import cn.hanyi.ctm.entity.DataAccess;
import cn.hanyi.ctm.entity.DataAccessParams;
import org.befun.core.repository.ResourceRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DataAccessParamsRepository extends ResourceRepository<DataAccessParams, Long> {
    List<DataAccessParams> findByDataAccessOrderBySequenceAscIdAsc(DataAccess dataAccess);

    Integer findTopOneByDataAccessOrderBySequenceDesc(DataAccess dataAccess);
}
