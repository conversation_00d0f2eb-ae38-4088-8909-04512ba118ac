package cn.hanyi.ctm.dataaccess;


import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
@ConditionalOnProperty(name = "data-access.scheduling.enabled", havingValue = "true")
public class DataAccessScheduling {

    @Autowired(required = false)
    private List<DataAccessExecuter> dataAccessExecuterList = new ArrayList<>();

    @Scheduled(cron = "${data-access.scheduling.cron:-}")
    public void scheduled() {
        dataAccessExecuterList.forEach(executer -> {
            try {
                executer.execute();
            } catch (Throwable e) {
                log.error("{} execute error", executer.getClass().getName(), e);
            }
        });
    }

}
