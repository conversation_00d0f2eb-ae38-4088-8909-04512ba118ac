package cn.hanyi.ctm.dataaccess;


import cn.hanyi.ctm.constant.DataAccessCellStatus;
import cn.hanyi.ctm.constant.ParamsFormatType;
import cn.hanyi.ctm.dto.DataAccessContextDto;
import cn.hanyi.ctm.dto.DataAccessDataDto;
import cn.hanyi.ctm.entity.DataAccessCell;
import cn.hanyi.ctm.entity.DataAccessParams;
import cn.hanyi.ctm.repository.DataAccessCellRepository;
import cn.hanyi.ctm.workertrigger.ICtmEventTrigger;
import cn.hutool.core.date.DateUtil;
import com.jayway.jsonpath.Configuration;
import com.jayway.jsonpath.JsonPath;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.util.Map;
import java.util.function.Supplier;

@Slf4j
public abstract class DataAccessParser<DATA extends DataAccessDataDto, CONTEXT extends DataAccessContextDto<DATA>> {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private DataAccessCellRepository cellRepository;
    @Autowired
    private ICtmEventTrigger ctmEventTrigger;

    public abstract boolean isParseData(CONTEXT context);

    public boolean parseData(CONTEXT context) {
        return parseJsonData(context);
    }

    /**
     * 解析json格式的消息，如果消息数据不是json，则会抛出异常
     */
    protected boolean parseJsonData(CONTEXT context) {
        String originData = context.getData().getRawData();
        Object document = Configuration.defaultConfiguration().jsonProvider().parse(originData);
        for (DataAccessParams param : context.getDataAccessParams()) {
            boolean success = paramValueIsCheck(context, param, () -> paramValue(context, param, document));
            if (!success) {
                return false;
            }
        }
        return true;
    }

    /**
     * 判断强校验
     * 消息 id 去重
     *
     * @return true 通过 false 不通过
     */
    protected boolean paramValueIsCheck(CONTEXT context, DataAccessParams param, Supplier<Object> getValue) {
        Object formatValue = null;
        try {
            formatValue = getValue.get();
        } catch (Exception e) {
            log.warn("context:{}, param解析参数失败:{}", context.getData().getRawData(), param.getParamsTitle(), e);
        }
        if (formatValue == null && (param.getIsCheck() != null && param.getIsCheck())) {
            // 没有解析出此参数，并且这个参数开启了强检验，直接结束
            log.error("解析参数失败，开启了强校验，直接丢弃此条消息：accessId={}, param={}, message={}", context.getDataAccess().getId(), param.getParamsName(), context.getData().getRawData());
            return false;
        }
        if (formatValue != null && (param.getIsUnique() != null && param.getIsUnique())) {
            // 已解析出此参数，并且这个参数是消息 id, 判断去重
            String messageId = formatValue.toString();
            if (checkMessageUnique(context, messageId)) {
                context.setMessageId(messageId);
            } else {
                log.warn("skip duplicate message: accessId={}, messageId={}", context.getDataAccess().getId(), messageId);
                return false;
            }
        }
        return true;
    }

    /**
     * 解析参数
     *
     * @return true 解析成功 false 解析失败
     */
    protected Object paramValue(CONTEXT context, DataAccessParams param, Object document) {
        Object value = JsonPath.read(document, param.getParamsMatchRule());
        return caseValue(context, param, value);
    }

    /**
     * 参数转换类型
     *
     * @return true 转换成功; false 转换失败
     */
    protected Object caseValue(CONTEXT context, DataAccessParams param, Object value) {
        Object formatValue = null;
        if (value != null) {
            // 转换类型
            if (param.getParamsFormat() == ParamsFormatType.STRING) {
                formatValue = castString(value, param.getParamsValue());
            } else if (param.getParamsFormat() == ParamsFormatType.NUMBER) {
                formatValue = castNumber(value);
            } else if (param.getParamsFormat() == ParamsFormatType.DATETIME) {
                formatValue = castDate(value);
            }
        }
        if (formatValue != null) {
            context.getParsedParameters().put(param.getParamsName(), formatValue);
            return formatValue;
        } else {
            return null;
        }
    }

    protected Object castDate(Object value) {
        return DateUtil.parse(value.toString());
    }

    protected Object castString(Object value, Map<String, Object> paramsValue) {
        String formatValue = value.toString();
        if (paramsValue != null && !paramsValue.isEmpty()) {
            return paramsValue.get(formatValue);
        }
        if (formatValue != null && formatValue.trim().isEmpty()) {
            return null;
        }
        return formatValue;
    }

    protected Object castNumber(Object value) {
        return Long.valueOf(value.toString());
    }

    public static final String MESSAGE_UNIQUE_LOCK = "data-access:%d:message-id:%s";
    public static final String MESSAGE_UNIQUE = "data-access:%d:message-id";

    private String getMessageUniqueKey(Long accessId) {
        return String.format(MESSAGE_UNIQUE, accessId);
    }

    /**
     * 1 锁定这个 messageId, 同一时刻如果有相同的 messageId 直接跳过
     * 2 判断这个 messageId 不在已解析的列表中
     */
    protected boolean checkMessageUnique(CONTEXT context, String messageId) {
        // lock key: data-access:{}:message-id:{}
        // zset key: data-access:{}:message-id score:time
        String lockKey = String.format(MESSAGE_UNIQUE_LOCK, context.getDataAccess().getId(), messageId);
        Boolean lock = stringRedisTemplate.opsForValue().setIfAbsent(lockKey, "1", Duration.ofSeconds(5));
        if (lock != null && lock) {
            String key = getMessageUniqueKey(context.getDataAccess().getId());
            return stringRedisTemplate.opsForZSet().score(key, messageId) == null;
        }
        return false;
    }

    /**
     * 1 裁剪 已解析列表，保留 最近 retainHours 小时的 messageId
     */
    public void trimMessageUnique(Long accessId, long retainHours) {
        long retainMinTime = System.currentTimeMillis() - (retainHours * 60 * 60 * 1000);
        String key = getMessageUniqueKey(accessId);
        stringRedisTemplate.opsForZSet().removeRange(key, 0, retainMinTime);
    }

    protected void addMessageUnique(CONTEXT context) {
        if (StringUtils.isNotEmpty(context.getMessageId())) {
            String key = getMessageUniqueKey(context.getDataAccess().getId());
            stringRedisTemplate.opsForZSet().add(key, context.getMessageId(), System.currentTimeMillis());
        }
    }

    @Transactional
    public void save(CONTEXT context) {
        DataAccessCell cell = new DataAccessCell(context.getDataAccess());
        cell.setStatus(DataAccessCellStatus.INIT);
        cell.setMessageId(context.getMessageId());
        cell.setMessageData(context.getData().getRawData());
        cell.setExtraData(JsonHelper.toJson(context.getExtraData()));
        cell.setParsedParams(JsonHelper.toJson(context.getParsedParameters()));
        saveAndTriggerEvent(context, cell);
    }

    protected void save(CONTEXT context, DataAccessCell cell) {
        cellRepository.save(cell);
        addMessageUnique(context);
    }

    protected void saveAndTriggerEvent(CONTEXT context, DataAccessCell cell) {
        save(context, cell);
        ctmEventTrigger.dataAccess(
                context.getDataAccess().getOrgId(),
                null,
                context.getDataAccess().getId(),
                cell.getId()
        );
    }


}
