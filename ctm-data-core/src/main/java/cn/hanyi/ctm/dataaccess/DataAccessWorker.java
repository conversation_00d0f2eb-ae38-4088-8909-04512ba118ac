package cn.hanyi.ctm.dataaccess;

import cn.hanyi.ctm.dto.DataAccessContextDto;
import cn.hanyi.ctm.dto.DataAccessDataDto;
import cn.hanyi.ctm.metrics.DataAccessMetrics;
import cn.hanyi.ctm.properties.DataAccessProperties;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;

@Slf4j
public abstract class DataAccessWorker<DATA extends DataAccessDataDto, CONTEXT extends DataAccessContextDto<DATA>, CONNECTION extends DataAccessConnection<DATA>> {

    private final CONNECTION connection;
    private final DataAccessParser<DATA, CONTEXT> dataAccessParser;
    protected final DataAccessMetrics dataAccessMetrics;
    private final int consumerThreads;
    private final int consumerWaitSeconds;
    private final ExecutorService consumerExecuter;
    private final Semaphore consumerSemaphore;

    public DataAccessWorker(CONNECTION connection,
                            DataAccessParser<DATA, CONTEXT> dataAccessParser,
                            DataAccessMetrics dataAccessMetrics,
                            DataAccessProperties dataAccessProperties) {
        this.connection = connection;
        this.dataAccessParser = dataAccessParser;
        this.dataAccessMetrics = dataAccessMetrics != null ? dataAccessMetrics : new DataAccessMetrics.Empty();
        this.consumerThreads = dataAccessProperties.getConsumerThreads();
        this.consumerWaitSeconds = dataAccessProperties.getConsumerWaitSeconds();
        if (consumerThreads > 1) {
            consumerExecuter = Executors.newFixedThreadPool(consumerThreads);
            consumerSemaphore = new Semaphore(consumerThreads);
        } else {
            consumerExecuter = null;
            consumerSemaphore = null;
        }
    }

    public abstract CONTEXT buildContext(DATA data);

    public CONNECTION connection() {
        return connection;
    }

    public Long id() {
        return connection().id();
    }

    public void awaitTermination() {
        if (consumerExecuter != null) {
            try {
                consumerExecuter.awaitTermination(consumerWaitSeconds, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
                log.error("data access ({}) consumer termination error", getClass().getName(), e);
            }
        }
    }

    protected void consumerData(List<DATA> data) {
        if (CollectionUtils.isEmpty(data)) {
            return;
        }
        // 记录接受到了n条消息
        dataAccessMetrics.receiveDataHub(id(), data.size());
        if (consumerExecuter != null) {
            asyncConsumerListData(data);
        } else {
            syncConsumerListData(data);
        }
    }

    private void syncConsumerListData(List<DATA> data) {
        data.forEach(this::syncConsumerData);
    }

    @SneakyThrows
    private void asyncConsumerListData(List<DATA> data) {
        data.forEach(i -> {
            try {
                consumerSemaphore.acquire();
                consumerExecuter.execute(() -> {
                    try {
                        syncConsumerData(i);
                    } finally {
                        consumerSemaphore.release();
                    }
                });
            } catch (Throwable e) {
                // 添加到处理线程池失败，直接标记提交
                connection().ack(i);
                consumerSemaphore.release();
            }
        });
    }

    private void syncConsumerData(DATA data) {
        boolean handle = false;
        long startMs = System.currentTimeMillis();
        try {
            CONTEXT context = buildContext(data);
            if (dataAccessParser.isParseData(context) && dataAccessParser.parseData(context)) {
                dataAccessParser.save(context);
                handle = true;
            }
        } catch (Throwable e) {
            log.error("data access ({}) consumer data error", getClass().getName(), e);
        } finally {
            connection().ack(data);
            // 记录消费时长
            dataAccessMetrics.costDataHub(id(), startMs);
            if (handle) {
                // 记录这条消息已处理
                dataAccessMetrics.handleDataHub(id(), 1);
            } else {
                // 记录这条消息已丢弃
                dataAccessMetrics.discardDataHub(id(), 1);
            }
        }
    }

}
