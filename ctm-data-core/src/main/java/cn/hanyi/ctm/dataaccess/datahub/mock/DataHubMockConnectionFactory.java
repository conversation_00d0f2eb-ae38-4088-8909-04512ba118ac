package cn.hanyi.ctm.dataaccess.datahub.mock;

import cn.hanyi.ctm.dataaccess.datahub.DataHubConnection;
import cn.hanyi.ctm.entity.DataAccess;
import cn.hanyi.ctm.entity.DataAccessParams;
import cn.hanyi.ctm.properties.DataHubProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@ConditionalOnProperty(prefix = "data-access.datahub", name = "enable-mock-connection", havingValue = "true")
public class DataHubMockConnectionFactory {
    @Autowired
    private StringRedisTemplate redisTemplate;

    public DataHubConnection createMockConnection(DataAccess dataAccess, List<DataAccessParams> params, DataHubProperties dataHubProperties) {
        return new DataHubMockConnection(dataAccess, params, dataHubProperties, redisTemplate);
    }
}