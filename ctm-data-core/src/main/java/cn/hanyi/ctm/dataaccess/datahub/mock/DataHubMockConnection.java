package cn.hanyi.ctm.dataaccess.datahub.mock;

import cn.hanyi.ctm.dataaccess.datahub.DataHubConnection;
import cn.hanyi.ctm.dto.DataHubConfigDto;
import cn.hanyi.ctm.dto.DataHubDataDto;
import cn.hanyi.ctm.entity.DataAccess;
import cn.hanyi.ctm.entity.DataAccessParams;
import cn.hanyi.ctm.properties.DataHubProperties;
import com.aliyun.datahub.client.model.RecordEntry;
import com.aliyun.datahub.client.model.RecordKey;
import com.aliyun.datahub.client.model.RecordSchema;
import com.aliyun.datahub.client.model.TupleRecordData;
import com.aliyun.datahub.clientlibrary.config.ProducerConfig;
import com.aliyun.datahub.clientlibrary.producer.DatahubProducer;
import org.apache.commons.collections4.CollectionUtils;
import org.befun.core.utils.JsonHelper;
import org.springframework.data.redis.connection.stream.*;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class DataHubMockConnection extends DataHubConnection {

    public StringRedisTemplate redisTemplate;
    private final String queue;
    private final String group = "data-access-group";

    public DataHubMockConnection(DataAccess dataAccess, List<DataAccessParams> params, DataHubProperties dataHubProperties, StringRedisTemplate redisTemplate) {
        super(dataAccess, params, dataHubProperties);
        this.redisTemplate = redisTemplate;
        this.queue = String.format(dataHubProperties.getMockConnectionQueue(), dataAccess.getId());
        init();
        afterInit();
    }

    @Override
    public void init() {
        try {
            redisTemplate.opsForStream().createGroup(queue, group);
        } catch (Throwable e) {
            // ignore
        }
    }

    @Override
    public List<DataHubDataDto> getData(int size) {
        List<DataHubDataDto> result = new ArrayList<>();
        Consumer consumer = Consumer.from(group, "data-access-consumer");
        for (int i = 0; i < size; i++) {
            StreamReadOptions options = StreamReadOptions.empty().count(1);
            StreamOffset<String> offset = StreamOffset.create(queue, ReadOffset.lastConsumed());
            List<ObjectRecord<String, DataHubDataDto>> list = redisTemplate.opsForStream().read(DataHubDataDto.class, consumer, options, offset);
            if (CollectionUtils.isEmpty(list)) {
                break;
            } else if (list.size() == 1) {
                DataHubDataDto data = list.get(0).getValue();
                data.setRecordKey(new MockRecordKey(redisTemplate, queue, group, list.get(0).getId().toString()));
                result.add(data);
            }
        }
        return result;
    }

    public static class MockRecordKey implements RecordKey {

        private StringRedisTemplate redisTemplate;
        private String queue;
        private String group;
        private String id;

        public MockRecordKey(StringRedisTemplate redisTemplate, String queue, String group, String id) {
            this.redisTemplate = redisTemplate;
            this.queue = queue;
            this.group = group;
            this.id = id;
        }

        @Override
        public void ack() {
            redisTemplate.opsForStream().acknowledge(queue, group, id);
        }
    }

    @Override
    public void release() {

    }

    public static void addMockData(StringRedisTemplate redisTemplate, Long accessId, String queueFormat, List<Map<String, Object>> data) {
        String queue = String.format(queueFormat, accessId);
        data.forEach(i -> {
            DataHubDataDto j = new DataHubDataDto(null, JsonHelper.toJson(i));
            ObjectRecord<String, DataHubDataDto> record = StreamRecords.newRecord().in(queue).ofObject(j);
            redisTemplate.opsForStream().add(record);
        });
    }

    public static void pushMockData(DataAccess dataAccess, List<Map<String, Object>> data) {

        DataHubConfigDto conf = dataAccess.getAccessConfiguration().getDataHub();
        String projectName = conf.getProjectName();
        String topicName = conf.getTopicName();

        ProducerConfig producerConfig = new ProducerConfig(conf.getEndPoint(), conf.getAccessId(), conf.getAccessKey());
        DatahubProducer producer = new DatahubProducer(projectName, topicName, producerConfig);

        RecordSchema schema = producer.getTopicSchema();
        List<RecordEntry> entries = new ArrayList<>();

        data.forEach(i -> {
            // 构造一条数据
            TupleRecordData recordData = new TupleRecordData(schema);
            JsonHelper.toMap(JsonHelper.toJson(i)).forEach(recordData::setField);
            RecordEntry entry = new RecordEntry();
            entry.setRecordData(recordData);
            entries.add(entry);
        });

        // 批量发送
        producer.send(entries);
        producer.flush(true);
        producer.close();
    }
}
