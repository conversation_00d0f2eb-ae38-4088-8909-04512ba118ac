package cn.hanyi.ctm.dataaccess;

import cn.hanyi.ctm.dto.DataAccessContextDto;
import cn.hanyi.ctm.dto.DataAccessDataDto;
import cn.hanyi.ctm.metrics.DataAccessMetrics;
import cn.hanyi.ctm.properties.DataAccessProperties;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class DataAccessRollPolingWorker<DATA extends DataAccessDataDto, CONTEXT extends DataAccessContextDto<DATA>, CONNECTION extends DataAccessConnection<DATA>>
        extends DataAccessWorker<DATA, CONTEXT, CONNECTION>
        implements Runnable {

    /**
     * 每次拉取的间隔时间，ms
     */
    private final long pullInterval;

    public DataAccessRollPolingWorker(CONNECTION connection, DataAccessParser<DATA, CONTEXT> dataHubParser, DataAccessMetrics dataAccessMetrics, DataAccessProperties dataAccessProperties) {
        super(connection, dataHubParser, dataAccessMetrics, dataAccessProperties);
        this.pullInterval = dataAccessProperties.getPullInterval();
    }

    /**
     * 拉取消息，处理消息
     *
     * @return true 跳过睡眠时间，立即开始下次拉取，false 进入睡眠时间
     */
    public abstract boolean pull() throws InterruptedException;

    @Override
    public void run() {
        while (connection().isAlive()) {
            try {
                boolean hasData = false;
                try {
                    hasData = pull();
                } catch (Throwable e) {
                    log.error("{} pull error", getClass().getName());
                }
                // 消费完消息后，重新检查连接状态，如果已经标记连接无效了，
                boolean alive = connection().isAlive();
                if (!alive) {
                    // 等到线程池的任务全部执行完毕后，在销毁连接资源
                    awaitTermination();
                    connection().release();
                }
                // 消费完消息后，重新检查连接状态，只有当前连接还是有效的，才会sleep
                if (alive && !hasData && pullInterval > 0) {
                    Thread.sleep(pullInterval);
                }
            } catch (Throwable e) {
                log.error("{} pull error", getClass().getName());
            }

        }
    }

}
