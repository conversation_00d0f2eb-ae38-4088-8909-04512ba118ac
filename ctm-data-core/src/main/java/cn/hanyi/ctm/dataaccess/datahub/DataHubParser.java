package cn.hanyi.ctm.dataaccess.datahub;

import cn.hanyi.ctm.dataaccess.DataAccessParser;
import cn.hanyi.ctm.dto.DataHubContextDto;
import cn.hanyi.ctm.dto.DataHubDataDto;
import org.springframework.stereotype.Component;

@Component
public class DataHubParser extends DataAccessParser<DataHubDataDto, DataHubContextDto> {

    @Override
    public boolean isParseData(DataHubContextDto dataHubContextDto) {
        return true;
    }
}
