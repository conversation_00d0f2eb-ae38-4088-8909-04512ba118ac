package cn.hanyi.ctm.dataaccess.datahub;

import cn.hanyi.ctm.constant.DataAccessType;
import cn.hanyi.ctm.dataaccess.DataAccessExecuter;
import cn.hanyi.ctm.dataaccess.datahub.mock.DataHubMockConnectionFactory;
import cn.hanyi.ctm.entity.DataAccess;
import cn.hanyi.ctm.entity.DataAccessParams;
import cn.hanyi.ctm.metrics.DataAccessMetrics;
import cn.hanyi.ctm.properties.DataAccessProperties;
import cn.hanyi.ctm.properties.DataHubProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.persistence.EntityManager;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static cn.hanyi.ctm.properties.DataHubProperties.ENABLE_KEY;

/**
 * 执行器
 */
@Slf4j
@Component
@ConditionalOnProperty(name = ENABLE_KEY, havingValue = "true")
public class DataHubExecuter extends DataAccessExecuter {

    @Autowired
    private EntityManager entityManager;
    @Autowired
    private DataHubParser dataHubParser;
    @Autowired(required = false)
    private DataHubMockConnectionFactory dataHubMockConnectionFactory;
    @Autowired
    private DataHubProperties dataHubProperties;
    @Autowired
    private DataAccessProperties dataAccessProperties;
    @Autowired(required = false)
    private DataAccessMetrics dataAccessMetrics;
    private ExecutorService executor;
    private final Map<Long, DataHubConnection> connectionMap = new HashMap<>();

    @Override
    protected DataAccessType type() {
        return DataAccessType.DATAHUB;
    }

    /**
     * 定时执行此方法，保证活跃的连接和配置的保持一致
     */
    @Override
    public void execute() {
        initExecuter();
        Map<Long, DataAccess> newMap = getNewMap();
        Map<Long, DataHubConnection> currentMap = new HashMap<>(connectionMap);
        List<Long> add = new ArrayList<>();
        List<Long> delete = new ArrayList<>();
        currentMap.forEach((id, connection) -> {
            DataAccess newDataAccess = newMap.get(id);
            if (newDataAccess == null) {
                // 配置项未启用，或者已删除，结束连接
                delete.add(id);
            } else {
                // 配置项有更新，结束连接
                int newVersion = newDataAccess.getVersion();
                int currentVersion = connection.getDataAccess().getVersion();
                if (newVersion > currentVersion) {
                    delete.add(id);
                }
            }
        });
        newMap.forEach((id, dataAccess) -> {
            if (currentMap.containsKey(id)) {
                if (delete.contains(id)) {
                    // 配置项在运行中，但是标记了版本已更新，新建连接
                    add.add(id);
                }
            } else {
                // 配置项不再运行中，新建连接
                add.add(id);
            }
        });
        if (!delete.isEmpty()) {
            delete.forEach(id -> {
                DataHubConnection connection = currentMap.get(id);
                if (connection != null) {
                    destroyConnection(connection);
                }
            });
        }
        if (!add.isEmpty()) {
            add.forEach(id -> {
                DataAccess dataAccess = newMap.get(id);
                if (dataAccess != null) {
                    initConnection(dataAccess);
                }
            });
        }
        Date now = new Date();
        connectionMap.forEach((id, connection) -> {
            int currentVersion = connection.currentVersion();
            updateActiveInfo(id, currentVersion, now);
            log.debug("update DATAHUB connection active info, id={}, version={}", id, currentVersion);
        });

        // trim message unique
        connectionMap.forEach((id, connection) -> {
            dataHubParser.trimMessageUnique(id, dataAccessProperties.getMessageUniqueRetainHours());
            log.debug("trim DATAHUB connection message unique, id={}}", id);
        });
    }

    private void initExecuter() {
        if (executor == null) {
            synchronized (this) {
                if (executor == null) {
                    executor = new ThreadPoolExecutor(
                            dataHubProperties.getMaxConnection(),
                            dataHubProperties.getMaxConnection(),
                            30,
                            TimeUnit.SECONDS,
                            new LinkedBlockingQueue<>(dataHubProperties.getMaxConnection()),
                            new ThreadPoolExecutor.DiscardPolicy());
                }
            }
        }
    }

    private Map<Long, DataAccess> getNewMap() {
        List<DataAccess> list = getAllEnabledDataAccess();
        Map<Long, DataAccess> newMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(list)) {
            for (DataAccess dataAccess : list) {
                newMap.put(dataAccess.getId(), dataAccess);
            }
        }
        return newMap;
    }

    private void initConnection(DataAccess dataAccess) {
        try {
            List<DataAccessParams> params = getParamsByDataAccess(dataAccess);
            DataHubConnection connection = createConnection(dataAccess, params, dataHubProperties);
            connectionMap.put(connection.id(), connection);
            executor.execute(new DataHubWorker(connection, dataHubParser, dataAccessMetrics, dataAccessProperties));
            log.info("init DATAHUB connection success, id={}, version={}", connection.id(), connection.currentVersion());
        } catch (Throwable e) {
            log.info("init DATAHUB connection failure, id={}, version={}", dataAccess.getId(), dataAccess.getVersion());
            log.error("init DATAHUB connection failure", e);
        }
    }

    private DataHubConnection createConnection(DataAccess dataAccess, List<DataAccessParams> params, DataHubProperties dataHubProperties) {
        // 取消持久化状态
        entityManager.detach(dataAccess);
        params.forEach(entityManager::detach);
        if (dataHubProperties.isEnableMockConnection() && dataHubMockConnectionFactory != null) {
            return dataHubMockConnectionFactory.createMockConnection(dataAccess, params, dataHubProperties);
        } else {
            return new DataHubConnection(dataAccess, params, dataHubProperties);
        }
    }

    private void destroyConnection(DataHubConnection connection) {
        connectionMap.remove(connection.id());
        connection.destroy();
        log.info("destroy DATAHUB connection, id={}, version={}", connection.id(), connection.currentVersion());
    }

}
