package cn.hanyi.ctm.dataaccess.datahub;


import cn.hanyi.ctm.dataaccess.DataAccessRollPolingWorker;
import cn.hanyi.ctm.dto.DataHubContextDto;
import cn.hanyi.ctm.dto.DataHubDataDto;
import cn.hanyi.ctm.metrics.DataAccessMetrics;
import cn.hanyi.ctm.properties.DataAccessProperties;

import java.util.List;

public class DataHubWorker extends DataAccessRollPolingWorker<DataHubDataDto, DataHubContextDto, DataHubConnection> {

    private final int pullSize;

    public DataHubWorker(DataHubConnection connection, DataHubParser dataHubParser, DataAccessMetrics dataAccessMetrics, DataAccessProperties dataAccessProperties) {
        super(connection, dataHubParser, dataAccessMetrics, dataAccessProperties);
        this.pullSize = dataAccessProperties.getPullSize();
    }

    @Override
    public boolean pull() {
        List<DataHubDataDto> data = connection().getData(pullSize);
        if (data == null || data.isEmpty()) {
            return false;
        } else {
            consumerData(data);
            return true;
        }
    }

    @Override
    public DataHubContextDto buildContext(DataHubDataDto dataHubDataDto) {
        return new DataHubContextDto(dataHubDataDto, connection().getDataAccess(), connection().getDataAccessParams());
    }
}
