package cn.hanyi.ctm.dataaccess;


import cn.hanyi.ctm.constant.DataAccessType;
import cn.hanyi.ctm.entity.DataAccess;
import cn.hanyi.ctm.entity.DataAccessParams;
import cn.hanyi.ctm.repository.DataAccessParamsRepository;
import cn.hanyi.ctm.repository.DataAccessRepository;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public abstract class DataAccessExecuter {

    @Autowired
    private DataAccessRepository dataAccessRepository;
    @Autowired
    private DataAccessParamsRepository dataAccessParamsRepository;

    protected abstract DataAccessType type();

    /**
     * 定时执行此方法，保证活跃的连接和配置的保持一致
     */
    public abstract void execute();

    protected List<DataAccess> getAllEnabledDataAccess() {
        return dataAccessRepository.findAllByTypeAndEnable(type(), true);
    }

    protected List<DataAccessParams> getParamsByDataAccess(DataAccess dataAccess) {
        if (dataAccess == null) {
            return new ArrayList<>();
        }
        return dataAccessParamsRepository.findByDataAccessOrderBySequenceAscIdAsc(dataAccess);
    }

    protected void updateActiveInfo(Long id, Integer activeVersion, Date activeTime) {
        dataAccessRepository.updateActiveVersionAndActiveTimeById(activeVersion, activeTime, id);
    }
}
