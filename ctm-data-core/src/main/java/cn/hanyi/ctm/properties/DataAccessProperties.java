package cn.hanyi.ctm.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration()
@ConfigurationProperties(prefix = "data-access")
public class DataAccessProperties {

    /**
     * 没有拉取到数据后，睡眠多少毫秒后，重新拉取
     */
    private int pullInterval = 5000;
    /**
     * 每次拉取多少条数据
     */
    private int pullSize = 1;
    /**
     * 获取到数据后，使用多少个线程来处理，如果是1个，则直接在当前处理，大于1个会启用线程池
     */
    private int consumerThreads = 1;

    /**
     * 启用线程池处理后，需要等到这一批消息处理完后，在全部提交，这个时候拉取消息的线程需要等待的时间秒
     */
    private int consumerWaitSeconds = 90;

    /**
     * 消息唯一标识符（消息唯一性），在 zset 中保留的时长
     */
    private int messageUniqueRetainHours = 24;

}
