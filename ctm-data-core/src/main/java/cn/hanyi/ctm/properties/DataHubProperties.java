package cn.hanyi.ctm.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Getter
@Setter
@Configuration()
@ConfigurationProperties(prefix = "data-access.datahub")
public class DataHubProperties {

    public static final String ENABLE_KEY = "data-access.datahub.enabled";

    private boolean enabled = false;
    /**
     * 最多只能启用这个多的数据接入配置
     */
    private int maxConnection = 50;
    /**
     * 客户端提交超时时间
     */
    private int commitTimeOut = 30000;
    /**
     * 客户端心跳超时时间
     */
    private int sessionTimeOut = 30000;
    /**
     * 获取消息时重试次数
     */
    private Integer maxRetry = 1;
    /**
     * 自动提交
     */
    private boolean autoCommit = false;
    /**
     * 是否开启mock
     */
    private boolean enableMockConnection = false;

    private String mockConnectionQueue = "data-access:data-hub-mock-data:%d";
}
