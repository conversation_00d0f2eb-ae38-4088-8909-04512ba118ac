package cn.hanyi.ctm.entity;

import cn.hanyi.ctm.constant.DataAccessCellStatus;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.BaseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.rest.view.ResourceViews;
import org.hibernate.annotations.NotFound;
import org.hibernate.annotations.NotFoundAction;

import javax.persistence.*;

@Entity
@Table(name = "data_access_cell")
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@EntityScopeStrategy
@DtoClass(includeAllFields = true)
public class DataAccessCell extends BaseEntity {

    @JoinColumn(name = "access_id")
    @JsonView(ResourceViews.Basic.class)
    @ManyToOne
    @NotFound(action = NotFoundAction.IGNORE)
    private DataAccess dataAccess;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    @JsonView(ResourceViews.Basic.class)
    private DataAccessCellStatus status;

    @Column(name = "message_id")
    @JsonView(ResourceViews.Basic.class)
    private String messageId;

    @Column(name = "message_data")
    @JsonView(ResourceViews.Basic.class)
    private String messageData;

    @Column(name = "extra_data")
    @JsonView(ResourceViews.Basic.class)
    private String extraData;

    @Column(name = "parsed_params")
    @JsonView(ResourceViews.Basic.class)
    private String parsedParams;

    public DataAccessCell(DataAccess dataAccess) {
        this.dataAccess = dataAccess;
    }
}