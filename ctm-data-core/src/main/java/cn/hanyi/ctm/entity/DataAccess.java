package cn.hanyi.ctm.entity;

import cn.hanyi.ctm.constant.DataAccessType;
import cn.hanyi.ctm.dto.DataAccessConfigCovert;
import cn.hanyi.ctm.dto.DataConfigDto;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.apache.pulsar.shade.com.fasterxml.jackson.annotation.JsonIgnore;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

import static cn.hanyi.ctm.constant.DataAccessType.DATAHUB;

@Entity
@Table(name = "data_access")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EntityScopeStrategy
@DtoClass(includeAllFields = true)
public class DataAccess extends EnterpriseEntity {

    @Column(name = "name")
    @DtoProperty(jsonView = ResourceViews.Basic.class, queryable = true)
    private String name;

    @Column(name = "access_configuration")
    @JsonView(ResourceViews.Basic.class)
    @Convert(converter = DataAccessConfigCovert.class)
    private DataConfigDto accessConfiguration;

    @Column(name = "enable")
    @JsonView(ResourceViews.Basic.class)
    private Boolean enable = false;

    @JsonView(ResourceViews.Basic.class)
    @OneToMany(mappedBy = "dataAccess", fetch = FetchType.EAGER)
    @OrderBy("sequence ASC")
    private List<DataAccessParams> paramsConfiguration;

    @Column(name = "type")
    @Enumerated(EnumType.STRING)
    @JsonView(ResourceViews.Basic.class)
    private DataAccessType type = DATAHUB;

    @Column(name = "version")
    @JsonView(ResourceViews.Detail.class)
    @JsonIgnore
    private Integer version = 0;

    @Column(name = "active_version")
    @JsonView(ResourceViews.Basic.class)
    @JsonIgnore
    private Integer activeVersion;

    @Column(name = "active_time")
    @JsonView(ResourceViews.Basic.class)
    @JsonIgnore
    private Date activeTime;

    @Column(name = "storage_period")
    @JsonView(ResourceViews.Basic.class)
    private Integer storagePeriod = 3;

    @Transient
    @DtoProperty(jsonView = ResourceViews.Basic.class)
    private Integer cellNumber = 0;
}

