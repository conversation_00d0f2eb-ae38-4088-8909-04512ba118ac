package cn.hanyi.ctm.entity;

import cn.hanyi.ctm.constant.AccessParamType;
import cn.hanyi.ctm.constant.ParamsFormatType;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.converter.HashMapConverter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.dto.annotation.DtoProperty;
import org.befun.core.entity.BaseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;
import javax.validation.constraints.NotEmpty;
import java.util.HashMap;
import java.util.Map;

@Entity
@Table(name = "data_access_params")
@Setter
@Getter
@NoArgsConstructor
@AllArgsConstructor
@EntityScopeStrategy
@DtoClass(includeAllFields = true)
public class DataAccessParams extends BaseEntity {

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "access_id")
    @DtoProperty(ignore = true)
    private DataAccess dataAccess;

    @Column(name = "params_format")
    @JsonView(ResourceViews.Basic.class)
    @Enumerated(EnumType.STRING)
    private ParamsFormatType paramsFormat = ParamsFormatType.STRING;

    @Column(name = "params_name")
    @JsonView(ResourceViews.Basic.class)
    @NotEmpty
    private String paramsName;

    @Column(name = "params_title")
    @NotEmpty
    @JsonView(ResourceViews.Basic.class)
    private String paramsTitle;

    @Column(name = "params_type")
    @JsonView(ResourceViews.Basic.class)
    @Enumerated(EnumType.STRING)
    private AccessParamType paramsType = AccessParamType.SYSTEM;

    @Column(name = "params_match_rule")
    @JsonView(ResourceViews.Basic.class)
    @NotEmpty
    private String paramsMatchRule;

    @Column(name = "params_value")
    @JsonView(ResourceViews.Basic.class)
    @Convert(converter = HashMapConverter.class)
    private Map<String, Object> paramsValue = new HashMap<>();

    @Column(name = "is_check")
    @JsonView(ResourceViews.Basic.class)
    private Boolean isCheck;

    @Column(name = "is_unique")
    @JsonView(ResourceViews.Basic.class)
    private Boolean isUnique;

    @Column(name = "sequence")
    @JsonView(ResourceViews.Basic.class)
    private Integer sequence = 0;

}