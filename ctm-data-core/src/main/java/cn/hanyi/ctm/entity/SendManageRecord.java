package cn.hanyi.ctm.entity;

import cn.hanyi.cem.core.dto.task.TaskCustomerSendCompositedDto;
import cn.hanyi.ctm.constant.SendManageRecordStatus;
import cn.hanyi.survey.core.constant.ReplyStatus;
import cn.hanyi.survey.core.constant.survey.ReceiveStatus;
import cn.hanyi.survey.core.constant.survey.SendStatus;
import com.fasterxml.jackson.annotation.JsonView;
import java.util.Date;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.converter.HashMapConverter;
import org.befun.core.dto.annotation.DtoClass;
import org.befun.core.entity.EnterpriseEntity;
import org.befun.core.entity.annotation.EntityScopeStrategy;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.*;

@Entity
@Table(name = "send_manage_record")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@EntityScopeStrategy
@DtoClass(includeAllFields = true)
public class SendManageRecord extends EnterpriseEntity {

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "send_manage_id")
    private Long sendManageId;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "source_id")
    private Long sourceId;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "survey_id")
    private Long surveyId;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "customer_id")
    private Long customerId;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "client_id")
    private String clientId;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "send_url")
    private String sendUrl;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "link_id")
    private Long linkId;

    @JsonView(ResourceViews.Basic.class)
    @Column(name = "response_id")
    private Long responseId;

    @JsonView(ResourceViews.Detail.class)
    @Column(name = "send_status")
    @Enumerated(EnumType.STRING)
    private SendStatus sendStatus = SendStatus.UN_SEND;

    @JsonView(ResourceViews.Detail.class)
    @Column(name = "receive_status")
    @Enumerated(EnumType.STRING)
    private ReceiveStatus receiveStatus = ReceiveStatus.UNKNOWN;

    @JsonView(ResourceViews.Detail.class)
    @Column(name = "reply_status")
    @Enumerated(EnumType.STRING)
    private ReplyStatus replyStatus = ReplyStatus.UN_VISIT;

    @JsonView(ResourceViews.Detail.class)
    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private SendManageRecordStatus status = SendManageRecordStatus.UN_COMPLETE;

    @JsonView(ResourceViews.Detail.class)
    @Column(name = "send_channel_info")
    private String sendChannelInfo;

    @JsonView(ResourceViews.Detail.class)
    @Column(name = "send_channel_status")
    private String sendChannelStatus;

    @JsonView(ResourceViews.Detail.class)
    @Column(name = "task_progress_id")
    private Long taskProgressId;

    // bi那边统计用 其他地方不会用到
    @JsonView(ResourceViews.Detail.class)
    @Column(name = "account")
    private String account;

    @JsonView(ResourceViews.Detail.class)
    @Column(name = "remind_count")
    private Integer remindCount;

    @JsonView(ResourceViews.Detail.class)
    @Column(name = "latest_remind_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date latestRemindTime;

    @JsonView(ResourceViews.Detail.class)
    @Column(name = "send_time")
    @Temporal(TemporalType.TIMESTAMP)
    private Date sendTime;

    @JsonView(ResourceViews.Detail.class)
    @Convert(converter = HashMapConverter.class)
    @Column(name = "params")
    private Map<String, Object> params;


    @Transient
    private TaskCustomerSendCompositedDto sendComposited;
}

