package cn.hanyi.ctm.service;

import cn.hanyi.common.file.storage.FileInfo;
import cn.hanyi.ctm.constant.SendManagePageType;
import cn.hanyi.ctm.constant.SendManageRepeatType;
import cn.hanyi.ctm.constant.SendManageTriggerType;
import cn.hanyi.ctm.dto.SendManageAnalysisRequestDto;
import cn.hanyi.ctm.dto.SendManageAnalysisResponseDto;
import cn.hanyi.ctm.dto.TriggerEventType;
import cn.hanyi.ctm.dto.customer.CustomerSelectedDto;
import cn.hanyi.ctm.entity.SendManage;
import cn.hanyi.ctm.entity.SendManageDto;
import cn.hanyi.ctm.repository.SendManageRecordRepository;
import cn.hanyi.ctm.repository.SendManageRepository;
import cn.hanyi.survey.core.constant.ReplyStatus;
import cn.hanyi.survey.core.constant.channel.ChannelType;
import cn.hanyi.survey.core.repository.SurveyChannelRepository;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.dto.auth.YouzanAuthDto;
import org.befun.auth.dto.auth.youzan.YouzanSupportEventDto;
import org.befun.auth.service.UserService;
import org.befun.auth.service.auth.AuthYouzanService;
import org.befun.auth.service.auth.ThirdPartyAuthHelper;
import org.befun.auth.service.auth.config.YouzanConfig;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.BaseService;
import org.befun.core.utils.JsonHelper;
import org.befun.extension.service.FileService;
import org.befun.extension.service.WeChatMiniProgramService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.io.File;
import java.time.LocalDate;
import java.util.*;

@Service
@Slf4j
public class SendManageService extends BaseService<SendManage, SendManageDto, SendManageRepository> {

    @Autowired
    private UserService userService;

    @Autowired
    private SendManageRecordRepository sendManageRecordRepository;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private AuthYouzanService authYouzanService;

    @Autowired
    private ThirdPartyAuthHelper thirdPartyAuthHelper;

    @Autowired
    private SurveyChannelRepository surveyChannelRepository;

    @Autowired(required = false)
    private WeChatMiniProgramService weChatMiniProgramService;
    @Value("${befun.extension.wechat-miniprogram.version:}")
    private String Version;

    @Autowired
    private FileService fileService;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private SendGroupService sendGroupService;
    @Value("${ctm.build-send-qrcode:false}")
    private boolean buildSendQrcode;

    @SneakyThrows
    @Override
    public void afterMapToDto(SendManage entity, SendManageDto dto) {
        fillTriggerEventTypes(dto);
        if (SendManageTriggerType.EMBED.equals(dto.getTriggerType()) && dto.getSendCids() != null) {
            HashMap<Long, String> channelIdTitleMap = new HashMap<>();
            surveyChannelRepository.findAllByIdIn(dto.getSendCids()).forEach(channel -> {
                channelIdTitleMap.put(channel.getId(), channel.getName());
            });
            dto.getSendCids().forEach(cid -> {
                dto.getChannelTitles().add(channelIdTitleMap.get(cid));
            });
        }
        super.afterMapToDto(entity, dto);
    }


    @Override
    public void afterMapToDto(List<SendManage> entity, List<SendManageDto> dto) {
        dto.forEach(d -> userService.getSimple(d.getEditorId()).ifPresent(d::setEditor));
        super.afterMapToDto(entity, dto);
    }

    public void checkToken(String token) {
        if (!isUniqueInAllOrg("sendToken", token, null)) {
            throw new BadRequestException("发送id已存在");
        }
    }

    private void fillTriggerEventTypes(SendManageDto dto) {
        if (SendManageTriggerType.TRIGGER.name().equals(dto.getTriggerType().name())) {
            List<YouzanSupportEventDto> eventDtos = authYouzanService.supportEvents();
            YouzanConfig config = authYouzanService.getConfig(dto.getTriggerId());
            Optional.ofNullable(config).ifPresent(conf -> {
                conf.getRules().forEach(rule -> {
                    TriggerEventType triggerEventType = new TriggerEventType();
                    triggerEventType.setEvent(rule.getEvent());
                    eventDtos.stream().filter(eventDto -> eventDto.getEvent().equals(rule.getEvent())).map(YouzanSupportEventDto::getName).findFirst().ifPresent(triggerEventType::setName);
                    dto.getTriggerEventTypes().add(triggerEventType);
                });
            });
        }
    }

    private void updateTrigger(SendManageDto sendManageDto, List<TriggerEventType> triggerEventTypes) {
        if (SendManageTriggerType.TRIGGER.name().equals(sendManageDto.getTriggerType().name())) {
            // 需要单独创建连接器,CEM
            YouzanAuthDto youzanAuthDto = authYouzanService.getSingle("cem");
            YouzanConfig config = youzanAuthDto.getConfig();
            config.setEnable(sendManageDto.getEnable() != null && sendManageDto.getEnable()); // fix v1.10.4
            List<YouzanConfig.TriggerJourneyRule> addTriggerConfig = new ArrayList<>();
            triggerEventTypes.forEach(triggerEventType -> {
                config.getRules().stream().filter(x -> x.getEvent().equals(triggerEventType.getEvent())).findFirst().ifPresentOrElse(
                        rule -> {
                            rule.setSendManageId(sendManageDto.getId());
                            addTriggerConfig.add(rule);
                        },
                        () -> {
                            YouzanConfig.TriggerJourneyRule rule = new YouzanConfig.TriggerJourneyRule();
                            rule.setEvent(triggerEventType.getEvent());
                            rule.setSendManageId(sendManageDto.getId());
                            addTriggerConfig.add(rule);
                        }
                );
            });
            config.setRules(addTriggerConfig);
            sendManageDto.getEntity().setTriggerId(config.getConfigId());
            authYouzanService.saveSingle(youzanAuthDto);
            save(sendManageDto);
        }
    }

    @SneakyThrows
    @Override
    public <S extends BaseEntityDTO<SendManage>> SendManageDto create(S data) {
        SendManageDto sendManageData = (SendManageDto) data;
        if (ChannelType.MP.equals(sendManageData.getEmbedType()) && StringUtils.isEmpty(sendManageData.getQrCode())) {
            sendManageData.setQrCode(buildQrCode(sendManageData.getSendToken()));
        }
        checkJourneyType(sendManageData);
        checkTimerType(sendManageData);
        SendManageDto sendManageDto = super.create(data);
        updateTrigger(sendManageDto, sendManageDto.getTriggerEventTypes());
        updateGroupModifyTime(sendManageDto);
        return sendManageDto;

    }

    public void updateGroupModifyTime(SendManageDto sendManageData) {
        Optional.ofNullable(sendManageData.getGroupId()).ifPresent(groupId -> {
            Optional.ofNullable(sendGroupService.get(groupId)).ifPresent(group -> {
                group.setModifyTime(new Date());
                sendGroupService.save(group);
            });
        });
    }

    private void checkJourneyType(SendManageDto data) {
        if (data.getTriggerType() == SendManageTriggerType.JOURNEY) {
            if (StringUtils.isEmpty(data.getSendToken())) {
                throw new BadRequestException("API接口触发缺少发送id");
            } else {
                checkToken(data.getSendToken());
            }
        }
    }

    private void checkTimerType(SendManageDto data) {
        if (data.getTriggerType() == SendManageTriggerType.TIMER) {
            if (data.getRepeatTimes() != SendManageRepeatType.REPEAT) {
                throw new BadRequestException("定时重复发送类型错误");
            }
            if (data.getTriggerTimer() == null) {
                throw new BadRequestException("定时重复发送触发条件不能为空");
            }
            data.getTriggerTimer().check();
            if (data.getTriggerTimerTarget() == null) {
                throw new BadRequestException("定时重复发送目标不能为空");
            }
            data.getTriggerTimerTarget().check();
        }
    }

    private String buildQrCode(String token) throws WxErrorException {
        if (!buildSendQrcode) return null;
        File file = weChatMiniProgramService.getQrcodeService().createWxaCodeUnlimit(
                String.format("::%s", token),
                "pages/index/index",
                "/tmp/",
                false, Version, 430, false, null, false
        );
        FileInfo fileInfo = fileService.fileStorageService.of(file).upload();
        file.deleteOnExit();
        return fileInfo.getUrl();
    }

    @SneakyThrows
    public SendManageDto updateOne(long id, HashMap<String, Object> change) {
        SendManage sendManage = require(id);

        if (change.containsKey("sendFilter") && StringUtils.isEmpty(change.get("sendFilter").toString())) {
            sendManage.setSendFilter(null);
        }

        if (change.containsKey("sendToken") && StringUtils.isNotEmpty(change.get("sendToken").toString()) && !Objects.toString(change.get("sendToken")).equals(sendManage.getSendToken())
        ) {
            fileService.delete(sendManage.getQrCode());
            sendManage.setQrCode(buildQrCode(change.get("sendToken").toString()));
        }

        // 需要单独更新连接器
        SendManageDto sendManageDto = super.updateOne(id, JsonHelper.toObject(change, SendManageDto.class));
        updateTrigger(sendManageDto, sendManageDto.getTriggerEventTypes());
        updateGroupModifyTime(sendManageDto);
        return sendManageDto;
    }

    public Page<SendManageDto> findByPage(SendManagePageType type, ResourceEntityQueryDto<SendManageDto> params) {
        if (!TenantContext.getCurrentIsAdmin() || SendManagePageType.owner.equals(type)) {
            params.getQueryCriteriaList().add(new ResourceQueryCriteria("userId", TenantContext.getCurrentUserId()));
        }
        return findAll(params);
    }

    public SendManageAnalysisResponseDto analysis(Long id, SendManageAnalysisRequestDto analysisDto) {
        analysisDto.checkDate();
        SendManage sendManage = require(id);
        String dateWhere = String.format("AND DATE(smr.create_time) BETWEEN '%s' AND '%s'",
                analysisDto.getStartDate(), analysisDto.getEndDate()
        );

        SendManageAnalysisResponseDto responseDto = new SendManageAnalysisResponseDto();

        String percent = String.format("SELECT " +
                "ROUND((COUNT(CASE WHEN reply_status !=? THEN 1 END) / COUNT(*)) * 100,2) AS openPercent," +
                "ROUND((COUNT(CASE WHEN reply_status =? THEN 1 END) / COUNT(*)) * 100,2) AS finishPercent " +
                "FROM send_manage_record smr WHERE send_manage_id=? %s;", dateWhere);
        jdbcTemplate.query(percent, rs -> {
                    Float openPercent = rs.getFloat("openPercent");
                    Float finishPercent = rs.getFloat("finishPercent");
                    responseDto.setOpenPercent(openPercent);
                    responseDto.setFinishPercent(finishPercent);
                }, ReplyStatus.UN_VISIT.name(),
                ReplyStatus.SUBMIT.name(),
                sendManage.getId()
        );

        String number = String.format("SELECT " +
                "(COUNT(CASE WHEN DATE(create_time) = CURDATE() THEN 1 END)) as today," +
                "COUNT(*) AS total FROM  send_manage_record smr WHERE " +
                "reply_status=? AND send_manage_id=? %s;", dateWhere);
        jdbcTemplate.query(number, rs -> {
                    int today = rs.getInt("today");
                    int total = rs.getInt("total");
                    responseDto.setToday(today);
                    responseDto.setTotal(total);
                },
                ReplyStatus.SUBMIT.name(),
                sendManage.getId()
        );

        Map<LocalDate, Integer> sendMap = new HashMap<>();
        String trendSql = String.format("SELECT " +
                " DATE(create_time) AS date," +
                " COUNT(*) AS send " +
                " FROM send_manage_record smr " +
                " WHERE send_manage_id=? %s" +
                " GROUP BY DATE(create_time) ", dateWhere);
        jdbcTemplate.queryForList(trendSql, new Object[]{
                sendManage.getId(),
        }).forEach(rs -> {
            sendMap.put(LocalDate.parse(rs.get("date").toString()), Integer.valueOf(rs.get("send").toString()));
        });

        Map<LocalDate, Integer> submitMap = new HashMap<>();
        trendSql = String.format("SELECT" +
                " DATE(sr.finish_time) AS date," +
                " COUNT(*) AS submit" +
                " FROM send_manage_record smr" +
                " INNER JOIN survey_response sr ON smr.response_id=sr.id AND sr.status=1" +
                " WHERE send_manage_id=? AND DATE(sr.finish_time) BETWEEN '%s' AND '%s'" +
                " GROUP BY DATE(sr.finish_time) ", analysisDto.getStartDate(), analysisDto.getEndDate());
        jdbcTemplate.queryForList(trendSql, new Object[]{
                sendManage.getId(),
        }).forEach(rs -> {
            submitMap.put(LocalDate.parse(rs.get("date").toString()), Integer.valueOf(rs.get("submit").toString()));
        });
        Set<LocalDate> dates = new HashSet<>();
        dates.addAll(sendMap.keySet());
        dates.addAll(submitMap.keySet());
        dates.stream().sorted().forEach(i -> {
            SendManageAnalysisResponseDto.Trend trend = new SendManageAnalysisResponseDto.Trend();
            trend.setDate(i.toString());
            trend.setSend(sendMap.getOrDefault(i, 0));
            trend.setSubmit(submitMap.getOrDefault(i, 0));
            responseDto.getTrendList().add(trend);
        });

        String surveySql = String.format("SELECT " +
                "s.title AS name," +
                "survey_id AS id," +
                "COUNT(CASE WHEN reply_status =? THEN 1 END) AS total," +
                "ROUND((COUNT(CASE WHEN reply_status =? THEN 1 END) / COUNT(*)) * 100,2) AS submit " +
                "FROM send_manage_record smr " +
                "JOIN survey s ON s.id=smr.survey_id " +
                "WHERE send_manage_id=? %s" +
                "GROUP BY survey_id " +
                "ORDER BY survey_id;", dateWhere);
        jdbcTemplate.queryForList(surveySql, new Object[]{
                ReplyStatus.SUBMIT.name(),
                ReplyStatus.SUBMIT.name(),
                sendManage.getId()
        }).forEach(rs -> {
            SendManageAnalysisResponseDto.Survey survey = new SendManageAnalysisResponseDto.Survey();
            survey.setName(rs.get("name").toString());
            survey.setId(Long.valueOf(rs.get("id").toString()));
            survey.setTotal(Integer.valueOf(rs.get("total").toString()));
            survey.setPercent(Float.valueOf(rs.get("submit").toString()));
            responseDto.getSurveyList().add(survey);
        });

        return responseDto;
    }

    public int countCustomer(CustomerSelectedDto target) {
        target.check();
        return customerService.countSelectCustomer(TenantContext.getCurrentTenant(), TenantContext.getCurrentUserId(), target);
    }

    public HashMap<Long, Integer> countGroupItems(Set<Long> groupIds) {
        HashMap<Long, Integer> counts = new HashMap<>();

        if (CollectionUtils.isNotEmpty(groupIds)) {
            String sql = "SELECT group_id, COUNT(*) AS count FROM send_manage WHERE group_id IN (" + StringUtils.join(groupIds, ",") + ") GROUP BY group_id";
            jdbcTemplate.query(sql, rs -> {
                counts.put(rs.getLong("group_id"), rs.getInt("count"));
            });
        }

        return counts;
    }

    @Override
    public Boolean deleteOne(long id) {
        updateGroupModifyTime(mapToDto(get(id)));
        return super.deleteOne(id);
    }
}

