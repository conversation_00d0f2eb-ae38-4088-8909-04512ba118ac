package cn.hanyi.ctm.service;


import cn.hanyi.ctm.dto.SendSimpleListDto;
import cn.hanyi.ctm.entity.SendGroup;
import cn.hanyi.ctm.entity.SendGroupDto;
import cn.hanyi.ctm.entity.SendManage;
import cn.hanyi.ctm.repository.SendGroupRepository;
import org.befun.auth.entity.User;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.UserService;
import org.befun.core.constant.EntityScopeStrategyType;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.BaseMixedGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class SendGroupService extends BaseMixedGroupService<
        SendGroup, SendGroupDto, SendGroupRepository,
        SendManage, SendSimpleListDto, SendCustomQueryService> {

    @Autowired
    private UserService userService;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private SendManageService sendManageService;

    @Override
    public <S extends BaseEntityDTO<SendGroup>> SendGroupDto updateOne(long id, S change) {
        if (!currentIsAdmin()) {
            return scopeQuery(EntityScopeStrategyType.OWNER_CORPORATION, () -> super.updateOne(id, change));
        }
        return super.updateOne(id, change);
    }


    private void setCustomStrategy() {
        TenantContext.addCustomEntityScopeStrategy(SendGroup.class, EntityScopeStrategyType.OWNER);
    }

    @Override
    public List<SendGroupDto> findAll(ResourceEntityQueryDto<SendGroupDto> queryDto) {
        // 如果查询没有指定排序 默认使用modifyTime倒序
        if (queryDto.getSorts() == Sort.unsorted()) {
            queryDto.setSorts(Sort.by(Sort.Direction.DESC, "modifyTime"));
        }

        setCustomStrategy();
        List<SendGroupDto> list = super.findAll(queryDto);
        if (list == null) {
            list = new ArrayList<>();
        }
        list.add(0, getEmptyGroup());
        return list;
    }

    public SendGroupDto getEmptyGroup() {
        Long orgId = TenantContext.getCurrentTenant();
        SendGroupDto defaultGroup = new SendGroupDto();
        defaultGroup.setOrgId(orgId);
        defaultGroup.setId(0L);
        defaultGroup.setSequence(0);
        defaultGroup.setTitle("项目列表");
        return defaultGroup;
    }

    @Override
    public void afterMapToDto(List<SendGroup> entity, List<SendGroupDto> dto) {
        Set<Long> updateUserIds = new HashSet<>();
        updateUserIds.addAll(entity.stream().map(SendGroup::getEditorId).collect(Collectors.toSet()));
        updateUserIds.addAll(entity.stream().map(SendGroup::getUserId).collect(Collectors.toSet()));
        SimpleUser superAdmin = userService.getAdminUser(TenantContext.getCurrentTenant());
        Map<Long, SimpleUser> userMap = userService.getGroupMapByIds(new ArrayList<>(updateUserIds), User::getId, SimpleUser::fromUser);
        Map<Long, Integer> numSendMap = sendManageService.countGroupItems(entity.stream().map(SendGroup::getId).collect(Collectors.toSet()));

        dto.forEach(d -> {
            Optional.ofNullable(d.getEditorId()).ifPresent(i -> d.setEditorUser(userMap.getOrDefault(i, superAdmin)));
            Optional.ofNullable(d.getUserId()).ifPresent(i -> d.setCreator(userMap.getOrDefault(i, superAdmin)));
            d.setNumOfSends(numSendMap.getOrDefault(d.getId(), 0));
        });
        // dto 根据modifyTime倒序
        dto.sort(Comparator.comparing(SendGroupDto::getModifyTime).reversed());
        super.afterMapToDto(entity, dto);
    }

    public boolean changeGroup(Long targetGroupId, List<Long> sendIds) {
        if (targetGroupId != 0) {
            checkIsCurrentOrg(require(targetGroupId));
        }
        Long userId = TenantContext.getCurrentUserId();
        List<SendManage> list = sendManageService.getRepository().findAllById(sendIds);
        list.forEach(i -> {
            sendManageService.checkIsCurrentOrg(i);
            if (userId == null || !userId.equals(i.getUserId())) {
                throw new BadRequestException();
            }
            i.setGroupId(targetGroupId);
            sendManageService.updateGroupModifyTime(sendManageService.mapToDto(i));
        });
        sendManageService.getRepository().saveAll(list);
        return true;
    }
}
