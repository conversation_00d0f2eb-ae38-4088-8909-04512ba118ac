package cn.hanyi.ctm.service;

import cn.hanyi.ctm.dto.SendSearchDto;
import cn.hanyi.ctm.dto.SendSimpleListDto;
import cn.hanyi.ctm.entity.SendGroup;
import cn.hanyi.ctm.entity.SendGroupDto;
import cn.hanyi.ctm.entity.SendManage;
import cn.hanyi.ctm.repository.SendManageRepository;
import cn.hanyi.survey.core.repository.SurveyRepository;
import org.befun.auth.constant.ResourcePermissionType;
import org.befun.auth.entity.User;
import org.befun.auth.projection.SimpleUser;
import org.befun.auth.service.OrganizationConfigService;
import org.befun.auth.service.UserService;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.service.BaseMixedResourceService;
import org.befun.core.service.MapperService;
import org.befun.core.service.ResourceCorporationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class SendCustomQueryService extends BaseMixedResourceService<
        SendManage, SendSimpleListDto, SendManageRepository,
        SendGroup, SendGroupDto, SendGroupService> {

    @Autowired
    private SurveyRepository surveyRepository;

    @Autowired
    @Lazy
    private SendManageService sendManageService;

    @Autowired
    private SendGroupService sendGroupService;

    @Autowired
    private UserService userService;

    @Autowired
    private MapperService mapperService;

    @Autowired
    private ResourceCorporationService resourceCorporationService;

    @Autowired
    private OrganizationConfigService organizationConfigService;


    @Override
    public void afterMapToDto(List<SendManage> sendManageList, List<SendSimpleListDto> sendDtoList) {
        Set<Long> updateUserIds = new HashSet<>();
        Set<Long> sendIds = new HashSet<>();

        sendManageList.forEach(send -> {
            Optional.ofNullable(send.getEditorId()).ifPresent(updateUserIds::add);
            Optional.ofNullable(send.getUserId()).ifPresent(updateUserIds::add);
            Optional.ofNullable(send.getId()).ifPresent(sendIds::add);
        });

        SimpleUser superAdmin = userService.getAdminUser(TenantContext.getCurrentTenant());
        Map<Long, SimpleUser> userMap = userService.getGroupMapByIds(new ArrayList<>(updateUserIds), User::getId, SimpleUser::fromUser);

        sendDtoList.forEach(sendSimpleListDto -> {
            SendManage send = sendSimpleListDto.getEntity();
            Optional.ofNullable(send.getEditorId()).ifPresent(i -> sendSimpleListDto.setEditorUser(userMap.getOrDefault(i, superAdmin)));
            Optional.ofNullable(send.getUserId()).ifPresent(i -> sendSimpleListDto.setCreator(userMap.getOrDefault(i, superAdmin)));
        });
    }

    public Page<SendSimpleListDto> findPageByGroup(SendSearchDto dto) {
        Page<SendSimpleListDto> sendSimpleListDtos;
        sendSimpleListDtos = findAll(dto);
        List<SendSimpleListDto> list = sendSimpleListDtos.getContent();
        Map<Integer, List<SendSimpleListDto>> s = list.stream().collect(Collectors.groupingBy(SendSimpleListDto::getItemType));
        Optional.ofNullable(s.get(1)).ifPresent(l -> resourceCorporationService.fillResourcePermissionInfo(l, ResourcePermissionType.SEND.name(), SendSimpleListDto::getId));
        Optional.ofNullable(s.get(2)).ifPresent(l -> resourceCorporationService.fillResourcePermissionInfo(l, ResourcePermissionType.SEND_GROUP.name(), ss -> ss.getGroup().getId()));
        sendSimpleListDtos.getContent().stream().filter(x -> x.getGroup() != null).forEach(x -> x.setId(x.getGroup().getId()));
        return sendSimpleListDtos;
    }
}