package cn.hanyi.ctm.service;

import cn.hanyi.ctm.entity.DataAccessCell;
import cn.hanyi.ctm.entity.DataAccessCellDto;
import cn.hanyi.ctm.repository.DataAccessCellRepository;
import lombok.extern.slf4j.Slf4j;
import org.befun.core.service.BaseService;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DataAccessCellService extends BaseService<DataAccessCell, DataAccessCellDto, DataAccessCellRepository> {

}

