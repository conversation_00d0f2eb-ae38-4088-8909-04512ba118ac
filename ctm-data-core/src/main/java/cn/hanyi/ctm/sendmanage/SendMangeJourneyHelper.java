package cn.hanyi.ctm.sendmanage;

import cn.hanyi.ctm.constant.SendManageChannelType;
import cn.hanyi.ctm.constant.SendManageTriggerType;
import cn.hanyi.ctm.dto.SendChannelConfigDto;
import cn.hanyi.ctm.dto.TemplateInfoDto;
import cn.hanyi.ctm.dto.customer.CustomerAddJourneyDto;
import cn.hanyi.ctm.dto.customer.CustomerSelectedDto;
import cn.hanyi.ctm.entity.Customer;
import cn.hanyi.ctm.entity.CustomerJourneyRecord;
import cn.hanyi.ctm.entity.SendManage;
import cn.hanyi.ctm.entity.SendManageRecord;
import cn.hanyi.ctm.repository.SendManageRepository;
import cn.hanyi.ctm.service.CustomerJourneyRecordService;
import cn.hanyi.ctm.workertrigger.dto.CustomerBatchAddJourneyDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.auth.constant.UserTaskType;
import org.befun.core.exception.BadRequestException;
import org.befun.core.rest.context.TenantContext;
import org.befun.core.utils.DateHelper;
import org.befun.core.utils.JsonHelper;
import org.befun.task.entity.TaskProgress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class SendMangeJourneyHelper extends SendMangeHelper {

    @Autowired
    private CustomerJourneyRecordService customerJourneyRecordService;
    @Autowired
    private SendManageRepository sendManageRepository;

    @Transactional
    public SendManageRecord sendByJourney(Long orgId, Long userId, Long sendManageId, Long journeyId, Long customerId, Long departmentId, Long taskProgressId, Map<String, Object> params) {
        return sendByJourney(orgId, userId, sendManageId, journeyId, customerService.get(customerId), departmentId, taskProgressId, params);
    }

    @Transactional
    public SendManageRecord sendByJourney(Long orgId, Long userId, Long sendManageId, Long journeyId, Customer customer, Long departmentId, Long taskProgressId, Map<String, Object> params) {
        if (customer == null) {
            return null;
        }
        SendManage sendManage = getSendManageById(orgId, sendManageId);
        if (sendManage == null) {
            return null;
        }
        if (journeyId == null) {
            journeyId = 0L;
        }
        replaceCustomerInfo(params, customer);
        SendManageRecord sendManageRecord = sendManage(orgId, userId, journeyId, taskProgressId, sendManage, params, () -> true, () -> customer);
        if (sendManageRecord != null && journeyId > 0 && customer.getId() != null && customer.getId() > 0) {
            // 只要有发送记录，不论是不是提前结束的状态，都记录客户历程
            CustomerJourneyRecord record = customerJourneyRecordService.addJourneyRecord(userId, customer.getId(), journeyId, departmentId);
            updateCustomerLatestJourneyRecord(record.getCustomerId(), record.getJourneyTitle(), record.getDepartmentTitle());
        }
        return sendManageRecord;
    }

    public SendManage getSendManageByJourney(Long orgId, Long journeyId) {
        // 关联的推送
        List<SendManage> sendManageList = sendManageRepository.findByOrgIdAndTriggerTypeAndTriggerIdAndEnable(orgId, SendManageTriggerType.JOURNEY, journeyId, true);
        if (CollectionUtils.isEmpty(sendManageList)) {
            return null;
        }
        return sendManageList.get(0);
    }

    private Long checkSendManageId(Long orgId, Long journeyId) {
        SendManage sendManage;
        if (journeyId != null && journeyId > 0) {
            sendManage = getSendManageByJourney(orgId, journeyId);
            if (sendManage != null) {
                return sendManage.getId();
            }
        }
        throw new BadRequestException("场景不存在");
    }

    /**
     * 单次替换客户信息
     */
    private void replaceCustomerInfo(Map<String, Object> urlParams, Customer customer) {
        if (MapUtils.isNotEmpty(urlParams)) {
            Object username = urlParams.get("username");
            Object externalUserId = urlParams.get("externalUserId");
            Object mobile = urlParams.get("mobile");
            Object email = urlParams.get("email");
            Object appId = urlParams.get("appId");
            Object openId = urlParams.get("openId");
            if (username instanceof String) {
                customer.setUsername((String) username);
            }
            if (mobile instanceof String && StringUtils.isEmpty(customer.getMobile())) {
                customer.setMobile((String) mobile);
            }
            if (email instanceof String && StringUtils.isEmpty(customer.getEmail())) {
                customer.setEmail((String) email);
            }
            if (externalUserId instanceof String && StringUtils.isEmpty(customer.getExternalUserId())) {
                customer.setExternalUserId((String) externalUserId);
            }
            if (appId instanceof String) {
                customer.getWechatParams().setAppId((String) appId);
            }
            if (openId instanceof String) {
                customer.getWechatParams().setOpenId((String) openId);
            }
        }
    }

    private void updateCustomerLatestJourneyRecord(Long customerId, String journeyTitle, String departmentTitle) {
        Customer customer = customerService.get(customerId);
        if (customer == null) {
            return;
        }
        String course = String.format("%s@%s", journeyTitle, departmentTitle);
        customer.setLatestJourneyRecord(course);
        customerService.save(customer);
    }

    public Integer checkSmsCost(Long orgId, Long userId, Long sendManageId, CustomerSelectedDto dto) {
        TemplateInfoDto templateInfo = getSmsTemplateBySendManage(orgId, sendManageId);
        if (templateInfo != null) {
            int count = customerService.countSelectCustomer(orgId, userId, dto);
            if (count > 0) {
                customerMessageService.checkSmsCost(orgId, templateInfo, count);
            }
            return count;
        }
        return null;
    }

    public void checkSmsCost(Long orgId, Long sendManageId, int customerSize) {
        TemplateInfoDto templateInfo = getSmsTemplateBySendManage(orgId, sendManageId);
        if (templateInfo != null) {
            customerMessageService.checkSmsCost(orgId, templateInfo, customerSize);
        }
    }

    public TemplateInfoDto getSmsTemplateBySendManage(Long orgId, Long sendManageId) {
        SendManage sendManage = getSendManageById(orgId, sendManageId);
        if (sendManage != null && CollectionUtils.isNotEmpty(sendManage.getChannel())) {
            SendChannelConfigDto channel = sendManage.getChannel().stream().filter(i -> i.getType() == SendManageChannelType.SMS).findFirst().orElse(null);
            if (channel != null && channel.getThirdpartyTemplateId() != null) {
                return customerMessageService.getSmsTemplate(channel.getThirdpartyTemplateId(), (String) channel.getContent().get("content"));
            }
        }
        return null;
    }

    /**
     * v 1.10.7 取消预扣费
     */
    @Transactional
    public boolean addCustomerJourney(CustomerAddJourneyDto dto) {
        Long orgId = TenantContext.getCurrentTenant();
        Long userId = TenantContext.getCurrentUserId();
        Long sendManageId = checkSendManageId(orgId, dto.getJourneyId());
        dto.setSendManageId(sendManageId);
        int countSelectCustomers = customerService.countSelectCustomer(orgId, userId, dto);
        TaskProgress taskProgress = userTaskService.createTask(orgId, userId, UserTaskType.batchAddJourney, countSelectCustomers, dto, sendManageId);
        CustomerBatchAddJourneyDto taskDto = new CustomerBatchAddJourneyDto(orgId, userId, taskProgress.getId(), JsonHelper.toJson(dto));
        ctmTaskTrigger.customerBatchAddJourney(taskDto, null);
        return true;
    }

    /**
     * v 1.10.7 取消预扣费
     */
    @Transactional
    public boolean addCustomerJourneyByTimer(SendManage sendManage, LocalDateTime triggerTime) {
        Long orgId = sendManage.getOrgId();
        Long userId = sendManage.getUserId();
        Long sendManageId = sendManage.getId();
        CustomerAddJourneyDto dto = new CustomerAddJourneyDto();
        sendManage.getTriggerTimerTarget().copyTo(dto).setSendManageId(sendManageId);
        int countSelectCustomers = customerService.countSelectCustomer(orgId, userId, dto);
        TaskProgress taskProgress = userTaskService.createTask(orgId, userId, UserTaskType.timerAddJourney, countSelectCustomers, dto, sendManageId);
        CustomerBatchAddJourneyDto taskDto = new CustomerBatchAddJourneyDto(orgId, userId, taskProgress.getId(), JsonHelper.toJson(dto));
        ctmTaskTrigger.customerBatchAddJourney(taskDto, Duration.between(LocalDateTime.now(), triggerTime));
        sendManage.setLastTriggerTimer(DateHelper.toDate(triggerTime));
        sendManageRepository.save(sendManage);
        return true;
    }


    @Transactional
    public boolean addCustomerJourneyByOpenApi(Long orgId, Long userId, Long sendManageId, Long journeyId, Long customerId, Long departmentId, Map<String, Object> params) {
        checkSmsCost(orgId, sendManageId, 1);
        CustomerAddJourneyDto dto = new CustomerAddJourneyDto();
        dto.setDepartmentId(departmentId);
        dto.setJourneyId(journeyId);
        dto.setSendManageId(sendManageId);
        dto.setSelectType(0);
        dto.setSelectCustomerIds(List.of(customerId));
        dto.setUrlCustomParams(params);
        CustomerBatchAddJourneyDto taskDto = new CustomerBatchAddJourneyDto(orgId, userId, null, JsonHelper.toJson(dto));
        ctmTaskTrigger.customerBatchAddJourney(taskDto, null);
        return true;
    }

}
