package cn.hanyi.ctm.sendmanage;

import cn.hanyi.ctm.constant.DataAccessCellStatus;
import cn.hanyi.ctm.constant.SendManageTriggerType;
import cn.hanyi.ctm.dto.SendManageDataAccessesDto;
import cn.hanyi.ctm.entity.Customer;
import cn.hanyi.ctm.entity.DataAccess;
import cn.hanyi.ctm.entity.DataAccessCell;
import cn.hanyi.ctm.entity.SendManage;
import cn.hanyi.ctm.repository.SendManageRepository;
import cn.hanyi.ctm.service.DataAccessCellService;
import cn.hanyi.survey.core.service.expression.ExpressionService;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.utils.JsonHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
@Component
public class SendMangeDataAccessHelper extends SendMangeHelper {

    @Autowired
    private DataAccessCellService dataAccessCellService;
    @Autowired
    private SendManageRepository sendManageRepository;
    @Autowired
    private ExpressionService expressionService;

    @Transactional
    public void sendByDataAccess(Long orgId, Long dataAccessCellId) {
        DataAccessCell cell = dataAccessCellService.get(dataAccessCellId);
        // 已处理，直接结束
        if (cell == null || cell.getStatus() == null || cell.getStatus().isCompleted()) {
            return;
        }
        DataAccess dataAccess = cell.getDataAccess();
        // 已禁用，修改状态后结束
        if (dataAccess == null || dataAccess.getEnable() == null || !dataAccess.getEnable()) {
            cell.setStatus(DataAccessCellStatus.DISABLED);
            dataAccessCellService.save(cell);
            return;
        }
        Map<String, Object> params = JsonHelper.toMap(cell.getParsedParams());
        if (params == null || params.isEmpty()) {
            cell.setStatus(DataAccessCellStatus.IGNORE);
            dataAccessCellService.save(cell);
            return;
        }
        // 关联的推送
        List<SendManage> sendManageList = sendManageRepository.findByOrgIdAndTriggerTypeAndEnable(orgId, SendManageTriggerType.ACCESS, true);
        List<SendManage> relatedSendManageList = sendManageList.stream().filter(s -> s.getDataAccesses().stream().anyMatch(d -> dataAccess.getId().equals(d.getDataAccessId()))).collect(
                Collectors.toList());
        if (CollectionUtils.isEmpty(sendManageList)) {
            cell.setStatus(DataAccessCellStatus.IGNORE);
            dataAccessCellService.save(cell);
            return;
        }
        AtomicInteger successCount = new AtomicInteger();
        AtomicReference<Customer> customer = new AtomicReference<>();
        relatedSendManageList.forEach(i -> {
            Optional<SendManageDataAccessesDto> relatedSendManageDataAccess = i.getDataAccesses().stream()
                    .filter(d -> dataAccess.getId().equals(d.getDataAccessId())).findFirst();

            if (relatedSendManageDataAccess.isPresent()) {
                boolean success = false;
                try {
                    success = sendManage(orgId, null, dataAccessCellId, i, params,
                            () -> checkFilter(relatedSendManageDataAccess.get().getConditions(), params),
                            () -> getOrCreateCustomer(orgId, customer, params)
                    );
                } catch (Throwable e) {
                    log.error("sendByDataAccess error {}", e.getMessage());
                }
                if (success) {
                    successCount.incrementAndGet();
                }
            }
        });
        cell.setStatus(successCount.get() > 0 ? DataAccessCellStatus.SUCCESS : DataAccessCellStatus.FAILURE);
        dataAccessCellService.save(cell);
    }

    private boolean checkFilter(String sendFilter, Map<String, Object> params) {
        if (StringUtils.isNotEmpty(sendFilter)) {
            Boolean filter = expressionService.triggerExpression(sendFilter, params);
            return filter != null && filter;
        }
        return true;
    }

    private Customer getOrCreateCustomer(Long orgId, AtomicReference<Customer> customerContainer, Map<String, Object> params) {
        Customer customer = customerContainer.get();
        if (customer != null) {
            return customer;
        }
        String externalUserId = (String) params.get("externalUserId");
        String mobile = (String) params.get("mobile");
        String departmentCode = (String) params.get("departmentCode");
        customer = customerService.findCustomer(orgId, null, externalUserId, null, null, mobile);
        if (customer == null) {
            customer = customerService.createCustomer(orgId, externalUserId, null, null, null, departmentCode, mobile, null, null);
        }
        customerContainer.set(customer);
        return customer;
    }


}
