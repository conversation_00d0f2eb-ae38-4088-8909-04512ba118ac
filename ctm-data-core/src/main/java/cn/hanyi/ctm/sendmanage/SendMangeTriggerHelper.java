package cn.hanyi.ctm.sendmanage;

import cn.hanyi.ctm.entity.Customer;
import cn.hanyi.ctm.entity.SendManage;
import cn.hanyi.ctm.entity.SendManageRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Map;

@Slf4j
@Component
public class SendMangeTriggerHelper extends SendMangeHelper {

    @Transactional
    public SendManageRecord sendByTrigger(Long orgId, Long userId, Long sendMangeId, Long messageId, Long customerId, Map<String, Object> params) {
        Customer customer = customerService.get(customerId);
        if (customer == null) {
            return null;
        }
        SendManage sendManage = getSendManageById(orgId, sendMangeId);
        if (sendManage == null) {
            return null;
        }
        return sendManage(orgId, userId, messageId, null, sendManage, params, () -> true, () -> customer);
    }


}
