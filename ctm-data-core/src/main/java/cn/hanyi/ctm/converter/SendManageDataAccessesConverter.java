package cn.hanyi.ctm.converter;

import cn.hanyi.ctm.dto.SendManageDataAccessesDto;
import java.util.List;
import javax.persistence.AttributeConverter;
import javax.persistence.Converter;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.utils.JsonHelper;

@Converter
public class SendManageDataAccessesConverter implements AttributeConverter<List<SendManageDataAccessesDto>, String> {


    @Override
    public String convertToDatabaseColumn(List<SendManageDataAccessesDto> dto) {
        if (dto == null) {
            return null;
        } else {
            return JsonHelper.toJson(dto);
        }
    }

    @Override
    public List<SendManageDataAccessesDto> convertToEntityAttribute(String dbData) {
        if (StringUtils.isEmpty(dbData)) {
            return null;
        } else {
            return JsonHelper.toList(dbData, SendManageDataAccessesDto.class);
        }
    }
}