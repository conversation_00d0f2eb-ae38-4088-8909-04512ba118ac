package cn.hanyi.ctm.dto;

import cn.hanyi.ctm.entity.DataAccess;
import cn.hanyi.ctm.entity.DataAccessParams;
import lombok.Getter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
public class DataHubContextDto extends DataAccessContextDto<DataHubDataDto> {

    public DataHubContextDto(DataHubDataDto data, DataAccess dataAccess, List<DataAccessParams> dataAccessParams) {
        super(data, dataAccess, dataAccessParams);
    }
}
