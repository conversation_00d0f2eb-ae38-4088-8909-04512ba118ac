package cn.hanyi.ctm.dto.ext;

import cn.hanyi.ctm.entity.SendGroup;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.projection.SimpleUser;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;


@Getter
@Setter
public class SendGroupExtDto extends BaseEntityDTO<SendGroup> {

    public SendGroupExtDto() {
    }

    public SendGroupExtDto(SendGroup entity) {
        super(entity);
    }

    @Schema(description = "创建人，可能为null")
    @JsonView(ResourceViews.Basic.class)
    private SimpleUser creator;

    @Schema(description = "修改人，可能为null")
    @JsonView(ResourceViews.Basic.class)
    private SimpleUser editorUser;

    @Schema(description = "项目数")
    @JsonView(ResourceViews.Basic.class)
    private Integer numOfSends = 0;

}
