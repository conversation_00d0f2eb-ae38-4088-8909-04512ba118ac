package cn.hanyi.ctm.dto;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

import javax.validation.constraints.NotEmpty;

@Setter
@Getter
public class DataHubConfigDto {
    @NotEmpty
    @JsonView(ResourceViews.Basic.class)
    private String endPoint;
    @NotEmpty
    @JsonView(ResourceViews.Basic.class)
    private String accessId;
    @NotEmpty
    @JsonView(ResourceViews.Basic.class)
    private String accessKey;
    @NotEmpty
    @JsonView(ResourceViews.Basic.class)
    private String projectName;
    @NotEmpty
    @JsonView(ResourceViews.Basic.class)
    private String topicName;
    @NotEmpty
    @JsonView(ResourceViews.Basic.class)
    private String subId;
}
