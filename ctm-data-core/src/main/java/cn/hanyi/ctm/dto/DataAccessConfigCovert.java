package cn.hanyi.ctm.dto;


import lombok.extern.slf4j.Slf4j;
import org.befun.core.utils.JsonHelper;

import javax.persistence.AttributeConverter;

/**
 * The class description
 *
 * <AUTHOR>
 */
@Slf4j
public class DataAccessConfigCovert implements AttributeConverter<DataConfigDto, String> {

    @Override
    public String convertToDatabaseColumn(DataConfigDto customerInfo) {
        return JsonHelper.toJson(customerInfo);
    }

    @Override
    public DataConfigDto convertToEntityAttribute(String customerInfoJSON) {
        return JsonHelper.toObject(customerInfoJSON, DataConfigDto.class);
    }

}

