package cn.hanyi.ctm.dto;

import com.fasterxml.jackson.annotation.JsonView;
import lombok.Value;
import org.befun.core.rest.view.ResourceViews;

import java.util.Date;

@Value
public class DataAccessSimpleDto {
  @JsonView(ResourceViews.Basic.class)
  private Long id;
  @JsonView(ResourceViews.Basic.class)
  private Date createTime;
  @JsonView(ResourceViews.Basic.class)
  private Date modifyTime;
  @JsonView(ResourceViews.Basic.class)
  private String name;
  @JsonView(ResourceViews.Basic.class)
  private Boolean enable = false;

}
