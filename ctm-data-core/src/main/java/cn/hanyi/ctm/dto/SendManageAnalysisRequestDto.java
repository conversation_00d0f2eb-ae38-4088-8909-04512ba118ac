package cn.hanyi.ctm.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.exception.BadRequestException;

import java.time.LocalDate;

@Setter
@Getter
public class SendManageAnalysisRequestDto {

    @Schema(description = "提交日期, 默认30天前")
    private String startDate;

    @Schema(description = "结束日期")
    private String endDate;

    public void checkDate() {
        LocalDate start, end;
        if (StringUtils.isNotEmpty(startDate)) {
            start = LocalDate.parse(startDate);
        } else {
            throw new BadRequestException("开始时间不能为空");
        }
        if (StringUtils.isNotEmpty(endDate)) {
            end = LocalDate.parse(endDate);
        } else {
            throw new BadRequestException("结束时间不能为空");
        }
        if (start.isAfter(end)) {
            throw new BadRequestException("开始时间不能大于结束时间");
        }
        startDate = start.toString();
        endDate = end.toString();
    }
}
