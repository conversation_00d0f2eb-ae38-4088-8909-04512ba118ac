package cn.hanyi.ctm.dto;

import cn.hanyi.ctm.constant.SendManageRepeatType;
import cn.hanyi.ctm.entity.SendGroup;
import cn.hanyi.ctm.entity.SendGroupDto;
import cn.hanyi.ctm.entity.SendManage;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import org.befun.core.constant.QueryOperator;
import org.befun.core.dto.query.ResourceCustomQueryWithGroupDto;
import org.befun.core.dto.query.ResourceEntityQueryDto;
import org.befun.core.dto.query.ResourceQueryCriteria;
import org.befun.core.rest.view.ResourceViews;
import org.springframework.data.domain.Sort;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;

@Getter
@Setter
public class SendSearchDto extends ResourceCustomQueryWithGroupDto<SendManage, SendSimpleListDto, SendGroup, SendGroupDto> {

    @JsonView(ResourceViews.Basic.class)
    @Enumerated(EnumType.STRING)
    private SendManageRepeatType repeatTimes;

    @JsonView(ResourceViews.Basic.class)
    private Boolean enable;

    @Override
    public Boolean getHasGroup() {
        return repeatTimes == null && enable == null;
    }

    @Override
    public ResourceEntityQueryDto<SendSimpleListDto> transformQueryResource(Set<Long> excludeGroupIds) {
        ResourceEntityQueryDto<SendSimpleListDto> queryDto = super.transformQueryResource(excludeGroupIds);
        buildSearchCriteria().forEach(queryDto::addCriteria);
        if (StringUtils.isNotEmpty(getQ())) {
            if (StringUtils.isNumeric(getQ()) && getQ().length() >= 16) {
                queryDto.addCriteria(new ResourceQueryCriteria("id", getQ()));
                queryDto.setQ(null);
            }
        }
        if (queryDto.getSorts() == null || queryDto.getSorts().isUnsorted()) {
            queryDto.setSorts(Sort.by(Sort.Direction.DESC, "modifyTime"));
        }
        return queryDto;
    }

    public List<ResourceQueryCriteria> buildSearchCriteria() {

        List<ResourceQueryCriteria> criteria = new ArrayList<>();

        Optional.ofNullable(repeatTimes).ifPresent(rt -> criteria.add(new ResourceQueryCriteria("repeatTimes", rt, QueryOperator.EQUAL)));
        Optional.ofNullable(enable).ifPresent(e -> criteria.add(new ResourceQueryCriteria("enable", e, QueryOperator.EQUAL)));
        return criteria;
    }
}
