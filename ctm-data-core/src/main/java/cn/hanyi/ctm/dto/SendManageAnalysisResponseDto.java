package cn.hanyi.ctm.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

@Setter
@Getter
public class SendManageAnalysisResponseDto {
    private Integer total = 0;
    private Integer today = 0;
    private Float openPercent = 0.00f;
    private Float finishPercent = 0.00f;

    private List<Trend> trendList = new ArrayList<>();
    private List<Survey> surveyList = new ArrayList<>();

    @Setter
    @Getter
    public static class Survey {
        private String name;
        private Long id;
        private Integer total = 0;
        private Float percent = 0.00f;
    }

    @Setter
    @Getter
    public static class Trend {
        private String date;
        private Integer send = 0;
        private Integer submit = 0;
    }
}
