package cn.hanyi.ctm.dto;

import cn.hanyi.ctm.constant.SendManageChannelLevel;
import cn.hanyi.ctm.constant.SendManageChannelType;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

import java.util.Map;

@Setter
@Getter
public class SendChannelConfigDto {

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "等级")
    private SendManageChannelLevel level = SendManageChannelLevel.LOW;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "推送渠道")
    private SendManageChannelType type;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "推送渠道(短信|邮件)时的模版id")
    private Long thirdpartyTemplateId;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "推送渠道微信时的模版id")
    private Long wechatOpenTemplateId;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "推送渠道Api时的api id")
    private Long apiConnectorId;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "推送渠道Api时的条件")
    private String apiCondition;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "推送自定义内容")
    private Map<String, Object> content;
}
