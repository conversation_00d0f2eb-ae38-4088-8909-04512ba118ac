package cn.hanyi.ctm.dto;

import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SendManageDataAccessesDto {

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "数据接入Id")
    private Long dataAccessId;

    @JsonView(ResourceViews.Basic.class)
    @Schema(description = "数据接入筛选")
    private String conditions;

}
