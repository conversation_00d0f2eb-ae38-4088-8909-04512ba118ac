package cn.hanyi.ctm.dto;

import cn.hanyi.ctm.entity.DataAccess;
import cn.hanyi.ctm.entity.DataAccessParams;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
public class DataAccessConsumer {
    private DataAccess dataAccess;
    private List<DataAccessParams> params;
    private Map result;
}
