package cn.hanyi.ctm.dto.ext;

import cn.hanyi.ctm.entity.SendManage;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.befun.core.dto.BaseEntityDTO;
import org.befun.core.rest.view.ResourceViews;

import java.util.ArrayList;
import java.util.List;


@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SendManageChannelDto extends BaseEntityDTO<SendManage> {

    public SendManageChannelDto(SendManage entity) {
        super(entity);
    }

    @JsonView(ResourceViews.Detail.class)
    private List<String> channelTitles= new ArrayList<>();

}
