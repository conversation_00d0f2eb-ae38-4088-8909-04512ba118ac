package cn.hanyi.ctm.dto;

import cn.hanyi.ctm.constant.SendManageRepeatType;
import cn.hanyi.ctm.constant.SendType;
import cn.hanyi.ctm.entity.SendGroupDto;
import cn.hanyi.ctm.entity.SendManage;
import com.fasterxml.jackson.annotation.JsonView;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.befun.auth.projection.SimpleUser;
import org.befun.core.dto.BaseResourcePermissionEntityDto;
import org.befun.core.dto.EntityWithGroupDTO;
import org.befun.core.rest.view.ResourceViews;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.util.Date;


@Getter
@Setter
public class SendSimpleListDto extends BaseResourcePermissionEntityDto<SendManage> implements EntityWithGroupDTO<SendGroupDto> {

    @JsonView(ResourceViews.Basic.class)
    Long id;
    @JsonView(ResourceViews.Basic.class)
    String title;

    @JsonView(ResourceViews.Basic.class)
    Date modifyTime;

    @JsonView(ResourceViews.Basic.class)
    Date createTime;

    @JsonView(ResourceViews.Basic.class)
    private Boolean enable;


    @Schema(description = "修改人，可能为null")
    @JsonView(ResourceViews.Basic.class)
    private SimpleUser editorUser;

    @Schema(description = "创建人，可能为null")
    @JsonView(ResourceViews.Basic.class)
    private SimpleUser creator;

    @Schema(description = "是否有分享给其他人")
    @JsonView(ResourceViews.Basic.class)
    private Boolean hasShared;

    @Schema(description = "项目类型：1 问卷 2 目录")
    @JsonView(ResourceViews.Basic.class)
    private int itemType = 1;

    @Schema(description = "目录信息")
    @JsonView(ResourceViews.Basic.class)
    private SendGroupDto group;

    @Schema(description = "问卷名称")
    @JsonView(ResourceViews.Basic.class)
    private String surveyTitles;

    @Schema(description = "发送类型")
    @JsonView(ResourceViews.Basic.class)
    @Enumerated(EnumType.STRING)
    private SendType sendType;

    @Schema(description = "重复类型")
    @JsonView(ResourceViews.Basic.class)
    @Enumerated(EnumType.STRING)
    private SendManageRepeatType repeatTimes;

}
