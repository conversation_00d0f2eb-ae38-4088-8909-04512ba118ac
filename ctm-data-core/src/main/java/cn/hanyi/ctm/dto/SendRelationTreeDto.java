package cn.hanyi.ctm.dto;

import cn.hanyi.ctm.dto.journey.PushChannelConfigDto;
import com.fasterxml.jackson.annotation.JsonView;
import lombok.Getter;
import lombok.Setter;
import org.befun.core.rest.view.ResourceViews;

import java.util.List;

@Setter
@Getter
public class SendRelationTreeDto extends PushChannelConfigDto {

    @JsonView(ResourceViews.Basic.class)
    private String id;

    @JsonView(ResourceViews.Basic.class)
    private String title;

    @JsonView(ResourceViews.Basic.class)
    private List<SendRelationTreeDto> children = List.of();
}
