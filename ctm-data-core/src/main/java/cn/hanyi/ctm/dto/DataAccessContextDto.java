package cn.hanyi.ctm.dto;

import cn.hanyi.ctm.entity.DataAccess;
import cn.hanyi.ctm.entity.DataAccessParams;
import lombok.Getter;
import lombok.Setter;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Getter
@Setter
public class DataAccessContextDto<DATA> {
    private final DATA data;
    private final DataAccess dataAccess;
    private final List<DataAccessParams> dataAccessParams;

    private String messageId;
    private Map<String, Object> extraData = new HashMap<>();
    private final Map<String, Object> parsedParameters = new HashMap<>();

    public DataAccessContextDto(DATA data, DataAccess dataAccess, List<DataAccessParams> dataAccessParams) {
        this.data = data;
        this.dataAccess = dataAccess;
        this.dataAccessParams = dataAccessParams;
    }
}
