#!/usr/bin/env bash

dir=$(pwd)
if [[ ${dir} == *"sh" ]]
then
  source .env
else
  cd sh
  source .env
fi
cd ..

dir_core=$DIR_CORE
dir_auth=$DIR_AUTH
dir_ctm=$DIR_CTM
dir_survey=$DIR_SURVEY
dir_worker=$DIR_WORKER

revision=$1

echo "开始替换所有模块版本号：$revision pwd=`pwd`"
cd ../${dir_core}
./sh/replace-revision.sh $revision
cd ../${dir_auth}
./sh/replace-revision.sh $revision
cd ../${dir_ctm}
./sh/replace-revision.sh $revision
cd ../${dir_survey}
./sh/replace-revision.sh $revision
cd ../${dir_worker}
./sh/replace-revision.sh $revision