#!/usr/bin/env bash

dir=$(pwd)
if [[ ${dir} == *"sh" ]]
then
  source .env
else
  cd sh
  source .env
fi
cd ..

dir_core=$DIR_CORE
dir_auth=$DIR_AUTH
dir_ctm=$DIR_CTM
dir_survey=$DIR_SURVEY
dir_worker=$DIR_WORKER

revision=$1
sha1=$2
changelist=$3

echo "开始替换所有模块版本号：$revision.$sha1-$changelist pwd=`pwd`"
cd ../${dir_core}
./sh/replace-version.sh $revision $sha1 $changelist
cd ../${dir_auth}
./sh/replace-version.sh $revision $sha1 $changelist
cd ../${dir_ctm}
./sh/replace-version.sh $revision $sha1 $changelist
cd ../${dir_survey}
./sh/replace-version.sh $revision $sha1 $changelist
cd ../${dir_worker}
./sh/replace-version.sh $revision $sha1 $changelist

echo "开始 install $dir_core"
cd ../${dir_core}
./sh/install.sh $revision $sha1 $changelist

echo "开始 install $dir_auth befun-auth-trigger"
cd ../${dir_auth}
./sh/install.sh $revision $sha1 $changelist 0 befun-auth-trigger

echo "开始 install $dir_ctm ctm-trigger"
cd ../${dir_ctm}
./sh/install.sh $revision $sha1 $changelist 0 ctm-trigger

echo "开始 install ${dir_survey} survey-trigger"
cd ../${dir_survey}
./sh/install.sh $revision $sha1 $changelist 0 survey-trigger

echo "开始 install ${dir_worker} cem-core"
cd ../${dir_worker}
./sh/install.sh $revision $sha1 $changelist 0 cem-core

echo "开始 install $dir_auth 所有模块"
cd ../${dir_auth}
./sh/install.sh $revision $sha1 $changelist

echo "开始 install $dir_ctm ctm-common-core"
cd ../${dir_ctm}
./sh/install.sh $revision $sha1 $changelist 0 ctm-common-core

echo "开始 install ${dir_survey} survey-core"
cd ../${dir_survey}
./sh/install.sh $revision $sha1 $changelist 0 survey-core

echo "开始 install $dir_ctm 所有模块"
cd ../${dir_ctm}
./sh/install.sh $revision $sha1 $changelist

echo "开始 install ${dir_survey} 所有模块"
cd ../${dir_survey}
./sh/install.sh $revision $sha1 $changelist