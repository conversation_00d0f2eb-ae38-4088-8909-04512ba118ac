#!/usr/bin/env bash

dir=$(pwd)
if [[ ${dir} == *"sh" ]]
then
  source .env
else
  cd sh
  source .env
fi
cd ..

dir_core=$DIR_CORE
dir_auth=$DIR_AUTH
dir_ctm=$DIR_CTM
dir_survey=$DIR_SURVEY
dir_worker=$DIR_WORKER

#从pom文件中获取当前的版本信息
revision=`grep '<revision>' pom.xml | cut -d \> -f 2 | cut -d \< -f 1`
sha1=`grep '<sha1>' pom.xml | cut -d \> -f 2 | cut -d \< -f 1`
changelist=`grep '<changelist>' pom.xml | cut -d \> -f 2 | cut -d \< -f 1`

cd ..
echo "开始提交所有模块：`pwd`"

cd ${dir_core}
./sh/commit.sh "$revision.$sha1-$changelist"

cd ../${dir_worker}
./sh/commit.sh "$revision.$sha1-$changelist"

cd ../${dir_auth}
./sh/commit.sh "$revision.$sha1-$changelist"

cd ../${dir_ctm}
./sh/commit.sh "$revision.$sha1-$changelist"

cd ../${dir_survey}
./sh/commit.sh "$revision.$sha1-$changelist"
