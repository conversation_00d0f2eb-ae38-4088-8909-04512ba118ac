#!/usr/bin/env bash

dir=$(pwd)
if [[ ${dir} == *"sh" ]]
then
  source .env
else
  cd sh
  source .env
fi
cd ..

#从命令行参数获取当前的版本信息
revision=$1
sha1=$2
changelist=$3
replace_version=$4
install_modules=$5

echo "$?"

#是否需要替换pom版本
if [[ $replace_version ]]
then
  ./sh/replace-version.sh $revision $sha1 $changelist
fi

#从pom文件中获取当前的版本信息
default_revision=`grep '<revision>' pom.xml | cut -d \> -f 2 | cut -d \< -f 1`
default_sha1=`grep '<sha1>' pom.xml | cut -d \> -f 2 | cut -d \< -f 1`
default_changelist=`grep '<changelist>' pom.xml | cut -d \> -f 2 | cut -d \< -f 1`

#maven命令行版本号参数
drevision=
dsha1=
dchangelist=

if [[ $revision ]]
then
  drevision="-Drevision=$revision"
else
  revision=$default_revision
fi

if [[ $sha1 ]]
then
  dsha1="-Dsha1=$sha1"
else
  sha1=$default_sha1
fi

if [[ $changelist ]]
then
  dchangelist="-Dchangelist=$changelist"
else
  changelist=$default_changelist
fi

if [[ $install_modules ]]
then
  install_modules=$install_modules
else
  install_modules=$MODULES
  echo "使用默认模块 $MODULES"
fi

echo "开始安装${PROJECT_NAME}的模块：$install_modules"

echo "version:$revision.$sha1-$changelist"
echo "mvn clean install -DskipTests=true $drevision $dsha1 $dchangelist -pl $install_modules -am"
mvn -P rdc clean install -DskipTests=true $drevision $dsha1 $dchangelist -pl $install_modules -am
