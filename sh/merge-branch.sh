#!/usr/bin/env bash

dir=$(pwd)
if [[ ${dir} == *"sh" ]]
then
  source .env
else
  cd sh
  source .env
fi
cd ..

feature="feature/$REVISION"

git checkout $feature

git_status=`git status`

echo $git_status

echo `git pull`

echo `git checkout develop`
echo `git pull`
echo `git merge $feature`

if [[ $1 ]]
then
    echo `git checkout test`
    echo `git pull`
    echo `git merge develop`
fi

git checkout $feature
