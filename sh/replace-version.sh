#!/usr/bin/env bash

revision=$1
new_sha1=$2
new_changelist=$3
new_version=${revision}.${new_sha1}-${new_changelist}
old_revision=`grep -n '<revision>' pom.xml | cut -d \> -f 2 | cut -d \< -f 1`
old_sha1=`grep -n '<sha1>' pom.xml | cut -d \> -f 2 | cut -d \< -f 1`
old_changelist=`grep -n '<changelist>' pom.xml | cut -d \> -f 2 | cut -d \< -f 1`
old_core=`grep -n '<core.version>' pom.xml | cut -d \> -f 2 | cut -d \< -f 1`
old_auth=`grep -n '<auth.version>' pom.xml | cut -d \> -f 2 | cut -d \< -f 1`
old_ctm=`grep -n '<ctm.version>' pom.xml | cut -d \> -f 2 | cut -d \< -f 1`
old_survey=`grep -n '<survey.version>' pom.xml | cut -d \> -f 2 | cut -d \< -f 1`
old_worker=`grep -n '<worker.version>' pom.xml | cut -d \> -f 2 | cut -d \< -f 1`
replace_all="s/<revision>${old_revision}<\/revision>/<revision>${revision}<\/revision>/;"
replace_all+="s/<sha1>${old_sha1}<\/sha1>/<sha1>${new_sha1}<\/sha1>/;"
replace_all+="s/<core.version>${old_core}<\/core.version>/<core.version>${new_version}<\/core.version>/;"
replace_all+="s/<auth.version>${old_auth}<\/auth.version>/<auth.version>${new_version}<\/auth.version>/;"
replace_all+="s/<ctm.version>${old_ctm}<\/ctm.version>/<ctm.version>${new_version}<\/ctm.version>/;"
replace_all+="s/<survey.version>${old_survey}<\/survey.version>/<survey.version>${new_version}<\/survey.version>/;"
replace_all+="s/<worker.version>${old_worker}<\/worker.version>/<worker.version>${new_version}<\/worker.version>/;"
replace_all+="s/<changelist>${old_changelist}<\/changelist>/<changelist>${new_changelist}<\/changelist>/"
echo $replace_all
sed -i '' -e $replace_all pom.xml