CREATE TABLE `cem_platform`.`resource_permission` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键',
  `org_id` BIGINT NULL COMMENT '企业id',
  `user_id` BIGINT NULL COMMENT '用户id',
  `resource_id` BIGINT NULL COMMENT '资源id',
  `resource_type` VARCHAR(45) NULL COMMENT '资源类型：JOURNEY 客户旅程；',
  `relation_type` VARCHAR(45) NULL COMMENT '用户和资源的关系：由具体的资源定义',
  `modify_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`))
COMMENT = '用户和资源的关系';

ALTER TABLE `cem_platform`.`journey_map`
ADD COLUMN `owner_user_id` BIGINT NULL COMMENT '拥有者id' AFTER `org_id`,
ADD COLUMN `cover` VARCHAR(255) NULL COMMENT '封面' AFTER `title`,
ADD COLUMN `modify_user_id` BIGINT NULL COMMENT '最后修改者id' AFTER `cover`,
ADD COLUMN `modify_user_truename` VARCHAR(45) NULL COMMENT '最后修改者姓名' AFTER `modify_user_id`;

-- 修改旅程的默认标题
update `cem_platform`.`journey_map` set title = '我的旅程' where (title = '' or title is null) and id > 0;