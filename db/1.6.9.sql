ALTER TABLE `cem_platform`.`role`
ADD COLUMN `type` INT NULL COMMENT '角色类型：1 超级管理员(不能编辑删除) 3 成员(只能编辑) 4 其他(可以编辑删除)' AFTER `editable`;

CREATE TABLE `user_invitation` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `org_id` bigint NOT NULL COMMENT '企业id',
  `user_id` bigint NOT NULL COMMENT '被邀请人id',
  `from_user_id` bigint NOT NULL COMMENT '邀请人id',
  `code` varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT '邀请编号',
  `expire_date` datetime DEFAULT NULL COMMENT '过期事件',
  `status` int DEFAULT '1' COMMENT '状态：1 未发送 2 已发送 0 无效',
  `modify_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户邀请记录';

update role set type = 1 , editable = 0 where name = '超级管理员' and id > 0;
update role set type = 3 , editable = 2 where name = '成员' and id > 0;
update role set type = 4 , editable = 1 where name != '超级管理员' and name != '成员' and id > 0;