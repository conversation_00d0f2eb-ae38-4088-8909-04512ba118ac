CREATE TABLE `cem_platform`.`event_result_permission` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `event_id` BIGINT NULL COMMENT '事件id',
  `type` INT NULL COMMENT '关系类型：0 角色id 1 用户id',
  `relation_id` BIGINT NULL COMMENT '关系id，由type确定id的类型',
  `modify_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`));
ALTER TABLE `cem_platform`.`event_result_permission`
ADD INDEX `relation_idx` (`event_id` ASC, `type` ASC, `relation_id` ASC) ;

CREATE TABLE `cem_platform`.`link` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `url` VARCHAR(500) NULL COMMENT '原始地址',
  `modify_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `create_time` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`));
ALTER TABLE `cem_platform`.`link`
ADD INDEX `url_idx` (`url` ASC) ;

ALTER TABLE `user` ADD `guide_info` VARCHAR(200)  NULL  DEFAULT NULL  COMMENT '用户指导完成状态'  AFTER `modify_time`;

ALTER TABLE `cem_platform`.`organization`
ADD COLUMN `optional_limit` VARCHAR(127) NULL DEFAULT NULL COMMENT 'child_user_limit：子账户数量限制 customer_lifecycle_limit：客户旅程数量限制 surveys_limit：问卷数量限制' AFTER `wework_expire`,
ADD COLUMN `auto_sync_customer` INT NULL DEFAULT 1 COMMENT '是否自动同步客户：1 自动同步 2 手动同步' AFTER `optional_limit`;

ALTER TABLE `cem_platform`.`customer`
ADD COLUMN `external_user_id` VARCHAR(64) NULL COMMENT '外部客户id，由企业自定义' AFTER `department_ids`;
ALTER TABLE `cem_platform`.`customer`
DROP INDEX `orgId_index` ;
ALTER TABLE `cem_platform`.`customer`
ADD INDEX `idx_externalUserId` (`org_id` ASC, `external_user_id` ASC);

ALTER TABLE `cem_platform`.`event_result`
CHANGE COLUMN `create_time` `create_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ,
CHANGE COLUMN `modify_time` `modify_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP ;

UPDATE `cem_platform`.`user` SET `guide_info` = 'CUSTOMER_CENTER,JOURNEY_MAP,SURVEY' ;


-- 修改短链转发规则 https://dev-t.xmplsu.cn/123456789 -> https://dev.xmplus.cn/api/ctm/t/123456789

-- bi
--INSERT INTO `cem_platform`.`menu` (`id`, `create_time`, `modify_time`, `display`, `full_name`, `full_path`, `name`, `path`, `pid`, `type`) VALUES
--('53', now(), now(), '1', '数据洞察',             '/Bi',                   '数据洞察', 'Bi', '0', '1'),
--('54', now(), now(), '1', '数据洞察/数据看板',     '/Bi/Index',             '数据看板',        'Index', '53', '2'),
--('55', now(), now(), '1', '数据洞察/数据看板/查看', '/Bi/Index/view',        '查看',           'view', '54', '3'),
--('56', now(), now(), '1', '数据洞察/数据看板/编辑', '/Bi/Index/edit',        '编辑',           'edit', '54', '3'),
--('57', now(), now(), '1', '数据洞察/数据源',       '/Bi/Datasource',        '数据源',        'Datasource', '53', '2'),
--('58', now(), now(), '1', '数据洞察/数据源/查看',   '/Bi/Datasource/view',   '查看',          'view', '57', '3'),
--('59', now(), now(), '1', '数据洞察/数据源/编辑',   '/Bi/Datasource/edit',   '编辑',          'edit', '57', '3'),
--('60', now(), now(), '1', '数据洞察/数据集',       '/Bi/Dataset',           '数据集',        'Dataset', '53', '2'),
--('61', now(), now(), '1', '数据洞察/数据集/查看',   '/Bi/Dataset/view',      '查看',          'view', '60', '3'),
--('62', now(), now(), '1', '数据洞察/数据集/编辑',   '/Bi/Dataset/edit',      '编辑',          'edit', '60', '3');


--INSERT INTO `cem_platform`.`permission` (`role_id`, `module`, `permission`, `create_time`, `modify_time`) VALUES
--( '3179', 'Action', '/Bi/Index/view', now(), now()),
--( '3179', 'Action', '/Bi/Index/edit', now(), now()),
--( '3179', 'Action', '/Bi/Datasource/view', now(), now()),
--( '3179', 'Action', '/Bi/Datasource/edit', now(), now()),
--( '3179', 'Action', '/Bi/Dataset/view', now(), now()),
--( '3179', 'Action', '/Bi/Dataset/edit', now(), now());



INSERT INTO `cem_platform`.`element_curve` (`org_id`, `journey_id`, `component_id`, `description`, `layout`, `color`, `emotion_type`) select jjcc.orgId,jjcc.jid,jjcc.jcid,'',132,'#c6c8cc', 2 from element_curve as ec RIGHT JOIN (select jjc.id as jid,jc.id as jcid,jc.org_id as orgId from (select j.id,jc.journey_map_id from journey j INNER JOIN journey_component jc on j.component_id=jc.id) jjc INNER JOIN journey_component as jc on jjc.journey_map_id=jc.journey_map_id where jc.type='curve') as jjcc on ec.journey_id=jjcc.jid and ec.component_id=jjcc.jcid where ec.id is NULL;


INSERT INTO `cem_platform`.`element_curve_publish` (`org_id`, `journey_id`, `component_id`, `description`, `layout`, `color`, `emotion_type`) select jjcc.orgId,jjcc.jid,jjcc.jcid,'',132,'#c6c8cc', 2 from element_curve_publish as ec RIGHT JOIN (select jjc.id as jid,jc.id as jcid,jc.org_id as orgId from (select j.id,jc.journey_map_id from journey_publish j INNER JOIN journey_component_publish jc on j.component_id=jc.id) jjc INNER JOIN journey_component_publish as jc on jjc.journey_map_id=jc.journey_map_id where jc.type='curve') as jjcc on ec.journey_id=jjcc.jid and ec.component_id=jjcc.jcid where ec.id is NULL;

